# RQRSDA2025

A human-in-the-loop collaborative framework for AI-assisted software development.

## Getting Started

### Prerequisites

- Node.js 18+
- Docker
- Supabase CLI

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/mouimet-infinisoft/rqrsda2025.git
   cd rqrsda2025
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Set up the local development environment:
   ```bash
   npm run setup:local
   ```

4. Start the development server:
   ```bash
   npm run dev
   ```

## Supabase Setup

This project uses Supabase for authentication, database, storage, and edge functions.

### Local Development

The local development environment is set up automatically using the setup script:

```bash
npm run setup:local
```

### Production Environment

To set up the production environment:

1. Create a Supabase Cloud project
2. Link your local project to the cloud project:
   ```bash
   ./scripts/link-supabase-cloud.sh <project-ref>
   ```
3. Apply migrations to the cloud project:
   ```bash
   npm run cloud:db:migration:up
   ```

For detailed instructions, see [Supabase Setup Guide](docs/supabase-setup.md).

## Available Scripts

- `npm run dev` - Start the development server
- `npm run build` - Build the production application
- `npm run start` - Start the production server
- `npm run lint` - Run ESLint
- `npm run format` - Format code with Prettier
- `npm run test` - Run tests
- `npm run test:coverage` - Run tests with coverage report

### Supabase Scripts

- `npm run supabase:start` - Start local Supabase
- `npm run supabase:stop` - Stop local Supabase
- `npm run supabase:reset` - Reset local Supabase database
- `npm run db:migration:new -- <name>` - Create a new migration
- `npm run db:migration:up` - Apply migrations locally
- `npm run cloud:db:migration:up` - Apply migrations to the linked cloud project
- `npm run test:rls` - Test Row Level Security policies

## Project Structure

- `src/` - Application source code
  - `app/` - Next.js app router pages
  - `components/` - React components
  - `utils/` - Utility functions
    - `supabase/` - Supabase utilities
  - `i18n/` - Internationalization files
- `supabase/` - Supabase configuration and migrations
- `scripts/` - Utility scripts
- `docs/` - Documentation

## License

This project is licensed under the MIT License - see the LICENSE file for details.
