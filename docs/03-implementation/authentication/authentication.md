# Authentication System Documentation

## Overview

The RQRSDA 2025 application uses Supabase for authentication with server-side handling. This document outlines the authentication architecture, components, and flows implemented in the system.

## Architecture

### Server-Side Authentication

The application uses a server-side authentication approach with Next.js App Router and Supabase. This provides several benefits:

- Enhanced security by keeping authentication tokens server-side
- Simplified client-side code without the need for context providers
- Better performance by reducing client-side JavaScript

### Key Components

1. **Supabase Client**
   - Server-side client: `src/utils/supabase/server.ts`
   - Middleware client: `src/utils/supabase/middleware.ts`
   - Client-side client: `src/utils/supabase/client.ts`

2. **Authentication Actions**
   - Located in `src/lib/auth/actions.ts`
   - Server actions for sign-in, sign-out, password reset

3. **Authentication Helpers**
   - Located in `src/lib/auth/helpers.ts`
   - Functions for checking authentication status and user roles

4. **UI Components**
   - Sign-in form: `src/components/login-form.tsx`
   - Reset password form: `src/components/reset-password-form.tsx`
   - Update password form: `src/components/update-password-form.tsx`

5. **Protected Pages**
   - Dashboard: `src/app/[lang]/dashboard/page.tsx`
   - Admin page: `src/app/[lang]/admin/page.tsx`
   - Coordinator page: `src/app/[lang]/coordinator/page.tsx`

## Authentication Flows

### Sign-In Flow

1. User enters email and password in the sign-in form
2. Form data is submitted to the `signIn` server action
3. Supabase authenticates the user and creates a session
4. Session is stored in cookies
5. User is redirected to the dashboard

```typescript
// Example sign-in implementation
async function onSubmit(event: React.FormEvent<HTMLFormElement>) {
  event.preventDefault();
  const formData = new FormData(event.currentTarget);
  const result = await signIn(formData);

  if (result?.error) {
    // Handle error
  }
  // Redirect handled by server action
}
```

### Sign-Out Flow

1. User clicks the sign-out button
2. Form is submitted to the `signOut` server action
3. Supabase invalidates the session
4. Session cookie is cleared
5. User is redirected to the sign-in page

```typescript
// Example sign-out implementation
<form action={signOut}>
  <Button variant="outline">Sign out</Button>
</form>
```

### Password Reset Flow

1. **Request Password Reset**
   - User enters email in the reset password form
   - Form data is submitted to the `resetPassword` server action
   - Supabase sends a password reset email with a token
   - User is shown a success message

2. **Update Password**
   - User clicks the link in the email
   - User is directed to the update password page
   - User enters a new password
   - Form data is submitted to the `updatePassword` server action
   - Password is updated in Supabase
   - User is redirected to the sign-in page

## Role-Based Access Control

The application implements role-based access control using Supabase Row Level Security (RLS) policies and user metadata.

### User Roles

- **Director**: Full access to all features
- **Coordinator**: Access to coordinator features and regular user features
- **User**: Access to basic features only

### Role Checking

Role checking is implemented in the `getUserRole` helper function:

```typescript
export async function getUserRole(): Promise<string | null> {
  const supabase = createClient();
  const { data: { user } } = await supabase.auth.getUser();

  if (!user) return null;

  // Get user role from database
  const { data, error } = await supabase
    .from('user_roles')
    .select('role')
    .eq('user_id', user.id)
    .single();

  if (error || !data) return null;

  return data.role;
}
```

### Protected Routes

Protected routes check for authentication and appropriate roles:

```typescript
// Example of a protected route
export default async function AdminPage({ params }: { params: Promise<{ lang: string }> }) {
  const { lang } = await params;

  // Check authentication
  const authenticated = await isAuthenticated();
  if (!authenticated) {
    redirect(`/${lang}/auth/signin`);
  }

  // Check role
  const userRole = await getUserRole();
  if (userRole !== "Director") {
    redirect(`/${lang}/dashboard`);
  }

  // Render admin page content
  // ...
}
```

## Session Management

Sessions are managed using Supabase's cookie-based session handling:

1. **Session Creation**: When a user signs in, Supabase creates a session and stores it in cookies
2. **Session Validation**: The middleware validates the session on each request
3. **Session Refresh**: Sessions are automatically refreshed when needed
4. **Session Termination**: When a user signs out, the session is invalidated

## Error Handling

Authentication errors are handled and displayed to the user:

1. **Sign-In Errors**: Invalid credentials, account not found, etc.
2. **Password Reset Errors**: Email not found, invalid token, etc.
3. **Update Password Errors**: Password too weak, token expired, etc.

## Internationalization

Authentication components support internationalization through the Next.js App Router's locale system:

- Routes are prefixed with the language code: `/[lang]/auth/signin`
- Text is loaded from language-specific dictionaries

## Security Considerations

1. **CSRF Protection**: Built into Next.js server actions
2. **XSS Protection**: React's built-in protection and CSP headers
3. **Session Security**: HTTP-only cookies for session storage
4. **Password Security**: Handled by Supabase with proper hashing and salting

## Future Improvements

1. **Audit Logging**: Add logging for authentication events
