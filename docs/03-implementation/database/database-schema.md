# Database Schema

## Organization Schema

### organizations

| Column      | Type      | Description                                      |
|-------------|-----------|--------------------------------------------------|
| id          | UUID      | Primary key                                      |
| name        | TEXT      | Organization name                                |
| status      | TEXT      | Organization status (active, inactive, suspended)|
| phones      | JSONB     | Phone numbers with types                         |
| emails      | JSONB     | Email addresses with types                       |
| address     | TEXT      | Full address as a single string                  |
| website_url | TEXT      | Organization website URL                         |
| logo_url    | TEXT      | Organization logo URL                            |
| settings    | JSONB     | Organization settings                            |
| created_at  | TIMESTAMP | Creation timestamp                               |
| updated_at  | TIMESTAMP | Last update timestamp                            |

Example of phones JSONB:
```json
{
  "main": "************",
  "fax": "************"
}
```

Example of emails JSONB:
```json
{
  "info": "<EMAIL>",
  "support": "<EMAIL>"
}
```

Example of settings JSONB:
```json
{
  "theme": {
    "primary": "#4B9CD3",
    "secondary": "#F5A623"
  },
  "features": {
    "scheduling": true,
    "reporting": true
  }
}
```

### locations

| Column         | Type      | Description                           |
|----------------|-----------|---------------------------------------|
| id             | UUID      | Primary key                           |
| organization_id| UUID      | Foreign key to organizations          |
| name           | TEXT      | Location name                         |
| status         | TEXT      | Location status (active, inactive)    |
| phones         | JSONB     | Phone numbers with types              |
| emails         | JSONB     | Email addresses with types            |
| address        | TEXT      | Full address as a single string       |
| created_at     | TIMESTAMP | Creation timestamp                    |
| updated_at     | TIMESTAMP | Last update timestamp                 |

### rooms

| Column         | Type      | Description                                    |
|----------------|-----------|------------------------------------------------|
| id             | UUID      | Primary key                                    |
| location_id    | UUID      | Foreign key to locations                       |
| organization_id| UUID      | Foreign key to organizations (for RLS)         |
| name           | TEXT      | Room name                                      |
| description    | TEXT      | Room description                               |
| capacity       | INTEGER   | Room capacity                                  |
| status         | TEXT      | Room status (active, inactive, maintenance)    |
| created_at     | TIMESTAMP | Creation timestamp                             |
| updated_at     | TIMESTAMP | Last update timestamp                          |

### business_hours

| Column         | Type      | Description                                    |
|----------------|-----------|------------------------------------------------|
| id             | UUID      | Primary key                                    |
| organization_id| UUID      | Foreign key to organizations                   |
| location_id    | UUID      | Foreign key to locations (nullable)            |
| day_of_week    | INTEGER   | Day of week (0-6, where 0 is Sunday)           |
| start_time     | TIME      | Start time                                     |
| end_time       | TIME      | End time                                       |
| is_closed      | BOOLEAN   | Whether the location is closed on this day     |
| created_at     | TIMESTAMP | Creation timestamp                             |
| updated_at     | TIMESTAMP | Last update timestamp                          |

## Row-Level Security

All tables have row-level security enabled with the following policies:

1. Users can only view data from their own organization
2. Directors can manage (insert, update, delete) data for their own organization
3. System administrators can manage all data across organizations

## Validation Functions

The schema includes validation functions for JSONB fields:

### validate_emails_jsonb

Validates that a JSONB object contains valid email addresses as values.

Example of valid emails JSONB:
```json
{
  "main": "<EMAIL>",
  "support": "<EMAIL>"
}
```

### validate_phones_jsonb

Validates that a JSONB object contains valid phone numbers as values.

Example of valid phones JSONB:
```json
{
  "main": "************",
  "fax": "************"
}
```

### validate_org_settings_jsonb

Validates that a JSONB object contains a valid organization settings structure.

Example of valid settings JSONB:
```json
{
  "theme": {
    "primary": "#4B9CD3",
    "secondary": "#F5A623"
  },
  "features": {
    "scheduling": true,
    "reporting": true
  }
}
```

## Automatic Timestamp Updates

All tables with `updated_at` columns have triggers that automatically update the timestamp whenever a row is updated.
