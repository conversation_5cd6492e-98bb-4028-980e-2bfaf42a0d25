# Supabase Setup Guide

This document outlines the setup process for Supa<PERSON> in both local development and production environments.

## Environment Overview

The project uses two Supabase environments:

1. **Local Development Environment**: Uses Supabase CLI for local development
2. **Production Environment**: Uses Supabase Cloud for the production deployment

## Local Development Setup

The local development environment is set up automatically using the setup script:

```bash
npm run setup:local
```

This script:
1. Checks for prerequisites (Docker, Node.js, etc.)
2. Initializes Supabase using `npx supabase init`
3. Starts Supabase using `npx supabase start`
4. Configures environment variables in `.env.local`

### Manual Setup

If you need to set up Supabase manually:

1. Initialize Supabase:
   ```bash
   npx supabase init
   ```

2. Start Supabase:
   ```bash
   npx supabase start
   ```

3. Copy the output values to your `.env.local` file:
   ```
   NEXT_PUBLIC_SUPABASE_URL=http://127.0.0.1:54321
   NEXT_PUBLIC_SUPABASE_ANON_KEY=<anon-key>
   SUPABASE_SERVICE_ROLE_KEY=<service-role-key>
   SUPABASE_DB_URL=postgresql://postgres:postgres@127.0.0.1:54322/postgres
   SUPABASE_STUDIO_URL=http://127.0.0.1:54323
   ```

### Managing Local Supabase

- Start Supabase: `npm run supabase:start`
- Stop Supabase: `npm run supabase:stop`
- Reset Supabase database: `npm run supabase:reset`

## Production (Supabase Cloud) Setup

### Creating a Supabase Cloud Project

1. Go to [Supabase Dashboard](https://app.supabase.io/)
2. Click "New Project"
3. Enter project details:
   - Name: `rqrsda2025-prod`
   - Database Password: (Generate a secure password)
   - Region: (Choose the closest region to your users)
4. Click "Create Project"

### Linking Local Project to Supabase Cloud

1. Get the project reference ID from the Supabase dashboard URL:
   - Example: `https://app.supabase.com/project/abcdefghijklmnopqrst`
   - The reference ID is `abcdefghijklmnopqrst`

2. Link your local project to the cloud project:
   ```bash
   npx supabase link --project-ref abcdefghijklmnopqrst
   ```

3. When prompted, enter your Supabase access token (found in Supabase dashboard under Account > Access Tokens)

### Applying Migrations to Supabase Cloud

After linking your project, you can apply migrations to the cloud project:

```bash
npm run cloud:db:migration:up
```

This will apply all migrations in the `supabase/migrations` directory to the linked cloud project.

### Setting Up Environment Variables for Production

Create a `.env.production` file with the following variables:

```
NEXT_PUBLIC_SUPABASE_URL=https://abcdefghijklmnopqrst.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=<anon-key>
SUPABASE_SERVICE_ROLE_KEY=<service-role-key>
```

You can find these values in the Supabase dashboard under Project Settings > API.

## Database Migrations

### Creating a New Migration

To create a new migration:

```bash
npm run db:migration:new -- migration_name
```

This will create a new migration file in `supabase/migrations` with a timestamp prefix.

### Applying Migrations Locally

To apply migrations to your local Supabase instance:

```bash
npm run db:migration:up
```

### Applying Migrations to Supabase Cloud

To apply migrations to the linked Supabase Cloud project:

```bash
npm run cloud:db:migration:up
```

## Seeding Data

To seed authentication data:

```bash
npm run seed:auth
```

This script creates initial users and roles in the database.

## Testing Row Level Security (RLS) Policies

To test RLS policies:

```bash
npm run test:rls
```

This runs pgTAP tests to verify that RLS policies are working correctly.

## Troubleshooting

### Local Development Issues

- If Supabase fails to start, try stopping it first: `npm run supabase:stop`
- If you encounter database connection issues, try resetting the database: `npm run supabase:reset`
- Check Docker is running and has sufficient resources allocated

### Cloud Migration Issues

- Ensure you have linked the correct project reference ID
- Verify your Supabase access token is valid
- Check that your migrations are compatible with the Supabase Cloud environment
