# Domain Implementation Guide

This guide provides a simple, step-by-step approach to implementing a new domain in the RQRSDA2025 application using the template-single-crud as a reference.

## Introduction

The RQRSDA2025 application follows a standardized folder structure for all domains, with a clear separation of concerns and a consistent approach to implementing features. The `template-single-crud` directory serves as the single source of truth for how to structure and implement new domains.

## High-Level Plan

To create a new domain from scratch:

1. **Plan your domain** - Define purpose, entities, and permissions
2. **Create database schema** - Set up tables with proper relationships and RLS policies
3. **Set up folder structure** - Follow the template pattern for consistent organization
4. **Implement services** - Create data access services for your domain
5. **Create server actions** - Implement server-side logic for CRUD operations
6. **Build UI components** - Develop reusable components for your domain
7. **Implement pages** - Create pages with proper loading, error, and not-found states
8. **Add authorization** - Configure permissions for your domain
9. **Test thoroughly** - Ensure everything works as expected

## Step-by-Step Guide

### 1. Domain Planning

Before writing any code:

- Define the domain's purpose and scope
- Identify key entities and their relationships
- List all operations (create, read, update, delete)
- Define permissions in the format `domain:feature:action`

### 2. Database Implementation

Create your database schema:

```sql
-- Example table structure (adapt to your needs)
CREATE TABLE your_domain_items (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  organization_id UUID REFERENCES organizations(id) NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  status TEXT NOT NULL CHECK (status IN ('active', 'inactive', 'draft')) DEFAULT 'active',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX idx_your_domain_items_org_id ON your_domain_items(organization_id);

-- Enable Row-Level Security
ALTER TABLE your_domain_items ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view items from their organization"
  ON your_domain_items FOR SELECT
  USING (auth.user_belongs_to_organization(organization_id));
```

### 3. Authorization Configuration

Configure permissions for your domain:

1. Update `src/lib/authorization/config/routePermissions.ts` with your domain's routes
2. Update `src/lib/authorization/services/ConfigurationService.ts` with role permissions

### 4. Folder Structure Setup

Create your domain's folder structure following the template pattern:

```bash
# Replace your-domain and your-feature with your actual domain and feature names
mkdir -p src/app/[lang]/protected/your-domain/{lib/{types,services},(features)/your-feature/{actions,components,lib/{types,services},(pages)/{list,create,[id]/{view,edit,remove}}}}
```

The structure should look like:

```
your-domain/
├── lib/
│   ├── types/
│   └── services/
└── (features)/
    └── your-feature/
        ├── actions/
        ├── components/
        ├── lib/
        │   ├── types/
        │   └── services/
        └── (pages)/
            ├── list/
            ├── create/
            └── [id]/
                ├── view/
                ├── edit/
                └── remove/
```

### 5. Types Implementation

Create domain-specific types:

1. Create `src/app/[lang]/protected/your-domain/lib/types/index.ts` for domain-level types
2. Create `src/app/[lang]/protected/your-domain/(features)/your-feature/lib/types/index.ts` for feature-specific types

Reference the template-single-crud implementation for type definitions.

### 6. Services Implementation

Create services to interact with your database:

1. Create `src/app/[lang]/protected/your-domain/lib/services/YourDomainService.ts` for domain-level services
2. Create `src/app/[lang]/protected/your-domain/(features)/your-feature/lib/services/YourFeatureService.ts` for feature-specific services

Follow the pattern in Feature1Service.ts, which includes:
- Consistent response formats using ServiceResponse
- Proper error handling
- Pagination for list operations
- Organization-scoped queries

### 7. Server Actions Implementation

Create server actions for CRUD operations:

1. Create individual action files in `src/app/[lang]/protected/your-domain/(features)/your-feature/actions/`:
   - `create.ts` - Create new items
   - `list.ts` - List items with pagination and search
   - `view.ts` - View a single item
   - `edit.ts` - Update an existing item
   - `remove.ts` - Delete an item

2. Create an `index.ts` file to export all actions

Follow the pattern in the template-single-crud actions, which:
- Use server-side validation
- Return consistent ActionState responses
- Include proper error handling
- Use permission decorators for authorization

### 8. UI Components Implementation

Create reusable UI components:

1. Create form components for create and edit operations
2. Create view components for displaying items
3. Create list components for displaying collections of items

Follow the pattern in the template-single-crud components, which:
- Separate concerns into small, focused components
- Use shadcn/ui components for consistent styling
- Handle loading, error, and empty states

### 9. Page Implementation

Create pages with proper loading, error, and not-found states:

1. For each page (list, create, view, edit, remove), create:
   - `page.tsx` - The main page component
   - `loading.tsx` - Loading state
   - `error.tsx` - Error handling
   - `not-found.tsx` (where appropriate) - Not found state

Follow the pattern in the template-single-crud pages, which:
- Use server components for data fetching
- Handle errors gracefully
- Show appropriate loading states
- Redirect after successful operations

## Best Practices

1. **Single Responsibility Principle** - Each component, service, and action should have a single responsibility
2. **Consistent Error Handling** - Use the standard error response formats
3. **Proper Loading States** - Always include loading states for better UX
4. **Type Safety** - Use TypeScript types for all data structures
5. **Authorization** - Always check permissions before performing operations
6. **Testing** - Write tests for services and components

## Conclusion

By following this guide and using the template-single-crud as a reference, you can quickly and consistently implement new domains in the RQRSDA2025 application. The standardized structure ensures that all domains follow the same patterns, making the codebase more maintainable and easier to understand.

For detailed implementation examples, refer to the template-single-crud code in the repository.


