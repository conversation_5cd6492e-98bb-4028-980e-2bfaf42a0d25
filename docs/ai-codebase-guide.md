# RQRSDA2025 Codebase Guide for AI Assistants

This guide provides essential information about the RQRSDA2025 codebase to help AI assistants understand key patterns and conventions.

## Core Architecture

- **Multi-tenant SaaS** for Quebec social services to manage supervised family visits
- **Tech Stack**: Next.js 15, React 19, Shadcn/UI with Tailwind 4, Supabase with row-level security
- **Server-First Paradigm**: React Server Components and Server Actions with no client-side state management
- **Internationalization**: Supports French (default) and English

## Key Directories & Files

```
src/
├── app/
│   └── [lang]/                  # Language parameter for i18n
│       ├── (public)/            # Public routes
│       └── (protected)/         # Authenticated routes
├── components/                  # Shared UI components
├── lib/
│   ├── authentication/          # Authentication services
│   ├── authorization/           # Permission system
│   ├── i18n/                    # Internationalization
│   │   ├── locales/             # Translation files
│   │   │   ├── en.json          # English translations
│   │   │   └── fr.json          # French translations
│   │   └── services/
│   │       └── I18nService.ts   # i18n service implementation
│   └── supabase/                # Supabase client and utilities
```

## Internationalization (I18n)

- **Service**: `src/lib/i18n/services/I18nService.ts` - Singleton class for handling translations
- **Locales**: JSON files in `src/lib/i18n/locales/` (en.json, fr.json)
- **Usage in Server Components**:
  ```typescript
  import { i18n } from "@/lib/i18n/services/I18nService";

  export default async function Page({ params }: { params: Promise<{ lang: string }> }) {
    // IMPORTANT: params is async and must be awaited
    const { lang } = await params;
    const dictionary = await i18n.getDictionary(lang);

    return <h1>{dictionary.common.title}</h1>;
  }
  ```
- **Adding Translations**:
  - Always add to both language files (en.json and fr.json)
  - NEVER type-assert dictionary types - typing is inferred automatically from JSON files
  - Fix missing keys in locale files rather than using type assertions

## Authentication & Authorization

- **Middleware Encapsulation**: Authentication and authorization are encapsulated within middleware and route RBAC permission checks - NOT the responsibility of pages or components
- **Authentication Service**: `src/lib/authentication/services/AuthenticationService.ts` - Use this service for authentication operations as it caches user data and encapsulates implementation details
- **NEVER use Supabase directly**: Always use the AuthenticationService, not Supabase directly
- **Permission System**: Domain-based format `domain:feature:action` (e.g., `user:profile:read`)
- **Permission Decorator**: Use `requirePermission` to protect server actions:
  ```typescript
  import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
  import { PERMISSIONS } from "../lib/security/permissions";

  export const updateUser = requirePermission(PERMISSIONS.USER.UPDATE)(
    async (formData: FormData) => {
      // Implementation
    }
  );
  ```
- **Supabase Client**: Always use `createClient()` from `src/lib/supabase/server.ts`

## Forms & Server Actions

- **Pattern**: Uncontrolled forms with `useActionState` for server actions
- **Form Submission**: Use form `action` attribute with server actions:
  ```tsx
  // Client component
  "use client";
  import { useActionState } from "react";
  import { updateItem } from "../actions";
  import { initialActionState } from "@/lib/types/responses";

  export function MyForm({ item }) {
    const [state, formAction, pending] = useActionState(updateItem, initialActionState());

    return (
      <form action={formAction}>
        <input name="name" defaultValue={item.name} />
        <button type="submit" disabled={pending}>
          {pending ? "Saving..." : "Save"}
        </button>
      </form>
    );
  }
  ```
- **Server Actions**: Define in separate files with "use server" directive:
  ```typescript
  // actions.ts
  "use server";

  import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
  import { PERMISSIONS } from "../lib/security/permissions";

  export const updateItem = requirePermission(PERMISSIONS.ITEM.UPDATE)(
    async (prevState, formData: FormData) => {
      try {
        // Implementation
        return { success: true, error: "", data: result };
      } catch (error) {
        return { success: false, error: error.message, data: null };
      }
    }
  );
  ```
- **Redirects**: Must be placed outside try-catch blocks or in finally blocks
- **Server Action Patterns**:
  - For forms that display errors: Return state through useActionState
  - For mutations that redirect: Handle redirects server-side with revalidatePath/redirect
  - Components should never handle redirects - that's the server action's responsibility

## UI Components & Design System

- **Design System**: Shadcn UI with Tailwind CSS
- **Page Layout**: Use `PageHeader` with title and description props
- **Forms**: Use `FormLayout` component for consistent form styling
- **Modals**: Use for quick actions, `Drawer` for information viewing
- **Multi-Step Forms**: Implement as file-based routes with step pages
- **Typography**: Consistent hierarchy with `PageLayout` titles (text-3xl)

## Development Practices

- **Class-Based Services**: Use private constructors for singleton classes
- **SQL Joins**: Prefer SQL joins in service methods over multiple separate queries
- **Vertical Slices**: Implement features vertically (one complete feature at a time)
- **Loading States**: Include loading.tsx and error.tsx files in page folders
- **Database Types**: Use Supabase CLI's gen-type command, never edit database.types.ts directly
- **Migrations**: Create new migration files rather than updating existing ones

## CRUD Pattern

- **Template**: Use the `template-single-crud` as a reference for implementing CRUD features
- **Actions**: Separate server actions for each operation (create, list, view, update, remove)
- **Components**: Separate form components for create and edit operations
- **Service Layer**: Domain-specific service classes that handle database operations
- **Permissions**: Domain-specific permission constants for each action
- **Standard Response Format**: Use ActionState<T> for consistent error handling

## Common Pitfalls

- **Params Handling**: In Next.js pages, params is async and must be awaited with `const {lang} = await params`
- **Multi-tenancy**: All entities require organization_id fields for row-level security
- **Authentication**: Use `supabase.auth.getUser()` instead of `getSession()`
- **Server Components**: Keep all data fetching in server components, minimize client components

## Quick Reference

- **I18n Service**: `import { i18n } from "@/lib/i18n/services/I18nService"`
- **Supabase Client**: `import { createClient } from "@/lib/supabase/server"`
- **Permission Decorator**: `import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator"`
- **Form State**: `import { initialActionState } from "@/lib/types/responses"`
- **UI Components**: `import { Button, Card, ... } from "@/components/ui/..."`
- **Typography**: `import { H1, H2, P, ... } from "@/components/typography"`
