# Business Case Document: RQRSDA Multi-Tenant SaaS Platform

## Problem Statement
Social service organizations within the RQRSDA (Regroupement Québécois des Ressources de Supervision des Droits d'Accès) face two critical challenges that limit their ability to serve families effectively:

1. **Inefficient Manual Scheduling Process**: The current process for coordinating supervised family visits requires manual management of multiple variables (employee availability, room availability, location availability, and family availability). This time-consuming process creates administrative overhead that reduces the organizations' capacity to serve families in need.

2. **Limited Business Intelligence Capabilities**: As community organizations reliant on yearly funding, RQRSDA members struggle to efficiently collect, analyze, and report on service metrics required by funders. The lack of centralized data collection and reporting tools makes securing continued funding challenging and time-consuming.

## Current Situation
- 36 independent organizations across Quebec provide supervised visitation services for families who have lost custody of their children
- All processes are currently managed with pen and paper
- A standardized business process exists but is executed manually
- Organizations vary in size, with larger ones managing up to:
  - 2-3 locations
  - 10 rooms
  - 20 employees
  - 100 families
- Significant staff time is devoted to administrative coordination rather than service delivery
- Gathering metrics for funding applications is labor-intensive and often incomplete

## Proposed Solution
A multi-tenant SaaS platform specifically designed for RQRSDA organizations that will:

1. **Automate the Standardized Business Process**: Digitize and automate the entire workflow from initial request to final reporting, including all decision points, validations, and state transitions.

2. **Centralize Information Management**: Provide comprehensive management of contacts, requests, case files, documents, and observation notes.

3. **Optimize Resource Scheduling**: Implement an intelligent scheduling system that automatically coordinates business hours, family availability, location/room availability, and employee schedules.

4. **Enable Data-Driven Reporting**: Generate standardized reports for both court submissions and funding applications, with metrics on requests, case files, and service delivery.

5. **Ensure Multi-Tenant Security**: Implement row-level security policies to maintain strict data segregation between organizations while enabling centralized reporting capabilities.

## Business Impact

### Quantitative Benefits
- **Increased Service Capacity**: Reduction in administrative overhead will allow organizations to serve more families without increasing staff
- **Improved Resource Utilization**: Optimized scheduling will maximize the use of available rooms, locations, and staff time
- **Enhanced Funding Success**: Better reporting capabilities will strengthen funding applications
- **Reduced Administrative Time**: Staff will spend less time on coordination and more time on direct service

### Qualitative Benefits
- **Improved Service Quality**: More consistent process execution and better information management
- **Enhanced Decision Making**: Data-driven insights into service delivery and resource allocation
- **Increased Staff Satisfaction**: Reduction in administrative burden and frustration
- **Better Family Experience**: More efficient scheduling and service delivery

## Success Criteria
The project will be considered successful if it achieves:

1. **Process Efficiency**: Reduces the time spent on administrative coordination by at least 50%
2. **Service Capacity**: Enables organizations to increase the number of families served by at least 20% without additional staff
3. **Reporting Capability**: Provides all metrics required for funding applications automatically
4. **User Adoption**: Achieves adoption by at least 80% of RQRSDA member organizations
5. **System Performance**: Maintains system availability of 99.5% or higher

## Constraints
- **Budget**: Limited budget requires cost-effective implementation approaches
- **Technical Infrastructure**: Multi-tenant architecture with single instance to minimize costs
- **Timeline**: Urgent need for implementation ("for yesterday")
- **User Technical Proficiency**: System must be intuitive for users with limited technical experience

## Assumptions
- The standardized business process documentation is complete and accurate
- Organizations will provide necessary information for system configuration
- Staff will be available for training and feedback during implementation
- Internet connectivity is reliable at all organization locations

## Stakeholders

### Organization Staff (Primary Users)
- **Directors**: Heads of each organization with full administrative access to their organization's data
  - Responsible for overall management and reporting
  - Need access to all data and metrics for their organization
  - Require dashboard views for organizational performance

- **Coordinators**: Middle management staff reporting to Directors
  - Responsible for day-to-day operations and scheduling
  - Manage requests and case files
  - Oversee social workers and resource allocation

- **Social Workers**: Front-line staff conducting supervised visits
  - Record observation notes during visits
  - Manage their own schedules and availability
  - Generate reports for court submissions

### External Stakeholders
- **RQRSDA Administration**: The umbrella organization connecting all member organizations
- **Families**: Recipients of supervised visitation services
- **Courts**: Entities ordering supervised visitation and receiving reports
- **Funding Agencies**: Organizations providing financial support to RQRSDA members

### System Management
- **System Administrators**: Technical staff managing the platform
  - Responsible for user management across organizations
  - Monitor system performance and security
  - Provide technical support to organizations
