# Initial Requirements List: RQRSDA Multi-Tenant SaaS Platform

## Functional Requirements

### Tenant Management (System Administrator Level)
- System must support creation and management of tenant organizations by system administrators
- Onboarding process for new organizations joining the platform
- Dashboard for system administrators to manage all organizations
- Ability to assign initial director accounts for each organization

### Organization Management (Director Level)
- Directors must be able to manage their own organization's profile and settings:
  - Organization contact information
  - Service offerings and details
  - Business hours
  - Locations and rooms
  - Branding elements (logo, colors)
- Ability to configure organization-specific settings
- Directors must have complete control over their organization's configuration without requiring system administrator intervention

### Multi-Tenant Architecture
- System must support 36 independent organizations
- Data must be segregated between organizations using row-level security policies
- Each organization must only have access to their own data
- System administrators must have the ability to manage all organizations

### User Management & Authentication
- Support for three user roles within each organization:
  - Directors (full administrative access to their organization)
  - Coordinators (middle management capabilities)
  - Social Workers (front-line staff capabilities)
- Role-based access control for all system functions
- Secure authentication using Supabase Auth
- Directors must be able to manage user accounts within their organization
- Each organization manages its own users without system administrator intervention

### User Profiles & Settings
- Each user must have a profile page to manage personal settings
- User profiles must include:
  - Personal information and contact details
  - Language preference (French/English)
  - Notification preferences
  - Interface customization options
  - Password and security settings
- Changes to user profiles must be tracked for audit purposes
- Profile information must be used throughout the system where appropriate

### Multi-Language Support
- System must support French (primary) and English languages
- All user interface elements must be available in both languages
- All system-generated content (emails, notifications, reports) must support both languages
- Language selection must be available at the user level through profile settings
- System must remember language preferences between sessions
- Default language should be configurable at the organization level

### Employee Management
- Directors must be able to create and manage employee profiles within their organization
- Employee profiles must include:
  - Personal and contact information
  - Role and permissions
  - Specializations or areas of expertise
  - Employment details
- Employee profiles must be linked to user accounts for system access
- Ability to activate/deactivate employees without deleting their records
- Employee availability management for scheduling purposes:
  - Define regular working hours
  - Set recurring availability patterns
  - Mark vacation time and other absences
  - Specify special availability exceptions
- Employee workload tracking and management
- Employee performance metrics and reporting

### Contact Management
- Ability to create and manage contacts for families, children, and other relevant parties
- Contact information must include all details necessary for scheduling and communication
- Contacts must be organization-specific
- **Relationship Management**:
  - Define and track relationships between contacts (e.g., parent-child, lawyer-client)
  - Flexible relationship types to accommodate various family structures and professional relationships
  - Ability to visualize relationship networks
  - Use relationships to determine roles within requests and case files

### Request Management
- Workflow to create and process service requests from families
- Capture of all relevant information including court judgments, service needs, and family availability
- Status tracking (draft, requested, waitlist, completed, closed)
- Automated decision support for service provision based on availability constraints
- **Change History**: Track all changes made to requests with timestamps and user information

### Case File Management
- Automatic creation of case files from approved requests
- Linkage between case files and original requests
- Document management for case-related files
- Comprehensive tracking of all case activities and status changes
- **Case File States**: Support for multiple states including:
  - Opening (onboarding, document signing)
  - Active (service delivery)
  - Suspended
  - Closed
- **Change History**: Track all changes made to case files with timestamps and user information

### Document Management
- Storage and retrieval of documents related to cases and requests
- **Flexible Document Attachments**: Ability to attach documents to:
  - Contacts
  - Requests
  - Case files
  - Observation notes
  - Any other relevant entity in the system
- **Document Template Management**:
  - Each organization can create and manage its own document templates
  - Rich text editor for template creation and modification
  - Ability to insert dynamic fields that will be populated with system data
  - Template versioning and history
  - Template categorization by purpose or document type
  - Ability to define which templates are triggered at specific workflow stages
- **Automatic Document Generation**:
  - Generate documents based on templates when a request is completed and case file is created
  - Populate templates with relevant information from the system
  - Support for different document types based on specific needs
  - Organization-specific document generation based on their custom templates
  - Configurable rules for which documents are generated at which workflow stages
- **Electronic Signatures**:
  - Capability for electronic signatures on generated documents
  - Configurable signature requirements for different document types
  - Tracking of signature status
- Document versioning and access control
- Support for various document formats

### Scheduling System
- Intelligent scheduling that considers:
  - Business hours
  - Family availability
  - Location and room availability
  - Employee schedule availability
- Comprehensive availability management:
  - Directors and coordinators can define and update employee availability
  - Employees can manage their own availability within organizational policies
  - System respects all availability constraints when scheduling
  - Visual calendar interface for availability management
- Conflict detection and resolution
- Calendar views for staff and resources
- Notification system for schedule changes
- Resource allocation optimization

### Observation Notes
- Editor for social workers to record observations during supervised visits
- Approval workflow for observation notes
- Versioning and history tracking
- Support for templates and standardized formats
- Ability to attach documents to observation notes

### Role-Specific Dashboards
- System must provide personalized dashboards for users upon login
- Dashboards must be role-specific with content tailored to user responsibilities:
  - **Director Dashboard**: Organizational metrics, pending approvals, system status
  - **Coordinator Dashboard**: Request queue, scheduling conflicts, pending tasks
  - **Social Worker Dashboard**: Upcoming appointments, observation notes needing completion
- Dashboard must include modular widgets showing relevant information:
  - Tasks and to-do items
  - Schedule/upcoming appointments
  - Recent activity
  - Important notifications
  - Quick access to frequently used functions
- Dashboard should support some level of customization by users
- Widget system should be extensible to allow for future additions

### Reporting & Analytics
- Generation of court reports based on observation notes and case data
- Metrics dashboards for organizational performance
- Funding-related reports with key statistics:
  - Requests per month/year grouped by state
  - Case file statistics by state (opening, active, suspended, closed)
  - Scheduling and appointment metrics
- Export capabilities for reports in standard formats

## Non-Functional Requirements

### Technical Stack
- Frontend: Next.js 15, React 19, Component UI with Shadcn and Tailwind
- Backend: Supabase for authentication, storage, database, and edge functions
- Development: Supabase CLI for local development
- Production: Supabase Cloud

### Performance
- System must support concurrent usage by staff across all organizations
- Page load times should not exceed 2 seconds
- Database queries should be optimized for multi-tenant architecture

### Security
- Row-level security policies for data segregation
- Secure authentication and authorization
- Data encryption for sensitive information
- Regular security audits and updates

### Usability
- Intuitive interface requiring minimal training
- Responsive design for use on various devices
- Clear workflow guidance for complex processes
- Comprehensive help documentation

### Reliability
- System availability of 99.5% or higher
- Regular backups of all data
- Disaster recovery procedures
- Graceful error handling and user notifications

### Scalability
- Architecture must support growth in users and data volume
- Performance should not degrade with increased usage
- Ability to add new features and capabilities over time

### Auditability
- Comprehensive change history for all major entities in the system
- Audit logs for security-relevant actions
- Non-repudiation for document signatures and approvals
