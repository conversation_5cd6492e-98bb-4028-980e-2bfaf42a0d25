# Workflow Engine Integration

This document outlines the integration between the RQRSDA2025 application and the n8n workflow engine.

## Architecture Overview

The workflow engine integration follows this architecture:

```
┌─────────────────┐      ┌─────────────────┐      ┌─────────────────┐
│                 │      │                 │      │                 │
│  Next.js App    │◄────►│    Supabase     │◄────►│  n8n Workflow   │
│                 │      │                 │      │     Engine      │
└─────────────────┘      └─────────────────┘      └─────────────────┘
```

## Integration Points

### 1. Application to n8n

The application triggers workflows in n8n through:

- **Webhook Triggers**: HTTP POST requests to n8n webhook endpoints
- **Database Events**: n8n can monitor Supabase tables for changes

### 2. n8n to Application

n8n communicates back to the application through:

- **API Calls**: n8n can call application API endpoints
- **Database Updates**: n8n can write to Supabase tables
- **Notification Creation**: n8n can create notifications in the workflow_notifications table

## Database Schema

The following tables will be used for workflow integration:

### workflow_drafts

Stores incomplete wizard data for multi-step forms.

```sql
CREATE TABLE public.workflow_drafts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES public.organizations(id),
    workflow_type VARCHAR(255) NOT NULL,
    data JSONB NOT NULL,
    created_by UUID NOT NULL REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);
```

### workflow_executions

Tracks running workflows and their status.

```sql
CREATE TABLE public.workflow_executions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES public.organizations(id),
    workflow_type VARCHAR(255) NOT NULL,
    status VARCHAR(50) NOT NULL,
    data JSONB NOT NULL,
    result JSONB,
    error TEXT,
    created_by UUID NOT NULL REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    completed_at TIMESTAMP WITH TIME ZONE
);
```

### workflow_notifications

Stores notifications for the notification center.

```sql
CREATE TABLE public.workflow_notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES public.organizations(id),
    user_id UUID NOT NULL REFERENCES auth.users(id),
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    link VARCHAR(255),
    read BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);
```

## Workflow Types

Initial workflow types will include:

1. **Employee Creation**: Handles the employee onboarding process
2. **Contact Creation**: Manages the contact creation process
3. **Case File Creation**: Orchestrates the case file setup process
4. **Scheduling**: Manages appointment scheduling and notifications

## Security Considerations

- n8n webhooks will be secured with authentication tokens
- Row-level security policies will be applied to all workflow tables
- n8n will run in a secure environment with limited access
- Sensitive data will be encrypted in transit and at rest

## Local Development Setup

### Docker Network Configuration

The n8n container is configured to join the same Docker network as the Supabase containers (`supabase_network_rqrsda2025`). This allows n8n to communicate directly with Supabase services using their internal Docker network names:

- Postgres database: `db:5432`
- Supabase API: `api:3000`
- Supabase Kong API Gateway: `kong:8000`

This network configuration enables n8n workflows to interact with the Supabase services without going through the public internet or localhost.

### Running n8n Locally

For local development:

1. Make sure Supabase is running:
   ```bash
   npm run supabase:start
   ```

2. Start the n8n instance:
   ```bash
   npm run n8n:start
   ```

3. Access the n8n editor at http://localhost:5678

4. Create and test workflows

5. Stop the n8n instance when done:
   ```bash
   npm run n8n:stop
   ```

## Production Deployment

For production, n8n will be deployed in a secure environment with:

- SSL/TLS encryption
- Authentication and authorization
- Database persistence
- Monitoring and alerting
- High availability configuration

## Next Steps

1. Implement the database schema
2. Create basic workflow templates in n8n
3. Develop the step wizard UI components
4. Implement the notification center
5. Connect the application to n8n webhooks
