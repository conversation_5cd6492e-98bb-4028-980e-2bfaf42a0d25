@startuml Workflow and Realtime Architecture

!define RECTANGLE class
!define DATABASE database
!define CLOUD cloud

skinparam backgroundColor white
skinparam componentStyle uml2
skinparam defaultFontName Arial
skinparam defaultFontSize 12
skinparam roundCorner 8
skinparam shadowing false

skinparam component {
  BorderColor #3B82F6
  BackgroundColor #EFF6FF
  ArrowColor #3B82F6
}

skinparam database {
  BorderColor #10B981
  BackgroundColor #ECFDF5
}

skinparam cloud {
  BorderColor #8B5CF6
  BackgroundColor #F5F3FF
}

skinparam note {
  BorderColor #F59E0B
  BackgroundColor #FFFBEB
}

' Components
component "Next.js App" as NextApp {
  component "Server Components" as ServerComponents
  component "Server Actions" as ServerActions
  component "Client Components" as ClientComponents
  
  component "Step Wizard UI" as StepWizard
  component "Notification Center" as NotificationCenter
}

database "Supabase" as Supabase {
  database "Tables" as Tables {
    [workflow_drafts]
    [workflow_executions]
    [workflow_notifications]
    [employees]
    [users]
  }
  
  component "Realtime" as Realtime
  component "Auth" as Auth
  component "RLS Policies" as RLS
}

cloud "n8n Workflow Engine" as n8n {
  component "Webhook Triggers" as Webhooks
  component "Workflow Execution" as WorkflowExecution
  component "Error Handling" as ErrorHandling
}

' Connections
ServerComponents --> ServerActions : uses
ClientComponents --> ServerActions : calls
StepWizard --> ServerActions : submits data
NotificationCenter --> Realtime : subscribes

ServerActions --> Tables : CRUD operations
ServerActions --> Webhooks : triggers workflows

Webhooks --> WorkflowExecution : starts
WorkflowExecution --> Tables : updates status
WorkflowExecution --> ErrorHandling : handles errors
ErrorHandling --> Tables : logs errors

Realtime --> NotificationCenter : pushes updates
Tables --> RLS : secured by
Auth --> RLS : enforces

' Notes
note right of StepWizard
  Collects minimal information
  Saves drafts between steps
  Triggers workflow on completion
end note

note right of NotificationCenter
  Subscribes to realtime channels
  Shows toast notifications
  Centralized notification inbox
  Tracks workflow progress
end note

note bottom of WorkflowExecution
  Creates employee records
  Creates user accounts
  Sends activation emails
  Updates notification table
end note

note bottom of Tables
  workflow_drafts: Stores incomplete wizard data
  workflow_executions: Tracks running workflows
  workflow_notifications: Stores user notifications
end note

@enduml
