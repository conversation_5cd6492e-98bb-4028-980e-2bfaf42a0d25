# Design Diagrams: RQRSDA Multi-Tenant SaaS Platform

## 1. System Architecture Diagrams

### 1.1 High-Level System Architecture

```mermaid
flowchart TD
    Client[Client Browser] <--> Frontend[Next.js Frontend]
    Frontend <--> Supabase[Supabase Backend]

    subgraph "Supabase Backend"
        Auth[Authentication]
        Database[PostgreSQL Database]
        Storage[File Storage]
        Functions[Edge Functions]
        RLS[Row-Level Security]
    end

    subgraph "Core Modules"
        UserModule[User & Organization]
        DataModule[Contacts & Relationships]
        WorkflowModule[Requests & Case Files]
        DocModule[Documents & Templates]
        ScheduleModule[Scheduling & Availability]
    end

    Frontend --- UserModule
    Frontend --- DataModule
    Frontend --- WorkflowModule
    Frontend --- DocModule
    Frontend --- ScheduleModule

    UserModule --- Supabase
    DataModule --- Supabase
    WorkflowModule --- Supabase
    DocModule --- Supabase
    ScheduleModule --- Supabase
```

This diagram shows the high-level architecture of the RQRSDA platform. The system uses Next.js for the frontend and Supabase for backend services including authentication, database, storage, and edge functions. The frontend is organized into modules corresponding to the main functional areas of the application. Row-level security in the database ensures proper data segregation between tenant organizations.

### 1.2 Deployment Architecture

```mermaid
flowchart TD
    Users[Users] --> CDN[Content Delivery Network]
    CDN --> NextApp[Next.js Application]
    NextApp --> SupabaseCloud[Supabase Cloud]

    subgraph "Supabase Cloud"
        Auth[Authentication]
        PostgreSQL[PostgreSQL Database]
        Storage[File Storage]
        EdgeFunctions[Edge Functions]

        PostgreSQL --> RLS[Row-Level Security]
    end

    subgraph "Development Environment"
        LocalDev[Local Development] --> SupabaseCLI[Supabase CLI]
        SupabaseCLI --> LocalDB[Local Database]
    end

    subgraph "CI/CD Pipeline"
        GitHub[GitHub Repository]
        GitHubActions[GitHub Actions]

        GitHub --> GitHubActions
        GitHubActions --> NextApp
        GitHubActions --> SupabaseCloud
    end
```

This diagram illustrates the deployment architecture of the system. In production, the Next.js application is deployed to a hosting service with a CDN, connecting to Supabase Cloud services. The development environment uses Supabase CLI for local development. A CI/CD pipeline using GitHub Actions automates the deployment process.

## 2. Data Model Diagrams

### 2.1 Core Entity-Relationship Diagram

```mermaid
erDiagram
    Organization ||--o{ User : "has"
    Organization ||--o{ Contact : "has"
    Organization ||--o{ Request : "has"
    Organization ||--o{ CaseFile : "has"
    Organization ||--o{ Document : "has"

    User }|--o{ Request : "manages"
    Contact }|--o{ Request : "involved in"

    Request ||--o| CaseFile : "creates"

    CaseFile ||--o{ ObservationNote : "has"
    CaseFile ||--o{ Appointment : "has"

    Organization {
        uuid id PK
        string name
    }

    User {
        uuid id PK
        uuid organization_id FK
    }

    Contact {
        uuid id PK
        uuid organization_id FK
    }

    ContactRelationship {
        uuid id PK
        uuid organization_id FK
    }

    Request {
        uuid id PK
        uuid organization_id FK
    }

    CaseFile {
        uuid id PK
        uuid organization_id FK
    }

    Appointment {
        uuid id PK
        uuid organization_id FK
    }

    ObservationNote {
        uuid id PK
        uuid organization_id FK
    }

    Document {
        uuid id PK
        uuid organization_id FK
    }

    DocumentTemplate {
        uuid id PK
        uuid organization_id FK
    }

    Location {
        uuid id PK
        uuid organization_id FK
    }

    Room {
        uuid id PK
        uuid organization_id FK
    }
```

**Note**: Every entity in the system includes an `organization_id` field to enforce row-level security policies. This ensures proper data segregation between tenant organizations at the database level.

This diagram shows the core entities in the system and their relationships. The Organization entity is at the top of the hierarchy, with all other entities associated with a specific organization. The diagram illustrates the relationships between users, contacts, requests, case files, and other key entities.

### 2.2 Multi-Tenant Data Model

```mermaid
flowchart TD
    subgraph "Database Tables"
        Organizations[Organizations Table]
        Users[Users Table]
        Contacts[Contacts Table]
        Requests[Requests Table]
        CaseFiles[Case Files Table]
        Documents[Documents Table]
    end

    subgraph "Row-Level Security"
        RLS[Row-Level Security Policies]
        OrgID[organization_id Column]
        JWT[JWT with org_id Claim]
    end

    User[User] --> Auth[Authentication]
    Auth --> JWT
    JWT --> RLS

    Organizations --> OrgID
    Users --> OrgID
    Contacts --> OrgID
    Requests --> OrgID
    CaseFiles --> OrgID
    Documents --> OrgID

    OrgID --> RLS

    RLS --> DataAccess[Data Access Control]
```

This diagram illustrates how multi-tenancy is implemented using row-level security in Supabase. Each table includes an organization_id column that is used by row-level security policies to restrict access to data. When a user authenticates, their JWT token includes their organization ID, which is used by the security policies to ensure they can only access data belonging to their organization.

## 3. State Diagrams

### 3.1 Request State Diagram

```mermaid
stateDiagram-v2
    [*] --> Draft
    Draft --> Requested
    Requested --> Waitlist
    Requested --> Approved
    Requested --> Rejected
    Waitlist --> Approved
    Approved --> Completed
    Completed --> [*]
    Rejected --> [*]
```

**Key Transitions**:
- Draft → Requested: When a request is submitted
- Requested → Waitlist: When resources aren't immediately available
- Requested/Waitlist → Approved: When resources become available
- Approved → Completed: When request is fulfilled and case file is created

This diagram shows the possible states of a service request and the transitions between them. A request starts in the Draft state, then moves to Requested when submitted. From there, it can be placed on a Waitlist, Approved, or Rejected. Approved requests are eventually Completed, which triggers the creation of a case file.

### 3.2 Case File State Diagram

```mermaid
stateDiagram-v2
    [*] --> Opening
    Opening --> Active
    Active --> Suspended
    Active --> Closed
    Suspended --> Active
    Suspended --> Closed
    Closed --> [*]
```

**Key Transitions**:
- Opening → Active: After onboarding and document signing
- Active → Suspended: When case needs to be temporarily paused
- Active/Suspended → Closed: When case is completed

This diagram illustrates the lifecycle of a case file. A case file is created in the Opening state when a request is completed. After onboarding and document signing, it moves to the Active state. A case can be Suspended temporarily or Closed permanently. The Active state has its own sub-states to track the progress of the case.

## 4. Core Process Flow Diagrams

### 4.1 Request to Case File Workflow

```mermaid
sequenceDiagram
    actor Coordinator
    participant Request
    participant Scheduling
    participant CaseFile
    participant DocGen as Document Generator

    Coordinator->>Request: Create Request
    Request->>Request: Save as Draft

    Coordinator->>Request: Add Contact Information
    Coordinator->>Request: Add Service Requirements
    Coordinator->>Request: Add Family Availability

    Coordinator->>Request: Submit Request
    Request->>Request: Change Status to Requested

    Coordinator->>Scheduling: Check Resource Availability
    Scheduling->>Coordinator: Return Availability Results

    alt Resources Available
        Coordinator->>Request: Approve Request
        Request->>Request: Change Status to Approved
        Coordinator->>Request: Complete Request
        Request->>CaseFile: Create Case File
        CaseFile->>CaseFile: Set Status to Opening
        CaseFile->>DocGen: Request Document Generation
        DocGen->>CaseFile: Generate Required Documents
        Coordinator->>CaseFile: Schedule Initial Appointments
    else Resources Not Available
        Coordinator->>Request: Place on Waitlist
        Request->>Request: Change Status to Waitlist
    end
```

This sequence diagram shows the core process flow from creating a request to generating a case file. The coordinator creates and submits a request, checks resource availability, and either approves the request or places it on a waitlist. When a request is completed, a case file is created, triggering the generation of required documents.

### 4.2 Document Generation Process

```mermaid
sequenceDiagram
    participant Director
    participant Template as Document Template
    participant Workflow
    participant Generator
    participant Document

    Director->>Template: Create Document Template
    Director->>Template: Add Dynamic Fields
    Director->>Template: Configure Trigger Points

    Workflow->>Generator: Trigger Document Generation
    Generator->>Template: Retrieve Appropriate Template
    Generator->>Generator: Gather Data for Fields
    Generator->>Document: Generate Document

    alt Signature Required
        Document->>Document: Set Status to Pending Signature
    else No Signature Required
        Document->>Document: Set Status to Completed
    end

    Document->>Workflow: Attach to Parent Entity
```

This diagram illustrates how document templates are created by directors and used to generate documents at specific points in the workflow. The document generator retrieves the appropriate template, gathers the necessary data, and creates a document. If signatures are required, the document is marked as pending signature.

## 5. Security Model Overview

### 5.1 Role-Based Access Control Model

```mermaid
flowchart TD
    SysAdmin[System Administrator] --> AllOrgs[All Organizations]

    Director --> OrgAdmin[Organization Administration]
    Director --> FullAccess[Full Organization Data Access]

    Coordinator --> LimitedAdmin[Limited Administration]
    Coordinator --> OperationalAccess[Operational Data Access]

    SocialWorker --> MinimalAccess[Minimal Data Access]

    AllOrgs --> RLS[Row-Level Security]
    FullAccess --> RLS
    OperationalAccess --> RLS
    MinimalAccess --> RLS
```

**Role Permissions Summary**:
- **System Administrator**: Can manage all organizations but cannot access organization data
- **Director**: Full control over their organization's settings, users, and data
- **Coordinator**: Can manage requests, case files, scheduling, and reports
- **Social Worker**: Limited to managing their schedule, observation notes, and profile

This diagram shows the role-based access control model for the system. It illustrates how different user roles (System Administrator, Director, Coordinator, Social Worker) are assigned different permission sets. These permissions are enforced through row-level security, JWT claims, UI component visibility, and API endpoint restrictions.
