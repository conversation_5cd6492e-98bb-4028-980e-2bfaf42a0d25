# Structure and Service Usage Examples

This document provides examples of how to use the standardized project structure and services in our Next.js application.

## Domain Structure Example

Here's an example of a complete domain structure for the `user` domain:

```
src/app/[lang]/(protected)/user/
├── layout.tsx                # User domain layout
├── page.tsx                  # User domain index page
├── lib/
│   ├── types/
│   │   ├── index.ts          # User domain types
│   │   └── permissions.ts    # User domain permissions
│   └── services/
│       └── UserService.ts    # User domain service
├── i18n/
│   ├── en.json               # English translations for user domain
│   └── fr.json               # French translations for user domain
└── (features)/
    ├── profile/
    │   ├── actions/
    │   │   ├── read.ts # Get profile action
    │   │   └── update.ts # Update profile action
    │   ├── components/
    │   │   ├── UpdateForm.tsx # Profile form component
    │   │   └── Details.tsx # Profile view component
    │   ├── lib/
    │   │   ├── types/
    │   │   │   ├── index.ts    # Profile feature types
    │   │   │   └── permissions.ts # Profile feature permissions
    │   │   └── services/
    │   │       └── ProfileService.ts # Profile feature service
    │   ├── i18n/
    │   │   ├── en.json         # English translations for profile feature
    │   │   └── fr.json         # French translations for profile feature
    │   └── (pages)/
    │       ├── page.tsx        # Profile page
    │       └── update/
    │           └── page.tsx    # Update profile page
    └── settings/
        ├── actions/
        │   ├── read.ts  # Get settings action
        │   └── update.ts # Update settings action
        ├── components/
        │   ├── UpdateForm.tsx # Settings form component
        │   └── Details.tsx # Settings view component
        ├── lib/
        │   ├── types/
        │   │   ├── index.ts     # Settings feature types
        │   │   └── permissions.ts # Settings feature permissions
        │   └── services/
        │       └── SettingsService.ts # Settings feature service
        ├── i18n/
        │   ├── en.json          # English translations for settings feature
        │   └── fr.json          # French translations for settings feature
        └── (pages)/
            ├── page.tsx         # Settings page
            └── update/
                └── page.tsx     # Update settings page
```

## Service Usage Examples

### Authentication Service

#### Service Definition

```typescript
// src/lib/authentication/services/AuthenticationService.ts
import { Service, Inject } from '@brainstack/inject';
import { LoggerService } from '@/lib/logger/services/LoggerService';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Database } from '@/lib/types/supabase';
import { SupabaseClient } from '@supabase/supabase-js';

@Service()
export class AuthenticationService {
  private supabase: SupabaseClient;

  constructor(
    @Inject private logger: LoggerService
  ) {
    this.supabase = createClientComponentClient<Database>();
  }

  async signIn(email: string, password: string): Promise<AuthResult> {
    this.logger.info(`Signing in user: ${email}`);
    return this.supabase.auth.signInWithPassword({
      email,
      password,
    });
  }

  async signOut(): Promise<void> {
    this.logger.info('Signing out user');
    await this.supabase.auth.signOut();
  }

  async resetPassword(email: string): Promise<void> {
    this.logger.info(`Resetting password for user: ${email}`);
    await this.supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/auth/update-password`,
    });
  }

  async updatePassword(password: string): Promise<void> {
    this.logger.info('Updating password');
    await this.supabase.auth.updateUser({
      password,
    });
  }

  async getSession(): Promise<Session | null> {
    const { data } = await this.supabase.auth.getSession();
    return data.session;
  }
}
```

#### Using Uncontrolled Form Action

```typescript
// src/app/[lang]/(public)/auth/(features)/sign-in/components/SignInForm.tsx

import { signIn } from '../actions/signIn';

export function SignInForm() {
  return (
    <form action={signIn}>
      {error && <div className="text-red-500">{error}</div>}

      <div className="mb-4">
        <label htmlFor="email" className="block mb-2">Email</label>
        <input
          id="email"
          name="email"
          type="email"
          className="w-full p-2 border rounded"
          required
        />
      </div>

      <div className="mb-4">
        <label htmlFor="password" className="block mb-2">Password</label>
        <input
          id="password"
          name="password"
          type="password"
          className="w-full p-2 border rounded"
          required
        />
      </div>

      <button type="submit" className="w-full p-2 bg-blue-500 text-white rounded">
        Sign In
      </button>
    </form>
  );
}
```

#### Using in a Server Action

```typescript
// src/app/[lang]/(public)/auth/(features)/sign-in/actions/signIn.ts
import { container } from '@/lib/di';
import { redirect } from 'next/navigation';
import { AuthenticationService } from '@/lib/authentication';
import { LoggerService } from '@/lib/logger';

export async function signIn(formData: FormData) {
  'use server';

  const authService = container.getInstance(AuthenticationService)
  const logger =  container.getInstance(LoggerService)

  const email = formData.get('email') as string;
  const password = formData.get('password') as string;

  try {
    logger.info(`Attempting to sign in user: ${email}`);

    const { error } = await authService.signIn(email, password);

    if (error) {
      logger.error(`Sign in error: ${error.message}`);
      return { error: error.message };
    }

    logger.info(`User signed in successfully: ${email}`);
    redirect('/dashboard');
  } catch (err) {
    logger.error(`Unexpected error during sign in: ${err}`);
    return { error: 'An unexpected error occurred' };
  }
}
```

### Internationalization Service

#### Service Definition

```typescript
// src/lib/i18n/services/I18nService.ts
import { Service, Inject } from '@brainstack/inject';
import { LoggerService } from '@/lib/logger/services/LoggerService';
import fs from 'fs';
import path from 'path';

@Service()
export class I18nService {
  constructor(@Inject private logger: LoggerService) {}

  async getDictionary(lang: string, domain?: string, feature?: string): Promise<Record<string, string>> {
    this.logger.info(`Loading dictionary for ${lang}${domain ? `, domain: ${domain}` : ''}${feature ? `, feature: ${feature}` : ''}`);

    const dictionaries: Record<string, Record<string, string>> = {};

    // Load common dictionary
    try {
      const commonDictPath = path.join(process.cwd(), 'src/lib/i18n/dictionaries', `${lang}.json`);
      if (fs.existsSync(commonDictPath)) {
        const commonDict = JSON.parse(fs.readFileSync(commonDictPath, 'utf8'));
        dictionaries.common = commonDict;
      }
    } catch (error) {
      this.logger.error(`Error loading common dictionary for ${lang}: ${error}`);
    }

    // Load domain dictionary if specified
    if (domain) {
      try {
        const domainDictPath = path.join(process.cwd(), `src/app/[lang]/(protected)/${domain}/i18n`, `${lang}.json`);
        if (fs.existsSync(domainDictPath)) {
          const domainDict = JSON.parse(fs.readFileSync(domainDictPath, 'utf8'));
          dictionaries.domain = domainDict;
        }
      } catch (error) {
        this.logger.error(`Error loading domain dictionary for ${domain}/${lang}: ${error}`);
      }

      // Load feature dictionary if specified
      if (feature) {
        try {
          const featureDictPath = path.join(process.cwd(), `src/app/[lang]/(protected)/${domain}/(features)/${feature}/i18n`, `${lang}.json`);
          if (fs.existsSync(featureDictPath)) {
            const featureDict = JSON.parse(fs.readFileSync(featureDictPath, 'utf8'));
            dictionaries.feature = featureDict;
          }
        } catch (error) {
          this.logger.error(`Error loading feature dictionary for ${domain}/${feature}/${lang}: ${error}`);
        }
      }
    }

    // Merge dictionaries
    return {
      ...dictionaries.common,
      ...dictionaries.domain,
      ...dictionaries.feature,
    };
  }

  getSupportedLanguages(): string[] {
    return ['en', 'fr'];
  }

  getDefaultLanguage(): string {
    return 'fr';
  }
}
```

#### Helper Function for Components

```typescript
// src/lib/i18n/getDictionary.ts
import { container } from '@/lib/di';
import { I18nService } from './services/I18nService';

export async function getDictionary(lang: string, domain?: string, feature?: string) {
  const i18nService = container.getInstance(I18nService);
  return i18nService.getDictionary(lang, domain, feature);
}
```

#### Using in a Server Component

```typescript
// src/app/[lang]/(protected)/user/(features)/profile/components/ProfileView.tsx
import { getDictionary } from '@/lib/i18n/getDictionary';

export async function ProfileView({ lang, user }) {
  // Server component can directly use the getDictionary helper
  const dictionary = await getDictionary(lang, 'user', 'profile');

  return (
    <div>
      <h1>{dictionary.profileTitle}</h1>
      <p>{dictionary.welcomeMessage.replace('{name}', user.name)}</p>

      <div className="mt-4">
        <h2>{dictionary.personalInformation}</h2>
        <p><strong>{dictionary.email}:</strong> {user.email}</p>
        <p><strong>{dictionary.phone}:</strong> {user.phone}</p>
      </div>
    </div>
  );
}
```

### Authorization Service

#### Service Definition

```typescript
// src/lib/authorization/services/PermissionService.ts
import { Service, Inject } from '@brainstack/inject';
import { LoggerService } from '@/lib/logger/services/LoggerService';
import { RoleService } from './RoleService';
import { Permission, UserRole } from '@/lib/authorization/types';

@Service()
export class PermissionService {
  constructor(
    @Inject private logger: LoggerService,
    @Inject private roleService: RoleService
  ) {}

  permissionMatches(userPermission: Permission, requiredPermission: Permission): boolean {
    // Wildcard permission
    if (userPermission === '*') {
      return true;
    }

    // Exact match
    if (userPermission === requiredPermission) {
      return true;
    }

    // Domain wildcard (domain:*)
    if (userPermission.endsWith(':*')) {
      const userDomain = userPermission.split(':')[0];
      const requiredDomain = requiredPermission.split(':')[0];

      if (userDomain === requiredDomain) {
        return true;
      }
    }

    // Feature wildcard (domain:feature:*)
    if (userPermission.endsWith(':*') && userPermission.split(':').length === 3) {
      const [userDomain, userFeature] = userPermission.split(':');
      const [requiredDomain, requiredFeature] = requiredPermission.split(':');

      if (userDomain === requiredDomain && userFeature === requiredFeature) {
        return true;
      }
    }

    return false;
  }

  hasPermission(role: UserRole, requiredPermission: Permission): boolean {
    const permissions = this.getPermissionsForRole(role);

    return permissions.some(permission =>
      this.permissionMatches(permission, requiredPermission)
    );
  }

  validatePermission(role: UserRole, permission: Permission): void {
    if (!this.hasPermission(role, permission)) {
      this.logger.error(`Permission denied: ${role} does not have ${permission}`);
      throw new Error(`Permission denied: ${role} does not have ${permission}`);
    }
  }

  async validateCurrentUserPermission(permission: Permission): Promise<void> {
    const role = await this.roleService.getCurrentUserRole();
    this.validatePermission(role, permission);
  }

  getPermissionsForRole(role: UserRole): Permission[] {
    // Implementation based on role permissions configuration
    const rolePermissions: Record<UserRole, Permission[]> = {
      'Director': ['*'], // Directors have full access
      'Coordinator': [
        'user:*',        // Full access to user domain
        'organization:*' // Full access to organization domain
      ],
      'SocialWorker': [
        'user:profile:read',
        'user:profile:update',
        'organization:view:read'
      ]
    };

    return rolePermissions[role] || [];
  }
}
```

#### Authorization Component

```typescript
// src/lib/authorization/components/AuthorizationRequired.tsx
'use client';

import { ReactNode, useEffect, useState } from 'react';
import { container } from '@/lib/di';
import { RoleService } from '@/lib/authorization/services/RoleService';
import { PermissionService } from '@/lib/authorization/services/PermissionService';
import { Permission } from '@/lib/authorization/types';

interface AuthorizationRequiredProps {
  permission: Permission;
  children: ReactNode;
  fallback?: ReactNode;
}

export function AuthorizationRequired({
  permission,
  children,
  fallback = null
}: AuthorizationRequiredProps) {
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);

  useEffect(() => {
    async function checkPermission() {
      try {
        const roleService = container.getInstance(RoleService);
        const permissionService = container.getInstance(PermissionService);

        const role = await roleService.getCurrentUserRole();
        const allowed = permissionService.hasPermission(role, permission);

        setHasPermission(allowed);
      } catch (error) {
        console.error('Error checking permission:', error);
        setHasPermission(false);
      }
    }

    checkPermission();
  }, [permission]);

  // Show nothing while checking permissions
  if (hasPermission === null) {
    return null;
  }

  // Show children if user has permission, otherwise show fallback
  return hasPermission ? <>{children}</> : <>{fallback}</>;
}
```

#### Using in a Client Component

```typescript
// src/app/[lang]/(protected)/user/(features)/profile/components/EditProfileButton.tsx
'use client';

import { useRouter } from 'next/navigation';
import { AuthorizationRequired } from '@/lib/authorization/components/AuthorizationRequired';

export function EditProfileButton({ userId }: { userId: string }) {
  const router = useRouter();

  const handleClick = () => {
    router.push(`/user/profile/${userId}/update`);
  };

  return (
    <AuthorizationRequired
      permission="user:profile:update"
      fallback={<span className="text-gray-400">Edit not allowed</span>}
    >
      <button
        onClick={handleClick}
        className="px-4 py-2 bg-blue-500 text-white rounded"
      >
        Edit Profile
      </button>
    </AuthorizationRequired>
  );
}
```

#### Using in a Server Action

```typescript
// src/app/[lang]/(protected)/user/(features)/profile/actions/update.ts
import { container } from '@/lib/di';
import { PermissionService } from '@/lib/authorization/services/PermissionService';
import { LoggerService } from '@/lib/logger/services/LoggerService';
import { UserService } from '@/app/[lang]/(protected)/user/lib/services/UserService';
import { redirect } from 'next/navigation';

export async function update(userId: string, formData: FormData) {
  'use server';

  // Get services from the container
  const permissionService = container.getInstance(PermissionService);
  const userService = container.getInstance(UserService);
  const logger = container.getInstance(LoggerService);

  try {
    // Validate permission
    await permissionService.validateCurrentUserPermission('user:profile:update');

    // Update profile
    const name = formData.get('name') as string;
    const email = formData.get('email') as string;
    const phone = formData.get('phone') as string;

    await userService.updateProfile(userId, { name, email, phone });

    logger.info(`Profile updated for user: ${userId}`);
    redirect(`/user/profile/${userId}`);
  } catch (error) {
    logger.error(`Error updating profile: ${error}`);
    return { error: error.message };
  }
}
```

## Conclusion

These examples demonstrate how to use the standardized project structure and services in our Next.js application. By following these patterns, we ensure a consistent approach to development, making the codebase more maintainable and testable.
