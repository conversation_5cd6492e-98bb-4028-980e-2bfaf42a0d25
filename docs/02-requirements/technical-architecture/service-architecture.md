# Service Architecture Documentation

## Overview

This document outlines the service architecture for our Next.js application. The architecture follows dependency injection principles using `@brainstack/inject` and implements logging using `@brainstack/log`. We leverage React Server Components, which allows us to use dependency injection directly without the need for React context providers.

## Dependency Injection with @brainstack/inject

We use `@brainstack/inject` for dependency injection, which provides a lightweight yet powerful way to manage dependencies in our application.

### Key Concepts

- **Services**: Reusable components that provide specific functionality
- **Dependency Injection**: A design pattern where dependencies are provided to a class instead of being created within the class
- **Service Decorators**: Used to mark classes as injectable services
- **Service Scopes**: Define the lifetime of a service instance (singleton, transient, or custom scope)

### Important Limitations

1. **Cannot inject interfaces**: TypeScript interfaces don't exist at runtime, so you must inject concrete classes
2. **Cannot register functions directly**: You can only register class instances with the container

### Service Definition

Services are defined using the `@Service` decorator:

```typescript
import { Service, Inject } from '@brainstack/inject';
import { LoggerService } from '@/lib/logger/services/LoggerService';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Database } from '@/lib/types/supabase';

@Service()
export class AuthenticationService {
  private supabase;

  constructor(
    @Inject private logger: LoggerService // Must inject concrete class
  ) {
    // Create Supabase client directly since it's a function, not a class
    this.supabase = createClientComponentClient<Database>();
  }

  async signIn(email: string, password: string): Promise<AuthResult> {
    this.logger.info('Signing in user');
    return this.supabase.auth.signInWithPassword({
      email,
      password,
    });
  }

  async signOut(): Promise<void> {
    this.logger.info('Signing out user');
    await this.supabase.auth.signOut();
  }

  async resetPassword(email: string): Promise<void> {
    this.logger.info(`Resetting password for user: ${email}`);
    await this.supabase.auth.resetPasswordForEmail(email);
  }

  // Other methods...
}
```

### Service Registration

Services decorated with `@Service` are automatically registered with the container. You can only register class instances manually:

```typescript
import { Container } from '@brainstack/inject';
import { ConfigService } from './ConfigService';

const container = new Container();

// Register a class instance manually
const configService = new ConfigService();
container.register('config', configService);

// You CANNOT register functions directly
// This will NOT work:
// container.register('supabase', createClientComponentClient<Database>());
```

### Service Resolution

Services can be resolved from the container:

```typescript
import { container } from '@/lib/di';
import { AuthenticationService } from '@/lib/authentication/services/AuthenticationService';

// For classes decorated with @Service, use getInstance
const authService = container.getInstance(AuthenticationService);

// For manually registered services with string identifiers, use get
const configService = container.get('config');
```

### Service Scopes

Services can have different scopes:

- **Singleton**: One instance shared across the application
- **Transient**: New instance created each time
- **Scoped**: Instances managed within a specific container

```typescript
import { SingletonService } from '@brainstack/inject';

@SingletonService() // This service will be a singleton
export class LoggerService {
  // Implementation...
}
```

## Centralized Dependency Injection Container

We use a centralized dependency injection container to manage services across the application:

```typescript
// src/lib/di/index.ts
import { Container } from '@brainstack/inject';
import { LoggerService } from '@/lib/logger/services/LoggerService';
import { I18nService } from '@/lib/i18n/services/I18nService';
import { AuthenticationService } from '@/lib/authentication/services/AuthenticationService';
import { RoleService } from '@/lib/authorization/services/RoleService';
import { PermissionService } from '@/lib/authorization/services/PermissionService';

// Create a singleton container
const container = new Container();

// Note: Services decorated with @Service() are automatically registered
// We don't need to manually register them

// Export the container for use throughout the application
export { container };

// Access services directly from the container
// container.getInstance(ServiceClass)
```

This approach allows us to access the container directly from any file in the application, making it easy to resolve services in both server and client components.

### Accessing Services

Services can be accessed directly from the container:

```typescript
import { container } from '@/lib/di';
import { AuthenticationService } from '@/lib/authentication/services/AuthenticationService';
import { LoggerService } from '@/lib/logger/services/LoggerService';

// Get services from the container
const authService = container.getInstance(AuthenticationService);
const logger = container.getInstance(LoggerService);
```

### Special Handling for Non-Class Dependencies

For dependencies that are not classes (like the Supabase client), we need to create them directly in the service constructor:

```typescript
@Service()
export class AuthenticationService {
  private supabase;

  constructor(
    @Inject private logger: LoggerService
  ) {
    // Create Supabase client directly
    this.supabase = createClientComponentClient<Database>();
  }

  // Methods...
}
```

This approach ensures that we can still use dependency injection for class-based services while handling non-class dependencies appropriately.

## Logging with @brainstack/log

We use `@brainstack/log` for logging, which provides a flexible and customizable logger.

### Logger Service

```typescript
// src/lib/logger/services/LoggerService.ts
import { Service } from '@brainstack/inject';
import { createLogger, consoleIntegration, Logger, LogLevel } from '@brainstack/log';

@Service()
export class LoggerService {
  private logger: Logger;

  constructor() {
    // Create logger with console integration
    this.logger = createLogger(LogLevel.INFO, [consoleIntegration]);
  }

  log(...message: any[]): void {
    this.logger.log(...message);
  }

  info(...message: any[]): void {
    this.logger.info(...message);
  }

  warn(...message: any[]): void {
    this.logger.warn(...message);
  }

  error(...message: any[]): void {
    this.logger.error(...message);
  }

  verbose(...message: any[]): void {
    this.logger.verbose(...message);
  }

  changeLogLevel(level: number): void {
    this.logger.changeLogLevel(level);
  }
}
```

## Core Services

### Internationalization Service

```typescript
// src/lib/i18n/services/I18nService.ts
import { Service, Inject } from '@brainstack/inject';
import { LoggerService } from '@/lib/logger/services/LoggerService';
import fs from 'fs';
import path from 'path';

@Service()
export class I18nService {
  constructor(@Inject private logger: LoggerService) {}

  async getDictionary(lang: string, domain?: string, feature?: string): Promise<Record<string, string>> {
    this.logger.info(`Loading dictionary for ${lang}${domain ? `, domain: ${domain}` : ''}${feature ? `, feature: ${feature}` : ''}`);

    // Implementation to load dictionaries from domain-specific locations
    const dictionaries: Record<string, Record<string, string>> = {};

    // Load common dictionary
    try {
      const commonDictPath = path.join(process.cwd(), 'src/lib/i18n/dictionaries', `${lang}.json`);
      if (fs.existsSync(commonDictPath)) {
        const commonDict = JSON.parse(fs.readFileSync(commonDictPath, 'utf8'));
        dictionaries.common = commonDict;
      }
    } catch (error) {
      this.logger.error(`Error loading common dictionary for ${lang}: ${error}`);
    }

    // Load domain and feature dictionaries if specified
    if (domain) {
      try {
        const domainDictPath = path.join(process.cwd(), `src/app/[lang]/(protected)/${domain}/i18n`, `${lang}.json`);
        if (fs.existsSync(domainDictPath)) {
          const domainDict = JSON.parse(fs.readFileSync(domainDictPath, 'utf8'));
          dictionaries.domain = domainDict;
        }
      } catch (error) {
        this.logger.error(`Error loading domain dictionary for ${domain}/${lang}: ${error}`);
      }

      // Load feature dictionary if specified
      if (feature) {
        try {
          const featureDictPath = path.join(process.cwd(), `src/app/[lang]/(protected)/${domain}/(features)/${feature}/i18n`, `${lang}.json`);
          if (fs.existsSync(featureDictPath)) {
            const featureDict = JSON.parse(fs.readFileSync(featureDictPath, 'utf8'));
            dictionaries.feature = featureDict;
          }
        } catch (error) {
          this.logger.error(`Error loading feature dictionary for ${domain}/${feature}/${lang}: ${error}`);
        }
      }
    }

    // Merge dictionaries
    return {
      ...dictionaries.common,
      ...dictionaries.domain,
      ...dictionaries.feature,
    };
  }

  getSupportedLanguages(): string[] {
    return ['en', 'fr'];
  }

  getDefaultLanguage(): string {
    return 'fr';
  }
}
```

### Authentication Service

```typescript
// src/lib/authentication/services/AuthenticationService.ts
import { Service, Inject } from '@brainstack/inject';
import { LoggerService } from '@/lib/logger/services/LoggerService';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Database } from '@/lib/types/supabase';
import { SupabaseClient } from '@supabase/supabase-js';

@Service()
export class AuthenticationService {
  private supabase: SupabaseClient;

  constructor(
    @Inject private logger: LoggerService
  ) {
    // For Supabase client, we need to create it directly since it's not a class
    // that can be injected with @brainstack/inject
    this.supabase = createClientComponentClient<Database>();
  }

  async signIn(email: string, password: string): Promise<AuthResult> {
    this.logger.info(`Signing in user: ${email}`);
    return this.supabase.auth.signInWithPassword({
      email,
      password,
    });
  }

  async signOut(): Promise<void> {
    this.logger.info('Signing out user');
    await this.supabase.auth.signOut();
  }

  async resetPassword(email: string): Promise<void> {
    this.logger.info(`Resetting password for user: ${email}`);
    await this.supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/auth/update-password`,
    });
  }

  async updatePassword(password: string): Promise<void> {
    this.logger.info('Updating password');
    await this.supabase.auth.updateUser({
      password,
    });
  }

  async getSession(): Promise<Session | null> {
    const { data } = await this.supabase.auth.getSession();
    return data.session;
  }
}
```

### Authorization Service

```typescript
// src/lib/authorization/services/RoleService.ts
import { Service, Inject } from '@brainstack/inject';
import { LoggerService } from '@/lib/logger/services/LoggerService';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Database } from '@/lib/types/supabase';
import { SupabaseClient } from '@supabase/supabase-js';
import { UserRole } from '@/lib/authorization/types';

@Service()
export class RoleService {
  private supabase: SupabaseClient;

  constructor(
    @Inject private logger: LoggerService
  ) {
    // For Supabase client, we need to create it directly since it's not a class
    // that can be injected with @brainstack/inject
    this.supabase = createClientComponentClient<Database>();
  }

  async getCurrentUserRole(): Promise<UserRole> {
    const { data: { session } } = await this.supabase.auth.getSession();
    if (!session) {
      throw new Error('No active session');
    }

    return this.getUserRole(session.user.id);
  }

  async getUserRole(userId: string): Promise<UserRole> {
    this.logger.info(`Getting role for user: ${userId}`);

    const { data, error } = await this.supabase
      .from('users')
      .select('role')
      .eq('id', userId)
      .single();

    if (error) {
      this.logger.error(`Error getting role: ${error.message}`);
      throw error;
    }

    return data.role;
  }

  isValidRole(role: string): boolean {
    return ['Director', 'Coordinator', 'SocialWorker'].includes(role);
  }

  getDisplayName(role: UserRole): string {
    const displayNames = {
      'Director': 'Director',
      'Coordinator': 'Coordinator',
      'SocialWorker': 'Social Worker',
    };

    return displayNames[role] || role;
  }
}
```

## Testing Services

Services are designed to be easily testable by using dependency injection and mocking:

```typescript
// src/lib/authentication/services/__tests__/AuthenticationService.test.ts
import { AuthenticationService } from '@/lib/authentication/services/AuthenticationService';
import { LoggerService } from '@/lib/logger/services/LoggerService';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';

// Mock the createClientComponentClient function
jest.mock('@supabase/auth-helpers-nextjs', () => ({
  createClientComponentClient: jest.fn(),
}));

describe('AuthenticationService', () => {
  let mockSupabase: any;
  let mockLogger: any;
  let authService: AuthenticationService;

  beforeEach(() => {
    // Create mock dependencies
    mockSupabase = {
      auth: {
        signInWithPassword: jest.fn(),
        signOut: jest.fn(),
        resetPasswordForEmail: jest.fn(),
        getSession: jest.fn().mockResolvedValue({ data: { session: null } }),
        updateUser: jest.fn(),
      },
    };

    mockLogger = {
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      log: jest.fn(),
      verbose: jest.fn(),
    };

    // Mock the createClientComponentClient to return our mock
    (createClientComponentClient as jest.Mock).mockReturnValue(mockSupabase);

    // Create the service with mocked dependencies
    authService = new AuthenticationService(mockLogger as unknown as LoggerService);
  });

  test('signIn calls supabase.auth.signInWithPassword', async () => {
    // Arrange
    const email = '<EMAIL>';
    const password = 'password';
    mockSupabase.auth.signInWithPassword.mockResolvedValue({ data: {}, error: null });

    // Act
    await authService.signIn(email, password);

    // Assert
    expect(mockSupabase.auth.signInWithPassword).toHaveBeenCalledWith({
      email,
      password,
    });

    expect(mockLogger.info).toHaveBeenCalledWith(`Signing in user: ${email}`);
  });

  test('signOut calls supabase.auth.signOut', async () => {
    // Arrange
    mockSupabase.auth.signOut.mockResolvedValue({ error: null });

    // Act
    await authService.signOut();

    // Assert
    expect(mockSupabase.auth.signOut).toHaveBeenCalled();
    expect(mockLogger.info).toHaveBeenCalledWith('Signing out user');
  });

  test('resetPassword calls supabase.auth.resetPasswordForEmail', async () => {
    // Arrange
    const email = '<EMAIL>';
    mockSupabase.auth.resetPasswordForEmail.mockResolvedValue({ data: {}, error: null });
    process.env.NEXT_PUBLIC_SITE_URL = 'https://example.com';

    // Act
    await authService.resetPassword(email);

    // Assert
    expect(mockSupabase.auth.resetPasswordForEmail).toHaveBeenCalledWith(
      email,
      {
        redirectTo: 'https://example.com/auth/update-password',
      }
    );
    expect(mockLogger.info).toHaveBeenCalledWith(`Resetting password for user: ${email}`);
  });

  // More tests...
});
```

This approach allows us to easily test services by mocking their dependencies. We mock the external dependencies like `createClientComponentClient` and create mock objects for the injected services.

## Conclusion

This service architecture provides a solid foundation for our Next.js application, with dependency injection using `@brainstack/inject` and logging using `@brainstack/log`. It ensures that our services are testable, maintainable, and follow the Single Responsibility Principle.
