# Project Structure Documentation

## Current Implementation Status

This document reflects the current implementation of the project structure as of May 2025. The domain structure described in this document is aspirational and represents the target architecture, while the actual implementation details (components, services, middleware, etc.) reflect the current state of the codebase.

## Server-First Paradigm with Next.js 15 and React 19

This project embraces the Server-First Paradigm introduced with Next.js 15 and React 19, leveraging React Server Components and Server Actions to build a more efficient, performant application. This approach represents a fundamental shift from traditional client-side React development.

### Key Principles

1. **React Server Components (RSC)**: Components render on the server, reducing client-side JavaScript and improving performance.
2. **Server Actions**: Server-side functions that can be called directly from the client, eliminating the need for API routes.
3. **Streaming and Suspense**: Components can stream and render incrementally as data becomes available.
4. **No Client-Side State Management**: Moving away from complex client-side state management libraries.
5. **Minimal Client-Side JavaScript**: Reducing the JavaScript sent to the client for faster load times.
6. **Progressive Enhancement**: Building from a server-rendered foundation and enhancing with client interactivity only where needed.

### Moving Beyond Traditional React Patterns

This paradigm shift means moving away from traditional React hooks like `useEffect`, `useState`, `useMemo`, `useReducer`, and `useContext` for most of our application logic. Instead, we:

- Fetch data directly in Server Components without loading states or useEffect
- Use Server Actions for mutations instead of client-side API calls
- Leverage React's automatic memoization in Server Components
- Use streaming and Suspense for loading states
- Structure our application around domains and features rather than state management

### Single Responsibility Principle

Our architecture follows the Single Responsibility Principle (SRP) by breaking the application into small, focused files that each do one thing well. These components are then composed together to solve complex problems in a way that's easier to understand, test, and maintain.

## Overview

This document outlines the standardized project structure for our Next.js application. The structure follows domain-driven design principles, with a clear separation of concerns and a focus on maintainability and testability.

## Directory Structure

```
src/
├── app/                      # Next.js App Router
│   ├── layout.tsx            # Root layout
│   ├── error.tsx             # Root error handling
│   ├── forbidden.tsx         # Forbidden page when forbidden() is called
│   ├── not-found.tsx         # Root not found page
│   ├── loading.tsx           # Root loading state
│   └── [lang]/               # Language parameter
│       ├── (public)/         # Public route group
│       │   ├── auth/         # Authentication domain
│       │   │   ├── page.tsx  # Auth domain index page
│       │   │   ├── error.tsx # Auth domain error handling
│       │   │   ├── loading.tsx # Auth domain loading state
│       │   │   └── (features)/ # Features for auth domain
│       │   │       ├── signin/  # Note: actual implementation uses 'signin' not 'sign-in'
│       │   │       │   ├── actions/
│       │   │       │   │   └── signin.ts
│       │   │       │   ├── components/
│       │   │       │   │   ├── SignInForm.tsx
│       │   │       │   │   └── SignInHeader.tsx
│       │   │       │   ├── page.tsx
│       │   │       │   ├── error.tsx
│       │   │       │   └── loading.tsx
│       │   │       ├── signout/  # Note: actual implementation uses 'signout' not 'sign-out'
│       │   │       │   ├── actions/
│       │   │       │   │   └── signout.ts
│       │   │       │   └── page.tsx
│       │   │       └── reset-password/
│       │   │           ├── actions/
│       │   │           │   ├── request-reset.ts
│       │   │           │   └── reset-password.ts
│       │   │           ├── components/
│       │   │           │   ├── RequestResetForm.tsx
│       │   │           │   └── ResetPasswordForm.tsx
│       │   │           ├── page.tsx
│       │   │           ├── error.tsx
│       │   │           └── loading.tsx
│       └── protected/        # Protected route group (not in a route group)
│           ├── components/   # Protected layout components
│           │   ├── navbar.tsx
│           │   ├── sidebar.tsx
│           │   ├── sidebar-toggle.tsx
│           │   ├── user-menu.tsx
│           │   └── role-switcher.tsx
│           ├── layout.tsx    # Protected layout with app shell
│           ├── dashboard/    # Dashboard domain
│           │   └── page.tsx
│           ├── admin/        # Admin domain
│           │   └── page.tsx
│           ├── settings/     # Settings domain
│           │   └── page.tsx
│           ├── user/         # User domain
│           │   └── page.tsx
│           └── users/        # Users domain
│               ├── actions.ts
│               └── page.tsx
├── lib/                      # Shared libraries
│   ├── i18n/                 # Internationalization
│   │   ├── locales/          # Translation files
│   │   │   ├── en.json       # English translations
│   │   │   └── fr.json       # French translations
│   │   ├── services/         # I18n services
│   │   │   └── I18nService.ts
│   │   ├── middleware/       # I18n middleware
│   │   │   └── i18nMiddleware.ts
│   │   ├── settings.ts       # I18n settings
│   │   └── index.ts          # I18n exports
│   ├── logger/               # Logging
│   │   └── services/         # Logger services
│   │       └── LoggerService.ts
│   ├── authentication/       # Authentication
│   │   ├── services/         # Authentication services
│   │   │   └── AuthenticationService.ts
│   │   └── middleware/       # Authentication middleware
│   │       └── authMiddleware.ts
│   ├── authorization/        # Authorization
│   │   ├── services/         # Authorization services
│   │   │   ├── ConfigurationService.ts
│   │   │   ├── PermissionService.ts
│   │   │   └── RoleService.ts
│   │   ├── middleware/       # Authorization middleware
│   │   │   ├── authorizeRoute.ts
│   │   │   └── routeAuthorizationMiddleware.ts
│   │   ├── components/       # Authorization components
│   │   │   └── AuthorizationRequired.tsx
│   │   ├── config/           # Authorization configuration
│   │   │   └── routePermissions.ts
│   │   ├── utils/            # Authorization utilities
│   │   │   ├── routeMatcher.ts
│   │   │   └── withPermission.ts
│   │   └── types.ts          # Authorization types
│   ├── supabase/             # Supabase client
│   │   ├── client.ts
│   │   ├── server.ts
│   │   └── middleware.ts
│   ├── types/                # Shared types
│   │   └── database.types.ts # Generated database types
│   └── utils.ts              # Utility functions
├── components/               # Shared components
│   ├── language-toggle.tsx   # Language toggle component
│   ├── theme-provider.tsx    # Theme provider component
│   ├── theme-toggle.tsx      # Theme toggle component
│   └── ui/                   # UI components
│       ├── button.tsx
│       ├── card.tsx
│       ├── dropdown-menu.tsx
│       └── ...
└── middleware.ts             # Next.js middleware
```

## Domain Organization

Each domain is organized as follows:

```
domain/                       # Domain directory
├── layout.tsx                # Domain layout (if needed)
├── page.tsx                  # Domain index page
├── error.tsx                 # Domain error handling
├── loading.tsx               # Domain loading state
├── not-found.tsx             # Domain not found page
├── lib/                      # Domain-specific libraries
│   ├── types/                # Domain-specific types
│   └── services/             # Domain-specific services
└── (features)/               # Features for this domain
    ├── feature-1/            # Feature 1
    │   ├── actions/          # Server actions
    │   ├── components/       # React components
    │   ├── lib/              # Feature-specific libraries
    │   │   ├── types/        # Feature-specific types
    │   │   └── services/     # Feature-specific services
    │   ├── page.tsx          # Feature index page
    │   ├── error.tsx         # Feature error handling
    │   ├── loading.tsx       # Feature loading state
    │   ├── not-found.tsx     # Feature not found page
    │   └── (pages)/          # Pages for this feature
    │       ├── list/         # List page
    │       │   ├── page.tsx  # List page component
    │       │   ├── error.tsx # List page error handling
    │       │   └── loading.tsx # List page loading state
    │       ├── create/       # Create page
    │       │   ├── page.tsx  # Create page component
    │       │   ├── error.tsx # Create page error handling
    │       │   └── loading.tsx # Create page loading state
    │       └── [id]/         # Detail page
    │           ├── page.tsx  # Detail page component
    │           ├── error.tsx # Detail page error handling
    │           ├── loading.tsx # Detail page loading state
    │           ├── not-found.tsx # Detail page not found
    │           └── update/   # Update page
    │               ├── page.tsx # Update page component
    │               ├── error.tsx # Update page error handling
    │               └── loading.tsx # Update page loading state
    └── feature-2/            # Feature 2
        └── ...
```

## Naming Conventions

The codebase follows these naming conventions:

- **Directories**: Use kebab-case for directory names (e.g., `reset-password`)
- **Components**: Use PascalCase for component names (e.g., `SignInForm.tsx`)
- **Services**: Use PascalCase for service names (e.g., `AuthenticationService.ts`)
- **Types**: Use PascalCase for type names (e.g., `UserRole`)
- **Actions**: Use camelCase for action names (e.g., `signin.ts`)
- **Utility Files**: Use camelCase for utility files (e.g., `utils.ts`)
- **Configuration Files**: Use camelCase for configuration files (e.g., `routePermissions.ts`)
- **Test Files**: Use the same name as the file being tested with a `.test.tsx` or `.test.ts` extension

## Route Groups

The application uses route groups to organize the routing structure:

- **Public** `(public)`: Routes that don't require authentication (e.g., `/[lang]/(public)/auth`)
- **Protected**: Routes that require authentication (e.g., `/[lang]/protected/dashboard`)

Note that in the current implementation:
- The public routes use a route group `(public)` which is not part of the URL path
- The protected routes are not in a route group but directly under `/[lang]/protected/`

Route groups (enclosed in parentheses) are a Next.js feature that allows organizing routes without affecting the URL structure.

## Language Parameter

The application supports multiple languages through the `[lang]` parameter in the URL. This parameter is handled by the i18nMiddleware, which:

1. Detects if the URL already has a language parameter
2. If not, redirects to the appropriate language based on:
   - The user's Accept-Language header
   - Falling back to the default language (French) if no preference is detected
3. Preserves the current path and query parameters during redirection
4. Adds the current pathname to response headers for server components

The supported languages are defined in `src/lib/i18n/settings.ts`:
- French (`fr`) - Default language
- English (`en`)

The language toggle component allows users to switch between languages while staying on the same page.

## Domain-Driven Design

The application follows domain-driven design principles:

- **Domains**: Represent a specific business area (e.g., `auth`, `user`, `organization`)
- **Features**: Represent specific functionality within a domain (e.g., `sign-in`, `profile`, `settings`)
- **Actions**: Represent specific operations within a feature (e.g., `signIn`, `updateProfile`, `changeSettings`)

## File Organization

### Components

Components are organized following the Server-First paradigm. Most components are Server Components by default, with Client Components used only when necessary for interactivity:

#### Server Component Example

```typescript
// src/lib/authorization/components/AuthorizationRequired.tsx
import { ReactNode } from "react";
import { Permission } from "../types";
import { PermissionService } from "../services/PermissionService";
import { RoleService } from "../services/RoleService";

interface AuthorizationRequiredProps {
  permission: Permission;
  children: ReactNode;
  fallback?: ReactNode;
}

/**
 * Server component that conditionally renders its children based on whether the current user has a permission
 * This is secure because the permission check happens on the server
 */
export async function AuthorizationRequired({
  permission,
  children,
  fallback = null,
}: AuthorizationRequiredProps) {
  try {
    // Get the current user's role
    const role = await RoleService.getCurrentUserRole();

    // Check if the user has the required permission
    const hasPermission = PermissionService.hasPermission(role, permission);

    // Render children if user has permission, otherwise render fallback
    return hasPermission ? <>{children}</> : <>{fallback}</>;
  } catch (error) {
    console.error(`Error checking permission ${permission}:`, error);

    // In case of error, don't render the protected content
    return <>{fallback}</>;
  }
}
```

#### Client Component Example

```typescript
// src/app/[lang]/(public)/auth/(features)/signin/components/SignInForm.tsx
"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { signin } from "../actions/signin";
import { useFormState } from "react-dom";

// Initial state for the form
const initialState = {
  error: null,
};

export function SignInForm({ dictionary }: { dictionary: any }) {
  const [isLoading, setIsLoading] = useState(false);
  const [state, formAction] = useFormState(signin, initialState);

  // Handle form submission
  const handleSubmit = async (formData: FormData) => {
    setIsLoading(true);
    try {
      await formAction(formData);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="grid gap-6">
      {state.error && (
        <div className="bg-destructive/15 text-destructive p-3 rounded-md text-sm">
          {state.error}
        </div>
      )}

      <form action={handleSubmit} className="space-y-4">
        <div className="space-y-2">
          <label htmlFor="email" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
            {dictionary.emailLabel}
          </label>
          <Input
            id="email"
            name="email"
            type="email"
            autoCapitalize="none"
            autoComplete="email"
            autoCorrect="off"
            disabled={isLoading}
            required
          />
        </div>
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <label htmlFor="password" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
              {dictionary.passwordLabel}
            </label>
            <a href="reset-password" className="text-sm text-primary hover:underline">
              {dictionary.forgotPassword}
            </a>
          </div>
          <Input
            id="password"
            name="password"
            type="password"
            autoComplete="current-password"
            disabled={isLoading}
            required
          />
        </div>
        <Button type="submit" className="w-full" disabled={isLoading}>
          {isLoading ? dictionary.signingIn : dictionary.signInButton}
        </Button>
      </form>
    </div>
  );
}
```

### Server Actions

Server Actions are a key part of the Server-First paradigm, allowing server-side mutations directly from the client without API routes:

```typescript
// src/app/[lang]/(public)/auth/(features)/signin/actions/signin.ts
"use server";

import { createClient } from "@/lib/supabase/server";
import { redirect } from "next/navigation";
import { logger } from "@/lib/logger/services/LoggerService";

// Server Action for form submission
export async function signin(prevState: any, formData: FormData) {
  try {
    // Get form data
    const email = formData.get("email") as string;
    const password = formData.get("password") as string;

    // Validate form data
    if (!email || !password) {
      return { error: "Email and password are required" };
    }

    logger.info(`Attempting to sign in user: ${email}`);

    // Create Supabase client
    const supabase = await createClient();

    // Sign in with Supabase Auth
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    // Handle authentication error
    if (error) {
      logger.error(`Sign in error: ${error.message}`);
      return { error: error.message };
    }

    logger.info(`User signed in successfully: ${email}`);

    // Redirect to dashboard on success
    redirect("/fr/protected/dashboard");
  } catch (error) {
    logger.error(`Unexpected error during sign in: ${error}`);
    return { error: "An unexpected error occurred. Please try again." };
  }
}
```

#### Signout Action

```typescript
// src/app/[lang]/(public)/auth/(features)/signout/actions/signout.ts
"use server";

import { createClient } from "@/lib/supabase/server";
import { redirect } from "next/navigation";
import { logger } from "@/lib/logger/services/LoggerService";

// Server Action for signing out
export async function signout() {
  try {
    logger.info("Signing out user");

    // Create Supabase client
    const supabase = await createClient();

    // Sign out with Supabase Auth
    const { error } = await supabase.auth.signOut();

    if (error) {
      logger.error(`Sign out error: ${error.message}`);
      throw error;
    }

    logger.info("User signed out successfully");
  } catch (error) {
    logger.error(`Error during sign out: ${error}`);
  }

  // Redirect to home page regardless of success/failure
  redirect("/");
}
```

### Pages

Pages in Next.js 15 are Server Components by default, allowing for direct data fetching and server-side rendering:

```typescript
// src/app/[lang]/protected/dashboard/page.tsx
import { i18n } from "@/lib/i18n/services/I18nService";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { PermissionService } from "@/lib/authorization/services/PermissionService";

// Server Component page with internationalization
export default async function DashboardPage({ params }: { params: { lang: string } }) {
  // Get translations for the current language
  const dictionary = await i18n.getDictionary(params.lang);

  // Server-side permission check
  await PermissionService.validateCurrentUserPermission("dashboard:view");

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
        <p className="text-muted-foreground">
          Overview of your organization's activities
        </p>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Active Cases</CardTitle>
            <CardDescription>Current active cases in your organization</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">24</div>
            <p className="text-xs text-muted-foreground">+2 from last month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Upcoming Visits</CardTitle>
            <CardDescription>Scheduled visits for the next 7 days</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">12</div>
            <p className="text-xs text-muted-foreground">3 today, 9 this week</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Pending Requests</CardTitle>
            <CardDescription>New requests awaiting processing</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">7</div>
            <p className="text-xs text-muted-foreground">2 high priority</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
```

### Error Handling

Error components are organized as follows:

```typescript
// src/app/[lang]/(public)/auth/(features)/sign-in/(pages)/error.tsx
'use client';

import { useEffect } from 'react';
import { Button } from '@/components/ui/button';

export default function SignInError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error(error);
  }, [error]);

  return (
    <div className="flex flex-col items-center justify-center h-full">
      <h2 className="text-xl font-bold mb-4">Something went wrong!</h2>
      <p className="mb-4">{error.message}</p>
      <Button onClick={reset}>Try again</Button>
    </div>
  );
}
```

### Loading States

Loading components are organized as follows:

```typescript
// src/app/[lang]/(public)/auth/(features)/sign-in/(pages)/loading.tsx
import { Skeleton } from '@/components/ui/skeleton';

export default function SignInLoading() {
  return (
    <div className="space-y-4">
      <h1 className="text-2xl font-bold">Sign In</h1>
      <div className="space-y-2">
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-10 w-full" />
      </div>
    </div>
  );
}
```

### Layouts

Layouts in Next.js 15 are Server Components by default, allowing for data fetching and internationalization directly in the layout:

```typescript
// src/app/[lang]/protected/layout.tsx
import { Sidebar, type SidebarDictionary } from "./components/sidebar";
import { Navbar, type NavbarDictionary } from "./components/navbar";
import { i18n } from "@/lib/i18n/services/I18nService";

// Combined dictionary type that satisfies both sidebar and navbar requirements
type LayoutDictionary = SidebarDictionary & NavbarDictionary;

export default async function ProtectedLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ lang: string }>;
}) {
  // Get language parameter and load dictionary
  const { lang } = await params;
  const dictionary = (await i18n.getDictionary(lang)) as LayoutDictionary;

  // For simplicity, we'll just use a fixed pathname for now
  // In a real app, you would get this from the request or context
  const pathname = `/protected`;

  return (
    <div className="flex h-screen overflow-hidden bg-background">
      {/* Sidebar with permission-based menu items */}
      <Sidebar lang={lang} dictionary={dictionary} pathname={pathname} />

      {/* Main content */}
      <div className="flex flex-col flex-1 overflow-hidden">
        {/* Navbar */}
        <Navbar lang={lang} dictionary={dictionary} />

        {/* Page content */}
        <main className="flex-1 overflow-auto p-6">{children}</main>
      </div>
    </div>
  );
}
```

This approach allows for:

1. **Internationalization at the Layout Level**: Loading translations for the entire layout
2. **Data Fetching in Layouts**: Fetching common data needed by all pages
3. **Composition**: Building complex UIs by composing server components
4. **Progressive Enhancement**: Starting with a fully server-rendered page and adding client interactivity only where needed

Note that authentication is handled by middleware before reaching this layout, so there's no need to check authentication status here.

## Internationalization

The application uses a centralized internationalization approach with the I18nService. Translation files are organized in the lib/i18n/locales directory:

```
src/
└── lib/
    └── i18n/
        ├── locales/
        │   ├── en.json     # English translations
        │   └── fr.json     # French translations
        ├── services/
        │   └── I18nService.ts  # Service for handling translations
        ├── middleware/
        │   └── i18nMiddleware.ts  # Middleware for language detection and redirection
        ├── settings.ts     # Language settings (locales, defaultLocale)
        └── index.ts        # Exports getDictionary function
```

The I18nService provides:
- Language detection based on the Accept-Language header
- Automatic redirection to the appropriate language version
- Dictionary-based translations with domain-specific localization
- Support for French (default) and English
- Fallback to default language when translations are missing

The language toggle component (`src/components/language-toggle.tsx`) allows users to switch between languages by replacing only the language slug in the URL path.

## Authentication and Authorization Flow

The application uses a multi-layered approach for authentication and authorization:

### Middleware Chain

The middleware chain handles authentication and authorization before the request reaches any page component:

```typescript
// src/middleware.ts
export async function middleware(request: NextRequest) {
  // First, handle internationalization
  const i18nResponse = i18nMiddleware(request);
  if (i18nResponse.status !== 200) {
    return i18nResponse;
  }

  // Handle authentication
  const authResponse = await authMiddleware(request);
  if (authResponse) {
    return authResponse;
  }

  // Handle authorization for protected routes
  if (request.nextUrl.pathname.includes("/protected")) {
    const authorizationResponse = await routeAuthorizationMiddleware(request);
    if (authorizationResponse.status !== 200) {
      return authorizationResponse;
    }
  }

  await updateSession(request);
}
```

### Authentication System

The authentication system uses Supabase Auth for user management:

1. **Authentication Service**: Handles user authentication operations
2. **Auth Middleware**: Redirects unauthenticated users trying to access protected routes
3. **Session Management**: Updates and validates the user session on each request

### Authorization System

The authorization system uses a role-based permission model:

1. **User Roles**: Three roles are defined - Director, Coordinator, and SocialWorker
2. **Permissions**: Permissions follow the format 'domain:feature:action' (e.g., 'users:list')
3. **Route Permissions**: Routes are mapped to required permissions in routePermissions.ts
4. **Authorization Middleware**: Checks if the user has the required permission for a route
5. **AuthorizationRequired Component**: Server component that conditionally renders content based on permissions

```typescript
// src/lib/authorization/components/AuthorizationRequired.tsx
export async function AuthorizationRequired({
  permission,
  children,
  fallback = null,
}: AuthorizationRequiredProps) {
  // Get the current user's role
  const role = await RoleService.getCurrentUserRole();

  // Check if the user has the required permission
  const hasPermission = PermissionService.hasPermission(role, permission);

  // Render children if user has permission, otherwise render fallback
  return hasPermission ? <>{children}</> : <>{fallback}</>;
}
```

### Multi-tenant Security

The application implements multi-tenant security at the database level:

1. **Organization ID**: All entities include an organization_id field
2. **Row-Level Security (RLS)**: Database policies filter data by organization_id
3. **Security Functions**: PostgreSQL functions check if a user belongs to an organization
4. **User-Organization Association**: Users are associated with an organization in the user_roles table

## Benefits of Error and Loading States at Each Level

Having error and loading states at each level of the application provides several benefits:

1. **Granular Error Handling**: Each page and feature can handle errors in a way that makes sense for its specific context.
2. **Improved User Experience**: Loading states provide visual feedback to users while data is being fetched.
3. **Streaming and Suspense**: Next.js can stream parts of the page as they become available, improving perceived performance.
4. **Isolation**: Errors in one part of the application don't affect other parts.
5. **Single Responsibility Principle**: Each component is responsible for its own error and loading states.
6. **Concurrent Rendering**: Different parts of the page can load independently, allowing for more efficient rendering.

## Conclusion

This standardized project structure provides a clear organization for our Next.js application, following domain-driven design principles. It ensures a separation of concerns, making the codebase more maintainable and testable. The inclusion of error, loading, and not-found states at each level enables a robust, resilient application with excellent user experience.

### Key Implementation Highlights

1. **Multi-tenant Architecture**: The application is designed from the ground up for multi-tenancy, with organization_id fields in all entities and row-level security at the database level.

2. **Internationalization**: A centralized approach with the I18nService provides language detection, translation, and language switching capabilities, with French as the default language.

3. **Authentication**: Supabase Auth is used for user authentication, with middleware handling redirects for unauthenticated users.

4. **Authorization**: A role-based permission system with three roles (Director, Coordinator, SocialWorker) and permissions in the format 'domain:feature:action'.

5. **Server-First Approach**: React Server Components and Server Actions are used extensively, with client components only where necessary for interactivity.

6. **Component Library**: UI components are built with Shadcn UI and Tailwind CSS v4, providing a consistent design system.

7. **Middleware Chain**: A chain of middleware handles internationalization, authentication, and authorization before requests reach page components.

8. **Type Safety**: TypeScript is used throughout the codebase, with database types generated from the Supabase schema.

This architecture provides a solid foundation for building a scalable, maintainable, and secure application for managing supervised family visits.
