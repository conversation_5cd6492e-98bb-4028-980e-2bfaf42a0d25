# Software Requirements Specification (SRS)

## RQRSDA Multi-Tenant SaaS Platform

**Version:** 1.0
**Date:** April 18, 2023
**Status:** Draft

## 1. Introduction

### 1.1 Purpose
This Software Requirements Specification (SRS) document describes the functional and non-functional requirements for the RQRSDA Multi-Tenant SaaS Platform. This platform will serve 36 independent social service organizations across Quebec that provide supervised visitation services for families.

### 1.2 Document Conventions
- **SHALL**: Indicates a mandatory requirement
- **SHOULD**: Indicates a recommended requirement
- **MAY**: Indicates an optional requirement

### 1.3 Intended Audience
- Development team
- RQRSDA stakeholders
- Organization directors and staff
- System administrators

### 1.4 Project Scope
The RQRSDA Multi-Tenant SaaS Platform will provide a comprehensive solution for managing supervised family visitation services, including request processing, case file management, scheduling, document management, and reporting. The system will automate manual processes, optimize resource allocation, and provide data-driven insights for funding applications.

### 1.5 References
- Business Case Document
- Executive Summary
- Initial Requirements List

## 2. Overall Description

### 2.1 Product Perspective
The RQRSDA Multi-Tenant SaaS Platform is a new web-based system designed to replace the current manual, paper-based processes used by RQRSDA member organizations. It will operate as a centralized platform with strict data segregation between organizations.

### 2.2 Product Features
The key features of the system include:
- Multi-tenant architecture with row-level security
- Two-level management (system and organization)
- User and employee management
- Contact management with relationship tracking
- Request and case file management
- Intelligent scheduling system
- Document management with template customization
- Observation notes with approval workflow
- Multi-language support
- Role-specific dashboards
- Comprehensive reporting and analytics

### 2.3 User Classes and Characteristics

#### 2.3.1 System Administrators
- Technical staff responsible for platform management
- Create and configure tenant organizations
- Assign initial director accounts
- Monitor system performance and security
- Provide technical support

#### 2.3.2 Directors
- Heads of each member organization
- Full administrative access to their organization
- Manage organization settings and configuration
- Oversee all operations within their organization
- Access comprehensive reporting and analytics
- Manage employees and user accounts

#### 2.3.3 Coordinators
- Middle management staff within organizations
- Manage day-to-day operations
- Process requests and create case files
- Coordinate scheduling and resource allocation
- Oversee social workers

#### 2.3.4 Social Workers
- Front-line staff conducting supervised visits
- Record observation notes
- Manage their schedules and availability
- Generate reports for court submissions

### 2.4 Operating Environment
- Web-based application accessible via modern browsers
- Responsive design for desktop and mobile devices
- Built on Next.js 15, React 19, and Supabase
- Cloud-hosted infrastructure

### 2.5 Design and Implementation Constraints
- Multi-tenant architecture with single instance to minimize costs
- Row-level security for data segregation
- Compliance with Quebec privacy regulations
- Support for French and English languages

### 2.6 Assumptions and Dependencies
- Internet connectivity is available at all organization locations
- Users have access to modern web browsers
- Organizations will provide necessary information for system configuration
- Staff will be available for training and feedback

## 3. Specific Requirements

### 3.1 External Interface Requirements

#### 3.1.1 User Interfaces
- **Login Screen**
  - Secure authentication form
  - Password reset functionality
  - Language selection option

- **Role-Specific Dashboards**
  - Director Dashboard with organizational metrics
  - Coordinator Dashboard with request queue and scheduling
  - Social Worker Dashboard with appointments and tasks

- **User Profile**
  - Personal information management
  - Language preference settings
  - Interface customization options
  - Password and security settings

- **Navigation**
  - Intuitive menu structure
  - Quick access to frequently used functions
  - Breadcrumb navigation for complex workflows

#### 3.1.2 Hardware Interfaces
- No specific hardware interfaces required
- System shall be accessible from standard computing devices

#### 3.1.3 Software Interfaces
- Integration with Supabase for:
  - Authentication
  - Database
  - Storage
  - Edge functions

#### 3.1.4 Communications Interfaces
- Standard HTTPS protocol for all communications
- WebSocket for real-time notifications (if applicable)

### 3.2 Functional Requirements

#### 3.2.1 Tenant Management

1. The system SHALL allow system administrators to create new tenant organizations.
2. The system SHALL provide an onboarding process for new organizations.
3. The system SHALL allow system administrators to assign initial director accounts.
4. The system SHALL provide a dashboard for system administrators to manage all organizations.

#### 3.2.2 Organization Management

1. The system SHALL allow directors to manage their organization's profile and settings.
2. The system SHALL allow directors to configure:
   - Organization contact information
   - Service offerings and details
   - Business hours
   - Locations and rooms
   - Branding elements (logo, colors)
3. The system SHALL allow directors to manage their organization without system administrator intervention.

#### 3.2.3 User Management & Authentication

1. The system SHALL support three user roles: Directors, Coordinators, and Social Workers.
2. The system SHALL implement role-based access control for all functions.
3. The system SHALL use Supabase Auth for secure authentication.
4. The system SHALL allow directors to manage user accounts within their organization.
5. The system SHALL enable organizations to manage their own users without system administrator intervention.

#### 3.2.4 User Profiles & Settings

1. The system SHALL provide a profile page for each user.
2. User profiles SHALL include:
   - Personal information and contact details
   - Language preference (French/English)
   - Notification preferences
   - Interface customization options
   - Password and security settings
3. The system SHALL track changes to user profiles for audit purposes.
4. The system SHALL use profile information throughout the system where appropriate.

#### 3.2.5 Multi-Language Support

1. The system SHALL support French (primary) and English languages.
2. All user interface elements SHALL be available in both languages.
3. All system-generated content SHALL support both languages.
4. The system SHALL allow language selection at the user level through profile settings.
5. The system SHALL remember language preferences between sessions.
6. The system SHALL allow default language configuration at the organization level.

#### 3.2.6 Employee Management

1. The system SHALL allow directors to create and manage employee profiles.
2. Employee profiles SHALL include:
   - Personal and contact information
   - Role and permissions
   - Specializations or areas of expertise
   - Employment details
3. The system SHALL link employee profiles to user accounts for system access.
4. The system SHALL allow activation/deactivation of employees without deleting records.
5. The system SHALL support employee availability management:
   - Define regular working hours
   - Set recurring availability patterns
   - Mark vacation time and other absences
   - Specify special availability exceptions
6. The system SHALL support employee workload tracking and management.
7. The system SHALL provide employee performance metrics and reporting.

#### 3.2.7 Contact Management

1. The system SHALL allow creation and management of contacts for families and other parties.
2. Contact information SHALL include all details necessary for scheduling and communication.
3. Contacts SHALL be organization-specific.
4. The system SHALL support relationship management between contacts:
   - Define and track relationships (e.g., parent-child, lawyer-client)
   - Support flexible relationship types
   - Visualize relationship networks
   - Use relationships to determine roles within requests and case files

#### 3.2.8 Request Management

1. The system SHALL provide a workflow to create and process service requests.
2. The system SHALL capture all relevant information including court judgments, service needs, and family availability.
3. The system SHALL track request status (draft, requested, waitlist, completed, closed).
4. The system SHALL provide automated decision support based on availability constraints.
5. The system SHALL track all changes made to requests with timestamps and user information.

#### 3.2.9 Case File Management

1. The system SHALL automatically create case files from approved requests.
2. The system SHALL maintain linkage between case files and original requests.
3. The system SHALL provide document management for case-related files.
4. The system SHALL track all case activities and status changes.
5. The system SHALL support multiple case file states:
   - Opening (onboarding, document signing)
   - Active (service delivery)
   - Suspended
   - Closed
6. The system SHALL track all changes made to case files with timestamps and user information.

#### 3.2.10 Document Management

1. The system SHALL provide storage and retrieval of documents.
2. The system SHALL support flexible document attachments to:
   - Contacts
   - Requests
   - Case files
   - Observation notes
   - Any other relevant entity
3. The system SHALL provide document template management:
   - Each organization can create and manage templates
   - Rich text editor for template creation
   - Dynamic fields for system data
   - Template versioning and history
   - Template categorization
   - Workflow stage triggers
4. The system SHALL support automatic document generation:
   - Generate documents when a request is completed and case file is created
   - Populate templates with system data
   - Support different document types
   - Organization-specific generation
   - Configurable rules for generation timing
5. The system SHALL support electronic signatures:
   - Capability for electronic signatures on generated documents
   - Configurable signature requirements
   - Signature status tracking
6. The system SHALL provide document versioning and access control.
7. The system SHALL support various document formats.

#### 3.2.11 Scheduling System

1. The system SHALL provide intelligent scheduling that considers:
   - Business hours
   - Family availability
   - Location and room availability
   - Employee schedule availability
2. The system SHALL support comprehensive availability management:
   - Directors and coordinators can update employee availability
   - Employees can manage their own availability
   - System respects all availability constraints
   - Visual calendar interface
3. The system SHALL provide conflict detection and resolution.
4. The system SHALL provide calendar views for staff and resources.
5. The system SHALL include a notification system for schedule changes.
6. The system SHALL optimize resource allocation.

#### 3.2.12 Observation Notes

1. The system SHALL provide an editor for recording observations during supervised visits.
2. The system SHALL implement an approval workflow for observation notes.
3. The system SHALL maintain versioning and history tracking for notes.
4. The system SHALL support templates and standardized formats for notes.
5. The system SHALL allow document attachments to observation notes.

#### 3.2.13 Role-Specific Dashboards

1. The system SHALL provide personalized dashboards upon login.
2. Dashboards SHALL be role-specific:
   - Director Dashboard: Organizational metrics, approvals, system status
   - Coordinator Dashboard: Request queue, scheduling conflicts, tasks
   - Social Worker Dashboard: Appointments, observation notes to complete
3. Dashboards SHALL include modular widgets:
   - Tasks and to-do items
   - Schedule/upcoming appointments
   - Recent activity
   - Important notifications
   - Quick access to frequently used functions
4. The system SHOULD support dashboard customization by users.
5. The widget system SHALL be extensible for future additions.

#### 3.2.14 Reporting & Analytics

1. The system SHALL generate court reports based on observation notes and case data.
2. The system SHALL provide metrics dashboards for organizational performance.
3. The system SHALL generate funding-related reports with key statistics:
   - Requests per month/year grouped by state
   - Case file statistics by state
   - Scheduling and appointment metrics
4. The system SHALL support export capabilities for reports in standard formats.

### 3.3 Non-Functional Requirements

#### 3.3.1 Performance Requirements

1. The system SHALL support concurrent usage by staff across all organizations.
2. Page load times SHALL NOT exceed 2 seconds under normal conditions.
3. Database queries SHALL be optimized for multi-tenant architecture.
4. The system SHALL handle at least 100 concurrent users without performance degradation.

#### 3.3.2 Security Requirements

1. The system SHALL implement row-level security policies for data segregation.
2. The system SHALL provide secure authentication and authorization.
3. The system SHALL encrypt sensitive information.
4. The system SHALL undergo regular security audits and updates.
5. The system SHALL implement appropriate password policies.
6. The system SHALL maintain audit logs for security-relevant actions.

#### 3.3.3 Usability Requirements

1. The system SHALL have an intuitive interface requiring minimal training.
2. The system SHALL provide responsive design for various devices.
3. The system SHALL include clear workflow guidance for complex processes.
4. The system SHALL provide comprehensive help documentation.
5. The system SHALL be accessible to users with disabilities where possible.

#### 3.3.4 Reliability Requirements

1. The system SHALL maintain availability of 99.5% or higher.
2. The system SHALL perform regular backups of all data.
3. The system SHALL have disaster recovery procedures.
4. The system SHALL implement graceful error handling and user notifications.

#### 3.3.5 Scalability Requirements

1. The architecture SHALL support growth in users and data volume.
2. Performance SHALL NOT degrade with increased usage.
3. The system SHALL support adding new features and capabilities over time.

#### 3.3.6 Auditability Requirements

1. The system SHALL maintain comprehensive change history for all major entities.
2. The system SHALL provide audit logs for security-relevant actions.
3. The system SHALL ensure non-repudiation for document signatures and approvals.

## 4. System Features

Detailed descriptions of key system features with user stories and acceptance criteria will be developed during the Comprehensive Planning phase.

## 5. Data Model and System Design

Please refer to the [Design Diagrams](design_diagrams.md) document for the following diagrams:

### 5.1 System Architecture
- High-Level System Architecture
- Deployment Architecture

### 5.2 Data Model
- Core Entity-Relationship Diagram
- Multi-Tenant Data Model

### 5.3 State Diagrams
- Request State Diagram
- Case File State Diagram

### 5.4 Process Flows
- Request to Case File Workflow
- Document Generation Process

### 5.5 Security Model
- Role-Based Access Control Model

## 6. Appendices

### 6.1 Glossary

- **RQRSDA**: Regroupement Québécois des Ressources de Supervision des Droits d'Accès
- **Tenant**: An independent organization within the RQRSDA
- **Case File**: A comprehensive record of a family's supervised visitation services
- **Request**: An initial application for supervised visitation services
- **Observation Notes**: Records of observations made during supervised visits
