{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "noImplicitAny": true, "noImplicitThis": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictBindCallApply": true, "strictPropertyInitialization": true, "noFallthroughCasesInSwitch": true, "forceConsistentCasingInFileNames": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"], "@contact/*": ["./src/app/[lang]/protected/contact/*"], "@contact/features/*": ["./src/app/[lang]/protected/contact/(features)/*"], "@contact/management/*": ["./src/app/[lang]/protected/contact/(features)/management/*"], "@organization/*": ["./src/app/[lang]/protected/organization/*"], "@organization/features/*": ["./src/app/[lang]/protected/organization/(features)/*"], "@organization/profile/*": ["./src/app/[lang]/protected/organization/(features)/profile/*"], "@organization/system-admin/*": ["./src/app/[lang]/protected/organization/(features)/system-admin/*"], "@user/*": ["./src/app/[lang]/protected/user/*"], "@components/*": ["./src/components/*"], "@lib/*": ["./src/lib/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "src/app/[lang]/protected/contact/(features)/management/(pages)/[id]/default.tsx", "src/app/[lang]/protected/contact/(features)/management/(pages)/[id]/view/layout.tsx"], "exclude": ["node_modules", "example", "**_tests__/**", "**/*.test.tsx", "**/*.test.ts"]}