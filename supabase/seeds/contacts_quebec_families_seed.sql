-- Quebec Families Contacts Seed Data
-- Contacts for the case file families to ensure referential integrity
-- Data in French targeting Quebec social services context

-- Insert Quebec family contacts for case files
INSERT INTO contacts (
  id,
  organization_id,
  name,
  email,
  phone,
  address,
  status,
  created_at,
  updated_at
) VALUES
-- <PERSON><PERSON><PERSON>
(
  gen_random_uuid(),
  (SELECT id FROM organizations WHERE name LIKE '%RQRSDA%' LIMIT 1),
  '<PERSON>',
  '{"personal": "<EMAIL>"}',
  '{"mobile": "************", "emergency": "************"}',
  '123 Rue Saint-Denis, Montréal, QC H2X 3K8',
  'active',
  '2025-01-10 10:00:00-05',
  '2025-01-10 10:00:00-05'
),
(
  gen_random_uuid(),
  (SELECT id FROM organizations WHERE name LIKE '%RQRSDA%' LIMIT 1),
  '<PERSON>',
  '{"personal": "<EMAIL>"}',
  '{"mobile": "************", "work": "************"}',
  '456 Avenue du Parc, Montréal, QC H2V 4E7',
  'active',
  '2025-01-10 10:15:00-05',
  '2025-01-10 10:15:00-05'
),
(
  gen_random_uuid(),
  (SELECT id FROM organizations WHERE name LIKE '%RQRSDA%' LIMIT 1),
  'Sophie Tremblay',
  '{}',
  '{}',
  '123 Rue Saint-Denis, Montréal, QC H2X 3K8',
  'active',
  '2025-01-10 10:30:00-05',
  '2025-01-10 10:30:00-05'
),
(
  gen_random_uuid(),
  (SELECT id FROM organizations WHERE name LIKE '%RQRSDA%' LIMIT 1),
  'Lucas Tremblay',
  '{}',
  '{}',
  '123 Rue Saint-Denis, Montréal, QC H2X 3K8',
  'active',
  '2025-01-10 10:45:00-05',
  '2025-01-10 10:45:00-05'
),

-- Famille Bouchard
(
  gen_random_uuid(),
  (SELECT id FROM organizations WHERE name LIKE '%RQRSDA%' LIMIT 1),
  'Julie Bouchard',
  '{"personal": "<EMAIL>"}',
  '{"mobile": "************", "emergency": "************"}',
  '789 Rue de la Couronne, Québec, QC G1R 3V4',
  'active',
  '2024-12-05 09:00:00-05',
  '2024-12-05 09:00:00-05'
),
(
  gen_random_uuid(),
  (SELECT id FROM organizations WHERE name LIKE '%RQRSDA%' LIMIT 1),
  'Marc Bouchard',
  '{"personal": "<EMAIL>"}',
  '{"mobile": "************", "work": "************"}',
  '321 Boulevard Charest, Québec, QC G1N 2E5',
  'active',
  '2024-12-05 09:15:00-05',
  '2024-12-05 09:15:00-05'
),

-- Famille Gagnon
(
  gen_random_uuid(),
  (SELECT id FROM organizations WHERE name LIKE '%RQRSDA%' LIMIT 1),
  'Sylvie Gagnon',
  '{"personal": "<EMAIL>"}',
  '{"mobile": "************", "emergency": "************"}',
  '654 Rue King Ouest, Sherbrooke, QC J1H 1S1',
  'active',
  '2024-09-01 11:00:00-04',
  '2024-09-01 11:00:00-04'
),
(
  gen_random_uuid(),
  (SELECT id FROM organizations WHERE name LIKE '%RQRSDA%' LIMIT 1),
  'Robert Gagnon',
  '{"personal": "<EMAIL>"}',
  '{"mobile": "************", "work": "************"}',
  '987 Rue Belvédère Sud, Sherbrooke, QC J1H 4C3',
  'active',
  '2024-09-01 11:15:00-04',
  '2024-09-01 11:15:00-04'
),

-- Famille Lavoie
(
  gen_random_uuid(),
  (SELECT id FROM organizations WHERE name LIKE '%RQRSDA%' LIMIT 1),
  'Nathalie Lavoie',
  '{"personal": "<EMAIL>"}',
  '{"mobile": "************", "emergency": "************"}',
  '147 Rue des Forges, Trois-Rivières, QC G9A 2G8',
  'active',
  '2024-05-25 08:00:00-04',
  '2024-05-25 08:00:00-04'
),
(
  gen_random_uuid(),
  (SELECT id FROM organizations WHERE name LIKE '%RQRSDA%' LIMIT 1),
  'Daniel Lavoie',
  '{"personal": "<EMAIL>"}',
  '{"mobile": "************", "work": "************"}',
  '258 Boulevard des Récollets, Trois-Rivières, QC G8Z 3X1',
  'active',
  '2024-05-25 08:15:00-04',
  '2024-05-25 08:15:00-04'
),

-- Famille Roy
(
  gen_random_uuid(),
  (SELECT id FROM organizations WHERE name LIKE '%RQRSDA%' LIMIT 1),
  'Catherine Roy',
  '{"personal": "<EMAIL>"}',
  '{"mobile": "************", "emergency": "************"}',
  '369 Boulevard Maloney Est, Gatineau, QC J8P 1E6',
  'active',
  '2025-01-18 13:00:00-05',
  '2025-01-18 13:00:00-05'
),
(
  gen_random_uuid(),
  (SELECT id FROM organizations WHERE name LIKE '%RQRSDA%' LIMIT 1),
  'Michael Roy',
  '{"personal": "<EMAIL>"}',
  '{"mobile": "************", "work": "************"}',
  '741 Riverside Drive, Ottawa, ON K1S 3W2',
  'active',
  '2025-01-18 13:15:00-05',
  '2025-01-18 13:15:00-05'
);

-- Comments for documentation
COMMENT ON TABLE contacts IS 'Contacts des familles québécoises pour les dossiers de cas de démonstration';

-- Summary of contacts created:
-- Famille Tremblay (CF-2025-001): Marie (mère), Pierre (père), Sophie (6 ans), Lucas (9 ans)
-- Famille Bouchard (CF-2025-002): Julie (mère), Marc (père) + 3 enfants
-- Famille Gagnon (CF-2024-045): Sylvie (mère), Robert (père) + 2 enfants
-- Famille Lavoie (CF-2024-023): Nathalie (mère), Daniel (père) + 2 enfants
-- Famille Roy (CF-2025-003): Catherine (mère), Michael (père) + 2 adolescents

-- Each contact includes:
-- - Realistic Quebec names and addresses
-- - JSONB email and phone fields matching schema
-- - Quebec-specific addresses with proper postal codes
-- - Quebec phone numbers with appropriate area codes

