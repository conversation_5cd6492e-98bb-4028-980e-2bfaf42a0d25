-- Seed file for RQRSDA social work/community service employees
-- This file creates sample employees with proper fields for the RQRSDA context
-- It also links some employees to user accounts created by the seed-auth.ts script

DO $$
DECLARE
    org_id UUID;
    director_user_id UUID;
    coordinator_user_id UUID;
    socialworker_user_id UUID;
    employee_id_1 UUID;
    employee_id_2 UUID;
    employee_id_3 UUID;
    employee_id_4 UUID;
BEGIN
    -- Get the first organization ID (should be the default one created by seed-auth.ts)
    SELECT id INTO org_id FROM organizations WHERE id = '********-0000-0000-0000-********0001';

    IF org_id IS NULL THEN
        -- Try to get any organization if the default one doesn't exist
        SELECT id INTO org_id FROM organizations LIMIT 1;

        IF org_id IS NULL THEN
            RAISE EXCEPTION 'No organization found. Please run the seed-auth.ts script first to create the default organization.';
        END IF;
    END IF;

    -- Get user IDs from auth.users table based on emails used in seed-auth.ts
    SELECT id INTO director_user_id FROM auth.users WHERE email = '<EMAIL>' LIMIT 1;
    SELECT id INTO coordinator_user_id FROM auth.users WHERE email = '<EMAIL>' LIMIT 1;
    SELECT id INTO socialworker_user_id FROM auth.users WHERE email = '<EMAIL>' LIMIT 1;

    -- Create Social Worker <NAME_EMAIL>
    INSERT INTO employees (
        first_name,
        last_name,
        employment_status,
        job_title,
        department,
        organization_id,
        profile_image,
        date_of_birth,
        gender,
        address,
        employee_id,
        hire_date,
        specializations,
        certifications,
        education,
        emails,
        phones,
        created_at,
        updated_at,
        user_account_id
    ) VALUES (
        'Social',
        'Worker',
        'active',
        'Social Worker',
        'Family Services',
        org_id,
        'https://randomuser.me/api/portraits/women/32.jpg',
        '1985-06-15',
        'Female',
        '123 Rue Principale, Montréal, QC H2X 1Y6',
        'SW-2023-001',
        '2023-01-15',
        ARRAY['Family Counseling', 'Crisis Intervention', 'Child Welfare'],
        ARRAY[
            json_build_object(
                'name', 'Licensed Social Worker',
                'issuer', 'Quebec Association of Social Workers',
                'date_issued', '2020-05-10',
                'expiry_date', '2025-05-10'
            ),
            json_build_object(
                'name', 'Crisis Intervention Specialist',
                'issuer', 'Canadian Crisis Response Institute',
                'date_issued', '2021-03-22',
                'expiry_date', '2024-03-22'
            )
        ],
        ARRAY[
            json_build_object(
                'degree', 'Master of Social Work',
                'institution', 'Université de Montréal',
                'year_completed', '2018'
            ),
            json_build_object(
                'degree', 'Bachelor of Social Work',
                'institution', 'McGill University',
                'year_completed', '2016'
            )
        ],
        ARRAY[
            json_build_object(
                'email', '<EMAIL>',
                'type', 'Work',
                'primary', true
            ),
            json_build_object(
                'email', '<EMAIL>',
                'type', 'Personal',
                'primary', false
            )
        ],
        ARRAY[
            json_build_object(
                'number', '************',
                'type', 'Work',
                'primary', true
            ),
            json_build_object(
                'number', '************',
                'type', 'Mobile',
                'primary', false
            )
        ],
        NOW(),
        NOW(),
        socialworker_user_id
    ) RETURNING id INTO employee_id_1;

    INSERT INTO employees (
        first_name,
        last_name,
        employment_status,
        job_title,
        department,
        organization_id,
        profile_image,
        date_of_birth,
        gender,
        address,
        employee_id,
        hire_date,
        specializations,
        certifications,
        education,
        emails,
        phones,
        created_at,
        updated_at
    ) VALUES (
        'Jean',
        'Lavoie',
        'active',
        'Case Manager',
        'Youth Services',
        org_id,
        'https://randomuser.me/api/portraits/men/45.jpg',
        '1982-09-23',
        'Male',
        '456 Boulevard St-Laurent, Montréal, QC H2Y 2X8',
        'CM-2022-015',
        '2022-05-10',
        ARRAY['Youth Counseling', 'Substance Abuse', 'Mental Health'],
        ARRAY[
            json_build_object(
                'name', 'Certified Case Manager',
                'issuer', 'Commission for Case Manager Certification',
                'date_issued', '2019-07-15',
                'expiry_date', '2024-07-15'
            ),
            json_build_object(
                'name', 'Youth Mental Health First Aid',
                'issuer', 'Mental Health Commission of Canada',
                'date_issued', '2021-11-05',
                'expiry_date', '2024-11-05'
            )
        ],
        ARRAY[
            json_build_object(
                'degree', 'Master of Social Work',
                'institution', 'Université du Québec à Montréal',
                'year_completed', '2015'
            )
        ],
        ARRAY[
            json_build_object(
                'email', '<EMAIL>',
                'type', 'Work',
                'primary', true
            )
        ],
        ARRAY[
            json_build_object(
                'number', '************',
                'type', 'Work',
                'primary', true
            )
        ],
        NOW(),
        NOW()
    ) RETURNING id INTO employee_id_2;

    -- Create Program Coordinator <NAME_EMAIL>
    INSERT INTO employees (
        first_name,
        last_name,
        employment_status,
        job_title,
        department,
        organization_id,
        profile_image,
        date_of_birth,
        gender,
        address,
        employee_id,
        hire_date,
        specializations,
        certifications,
        education,
        emails,
        phones,
        created_at,
        updated_at,
        user_account_id
    ) VALUES (
        'Visit',
        'Coordinator',
        'active',
        'Program Coordinator',
        'Community Outreach',
        org_id,
        'https://randomuser.me/api/portraits/women/68.jpg',
        '1979-03-12',
        'Female',
        '789 Avenue du Parc, Montréal, QC H2W 1S8',
        'PC-2020-007',
        '2020-02-01',
        ARRAY['Program Development', 'Community Engagement', 'Grant Writing'],
        ARRAY[
            json_build_object(
                'name', 'Project Management Professional',
                'issuer', 'Project Management Institute',
                'date_issued', '2018-04-30',
                'expiry_date', '2024-04-30'
            )
        ],
        ARRAY[
            json_build_object(
                'degree', 'Master of Public Administration',
                'institution', 'Concordia University',
                'year_completed', '2012'
            ),
            json_build_object(
                'degree', 'Bachelor of Social Sciences',
                'institution', 'University of Ottawa',
                'year_completed', '2010'
            )
        ],
        ARRAY[
            json_build_object(
                'email', '<EMAIL>',
                'type', 'Work',
                'primary', true
            )
        ],
        ARRAY[
            json_build_object(
                'number', '************',
                'type', 'Work',
                'primary', true
            ),
            json_build_object(
                'number', '************',
                'type', 'Mobile',
                'primary', false
            )
        ],
        NOW(),
        NOW(),
        coordinator_user_id
    ) RETURNING id INTO employee_id_3;

    -- Create Executive Director <NAME_EMAIL>
    INSERT INTO employees (
        first_name,
        last_name,
        employment_status,
        job_title,
        department,
        organization_id,
        profile_image,
        date_of_birth,
        gender,
        address,
        employee_id,
        hire_date,
        specializations,
        certifications,
        education,
        emails,
        phones,
        created_at,
        updated_at,
        user_account_id
    ) VALUES (
        'Organization',
        'Director',
        'active',
        'Executive Director',
        'Administration',
        org_id,
        'https://randomuser.me/api/portraits/men/22.jpg',
        '1972-11-05',
        'Male',
        '101 Rue Sherbrooke, Montréal, QC H2X 1C3',
        'ED-2018-001',
        '2018-07-01',
        ARRAY['Strategic Planning', 'Nonprofit Management', 'Fundraising'],
        ARRAY[
            json_build_object(
                'name', 'Certified Nonprofit Executive',
                'issuer', 'Nonprofit Leadership Alliance',
                'date_issued', '2016-09-15',
                'expiry_date', '2026-09-15'
            )
        ],
        ARRAY[
            json_build_object(
                'degree', 'MBA',
                'institution', 'HEC Montréal',
                'year_completed', '2005'
            ),
            json_build_object(
                'degree', 'Bachelor of Social Work',
                'institution', 'Université Laval',
                'year_completed', '2000'
            )
        ],
        ARRAY[
            json_build_object(
                'email', '<EMAIL>',
                'type', 'Work',
                'primary', true
            )
        ],
        ARRAY[
            json_build_object(
                'number', '514-555-4567',
                'type', 'Work',
                'primary', true
            )
        ],
        NOW(),
        NOW(),
        director_user_id
    ) RETURNING id INTO employee_id_4;

    -- Log the number of employees created and linked
    RAISE NOTICE 'Created 4 RQRSDA employees with proper fields';
    RAISE NOTICE 'Linked 3 employees to user accounts from seed-auth.ts';

END $$;
