# Données de Test - Dossiers de Cas Québécois

Ce répertoire contient des données de test complètes pour le domaine des dossiers de cas, spécifiquement conçues pour le contexte québécois des services sociaux.

## Fichiers de Données

### 1. `case_files_quebec_seed.sql`
Données principales pour les dossiers de cas avec différents états et scénarios québécois.

**Contenu:**
- 5 dossiers de cas représentant tous les états possibles
- Métadonnées complètes en français
- Liens vers les contacts familiaux
- Historique complet des transitions d'état

### 2. `contacts_quebec_families_seed.sql`
Contacts familiaux correspondant aux dossiers de cas pour assurer l'intégrité référentielle.

**Contenu:**
- Parents gardiens et non-gardiens
- Enfants avec informations scolaires
- Métadonnées de disponibilité et contraintes
- Cas spécial bilingue (fami<PERSON> Roy)

## Dossiers de Cas Créés

### CF-2025-001 - <PERSON><PERSON><PERSON> (État: Ouverture)
- **Situation:** Séparation récente, visites supervisées requises
- **Enfants:** Sophie (6 ans), Lucas (9 ans)
- **Région:** Montréal
- **Statut:** Documents en attente de signature
- **Contacts:** Marie (mère gardienne), Pierre (père non-gardien)

### CF-2025-002 - Famille Bouchard (État: Actif)
- **Situation:** Visites supervisées en cours, progrès positifs
- **Enfants:** 3 enfants (4, 7, 12 ans)
- **Région:** Québec
- **Statut:** 8 visites complétées, évaluation satisfaisante
- **Contacts:** Julie (mère gardienne), Marc (père non-gardien)

### CF-2024-045 - Famille Gagnon (État: Suspendu)
- **Situation:** Suspension temporaire - non-respect des conditions
- **Enfants:** 2 enfants (5, 8 ans)
- **Région:** Sherbrooke
- **Statut:** Absences répétées, révision prévue février 2025
- **Contacts:** Sylvie (mère gardienne), Robert (père non-gardien)

### CF-2024-023 - Famille Lavoie (État: Fermé)
- **Situation:** Objectifs atteints - réunification familiale réussie
- **Enfants:** 2 enfants (3, 6 ans)
- **Région:** Trois-Rivières
- **Statut:** Fermeture avec succès après 6 mois
- **Contacts:** Nathalie (mère gardienne), Daniel (père non-gardien)

### CF-2025-003 - Famille Roy (État: Ouverture)
- **Situation:** Famille bilingue, services en français et anglais
- **Enfants:** 2 adolescents (10, 14 ans)
- **Région:** Gatineau
- **Statut:** Besoins spéciaux identifiés (services bilingues)
- **Contacts:** Catherine (mère, français), Michael (père, anglais)

## Historique des Transitions

Chaque dossier inclut un historique complet montrant:
- Création du dossier
- Transitions d'état avec justifications
- Mises à jour de progrès
- Actions administratives
- Métadonnées de changement structurées

## Utilisation

### Installation des Données
```sql
-- 1. Installer d'abord les contacts (dépendances)
\i supabase/seeds/contacts_quebec_families_seed.sql

-- 2. Installer ensuite les dossiers de cas
\i supabase/seeds/case_files_quebec_seed.sql
```

### Requêtes de Test Utiles

```sql
-- Voir tous les dossiers par état
SELECT case_number, status, metadata->>'famille' as famille, 
       metadata->>'region' as region
FROM case_files 
ORDER BY status, created_at;

-- Historique complet d'un dossier
SELECT cf.case_number, cfh.action, cfh.changes, cfh.created_at
FROM case_files cf
JOIN case_file_history cfh ON cf.id = cfh.case_file_id
WHERE cf.case_number = 'CF-2025-001'
ORDER BY cfh.created_at;

-- Contacts par dossier
SELECT cf.case_number, c.first_name, c.last_name, 
       cfc.relationship_type
FROM case_files cf
JOIN case_file_contacts cfc ON cf.id = cfc.case_file_id
JOIN contacts c ON cfc.contact_id = c.id
ORDER BY cf.case_number, cfc.relationship_type;

-- Dossiers actifs avec progrès
SELECT case_number, 
       metadata->>'famille' as famille,
       metadata->'progres'->>'visites_completees' as visites,
       metadata->'progres'->>'evaluation' as evaluation
FROM case_files 
WHERE status = 'active';
```

## Caractéristiques Québécoises

### Données Culturelles
- Noms de famille québécois authentiques
- Adresses réelles de villes québécoises
- Terminologie des services sociaux du Québec
- Contexte légal et administratif approprié

### Multilinguisme
- Données principalement en français
- Cas spécial bilingue (famille Roy)
- Métadonnées adaptées aux préférences linguistiques
- Support pour services bilingues

### Contexte Régional
- Montréal, Québec, Sherbrooke, Trois-Rivières, Gatineau
- Codes postaux québécois valides
- Numéros de téléphone avec indicatifs régionaux appropriés
- Références aux institutions québécoises

## Notes Techniques

### Intégrité Référentielle
- Les contacts doivent être créés avant les dossiers de cas
- Les IDs utilisent des UUIDs fixes pour la reproductibilité
- Les références aux organisations et utilisateurs utilisent des requêtes dynamiques

### Métadonnées JSONB
- Structure flexible pour informations spécifiques
- Facilite les requêtes complexes
- Permet l'évolution du schéma sans migrations

### Audit Trail
- Historique complet de tous les changements
- Métadonnées structurées pour les transitions
- Traçabilité des actions utilisateur

## Maintenance

Ces données de test doivent être mises à jour si:
- Le schéma des dossiers de cas change
- De nouveaux états sont ajoutés
- Les exigences métier évoluent
- De nouveaux scénarios de test sont nécessaires

Pour des questions ou modifications, consulter la documentation du domaine case file dans `supabase/migrations/20250524090752_create_case_file_domain.sql`.
