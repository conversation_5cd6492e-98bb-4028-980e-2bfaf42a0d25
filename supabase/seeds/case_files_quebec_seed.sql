-- Case Files Quebec Seed Data
-- Comprehensive seed data for case file domain with Quebec-specific scenarios
-- Data in French targeting Quebec social services context

-- Insert case files with various states and Quebec family scenarios
INSERT INTO case_files (
  id,
  organization_id,
  request_id,
  case_number,
  status,
  created_by,
  assigned_to,
  created_at,
  updated_at,
  opened_at,
  activated_at,
  suspended_at,
  closed_at,
  metadata
) VALUES
-- Case File 1: Opening State - Famille Tremblay
(
  'cf001-0000-0000-0000-000000000001',
  (SELECT id FROM organizations WHERE name LIKE '%RQRSDA%' LIMIT 1),
  (SELECT id FROM requests WHERE status = 'approved' LIMIT 1),
  'CF-2025-001',
  'opening',
  (SELECT id FROM auth.users WHERE email LIKE '%director%' LIMIT 1),
  (SELECT id FROM employees WHERE job_title = 'Social Worker' LIMIT 1),
  '2025-01-15 09:00:00-05',
  '2025-01-15 09:00:00-05',
  '2025-01-15 09:00:00-05',
  NULL,
  NULL,
  NULL,
  '{
    "famille": "Tremblay",
    "situation": "Séparation récente, visites supervisées requises",
    "enfants_ages": [6, 9],
    "langue_principale": "français",
    "region": "Montréal",
    "priorite": "normale",
    "documents_requis": [
      "Entente de services",
      "Formulaire de consentement",
      "Plan de visite"
    ],
    "statut_documents": {
      "entente_services": "en_attente_signature",
      "consentement": "en_attente_signature",
      "plan_visite": "genere"
    }
  }'
),

-- Case File 2: Active State - Famille Bouchard
(
  'cf002-0000-0000-0000-000000000002',
  (SELECT id FROM organizations WHERE name LIKE '%RQRSDA%' LIMIT 1),
  (SELECT id FROM requests WHERE status = 'approved' OFFSET 1 LIMIT 1),
  'CF-2025-002',
  'active',
  (SELECT id FROM auth.users WHERE email LIKE '%director%' LIMIT 1),
  (SELECT id FROM employees WHERE job_title = 'Social Worker' LIMIT 1),
  '2024-12-10 10:30:00-05',
  '2025-01-20 14:15:00-05',
  '2024-12-10 10:30:00-05',
  '2024-12-15 16:00:00-05',
  NULL,
  NULL,
  '{
    "famille": "Bouchard",
    "situation": "Visites supervisées en cours, progrès positifs",
    "enfants_ages": [4, 7, 12],
    "langue_principale": "français",
    "region": "Québec",
    "priorite": "normale",
    "frequence_visites": "hebdomadaire",
    "duree_visite": 120,
    "lieu_visite": "Centre communautaire",
    "progres": {
      "visites_completees": 8,
      "visites_manquees": 1,
      "evaluation": "satisfaisante",
      "prochaine_revision": "2025-03-15"
    }
  }'
),

-- Case File 3: Suspended State - Famille Gagnon
(
  'cf003-0000-0000-0000-000000000003',
  (SELECT id FROM organizations WHERE name LIKE '%RQRSDA%' LIMIT 1),
  (SELECT id FROM requests WHERE status = 'approved' OFFSET 2 LIMIT 1),
  'CF-2024-045',
  'suspended',
  (SELECT id FROM auth.users WHERE email LIKE '%coordinator%' LIMIT 1),
  (SELECT id FROM employees WHERE job_title = 'Case Manager' LIMIT 1),
  '2024-09-05 11:00:00-04',
  '2025-01-10 09:30:00-05',
  '2024-09-05 11:00:00-04',
  '2024-09-12 14:00:00-04',
  '2025-01-10 09:30:00-05',
  NULL,
  '{
    "famille": "Gagnon",
    "situation": "Suspension temporaire - non-respect des conditions",
    "enfants_ages": [5, 8],
    "langue_principale": "français",
    "region": "Sherbrooke",
    "priorite": "elevee",
    "raison_suspension": "Absences répétées sans justification",
    "conditions_reprise": [
      "Rencontre avec travailleur social",
      "Engagement écrit de respect des horaires",
      "Évaluation psychosociale mise à jour"
    ],
    "date_revision_prevue": "2025-02-15",
    "contact_urgence": "Directeur des services"
  }'
),

-- Case File 4: Closed State - Famille Lavoie
(
  'cf004-0000-0000-0000-000000000004',
  (SELECT id FROM organizations WHERE name LIKE '%RQRSDA%' LIMIT 1),
  (SELECT id FROM requests WHERE status = 'approved' OFFSET 3 LIMIT 1),
  'CF-2024-023',
  'closed',
  (SELECT id FROM auth.users WHERE email LIKE '%director%' LIMIT 1),
  (SELECT id FROM employees WHERE job_title = 'Program Coordinator' LIMIT 1),
  '2024-06-01 08:00:00-04',
  '2024-12-20 16:00:00-05',
  '2024-06-01 08:00:00-04',
  '2024-06-08 10:00:00-04',
  NULL,
  '2024-12-20 16:00:00-05',
  '{
    "famille": "Lavoie",
    "situation": "Objectifs atteints - réunification familiale réussie",
    "enfants_ages": [3, 6],
    "langue_principale": "français",
    "region": "Trois-Rivières",
    "priorite": "normale",
    "duree_totale_mois": 6,
    "visites_totales": 24,
    "taux_reussite": "100%",
    "resultats": {
      "objectifs_atteints": true,
      "recommandations_suivies": true,
      "evaluation_finale": "excellente",
      "suivi_post_fermeture": "3_mois"
    },
    "raison_fermeture": "Objectifs du plan d'intervention atteints"
  }'
),

-- Case File 5: Opening State - Famille Roy (Bilingual)
(
  'cf005-0000-0000-0000-000000000005',
  (SELECT id FROM organizations WHERE name LIKE '%RQRSDA%' LIMIT 1),
  (SELECT id FROM requests WHERE status = 'approved' OFFSET 4 LIMIT 1),
  'CF-2025-003',
  'opening',
  (SELECT id FROM auth.users WHERE email LIKE '%coordinator%' LIMIT 1),
  (SELECT id FROM employees WHERE role = 'social_worker' OFFSET 1 LIMIT 1),
  '2025-01-20 13:30:00-05',
  '2025-01-20 13:30:00-05',
  '2025-01-20 13:30:00-05',
  NULL,
  NULL,
  NULL,
  '{
    "famille": "Roy",
    "situation": "Famille bilingue, services en français et anglais",
    "enfants_ages": [10, 14],
    "langue_principale": "français",
    "langue_secondaire": "anglais",
    "region": "Gatineau",
    "priorite": "normale",
    "besoins_speciaux": [
      "Interprétation bilingue",
      "Documents en deux langues"
    ],
    "documents_requis": [
      "Service Agreement / Entente de services",
      "Consent Form / Formulaire de consentement",
      "Visit Plan / Plan de visite"
    ],
    "preferences_communication": "français_prioritaire"
  }'
);

-- Insert case file contacts (linking families to case files)
INSERT INTO case_file_contacts (
  id,
  organization_id,
  case_file_id,
  contact_id,
  relationship_type,
  created_at
) VALUES
-- Famille Tremblay contacts
(
  'cfc01-000-000-000-000000000001',
  (SELECT id FROM organizations WHERE name LIKE '%RQRSDA%' LIMIT 1),
  'cf001-0000-0000-0000-000000000001',
  (SELECT id FROM contacts WHERE first_name = 'Marie' AND last_name = 'Tremblay' LIMIT 1),
  'parent_gardien',
  '2025-01-15 09:15:00-05'
),
(
  'cfc02-000-000-000-000000000002',
  (SELECT id FROM organizations WHERE name LIKE '%RQRSDA%' LIMIT 1),
  'cf001-0000-0000-0000-000000000001',
  (SELECT id FROM contacts WHERE first_name = 'Pierre' AND last_name = 'Tremblay' LIMIT 1),
  'parent_non_gardien',
  '2025-01-15 09:15:00-05'
),
(
  'cfc03-000-000-000-000000000003',
  (SELECT id FROM organizations WHERE name LIKE '%RQRSDA%' LIMIT 1),
  'cf001-0000-0000-0000-000000000001',
  (SELECT id FROM contacts WHERE first_name = 'Sophie' AND last_name = 'Tremblay' LIMIT 1),
  'enfant',
  '2025-01-15 09:15:00-05'
),
(
  'cfc04-000-000-000-000000000004',
  (SELECT id FROM organizations WHERE name LIKE '%RQRSDA%' LIMIT 1),
  'cf001-0000-0000-0000-000000000001',
  (SELECT id FROM contacts WHERE first_name = 'Lucas' AND last_name = 'Tremblay' LIMIT 1),
  'enfant',
  '2025-01-15 09:15:00-05'
),

-- Famille Bouchard contacts
(
  'cfc05-000-000-000-000000000005',
  (SELECT id FROM organizations WHERE name LIKE '%RQRSDA%' LIMIT 1),
  'cf002-0000-0000-0000-000000000002',
  (SELECT id FROM contacts WHERE first_name = 'Julie' AND last_name = 'Bouchard' LIMIT 1),
  'parent_gardien',
  '2024-12-10 10:45:00-05'
),
(
  'cfc06-000-000-000-000000000006',
  (SELECT id FROM organizations WHERE name LIKE '%RQRSDA%' LIMIT 1),
  'cf002-0000-0000-0000-000000000002',
  (SELECT id FROM contacts WHERE first_name = 'Marc' AND last_name = 'Bouchard' LIMIT 1),
  'parent_non_gardien',
  '2024-12-10 10:45:00-05'
),

-- Famille Gagnon contacts
(
  'cfc07-000-000-000-000000000007',
  (SELECT id FROM organizations WHERE name LIKE '%RQRSDA%' LIMIT 1),
  'cf003-0000-0000-0000-000000000003',
  (SELECT id FROM contacts WHERE first_name = 'Sylvie' AND last_name = 'Gagnon' LIMIT 1),
  'parent_gardien',
  '2024-09-05 11:15:00-04'
),
(
  'cfc08-000-000-000-000000000008',
  (SELECT id FROM organizations WHERE name LIKE '%RQRSDA%' LIMIT 1),
  'cf003-0000-0000-0000-000000000003',
  (SELECT id FROM contacts WHERE first_name = 'Robert' AND last_name = 'Gagnon' LIMIT 1),
  'parent_non_gardien',
  '2024-09-05 11:15:00-04'
),

-- Famille Lavoie contacts
(
  'cfc09-000-000-000-000000000009',
  (SELECT id FROM organizations WHERE name LIKE '%RQRSDA%' LIMIT 1),
  'cf004-0000-0000-0000-000000000004',
  (SELECT id FROM contacts WHERE first_name = 'Nathalie' AND last_name = 'Lavoie' LIMIT 1),
  'parent_gardien',
  '2024-06-01 08:15:00-04'
),
(
  'cfc10-000-000-000-000000000010',
  (SELECT id FROM organizations WHERE name LIKE '%RQRSDA%' LIMIT 1),
  'cf004-0000-0000-0000-000000000004',
  (SELECT id FROM contacts WHERE first_name = 'Daniel' AND last_name = 'Lavoie' LIMIT 1),
  'parent_non_gardien',
  '2024-06-01 08:15:00-04'
),

-- Famille Roy contacts
(
  'cfc11-000-000-000-000000000011',
  (SELECT id FROM organizations WHERE name LIKE '%RQRSDA%' LIMIT 1),
  'cf005-0000-0000-0000-000000000005',
  (SELECT id FROM contacts WHERE first_name = 'Catherine' AND last_name = 'Roy' LIMIT 1),
  'parent_gardien',
  '2025-01-20 13:45:00-05'
),
(
  'cfc12-000-000-000-000000000012',
  (SELECT id FROM organizations WHERE name LIKE '%RQRSDA%' LIMIT 1),
  'cf005-0000-0000-0000-000000000005',
  (SELECT id FROM contacts WHERE first_name = 'Michael' AND last_name = 'Roy' LIMIT 1),
  'parent_non_gardien',
  '2025-01-20 13:45:00-05'
);

-- Insert case file history (audit trail of state changes)
INSERT INTO case_file_history (
  id,
  organization_id,
  case_file_id,
  user_id,
  action,
  changes,
  created_at
) VALUES
-- Famille Tremblay history
(
  'cfh01-000-000-000-000000000001',
  (SELECT id FROM organizations WHERE name LIKE '%RQRSDA%' LIMIT 1),
  'cf001-0000-0000-0000-000000000001',
  (SELECT id FROM auth.users WHERE email LIKE '%director%' LIMIT 1),
  'created',
  '{
    "status": {"from": null, "to": "opening"},
    "assigned_to": {"from": null, "to": "social_worker"},
    "case_number": {"from": null, "to": "CF-2025-001"},
    "note": "Dossier créé suite à approbation de la demande - Famille Tremblay"
  }',
  '2025-01-15 09:00:00-05'
),

-- Famille Bouchard history (multiple transitions)
(
  'cfh02-000-000-000-000000000002',
  (SELECT id FROM organizations WHERE name LIKE '%RQRSDA%' LIMIT 1),
  'cf002-0000-0000-0000-000000000002',
  (SELECT id FROM auth.users WHERE email LIKE '%director%' LIMIT 1),
  'created',
  '{
    "status": {"from": null, "to": "opening"},
    "assigned_to": {"from": null, "to": "social_worker"},
    "case_number": {"from": null, "to": "CF-2025-002"},
    "note": "Dossier créé - Famille Bouchard, 3 enfants"
  }',
  '2024-12-10 10:30:00-05'
),
(
  'cfh03-000-000-000-000000000003',
  (SELECT id FROM organizations WHERE name LIKE '%RQRSDA%' LIMIT 1),
  'cf002-0000-0000-0000-000000000002',
  (SELECT id FROM auth.users WHERE email LIKE '%coordinator%' LIMIT 1),
  'status_change',
  '{
    "status": {"from": "opening", "to": "active"},
    "activated_at": {"from": null, "to": "2024-12-15T16:00:00-05:00"},
    "note": "Tous les documents signés, dossier activé. Première visite programmée."
  }',
  '2024-12-15 16:00:00-05'
),
(
  'cfh04-000-000-000-000000000004',
  (SELECT id FROM organizations WHERE name LIKE '%RQRSDA%' LIMIT 1),
  'cf002-0000-0000-0000-000000000002',
  (SELECT id FROM auth.users WHERE email LIKE '%social_worker%' LIMIT 1),
  'progress_update',
  '{
    "visites_completees": {"from": 4, "to": 8},
    "evaluation": {"from": "en_cours", "to": "satisfaisante"},
    "note": "Progrès positifs observés. Famille respecte les conditions."
  }',
  '2025-01-20 14:15:00-05'
),

-- Famille Gagnon history (suspension)
(
  'cfh05-000-000-000-000000000005',
  (SELECT id FROM organizations WHERE name LIKE '%RQRSDA%' LIMIT 1),
  'cf003-0000-0000-0000-000000000003',
  (SELECT id FROM auth.users WHERE email LIKE '%coordinator%' LIMIT 1),
  'created',
  '{
    "status": {"from": null, "to": "opening"},
    "assigned_to": {"from": null, "to": "social_worker"},
    "case_number": {"from": null, "to": "CF-2024-045"},
    "note": "Dossier créé - Famille Gagnon"
  }',
  '2024-09-05 11:00:00-04'
),
(
  'cfh06-000-000-000-000000000006',
  (SELECT id FROM organizations WHERE name LIKE '%RQRSDA%' LIMIT 1),
  'cf003-0000-0000-0000-000000000003',
  (SELECT id FROM auth.users WHERE email LIKE '%coordinator%' LIMIT 1),
  'status_change',
  '{
    "status": {"from": "opening", "to": "active"},
    "activated_at": {"from": null, "to": "2024-09-12T14:00:00-04:00"},
    "note": "Dossier activé après signature des documents"
  }',
  '2024-09-12 14:00:00-04'
),
(
  'cfh07-000-000-000-000000000007',
  (SELECT id FROM organizations WHERE name LIKE '%RQRSDA%' LIMIT 1),
  'cf003-0000-0000-0000-000000000003',
  (SELECT id FROM auth.users WHERE email LIKE '%director%' LIMIT 1),
  'status_change',
  '{
    "status": {"from": "active", "to": "suspended"},
    "suspended_at": {"from": null, "to": "2025-01-10T09:30:00-05:00"},
    "raison": "Absences répétées sans justification (3 visites manquées consécutives)",
    "conditions_reprise": ["Rencontre obligatoire", "Engagement écrit", "Évaluation mise à jour"],
    "note": "Suspension temporaire - non-respect des conditions de visite"
  }',
  '2025-01-10 09:30:00-05'
),

-- Famille Lavoie history (complete lifecycle)
(
  'cfh08-000-000-000-000000000008',
  (SELECT id FROM organizations WHERE name LIKE '%RQRSDA%' LIMIT 1),
  'cf004-0000-0000-0000-000000000004',
  (SELECT id FROM auth.users WHERE email LIKE '%director%' LIMIT 1),
  'created',
  '{
    "status": {"from": null, "to": "opening"},
    "assigned_to": {"from": null, "to": "social_worker"},
    "case_number": {"from": null, "to": "CF-2024-023"},
    "note": "Dossier créé - Famille Lavoie, 2 enfants en bas âge"
  }',
  '2024-06-01 08:00:00-04'
),
(
  'cfh09-000-000-000-000000000009',
  (SELECT id FROM organizations WHERE name LIKE '%RQRSDA%' LIMIT 1),
  'cf004-0000-0000-0000-000000000004',
  (SELECT id FROM auth.users WHERE email LIKE '%coordinator%' LIMIT 1),
  'status_change',
  '{
    "status": {"from": "opening", "to": "active"},
    "activated_at": {"from": null, "to": "2024-06-08T10:00:00-04:00"},
    "note": "Activation rapide - documents signés, famille très coopérative"
  }',
  '2024-06-08 10:00:00-04'
),
(
  'cfh10-000-000-000-000000000010',
  (SELECT id FROM organizations WHERE name LIKE '%RQRSDA%' LIMIT 1),
  'cf004-0000-0000-0000-000000000004',
  (SELECT id FROM auth.users WHERE email LIKE '%social_worker%' LIMIT 1),
  'progress_update',
  '{
    "visites_completees": {"from": 12, "to": 24},
    "evaluation": {"from": "satisfaisante", "to": "excellente"},
    "objectifs_atteints": {"from": "en_cours", "to": "complets"},
    "note": "Objectifs du plan d'intervention atteints. Recommandation de fermeture."
  }',
  '2024-12-15 15:00:00-05'
),
(
  'cfh11-000-000-000-000000000011',
  (SELECT id FROM organizations WHERE name LIKE '%RQRSDA%' LIMIT 1),
  'cf004-0000-0000-0000-000000000004',
  (SELECT id FROM auth.users WHERE email LIKE '%director%' LIMIT 1),
  'status_change',
  '{
    "status": {"from": "active", "to": "closed"},
    "closed_at": {"from": null, "to": "2024-12-20T16:00:00-05:00"},
    "raison_fermeture": "Objectifs atteints - réunification familiale réussie",
    "suivi_recommande": "3 mois post-fermeture",
    "note": "Fermeture avec succès. Famille autonome et stable."
  }',
  '2024-12-20 16:00:00-05'
),

-- Famille Roy history (recent creation)
(
  'cfh12-000-000-000-000000000012',
  (SELECT id FROM organizations WHERE name LIKE '%RQRSDA%' LIMIT 1),
  'cf005-0000-0000-0000-000000000005',
  (SELECT id FROM auth.users WHERE email LIKE '%coordinator%' LIMIT 1),
  'created',
  '{
    "status": {"from": null, "to": "opening"},
    "assigned_to": {"from": null, "to": "social_worker"},
    "case_number": {"from": null, "to": "CF-2025-003"},
    "besoins_speciaux": ["Services bilingues", "Documents en deux langues"],
    "note": "Dossier créé - Famille Roy (bilingue), besoins spéciaux identifiés"
  }',
  '2025-01-20 13:30:00-05'
);

-- Comments for documentation
COMMENT ON TABLE case_files IS 'Dossiers de cas avec états multiples pour démonstration du système québécois';
COMMENT ON TABLE case_file_contacts IS 'Liens familiaux pour les dossiers de cas québécois';
COMMENT ON TABLE case_file_history IS 'Historique complet des transitions d''état et modifications';

-- Summary of seed data created:
-- 5 case files representing different states and Quebec family scenarios:
-- 1. CF-2025-001 (Tremblay) - Opening state, documents pending
-- 2. CF-2025-002 (Bouchard) - Active state, ongoing visits
-- 3. CF-2024-045 (Gagnon) - Suspended state, non-compliance issues
-- 4. CF-2024-023 (Lavoie) - Closed state, successful completion
-- 5. CF-2025-003 (Roy) - Opening state, bilingual family

-- Each case file includes:
-- - Realistic Quebec family names and scenarios
-- - Appropriate metadata in French
-- - Complete contact relationships (parents, children)
-- - Detailed audit trail showing state transitions
-- - Quebec-specific context and terminology
