-- Script d'installation des données de test pour les dossiers de cas québécois
-- Exécute les seeds dans le bon ordre pour respecter l'intégrité référentielle

-- Vérification des prérequis
DO $$
BEGIN
    -- Vérifier que les tables existent
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'case_files') THEN
        RAISE EXCEPTION 'Table case_files n''existe pas. Exécuter d''abord les migrations.';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'contacts') THEN
        RAISE EXCEPTION 'Table contacts n''existe pas. Exécuter d''abord les migrations.';
    END IF;
    
    -- Vérifier qu'il y a au moins une organisation
    IF NOT EXISTS (SELECT 1 FROM organizations) THEN
        RAISE EXCEPTION 'Aucune organisation trouvée. Créer d''abord une organisation.';
    END IF;
    
    -- Vérifier qu'il y a au moins un utilisateur
    IF NOT EXISTS (SELECT 1 FROM auth.users) THEN
        RAISE EXCEPTION 'Aucun utilisateur trouvé. Créer d''abord des utilisateurs.';
    END IF;
    
    RAISE NOTICE 'Prérequis vérifiés avec succès.';
END $$;

-- Nettoyer les données existantes (optionnel - décommenter si nécessaire)
-- DELETE FROM case_file_history WHERE case_file_id LIKE 'cf%';
-- DELETE FROM case_file_contacts WHERE case_file_id LIKE 'cf%';
-- DELETE FROM case_files WHERE id LIKE 'cf%';
-- DELETE FROM contacts WHERE first_name IN ('Marie', 'Pierre', 'Sophie', 'Lucas', 'Julie', 'Marc', 'Sylvie', 'Robert', 'Nathalie', 'Daniel', 'Catherine', 'Michael');

-- Étape 1: Installer les contacts des familles québécoises
\echo 'Installation des contacts des familles québécoises...'
\i contacts_quebec_families_seed.sql

-- Étape 2: Installer les dossiers de cas québécois
\echo 'Installation des dossiers de cas québécois...'
\i case_files_quebec_seed.sql

-- Vérification des données installées
\echo 'Vérification des données installées...'

SELECT 
    'Dossiers de cas créés' as type,
    COUNT(*) as nombre
FROM case_files 
WHERE case_number LIKE 'CF-2024-%' OR case_number LIKE 'CF-2025-%'

UNION ALL

SELECT 
    'Contacts familiaux créés' as type,
    COUNT(*) as nombre
FROM contacts 
WHERE last_name IN ('Tremblay', 'Bouchard', 'Gagnon', 'Lavoie', 'Roy')

UNION ALL

SELECT 
    'Liens famille-dossier créés' as type,
    COUNT(*) as nombre
FROM case_file_contacts 
WHERE case_file_id LIKE 'cf%'

UNION ALL

SELECT 
    'Entrées d''historique créées' as type,
    COUNT(*) as nombre
FROM case_file_history 
WHERE case_file_id LIKE 'cf%';

-- Résumé par état des dossiers
\echo 'Résumé des dossiers par état:'
SELECT 
    status as etat,
    COUNT(*) as nombre_dossiers,
    STRING_AGG(metadata->>'famille', ', ') as familles
FROM case_files 
WHERE case_number LIKE 'CF-2024-%' OR case_number LIKE 'CF-2025-%'
GROUP BY status
ORDER BY status;

\echo 'Installation terminée avec succès!'
\echo 'Consultez le fichier README_case_files_quebec.md pour plus d''informations.'
