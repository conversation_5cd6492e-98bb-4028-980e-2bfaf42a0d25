-- Sample contact data for the RQRSDA Montreal organization
-- Organization ID: ********-0000-0000-0000-********0001

-- Family contacts
INSERT INTO contacts (id, organization_id, name, type, status, email, phone, address, notes)
VALUES
  ('********-0000-0000-0000-********0101', '********-0000-0000-0000-********0001', 
   '<PERSON>', 'family', 'active',
   '{"personal": "<EMAIL>"}',
   '{"mobile": "************", "home": "************"}',
   '123 Rue Saint-Denis, Montreal, QC H2X 3L9',
   'Father of two children, primary contact for family visits'),

  ('********-0000-0000-0000-********0102', '********-0000-0000-0000-********0001', 
   '<PERSON>', 'family', 'active',
   '{"personal": "<EMAIL>", "work": "<EMAIL>"}',
   '{"mobile": "************"}',
   '456 Boulevard Saint-Laurent, Montreal, QC H2Y 2Y5',
   'Mother of two children, secondary contact'),

  ('********-0000-0000-0000-********0103', '********-0000-0000-0000-********0001', 
   'Sophie Lavoie', 'family', 'active',
   '{"personal": "<EMAIL>"}',
   '{"mobile": "************", "home": "************"}',
   '789 Avenue du Mont-Royal, Montreal, QC H2J 1X1',
   'Mother of one child, primary caregiver'),

  ('********-0000-0000-0000-********0104', '********-0000-0000-0000-********0001', 
   'Robert Gagnon', 'family', 'inactive',
   '{"personal": "<EMAIL>"}',
   '{"mobile": "************"}',
   '321 Rue Sherbrooke, Montreal, QC H2L 1E3',
   'Father, currently not participating in visits'),

  ('********-0000-0000-0000-********0105', '********-0000-0000-0000-********0001', 
   'Isabelle Côté', 'family', 'active',
   '{"personal": "<EMAIL>", "work": "<EMAIL>"}',
   '{"mobile": "************", "home": "************"}',
   '654 Rue Sainte-Catherine, Montreal, QC H3B 1A7',
   'Mother of three children, requires interpreter for visits');

-- Professional contacts
INSERT INTO contacts (id, organization_id, name, type, status, email, phone, address, notes)
VALUES
  ('********-0000-0000-0000-********0201', '********-0000-0000-0000-********0001', 
   'Dr. Michel Bergeron', 'professional', 'active',
   '{"work": "<EMAIL>", "personal": "<EMAIL>"}',
   '{"work": "************", "mobile": "************"}',
   'Montreal General Hospital, 1650 Cedar Ave, Montreal, QC H3G 1A4',
   'Child psychologist, available for consultations on Tuesdays and Thursdays'),

  ('********-0000-0000-0000-********0202', '********-0000-0000-0000-********0001', 
   'Me. Julie Lemieux', 'professional', 'active',
   '{"work": "<EMAIL>"}',
   '{"work": "************", "mobile": "************"}',
   '1000 Place Jean-Paul-Riopelle, Montreal, QC H2Z 1H2',
   'Family lawyer, represents multiple clients'),

  ('********-0000-0000-0000-********0203', '********-0000-0000-0000-********0001', 
   'Sarah Thompson', 'professional', 'active',
   '{"work": "<EMAIL>"}',
   '{"work": "************", "mobile": "************"}',
   '2100 Boulevard Edouard-Montpetit, Montreal, QC H3T 1J4',
   'Social worker from Youth Protection Services'),

  ('********-0000-0000-0000-********0204', '********-0000-0000-0000-********0001', 
   'Pierre Morin', 'professional', 'inactive',
   '{"work": "<EMAIL>", "personal": "<EMAIL>"}',
   '{"work": "************", "mobile": "************"}',
   '3600 Rue University, Montreal, QC H3A 2B2',
   'Family counselor, currently on leave'),

  ('********-0000-0000-0000-********0205', '********-0000-0000-0000-********0001', 
   'Dr. Fatima Hassan', 'professional', 'active',
   '{"work": "<EMAIL>"}',
   '{"work": "************", "mobile": "************"}',
   'Montreal Childrens Hospital, 1001 Decarie Blvd, Montreal, QC H4A 3J1',
   'Pediatrician, available for medical consultations');

-- Organization contacts
INSERT INTO contacts (id, organization_id, name, type, status, email, phone, address, notes)
VALUES
  ('********-0000-0000-0000-********0301', '********-0000-0000-0000-********0001', 
   'Centre Jeunesse de Montréal', 'organization', 'active',
   '{"info": "<EMAIL>", "referrals": "<EMAIL>"}',
   '{"main": "************", "fax": "************"}',
   '1001 Boulevard de Maisonneuve Est, Montreal, QC H2L 4P9',
   'Youth protection services, primary referral partner'),

  ('********-0000-0000-0000-********0302', '********-0000-0000-0000-********0001', 
   'CLSC Plateau-Mont-Royal', 'organization', 'active',
   '{"info": "<EMAIL>"}',
   '{"main": "************", "fax": "************"}',
   '4625 Avenue de Lorimier, Montreal, QC H2H 2B4',
   'Local community service center, provides family support services'),

  ('********-0000-0000-0000-********0303', '********-0000-0000-0000-********0001', 
   'Maison des Familles', 'organization', 'active',
   '{"info": "<EMAIL>"}',
   '{"main": "************", "mobile": "************"}',
   '3245 Avenue Émile-Duployé, Montreal, QC H1Z 1G9',
   'Family resource center, offers parenting workshops'),

  ('********-0000-0000-0000-********0304', '********-0000-0000-0000-********0001', 
   'École Primaire Saint-Jean', 'organization', 'active',
   '{"info": "<EMAIL>"}',
   '{"main": "************", "fax": "************"}',
   '4200 Rue Drolet, Montreal, QC H2W 2L5',
   'Elementary school, several children attend this school'),

  ('********-0000-0000-0000-********0305', '********-0000-0000-0000-********0001', 
   'Tribunal de la Famille', 'organization', 'active',
   '{"info": "<EMAIL>"}',
   '{"main": "************", "fax": "************"}',
   '1 Rue Notre-Dame Est, Montreal, QC H2Y 1B6',
   'Family court, handles custody and visitation orders');

-- Other contacts
INSERT INTO contacts (id, organization_id, name, type, status, email, phone, address, notes)
VALUES
  ('********-0000-0000-0000-********0401', '********-0000-0000-0000-********0001', 
   'Interprète Services', 'other', 'active',
   '{"info": "<EMAIL>"}',
   '{"main": "************", "mobile": "************"}',
   '2100 Rue Sainte-Catherine Ouest, Montreal, QC H3H 1M6',
   'Interpretation services for multiple languages'),

  ('********-0000-0000-0000-********0402', '********-0000-0000-0000-********0001', 
   'Transport Adapté Montréal', 'other', 'active',
   '{"info": "<EMAIL>"}',
   '{"main": "************", "dispatch": "************"}',
   '3600 Boulevard Crémazie Est, Montreal, QC H2M 1L9',
   'Adapted transport services for clients with mobility issues'),

  ('********-0000-0000-0000-********0403', '********-0000-0000-0000-********0001', 
   'Sécurité Vigilance', 'other', 'active',
   '{"info": "<EMAIL>"}',
   '{"main": "************", "emergency": "************"}',
   '1234 Rue Ontario Est, Montreal, QC H2L 1R7',
   'Security services for high-risk visits'),

  ('********-0000-0000-0000-********0404', '********-0000-0000-0000-********0001', 
   'Garderie Les Petits Pas', 'other', 'active',
   '{"info": "<EMAIL>"}',
   '{"main": "************"}',
   '5678 Avenue du Parc, Montreal, QC H2V 4H4',
   'Daycare center, provides childcare during parent meetings'),

  ('********-0000-0000-0000-********0405', '********-0000-0000-0000-********0001', 
   'Banque Alimentaire Montréal', 'other', 'active',
   '{"info": "<EMAIL>"}',
   '{"main": "************"}',
   '9012 Rue Saint-Urbain, Montreal, QC H2N 1Y6',
   'Food bank, resource for families in need');
