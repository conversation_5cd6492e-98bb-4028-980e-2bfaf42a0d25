BEGIN;

-- Load the pgtap extension
SELECT plan(34);

-- Test tables exist
SELECT has_table('public', 'organizations', 'organizations table should exist');
SELECT has_table('public', 'locations', 'locations table should exist');
SELECT has_table('public', 'rooms', 'rooms table should exist');
SELECT has_table('public', 'business_hours', 'business_hours table should exist');

-- Test columns exist in organizations table
SELECT has_column('public', 'organizations', 'id', 'organizations table should have id column');
SELECT has_column('public', 'organizations', 'name', 'organizations table should have name column');
SELECT has_column('public', 'organizations', 'status', 'organizations table should have status column');
SELECT has_column('public', 'organizations', 'phones', 'organizations table should have phones column');
SELECT has_column('public', 'organizations', 'emails', 'organizations table should have emails column');
SELECT has_column('public', 'organizations', 'address', 'organizations table should have address column');
SELECT has_column('public', 'organizations', 'settings', 'organizations table should have settings column');

-- Test columns exist in locations table
SELECT has_column('public', 'locations', 'id', 'locations table should have id column');
SELECT has_column('public', 'locations', 'organization_id', 'locations table should have organization_id column');
SELECT has_column('public', 'locations', 'name', 'locations table should have name column');
SELECT has_column('public', 'locations', 'status', 'locations table should have status column');
SELECT has_column('public', 'locations', 'phones', 'locations table should have phones column');
SELECT has_column('public', 'locations', 'emails', 'locations table should have emails column');
SELECT has_column('public', 'locations', 'address', 'locations table should have address column');

-- Test columns exist in rooms table
SELECT has_column('public', 'rooms', 'id', 'rooms table should have id column');
SELECT has_column('public', 'rooms', 'location_id', 'rooms table should have location_id column');
SELECT has_column('public', 'rooms', 'organization_id', 'rooms table should have organization_id column');
SELECT has_column('public', 'rooms', 'name', 'rooms table should have name column');
SELECT has_column('public', 'rooms', 'status', 'rooms table should have status column');

-- Test columns exist in business_hours table
SELECT has_column('public', 'business_hours', 'id', 'business_hours table should have id column');
SELECT has_column('public', 'business_hours', 'organization_id', 'business_hours table should have organization_id column');
SELECT has_column('public', 'business_hours', 'location_id', 'business_hours table should have location_id column');
SELECT has_column('public', 'business_hours', 'day_of_week', 'business_hours table should have day_of_week column');
SELECT has_column('public', 'business_hours', 'start_time', 'business_hours table should have start_time column');
SELECT has_column('public', 'business_hours', 'end_time', 'business_hours table should have end_time column');
SELECT has_column('public', 'business_hours', 'is_closed', 'business_hours table should have is_closed column');

-- Test constraints
SELECT col_is_pk('public', 'organizations', 'id', 'organizations.id should be a primary key');
SELECT col_is_pk('public', 'locations', 'id', 'locations.id should be a primary key');
SELECT col_is_pk('public', 'rooms', 'id', 'rooms.id should be a primary key');
SELECT col_is_pk('public', 'business_hours', 'id', 'business_hours.id should be a primary key');

-- Finish the tests
SELECT * FROM finish();

ROLLBACK;
