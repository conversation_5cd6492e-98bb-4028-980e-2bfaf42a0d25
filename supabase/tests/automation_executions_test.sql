BEGIN;

-- Load the pgTAP extension
SELECT plan(17);

-- Test if the public schema exists
SELECT has_schema('public', 'Public schema should exist');

-- Test if the executions table exists in the public schema
SELECT has_table('public', 'executions', 'Executions table should exist');

-- Test if the table has the correct columns
SELECT has_column('public', 'executions', 'id', 'Table should have id column');
SELECT has_column('public', 'executions', 'user_id', 'Table should have user_id column');
SELECT has_column('public', 'executions', 'organization_id', 'Table should have organization_id column');
SELECT has_column('public', 'executions', 'workflow_type', 'Table should have workflow_type column');
SELECT has_column('public', 'executions', 'status', 'Table should have status column');
SELECT has_column('public', 'executions', 'data', 'Table should have data column');
SELECT has_column('public', 'executions', 'result', 'Table should have result column');
SELECT has_column('public', 'executions', 'error', 'Table should have error column');
SELECT has_column('public', 'executions', 'created_at', 'Table should have created_at column');
SELECT has_column('public', 'executions', 'updated_at', 'Table should have updated_at column');

-- Test if the columns have the correct types
SELECT col_type_is('public', 'executions', 'id', 'uuid', 'id column should be of type uuid');
SELECT col_type_is('public', 'executions', 'data', 'jsonb', 'data column should be of type jsonb');
SELECT col_type_is('public', 'executions', 'result', 'jsonb', 'result column should be of type jsonb');
SELECT col_type_is('public', 'executions', 'error', 'text', 'error column should be of type text');
SELECT col_type_is('public', 'executions', 'status', 'text', 'status column should be of type text');

-- Finish the tests and clean up
SELECT * FROM finish();
ROLLBACK;
BEGIN;

-- Load the pgTAP extension
SELECT plan(19);

-- Test if the automation schema exists
SELECT has_schema('automation', 'Automation schema should exist');

-- Test if the executions table exists in the automation schema
SELECT has_table('automation', 'executions', 'Automation executions table should exist');

-- Test if the table has the correct columns
SELECT has_column('automation', 'executions', 'id', 'Table should have id column');
SELECT has_column('automation', 'executions', 'user_id', 'Table should have user_id column');
SELECT has_column('automation', 'executions', 'organization_id', 'Table should have organization_id column');
SELECT has_column('automation', 'executions', 'workflow_type', 'Table should have workflow_type column');
SELECT has_column('automation', 'executions', 'status', 'Table should have status column');
SELECT has_column('automation', 'executions', 'data', 'Table should have data column');
SELECT has_column('automation', 'executions', 'result', 'Table should have result column');
SELECT has_column('automation', 'executions', 'error', 'Table should have error column');
SELECT has_column('automation', 'executions', 'started_at', 'Table should have started_at column');
SELECT has_column('automation', 'executions', 'completed_at', 'Table should have completed_at column');
SELECT has_column('automation', 'executions', 'created_at', 'Table should have created_at column');
SELECT has_column('automation', 'executions', 'updated_at', 'Table should have updated_at column');

-- Test if the columns have the correct types
SELECT col_type_is('automation', 'executions', 'id', 'uuid', 'id column should be of type uuid');
SELECT col_type_is('automation', 'executions', 'data', 'jsonb', 'data column should be of type jsonb');
SELECT col_type_is('automation', 'executions', 'result', 'jsonb', 'result column should be of type jsonb');
SELECT col_type_is('automation', 'executions', 'error', 'jsonb', 'error column should be of type jsonb');
SELECT col_type_is('automation', 'executions', 'status', 'text', 'status column should be of type text');

-- Finish the tests and clean up
SELECT * FROM finish();
ROLLBACK;
