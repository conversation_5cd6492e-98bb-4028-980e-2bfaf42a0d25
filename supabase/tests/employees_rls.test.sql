BEGIN;

-- Load the pgtap extension
SELECT plan(11);

-- Set up test data
INSERT INTO organizations (id, name, status)
VALUES
  ('11111111-1111-1111-1111-111111111111', 'Test Organization 1', 'active'),
  ('*************-2222-2222-************', 'Test Organization 2', 'active');

-- Create test employees
INSERT INTO employees (id, organization_id, first_name, last_name, employment_status, job_title, department)
VALUES
  ('*************-3333-3333-************', '11111111-1111-1111-1111-111111111111', '<PERSON>', 'Doe', 'active', 'Manager', 'HR'),
  ('*************-4444-4444-************', '*************-2222-2222-************', 'Jane', 'Smith', 'active', 'Developer', 'IT');

-- Create test users in auth.users table
INSERT INTO auth.users (id, email)
VALUES
  ('*************-9999-9999-************', '<EMAIL>'),
  ('aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee', '<EMAIL>');

-- Test 1: Anonymous users cannot access employees
SET LOCAL ROLE anon;
SELECT is_empty(
  'SELECT * FROM employees',
  'Anonymous users cannot access employees'
);

-- Test 2: User from Organization 1 can only see employees from Organization 1
SET LOCAL ROLE authenticated;
SET LOCAL "request.jwt.claim.sub" TO '*************-9999-9999-************';
-- Simulate user_roles entry for this user
INSERT INTO user_roles (id, user_id, role, organization_id)
VALUES ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '*************-9999-9999-************', 'Director', '11111111-1111-1111-1111-111111111111');

SELECT results_eq(
  'SELECT id FROM employees',
  ARRAY['*************-3333-3333-************']::UUID[],
  'User from Organization 1 can only see employees from Organization 1'
);

-- Test 3: User from Organization 1 cannot see employees from Organization 2
SELECT is_empty(
  'SELECT * FROM employees WHERE organization_id = ''*************-2222-2222-************''',
  'User from Organization 1 cannot see employees from Organization 2'
);

-- Test 4: User from Organization 1 can update employees from Organization 1
SELECT lives_ok(
  $$
    UPDATE employees
    SET job_title = 'Senior Manager'
    WHERE id = '*************-3333-3333-************'
    RETURNING job_title
  $$,
  'User from Organization 1 can update employees from Organization 1'
);

-- Test 5: User from Organization 1 cannot update employees from Organization 2
SELECT is_empty(
  $$
    UPDATE employees
    SET job_title = 'Should Not Update'
    WHERE id = '*************-4444-4444-************'
    RETURNING job_title
  $$,
  'User from Organization 1 cannot update employees from Organization 2'
);

-- Test 6: User from Organization 1 can insert employees for Organization 1
SELECT lives_ok(
  $$
    INSERT INTO employees (organization_id, first_name, last_name, employment_status, job_title, department)
    VALUES ('11111111-1111-1111-1111-111111111111', 'New', 'Employee', 'active', 'Assistant', 'HR')
    RETURNING first_name
  $$,
  'User from Organization 1 can insert employees for Organization 1'
);

-- Test 7: User from Organization 1 cannot insert employees for Organization 2
SELECT throws_ok(
  $$
    INSERT INTO employees (organization_id, first_name, last_name, employment_status, job_title, department)
    VALUES ('*************-2222-2222-************', 'Should', 'NotInsert', 'active', 'Assistant', 'HR')
    RETURNING first_name
  $$,
  'new row violates row-level security policy for table "employees"',
  'User from Organization 1 cannot insert employees for Organization 2'
);

-- Test 8: User from Organization 1 can delete employees from Organization 1
SELECT lives_ok(
  $$
    DELETE FROM employees
    WHERE id = '*************-3333-3333-************'
    RETURNING id
  $$,
  'User from Organization 1 can delete employees from Organization 1'
);

-- Test 9: User from Organization 1 cannot delete employees from Organization 2
SELECT is_empty(
  $$
    DELETE FROM employees
    WHERE id = '*************-4444-4444-************'
    RETURNING id
  $$,
  'User from Organization 1 cannot delete employees from Organization 2'
);

-- Test 10: Switch to user from Organization 2
DELETE FROM user_roles WHERE user_id = '*************-9999-9999-************';
SET LOCAL "request.jwt.claim.sub" TO 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee';
-- Simulate user_roles entry for this user
INSERT INTO user_roles (id, user_id, role, organization_id)
VALUES ('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee', 'Director', '*************-2222-2222-************');

-- Test 11: User from Organization 2 can only see employees from Organization 2
SELECT results_eq(
  'SELECT id FROM employees',
  ARRAY['*************-4444-4444-************']::UUID[],
  'User from Organization 2 can only see employees from Organization 2'
);

-- Test 12: User from Organization 2 cannot see employees from Organization 1
SELECT is_empty(
  'SELECT * FROM employees WHERE organization_id = ''11111111-1111-1111-1111-111111111111''',
  'User from Organization 2 cannot see employees from Organization 1'
);

-- Finish the tests
SELECT * FROM finish();

ROLLBACK;
