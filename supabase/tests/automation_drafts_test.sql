BEGIN;

-- Load the pgTAP extension
SELECT plan(14);

-- Test if the automation schema exists
SELECT has_schema('automation', 'Automation schema should exist');

-- Test if the drafts table exists in the automation schema
SELECT has_table('automation', 'drafts', 'Automation drafts table should exist');

-- Test if the table has the correct columns
SELECT has_column('automation', 'drafts', 'id', 'Table should have id column');
SELECT has_column('automation', 'drafts', 'user_id', 'Table should have user_id column');
SELECT has_column('automation', 'drafts', 'organization_id', 'Table should have organization_id column');
SELECT has_column('automation', 'drafts', 'workflow_type', 'Table should have workflow_type column');
SELECT has_column('automation', 'drafts', 'current_step', 'Table should have current_step column');
SELECT has_column('automation', 'drafts', 'data', 'Table should have data column');
SELECT has_column('automation', 'drafts', 'created_at', 'Table should have created_at column');
SELECT has_column('automation', 'drafts', 'updated_at', 'Table should have updated_at column');

-- Test if the columns have the correct types
SELECT col_type_is('automation', 'drafts', 'id', 'uuid', 'id column should be of type uuid');
SELECT col_type_is('automation', 'drafts', 'data', 'jsonb', 'data column should be of type jsonb');
SELECT col_type_is('automation', 'drafts', 'workflow_type', 'text', 'workflow_type column should be of type text');
SELECT col_type_is('automation', 'drafts', 'current_step', 'text', 'current_step column should be of type text');

-- Finish the tests and clean up
SELECT * FROM finish();
ROLLBACK;
