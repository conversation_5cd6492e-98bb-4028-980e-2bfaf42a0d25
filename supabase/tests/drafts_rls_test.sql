BEGIN;

-- Load the pgTAP extension
SELECT plan(8);

-- Temporarily disable <PERSON><PERSON> for setup
SELECT disable_rls_for_tests();

-- Create test users and organizations
INSERT INTO auth.users (id, email) VALUES
  ('11111111-1111-1111-1111-111111111111', '<EMAIL>'),
  ('22222222-2222-2222-2222-222222222222', '<EMAIL>'),
  ('33333333-3333-3333-3333-333333333333', '<EMAIL>');

INSERT INTO organizations (id, name) VALUES
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'Organization A'),
  ('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'Organization B');

INSERT INTO user_roles (user_id, organization_id, role) VALUES
  ('11111111-1111-1111-1111-111111111111', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'Director'),
  ('22222222-2222-2222-2222-222222222222', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'Coordinator'),
  ('33333333-3333-3333-3333-333333333333', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'SystemAdmin');

-- Insert test drafts
INSERT INTO drafts (id, user_id, organization_id, workflow_type, current_step, data) VALUES
  ('dddddddd-dddd-dddd-dddd-dddddddddddd', '11111111-1111-1111-1111-111111111111', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'employee_creation', 'step1', '{"firstName": "John", "lastName": "Doe"}'::jsonb),
  ('eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', '22222222-2222-2222-2222-222222222222', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'employee_creation', 'step1', '{"firstName": "Jane", "lastName": "Smith"}'::jsonb);

-- Re-enable RLS after setup
SELECT enable_rls_for_tests();



-- Test RLS policies

-- Test as user1
SET LOCAL ROLE postgres;
SET LOCAL "request.jwt.claim.sub" TO '11111111-1111-1111-1111-111111111111';

-- User1 should see their own draft in org A
SELECT results_eq(
  'SELECT count(*) FROM drafts WHERE organization_id = ''aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa''',
  ARRAY[1::bigint],
  'User1 should see their own draft in org A'
);

-- User1 should not see drafts in org B
SELECT results_eq(
  'SELECT count(*) FROM drafts WHERE organization_id = ''bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb''',
  ARRAY[0::bigint],
  'User1 should not see drafts in org B'
);

-- Test as user2
SET LOCAL "request.jwt.claim.sub" TO '22222222-2222-2222-2222-222222222222';

-- User2 should see their own draft in org B
SELECT results_eq(
  'SELECT count(*) FROM drafts WHERE organization_id = ''bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb''',
  ARRAY[1::bigint],
  'User2 should see their own draft in org B'
);

-- User2 should not see drafts in org A
SELECT results_eq(
  'SELECT count(*) FROM drafts WHERE organization_id = ''aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa''',
  ARRAY[0::bigint],
  'User2 should not see drafts in org A'
);

-- Test as system admin
SET LOCAL "request.jwt.claim.sub" TO '33333333-3333-3333-3333-333333333333';

-- System admin should see all drafts
SELECT results_eq(
  'SELECT count(*) FROM drafts',
  ARRAY[2::bigint],
  'System admin should see all drafts'
);

-- Test insert policy
SET LOCAL "request.jwt.claim.sub" TO '11111111-1111-1111-1111-111111111111';

-- User1 should be able to insert their own draft in org A
SELECT lives_ok(
  $$INSERT INTO drafts (user_id, organization_id, workflow_type, current_step, data)
    VALUES ('11111111-1111-1111-1111-111111111111', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'contact_creation', 'step1', '{"firstName": "Test"}'::jsonb)$$,
  'User1 should be able to insert their own draft in org A'
);

-- User1 should not be able to insert a draft for user2
SELECT throws_ok(
  $$INSERT INTO drafts (user_id, organization_id, workflow_type, current_step, data)
    VALUES ('22222222-2222-2222-2222-222222222222', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'contact_creation', 'step1', '{"firstName": "Test"}'::jsonb)$$,
  'new row violates row-level security policy',
  'User1 should not be able to insert a draft for user2'
);

-- User1 should not be able to insert a draft in org B
SELECT throws_ok(
  $$INSERT INTO drafts (user_id, organization_id, workflow_type, current_step, data)
    VALUES ('11111111-1111-1111-1111-111111111111', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'contact_creation', 'step1', '{"firstName": "Test"}'::jsonb)$$,
  'new row violates row-level security policy',
  'User1 should not be able to insert a draft in org B'
);

-- Finish the tests and clean up
SELECT * FROM finish();
ROLLBACK;
