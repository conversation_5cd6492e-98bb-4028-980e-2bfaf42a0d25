BEGIN;

-- Load the pgtap extension
SELECT plan(31);

-- Test tables exist
SELECT has_table('public', 'contacts', 'contacts table should exist');
SELECT has_table('public', 'contact_relationships', 'contact_relationships table should exist');
SELECT has_table('public', 'contact_history', 'contact_history table should exist');

-- Test columns exist in contacts table
SELECT has_column('public', 'contacts', 'id', 'contacts table should have id column');
SELECT has_column('public', 'contacts', 'organization_id', 'contacts table should have organization_id column');
SELECT has_column('public', 'contacts', 'name', 'contacts table should have name column');
SELECT has_column('public', 'contacts', 'email', 'contacts table should have email column');
SELECT has_column('public', 'contacts', 'phone', 'contacts table should have phone column');
SELECT has_column('public', 'contacts', 'address', 'contacts table should have address column');
SELECT has_column('public', 'contacts', 'status', 'contacts table should have status column');
SELECT has_column('public', 'contacts', 'type', 'contacts table should have type column');
SELECT has_column('public', 'contacts', 'notes', 'contacts table should have notes column');

-- Test columns exist in contact_relationships table
SELECT has_column('public', 'contact_relationships', 'id', 'contact_relationships table should have id column');
SELECT has_column('public', 'contact_relationships', 'organization_id', 'contact_relationships table should have organization_id column');
SELECT has_column('public', 'contact_relationships', 'subject_contact_id', 'contact_relationships table should have subject_contact_id column');
SELECT has_column('public', 'contact_relationships', 'related_contact_id', 'contact_relationships table should have related_contact_id column');
SELECT has_column('public', 'contact_relationships', 'relationship', 'contact_relationships table should have relationship column');
SELECT has_column('public', 'contact_relationships', 'status', 'contact_relationships table should have status column');

-- Test columns exist in contact_history table
SELECT has_column('public', 'contact_history', 'id', 'contact_history table should have id column');
SELECT has_column('public', 'contact_history', 'contact_id', 'contact_history table should have contact_id column');
SELECT has_column('public', 'contact_history', 'user_id', 'contact_history table should have user_id column');
SELECT has_column('public', 'contact_history', 'action', 'contact_history table should have action column');
SELECT has_column('public', 'contact_history', 'changes', 'contact_history table should have changes column');

-- Test constraints
SELECT col_is_pk('public', 'contacts', 'id', 'contacts.id should be a primary key');
SELECT col_is_pk('public', 'contact_relationships', 'id', 'contact_relationships.id should be a primary key');
SELECT col_is_pk('public', 'contact_history', 'id', 'contact_history.id should be a primary key');

-- Test foreign keys
SELECT col_is_fk('public', 'contacts', 'organization_id', 'contacts.organization_id should be a foreign key');
SELECT col_is_fk('public', 'contact_relationships', 'organization_id', 'contact_relationships.organization_id should be a foreign key');
SELECT col_is_fk('public', 'contact_relationships', 'subject_contact_id', 'contact_relationships.subject_contact_id should be a foreign key');
SELECT col_is_fk('public', 'contact_relationships', 'related_contact_id', 'contact_relationships.related_contact_id should be a foreign key');
SELECT col_is_fk('public', 'contact_history', 'contact_id', 'contact_history.contact_id should be a foreign key');

-- Finish the tests
SELECT * FROM finish();

ROLLBACK;
