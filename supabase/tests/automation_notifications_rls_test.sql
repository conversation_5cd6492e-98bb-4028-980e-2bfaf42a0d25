BEGIN;

-- Load the pgTAP extension
SELECT plan(10);

-- Temporarily disable <PERSON><PERSON> for setup
SELECT disable_rls_for_tests();

-- Create test users and organizations
INSERT INTO auth.users (id, email) VALUES
  ('11111111-1111-1111-1111-111111111111', '<EMAIL>'),
  ('22222222-2222-2222-2222-222222222222', '<EMAIL>'),
  ('33333333-3333-3333-3333-333333333333', '<EMAIL>'),
  ('44444444-4444-4444-4444-444444444444', '<EMAIL>');

INSERT INTO organizations (id, name) VALUES
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'Organization A'),
  ('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'Organization B');

INSERT INTO user_roles (user_id, organization_id, role) VALUES
  ('11111111-1111-1111-1111-111111111111', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'SocialWorker'),
  ('22222222-2222-2222-2222-222222222222', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'Coordinator'),
  ('33333333-3333-3333-3333-333333333333', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'SystemAdmin'),
  ('44444444-4444-4444-4444-444444444444', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'Director');

-- Insert test notifications
INSERT INTO notifications (id, user_id, organization_id, type, title, message, data, read) VALUES
  ('dddddddd-dddd-dddd-dddd-dddddddddddd', '11111111-1111-1111-1111-111111111111', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'workflow_completed', 'Workflow Completed', 'Employee creation workflow completed successfully', '{"workflowId": "12345"}'::jsonb, false),
  ('eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', '22222222-2222-2222-2222-222222222222', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'workflow_started', 'Workflow Started', 'Employee creation workflow started', '{"workflowId": "67890"}'::jsonb, false),
  ('ffffffff-ffff-ffff-ffff-ffffffffffff', '44444444-4444-4444-4444-444444444444', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'workflow_error', 'Workflow Error', 'Error in employee creation workflow', '{"workflowId": "54321", "error": "Invalid data"}'::jsonb, true);

-- Re-enable RLS after setup
SELECT enable_rls_for_tests();

-- Test RLS policies

-- Test as user1 (SocialWorker)
SET LOCAL ROLE postgres;
SET LOCAL "request.jwt.claim.sub" TO '11111111-1111-1111-1111-111111111111';

-- User1 should see their own notifications in org A
SELECT results_eq(
  'SELECT count(*) FROM notifications WHERE user_id = ''11111111-1111-1111-1111-111111111111''',
  ARRAY[1::bigint],
  'User1 should see their own notifications in org A'
);

-- User1 should not see notifications in org B
SELECT results_eq(
  'SELECT count(*) FROM notifications WHERE organization_id = ''bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb''',
  ARRAY[0::bigint],
  'User1 should not see notifications in org B'
);

-- Test as Director
SET LOCAL "request.jwt.claim.sub" TO '44444444-4444-4444-4444-444444444444';

-- Director should see all notifications in their organization
SELECT results_eq(
  'SELECT count(*) FROM notifications WHERE organization_id = ''aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa''',
  ARRAY[2::bigint],
  'Director should see all notifications in their organization'
);

-- Director should not see notifications in other organizations
SELECT results_eq(
  'SELECT count(*) FROM notifications WHERE organization_id = ''bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb''',
  ARRAY[0::bigint],
  'Director should not see notifications in other organizations'
);

-- Test as system admin
SET LOCAL "request.jwt.claim.sub" TO '33333333-3333-3333-3333-333333333333';

-- System admin should see all notifications
SELECT results_eq(
  'SELECT count(*) FROM notifications',
  ARRAY[3::bigint],
  'System admin should see all notifications'
);

-- Test insert policy
SET LOCAL "request.jwt.claim.sub" TO '11111111-1111-1111-1111-111111111111';

-- User1 should be able to insert their own notification in org A
SELECT lives_ok(
  $$INSERT INTO notifications (user_id, organization_id, type, title, message, data)
    VALUES ('11111111-1111-1111-1111-111111111111', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'workflow_started', 'Test Notification', 'This is a test notification', '{"test": true}'::jsonb)$$,
  'User1 should be able to insert their own notification in org A'
);

-- User1 should not be able to insert a notification for user2
SELECT throws_ok(
  $$INSERT INTO notifications (user_id, organization_id, type, title, message, data)
    VALUES ('22222222-2222-2222-2222-222222222222', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'workflow_started', 'Test Notification', 'This is a test notification', '{"test": true}'::jsonb)$$,
  'new row violates row-level security policy',
  'User1 should not be able to insert a notification for user2'
);

-- User1 should not be able to insert a notification in org B
SELECT throws_ok(
  $$INSERT INTO notifications (user_id, organization_id, type, title, message, data)
    VALUES ('11111111-1111-1111-1111-111111111111', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'workflow_started', 'Test Notification', 'This is a test notification', '{"test": true}'::jsonb)$$,
  'new row violates row-level security policy',
  'User1 should not be able to insert a notification in org B'
);

-- Test update policy
-- User1 should be able to update their own notification (mark as read)
SELECT lives_ok(
  $$UPDATE notifications
    SET read = true
    WHERE id = 'dddddddd-dddd-dddd-dddd-dddddddddddd'$$,
  'User1 should be able to update their own notification'
);

-- Finish the tests and clean up
SELECT * FROM finish();
ROLLBACK;
