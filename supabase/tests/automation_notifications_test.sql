BEGIN;

-- Load the pgTAP extension
SELECT plan(17);

-- Test if the public schema exists
SELECT has_schema('public', 'Public schema should exist');

-- Test if the notifications table exists in the public schema
SELECT has_table('public', 'notifications', 'Notifications table should exist');

-- Test if the table has the correct columns
SELECT has_column('public', 'notifications', 'id', 'Table should have id column');
SELECT has_column('public', 'notifications', 'user_id', 'Table should have user_id column');
SELECT has_column('public', 'notifications', 'organization_id', 'Table should have organization_id column');
SELECT has_column('public', 'notifications', 'type', 'Table should have type column');
SELECT has_column('public', 'notifications', 'title', 'Table should have title column');
SELECT has_column('public', 'notifications', 'message', 'Table should have message column');
SELECT has_column('public', 'notifications', 'data', 'Table should have data column');
SELECT has_column('public', 'notifications', 'read', 'Table should have read column');
SELECT has_column('public', 'notifications', 'created_at', 'Table should have created_at column');
SELECT has_column('public', 'notifications', 'updated_at', 'Table should have updated_at column');

-- Test if the columns have the correct types
SELECT col_type_is('public', 'notifications', 'id', 'uuid', 'id column should be of type uuid');
SELECT col_type_is('public', 'notifications', 'data', 'jsonb', 'data column should be of type jsonb');
SELECT col_type_is('public', 'notifications', 'type', 'text', 'type column should be of type text');
SELECT col_type_is('public', 'notifications', 'read', 'boolean', 'read column should be of type boolean');

-- Finish the tests and clean up
SELECT * FROM finish();
ROLLBACK;
