BEGIN;

-- Deallocate any prepared statements to reset the session
DEALLOCATE ALL;

-- Format the output for TAP testing
\pset format unaligned
\pset tuples_only true
\pset pager off

-- Revert all changes on failure
\set ON_ERROR_ROLLBACK 1
\set ON_ERROR_STOP true

-- Load the pgtap extension
SELECT plan(10);

-- Set up test users
DO $$
DECLARE
  director_id UUID := '11111111-1111-1111-1111-111111111111';
  coordinator_id UUID := '*************-2222-2222-************';
  social_worker_id UUID := '*************-3333-3333-************';
  new_user_id UUID := '*************-4444-4444-************';
BEGIN
  -- Create test users
  INSERT INTO auth.users (id, email)
  VALUES
    (director_id, '<EMAIL>'),
    (coordinator_id, '<EMAIL>'),
    (social_worker_id, '<EMAIL>'),
    (new_user_id, '<EMAIL>');

  -- Create test organizations
  INSERT INTO organizations (id, name, status)
  VALUES
    ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'Test Organization 1', 'active'),
    ('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'Test Organization 2', 'active');

  -- Assign roles
  INSERT INTO user_roles (user_id, role, organization_id)
  VALUES
    (director_id, 'Director', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa'),
    (coordinator_id, 'Coordinator', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa'),
    (social_worker_id, 'SocialWorker', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa');
END;
$$;

-- Test 1: Anonymous users cannot access user_roles
SET LOCAL ROLE anon;
SELECT is_empty(
  'SELECT * FROM user_roles',
  'Anonymous users cannot access user_roles'
);

-- Test 2: Authenticated users can read their own role
SET LOCAL ROLE authenticated;
SET LOCAL "request.jwt.claim.sub" TO '11111111-1111-1111-1111-111111111111';
SELECT results_eq(
  'SELECT role FROM user_roles WHERE user_id = ''11111111-1111-1111-1111-111111111111''',
  ARRAY['Director'],
  'Authenticated users can read their own role'
);

-- Test 3: Authenticated users cannot read other users' roles
SET LOCAL ROLE authenticated;
SET LOCAL "request.jwt.claim.sub" TO '*************-3333-3333-************';
SELECT is_empty(
  'SELECT role FROM user_roles WHERE user_id = ''11111111-1111-1111-1111-111111111111''',
  'Social worker cannot read director''s role'
);

-- Test 4: Directors can create new user roles
SET LOCAL ROLE authenticated;
SET LOCAL "request.jwt.claim.sub" TO '11111111-1111-1111-1111-111111111111';
SELECT lives_ok(
  $$
    INSERT INTO user_roles (user_id, role, organization_id)
    VALUES ('*************-4444-4444-************', 'SocialWorker', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa')
  $$,
  'Director can create new user roles'
);

-- Test 5: Non-directors cannot create user roles
SET LOCAL ROLE authenticated;
SET LOCAL "request.jwt.claim.sub" TO '*************-3333-3333-************';
SELECT throws_ok(
  $$
    INSERT INTO user_roles (user_id, role, organization_id)
    VALUES ('55555555-5555-5555-5555-555555555555', 'SocialWorker', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa')
  $$,
  'new row violates row-level security policy for table "user_roles"',
  'Social worker cannot create new user roles'
);

-- Test 6: Directors can update user roles
SET LOCAL ROLE authenticated;
SET LOCAL "request.jwt.claim.sub" TO '11111111-1111-1111-1111-111111111111';
SELECT lives_ok(
  $$
    UPDATE user_roles
    SET role = 'Coordinator'
    WHERE user_id = '*************-3333-3333-************'
    RETURNING role
  $$,
  'Director can update user roles'
);

-- Test 7: Non-directors cannot update user roles
SET LOCAL ROLE authenticated;
SET LOCAL "request.jwt.claim.sub" TO '*************-2222-2222-************';
SELECT is_empty(
  $$
    UPDATE user_roles
    SET role = 'Director'
    WHERE user_id = '*************-3333-3333-************'
    RETURNING role
  $$,
  'Coordinator cannot update user roles'
);

-- Test 8: Directors can delete user roles
SET LOCAL ROLE authenticated;
SET LOCAL "request.jwt.claim.sub" TO '11111111-1111-1111-1111-111111111111';
SELECT lives_ok(
  $$
    DELETE FROM user_roles
    WHERE user_id = '*************-4444-4444-************'
    RETURNING user_id
  $$,
  'Director can delete user roles'
);

-- Test 9: Non-directors cannot delete user roles
SET LOCAL ROLE authenticated;
SET LOCAL "request.jwt.claim.sub" TO '*************-2222-2222-************';
SELECT is_empty(
  $$
    DELETE FROM user_roles
    WHERE user_id = '*************-3333-3333-************'
    RETURNING user_id
  $$,
  'Coordinator cannot delete user roles'
);

-- Test 10: Directors can read all user roles
SET LOCAL ROLE authenticated;
SET LOCAL "request.jwt.claim.sub" TO '11111111-1111-1111-1111-111111111111';
SELECT isnt_empty(
  'SELECT * FROM user_roles',
  'Director can read all user roles'
);

-- Finish the tests
SELECT * FROM finish();

ROLLBACK;
