BEGIN;

-- Load the pgtap extension
SELECT plan(12);

-- Set up test data
INSERT INTO organizations (id, name, status)
VALUES
  ('11111111-1111-1111-1111-111111111111', 'Test Organization 1', 'active'),
  ('*************-2222-2222-************', 'Test Organization 2', 'active');

-- Create test contacts
INSERT INTO contacts (id, organization_id, name, type, status)
VALUES
  ('*************-3333-3333-************', '11111111-1111-1111-1111-111111111111', 'Contact 1 Org 1', 'family', 'active'),
  ('*************-4444-4444-************', '*************-2222-2222-************', 'Contact 1 Org 2', 'professional', 'active');

-- Create additional contacts for relationships
INSERT INTO contacts (id, organization_id, name, type, status)
VALUES
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '11111111-1111-1111-1111-111111111111', 'Contact 2 Org 1', 'family', 'active'),
  ('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', '*************-2222-2222-************', 'Contact 2 Org 2', 'professional', 'active');

-- Create test contact relationships
INSERT INTO contact_relationships (id, organization_id, subject_contact_id, related_contact_id, relationship, status)
VALUES
  ('*************-5555-5555-************', '11111111-1111-1111-1111-111111111111', '*************-3333-3333-************', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'parent', 'active'),
  ('*************-6666-6666-************', '*************-2222-2222-************', '*************-4444-4444-************', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'parent', 'active');

-- Create test users in auth.users table
INSERT INTO auth.users (id, email)
VALUES
  ('*************-7777-7777-************', '<EMAIL>'),
  ('*************-8888-8888-************', '<EMAIL>');

-- Create user roles
INSERT INTO user_roles (id, user_id, role, organization_id)
VALUES
  ('99999999-9999-9999-9999-999999999999', '*************-7777-7777-************', 'Director', '11111111-1111-1111-1111-111111111111'),
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '*************-8888-8888-************', 'Director', '*************-2222-2222-************');

-- Test 1: Anonymous users cannot access contacts
SET LOCAL ROLE anon;
SELECT is_empty(
  'SELECT * FROM contacts',
  'Anonymous users cannot access contacts'
);

-- Test 2: User from Organization 1 can only see contacts from Organization 1
SET LOCAL ROLE authenticated;
SET LOCAL "request.jwt.claim.sub" TO '*************-7777-7777-************';
SELECT results_eq(
  'SELECT id FROM contacts ORDER BY id',
  ARRAY['*************-3333-3333-************', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa']::UUID[],
  'User from Organization 1 can only see contacts from Organization 1'
);

-- Test 3: User from Organization 1 can only see contact relationships from Organization 1
SELECT results_eq(
  'SELECT id FROM contact_relationships',
  ARRAY['*************-5555-5555-************']::UUID[],
  'User from Organization 1 can only see contact relationships from Organization 1'
);

-- Test 4: User from Organization 1 can insert a contact for Organization 1
SELECT lives_ok(
  $$
    INSERT INTO contacts (organization_id, name, type, status)
    VALUES ('11111111-1111-1111-1111-111111111111', 'New Contact Org 1', 'family', 'active')
    RETURNING name
  $$,
  'User from Organization 1 can insert a contact for Organization 1'
);

-- Test 5: User from Organization 1 cannot insert a contact for Organization 2
SELECT throws_ok(
  $$
    INSERT INTO contacts (organization_id, name, type, status)
    VALUES ('*************-2222-2222-************', 'Should Not Insert', 'family', 'active')
    RETURNING name
  $$,
  'new row violates row-level security policy for table "contacts"',
  'User from Organization 1 cannot insert a contact for Organization 2'
);

-- Test 6: User from Organization 1 can update a contact in Organization 1
SELECT lives_ok(
  $$
    UPDATE contacts
    SET name = 'Updated Contact Org 1'
    WHERE id = '*************-3333-3333-************'
    RETURNING name
  $$,
  'User from Organization 1 can update a contact in Organization 1'
);

-- Test 7: User from Organization 1 cannot update a contact in Organization 2
SELECT is_empty(
  $$
    UPDATE contacts
    SET name = 'Should Not Update'
    WHERE id = '*************-4444-4444-************'
    RETURNING name
  $$,
  'User from Organization 1 cannot update a contact in Organization 2'
);

-- Test 8: User from Organization 1 cannot change the organization_id of a contact
SELECT throws_ok(
  $$
    UPDATE contacts
    SET organization_id = '*************-2222-2222-************'
    WHERE id = '*************-3333-3333-************'
    RETURNING name
  $$,
  'new row violates row-level security policy for table "contacts"',
  'User from Organization 1 cannot change the organization_id of a contact'
);

-- Test 9: User from Organization 1 can delete a contact in Organization 1
SELECT lives_ok(
  $$
    -- First delete any relationships involving this contact
    DELETE FROM contact_relationships
    WHERE subject_contact_id = '*************-3333-3333-************'
    OR related_contact_id = '*************-3333-3333-************';

    -- Then delete contact history
    DELETE FROM contact_history
    WHERE contact_id = '*************-3333-3333-************';

    -- Finally delete the contact
    DELETE FROM contacts
    WHERE id = '*************-3333-3333-************'
    RETURNING id
  $$,
  'User from Organization 1 can delete a contact in Organization 1'
);

-- Test 10: User from Organization 1 cannot delete a contact in Organization 2
SELECT is_empty(
  $$
    DELETE FROM contacts WHERE id = '*************-4444-4444-************' RETURNING id
  $$,
  'User from Organization 1 cannot delete a contact in Organization 2'
);

-- Test 11: User from Organization 2 can only see contacts from Organization 2
SET LOCAL "request.jwt.claim.sub" TO '*************-8888-8888-************';
SELECT results_eq(
  'SELECT id FROM contacts ORDER BY id',
  ARRAY['*************-4444-4444-************', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb']::UUID[],
  'User from Organization 2 can only see contacts from Organization 2'
);

-- Test 12: User from Organization 2 can only see contact relationships from Organization 2
SELECT results_eq(
  'SELECT id FROM contact_relationships',
  ARRAY['*************-6666-6666-************']::UUID[],
  'User from Organization 2 can only see contact relationships from Organization 2'
);

-- Finish the tests
SELECT * FROM finish();

ROLLBACK;
