BEGIN;

-- Load the pgtap extension
SELECT plan(14);

-- Temporarily disable <PERSON><PERSON> for this test
SELECT disable_rls_for_tests();

-- Set up test data
INSERT INTO organizations (id, name, status)
VALUES
  ('11111111-1111-1111-1111-111111111111', 'Test Organization 1', 'active');

-- Create test user in auth.users table
INSERT INTO auth.users (id, email)
VALUES
  ('99999999-9999-9999-9999-999999999999', '<EMAIL>');

-- Create user role for the test user
INSERT INTO user_roles (id, user_id, role, organization_id)
VALUES
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '99999999-9999-9999-9999-999999999999', 'Director', '11111111-1111-1111-1111-111111111111');

-- Set the role to authenticated and set the user ID
SET LOCAL ROLE authenticated;
SET LOCAL "request.jwt.claim.sub" TO '99999999-9999-9999-9999-999999999999';

-- Test 1: Insert a contact
SELECT lives_ok(
  $$
    INSERT INTO contacts (id, organization_id, name, type, email, phone, status)
    VALUES (
      '22222222-2222-2222-2222-222222222222',
      '11111111-1111-1111-1111-111111111111',
      'John Doe',
      'family',
      '{"personal": "<EMAIL>"}',
      '{"mobile": "************"}',
      'active'
    )
  $$,
  'Should be able to insert a contact'
);

-- Test 2: Verify contact was inserted
SELECT results_eq(
  'SELECT name FROM contacts WHERE id = ''22222222-2222-2222-2222-222222222222''',
  ARRAY['John Doe'],
  'Contact should be inserted with correct name'
);

-- Test 3: Update a contact
SELECT lives_ok(
  $$
    UPDATE contacts
    SET name = 'Jane Doe'
    WHERE id = '22222222-2222-2222-2222-222222222222'
  $$,
  'Should be able to update a contact'
);

-- Test 4: Verify contact was updated
SELECT results_eq(
  'SELECT name FROM contacts WHERE id = ''22222222-2222-2222-2222-222222222222''',
  ARRAY['Jane Doe'],
  'Contact should be updated with new name'
);

-- Test 5: Insert a second contact
SELECT lives_ok(
  $$
    INSERT INTO contacts (id, organization_id, name, type, status)
    VALUES (
      '33333333-3333-3333-3333-333333333333',
      '11111111-1111-1111-1111-111111111111',
      'Bob Smith',
      'professional',
      'active'
    )
  $$,
  'Should be able to insert a second contact'
);

-- Test 6: Create a contact relationship
SELECT lives_ok(
  $$
    INSERT INTO contact_relationships (
      id,
      organization_id,
      subject_contact_id,
      related_contact_id,
      relationship,
      status
    )
    VALUES (
      '44444444-4444-4444-4444-444444444444',
      '11111111-1111-1111-1111-111111111111',
      '22222222-2222-2222-2222-222222222222',
      '33333333-3333-3333-3333-333333333333',
      'client',
      'active'
    )
  $$,
  'Should be able to create a contact relationship'
);

-- Test 7: Verify relationship was created
SELECT results_eq(
  'SELECT relationship FROM contact_relationships WHERE id = ''44444444-4444-4444-4444-444444444444''',
  ARRAY['client'],
  'Relationship should be created with correct type'
);

-- Test 8: Update a relationship
SELECT lives_ok(
  $$
    UPDATE contact_relationships
    SET relationship = 'lawyer'
    WHERE id = '44444444-4444-4444-4444-444444444444'
  $$,
  'Should be able to update a relationship'
);

-- Test 9: Verify relationship was updated
SELECT results_eq(
  'SELECT relationship FROM contact_relationships WHERE id = ''44444444-4444-4444-4444-444444444444''',
  ARRAY['lawyer'],
  'Relationship should be updated with new type'
);

-- Test 10: Check contact history trigger on insert
SELECT isnt_empty(
  'SELECT * FROM contact_history WHERE contact_id = ''22222222-2222-2222-2222-222222222222'' AND action = ''INSERT''',
  'Contact history should record insert action'
);

-- Test 11: Check contact history trigger on update
SELECT isnt_empty(
  'SELECT * FROM contact_history WHERE contact_id = ''22222222-2222-2222-2222-222222222222'' AND action = ''UPDATE''',
  'Contact history should record update action'
);

-- Test 12: Delete a relationship
SELECT lives_ok(
  $$
    DELETE FROM contact_relationships
    WHERE id = '44444444-4444-4444-4444-444444444444'
  $$,
  'Should be able to delete a relationship'
);

-- Test 13: Verify relationship was deleted
SELECT is_empty(
  'SELECT * FROM contact_relationships WHERE id = ''44444444-4444-4444-4444-444444444444''',
  'Relationship should be deleted'
);

-- Test 14: Delete a contact (first delete history records)
SELECT lives_ok(
  $$
    DELETE FROM contact_history
    WHERE contact_id = '22222222-2222-2222-2222-222222222222';

    DELETE FROM contacts
    WHERE id = '22222222-2222-2222-2222-222222222222';
  $$,
  'Should be able to delete a contact'
);

-- Note: We're not re-enabling RLS here because we don't have permissions
-- RLS will be re-enabled when the test transaction is rolled back

-- Finish the tests
SELECT * FROM finish();

ROLLBACK;
