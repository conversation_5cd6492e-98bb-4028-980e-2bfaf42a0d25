BEGIN;

-- Load the pgTAP extension
SELECT plan(10);

-- Temporarily disable <PERSON><PERSON> for setup
SELECT disable_rls_for_tests();

-- Create test users and organizations
INSERT INTO auth.users (id, email) VALUES
  ('11111111-1111-1111-1111-111111111111', '<EMAIL>'),
  ('22222222-2222-2222-2222-222222222222', '<EMAIL>'),
  ('33333333-3333-3333-3333-333333333333', '<EMAIL>'),
  ('44444444-4444-4444-4444-444444444444', '<EMAIL>');

INSERT INTO organizations (id, name) VALUES
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'Organization A'),
  ('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'Organization B');

INSERT INTO user_roles (user_id, organization_id, role) VALUES
  ('11111111-1111-1111-1111-111111111111', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'SocialWorker'),
  ('22222222-2222-2222-2222-222222222222', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'Coordinator'),
  ('33333333-3333-3333-3333-333333333333', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'SystemAdmin'),
  ('44444444-4444-4444-4444-444444444444', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'Director');

-- Insert test executions
INSERT INTO executions (id, user_id, organization_id, workflow_type, status, data, started_at, completed_at) VALUES
  ('dddddddd-dddd-dddd-dddd-dddddddddddd', '11111111-1111-1111-1111-111111111111', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'employee_creation', 'completed', '{"firstName": "John", "lastName": "Doe"}'::jsonb, now() - interval '1 hour', now()),
  ('eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', '22222222-2222-2222-2222-222222222222', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'employee_creation', 'running', '{"firstName": "Jane", "lastName": "Smith"}'::jsonb, now() - interval '30 minutes', null),
  ('ffffffff-ffff-ffff-ffff-ffffffffffff', '44444444-4444-4444-4444-444444444444', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'contact_creation', 'pending', '{"name": "Test Contact"}'::jsonb, null, null);

-- Re-enable RLS after setup
SELECT enable_rls_for_tests();

-- Test RLS policies

-- Test as user1 (SocialWorker)
SET LOCAL ROLE postgres;
SET LOCAL "request.jwt.claim.sub" TO '11111111-1111-1111-1111-111111111111';

-- User1 should see their own execution in org A
SELECT results_eq(
  'SELECT count(*) FROM executions WHERE user_id = ''11111111-1111-1111-1111-111111111111''',
  ARRAY[1::bigint],
  'User1 should see their own execution in org A'
);

-- User1 should not see executions in org B
SELECT results_eq(
  'SELECT count(*) FROM executions WHERE organization_id = ''bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb''',
  ARRAY[0::bigint],
  'User1 should not see executions in org B'
);

-- Test as Director
SET LOCAL "request.jwt.claim.sub" TO '44444444-4444-4444-4444-444444444444';

-- Director should see all executions in their organization
SELECT results_eq(
  'SELECT count(*) FROM executions WHERE organization_id = ''aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa''',
  ARRAY[2::bigint],
  'Director should see all executions in their organization'
);

-- Director should not see executions in other organizations
SELECT results_eq(
  'SELECT count(*) FROM executions WHERE organization_id = ''bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb''',
  ARRAY[0::bigint],
  'Director should not see executions in other organizations'
);

-- Test as system admin
SET LOCAL "request.jwt.claim.sub" TO '33333333-3333-3333-3333-333333333333';

-- System admin should see all executions
SELECT results_eq(
  'SELECT count(*) FROM executions',
  ARRAY[3::bigint],
  'System admin should see all executions'
);

-- Test insert policy
SET LOCAL "request.jwt.claim.sub" TO '11111111-1111-1111-1111-111111111111';

-- User1 should be able to insert their own execution in org A
SELECT lives_ok(
  $$INSERT INTO executions (user_id, organization_id, workflow_type, status, data)
    VALUES ('11111111-1111-1111-1111-111111111111', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'contact_creation', 'pending', '{"firstName": "Test"}'::jsonb)$$,
  'User1 should be able to insert their own execution in org A'
);

-- User1 should not be able to insert an execution for user2
SELECT throws_ok(
  $$INSERT INTO executions (user_id, organization_id, workflow_type, status, data)
    VALUES ('22222222-2222-2222-2222-222222222222', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'contact_creation', 'pending', '{"firstName": "Test"}'::jsonb)$$,
  'new row violates row-level security policy',
  'User1 should not be able to insert an execution for user2'
);

-- User1 should not be able to insert an execution in org B
SELECT throws_ok(
  $$INSERT INTO executions (user_id, organization_id, workflow_type, status, data)
    VALUES ('11111111-1111-1111-1111-111111111111', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'contact_creation', 'pending', '{"firstName": "Test"}'::jsonb)$$,
  'new row violates row-level security policy',
  'User1 should not be able to insert an execution in org B'
);

-- Test update policy
-- User1 should be able to update their own execution
SELECT lives_ok(
  $$UPDATE executions
    SET status = 'running'
    WHERE id = 'dddddddd-dddd-dddd-dddd-dddddddddddd'$$,
  'User1 should be able to update their own execution'
);

-- Finish the tests and clean up
SELECT * FROM finish();
ROLLBACK;

BEGIN;

-- Load the pgTAP extension
SELECT plan(10);

-- Temporarily disable RLS for setup
SELECT disable_rls_for_tests();

-- Create test users and organizations
INSERT INTO auth.users (id, email) VALUES
  ('11111111-1111-1111-1111-111111111111', '<EMAIL>'),
  ('22222222-2222-2222-2222-222222222222', '<EMAIL>'),
  ('33333333-3333-3333-3333-333333333333', '<EMAIL>'),
  ('44444444-4444-4444-4444-444444444444', '<EMAIL>');

INSERT INTO organizations (id, name) VALUES
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'Organization A'),
  ('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'Organization B');

INSERT INTO user_roles (user_id, organization_id, role) VALUES
  ('11111111-1111-1111-1111-111111111111', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'SocialWorker'),
  ('22222222-2222-2222-2222-222222222222', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'Coordinator'),
  ('33333333-3333-3333-3333-333333333333', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'SystemAdmin'),
  ('44444444-4444-4444-4444-444444444444', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'Director');

-- Insert test executions
INSERT INTO automation.executions (id, user_id, organization_id, workflow_type, status, data, started_at, completed_at) VALUES
  ('dddddddd-dddd-dddd-dddd-dddddddddddd', '11111111-1111-1111-1111-111111111111', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'employee_creation', 'completed', '{"firstName": "John", "lastName": "Doe"}'::jsonb, now() - interval '1 hour', now()),
  ('eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', '22222222-2222-2222-2222-222222222222', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'employee_creation', 'running', '{"firstName": "Jane", "lastName": "Smith"}'::jsonb, now() - interval '30 minutes', null),
  ('ffffffff-ffff-ffff-ffff-ffffffffffff', '44444444-4444-4444-4444-444444444444', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'contact_creation', 'pending', '{"name": "Test Contact"}'::jsonb, null, null);

-- Re-enable RLS after setup
SELECT enable_rls_for_tests();

-- Test RLS policies

-- Test as user1 (SocialWorker)
SET LOCAL ROLE postgres;
SET LOCAL "request.jwt.claim.sub" TO '11111111-1111-1111-1111-111111111111';

-- User1 should see their own execution in org A
SELECT results_eq(
  'SELECT count(*) FROM automation.executions WHERE user_id = ''11111111-1111-1111-1111-111111111111''',
  ARRAY[1::bigint],
  'User1 should see their own execution in org A'
);

-- User1 should not see executions in org B
SELECT results_eq(
  'SELECT count(*) FROM automation.executions WHERE organization_id = ''bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb''',
  ARRAY[0::bigint],
  'User1 should not see executions in org B'
);

-- Test as Director
SET LOCAL "request.jwt.claim.sub" TO '44444444-4444-4444-4444-444444444444';

-- Director should see all executions in their organization
SELECT results_eq(
  'SELECT count(*) FROM automation.executions WHERE organization_id = ''aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa''',
  ARRAY[2::bigint],
  'Director should see all executions in their organization'
);

-- Director should not see executions in other organizations
SELECT results_eq(
  'SELECT count(*) FROM automation.executions WHERE organization_id = ''bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb''',
  ARRAY[0::bigint],
  'Director should not see executions in other organizations'
);

-- Test as system admin
SET LOCAL "request.jwt.claim.sub" TO '33333333-3333-3333-3333-333333333333';

-- System admin should see all executions
SELECT results_eq(
  'SELECT count(*) FROM automation.executions',
  ARRAY[3::bigint],
  'System admin should see all executions'
);

-- Test insert policy
SET LOCAL "request.jwt.claim.sub" TO '11111111-1111-1111-1111-111111111111';

-- User1 should be able to insert their own execution in org A
SELECT lives_ok(
  $$INSERT INTO automation.executions (user_id, organization_id, workflow_type, status, data)
    VALUES ('11111111-1111-1111-1111-111111111111', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'contact_creation', 'pending', '{"firstName": "Test"}'::jsonb)$$,
  'User1 should be able to insert their own execution in org A'
);

-- User1 should not be able to insert an execution for user2
SELECT throws_ok(
  $$INSERT INTO automation.executions (user_id, organization_id, workflow_type, status, data)
    VALUES ('22222222-2222-2222-2222-222222222222', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'contact_creation', 'pending', '{"firstName": "Test"}'::jsonb)$$,
  'new row violates row-level security policy',
  'User1 should not be able to insert an execution for user2'
);

-- User1 should not be able to insert an execution in org B
SELECT throws_ok(
  $$INSERT INTO automation.executions (user_id, organization_id, workflow_type, status, data)
    VALUES ('11111111-1111-1111-1111-111111111111', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'contact_creation', 'pending', '{"firstName": "Test"}'::jsonb)$$,
  'new row violates row-level security policy',
  'User1 should not be able to insert an execution in org B'
);

-- Test update policy
-- User1 should be able to update their own execution
SELECT lives_ok(
  $$UPDATE automation.executions 
    SET status = 'running', started_at = now() 
    WHERE id = 'dddddddd-dddd-dddd-dddd-dddddddddddd'$$,
  'User1 should be able to update their own execution'
);

-- Finish the tests and clean up
SELECT * FROM finish();
ROLLBACK;
