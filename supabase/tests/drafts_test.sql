BEGIN;

-- Load the pgTAP extension
SELECT plan(13);

-- Test if the drafts table exists in the public schema
SELECT has_table('public', 'drafts', 'Drafts table should exist');

-- Test if the table has the correct columns
SELECT has_column('public', 'drafts', 'id', 'Table should have id column');
SELECT has_column('public', 'drafts', 'user_id', 'Table should have user_id column');
SELECT has_column('public', 'drafts', 'organization_id', 'Table should have organization_id column');
SELECT has_column('public', 'drafts', 'workflow_type', 'Table should have workflow_type column');
SELECT has_column('public', 'drafts', 'current_step', 'Table should have current_step column');
SELECT has_column('public', 'drafts', 'data', 'Table should have data column');
SELECT has_column('public', 'drafts', 'created_at', 'Table should have created_at column');
SELECT has_column('public', 'drafts', 'updated_at', 'Table should have updated_at column');

-- Test if the columns have the correct types
SELECT col_type_is('public', 'drafts', 'id', 'uuid', 'id column should be of type uuid');
SELECT col_type_is('public', 'drafts', 'data', 'jsonb', 'data column should be of type jsonb');
SELECT col_type_is('public', 'drafts', 'workflow_type', 'text', 'workflow_type column should be of type text');
SELECT col_type_is('public', 'drafts', 'current_step', 'text', 'current_step column should be of type text');

-- Finish the tests and clean up
SELECT * FROM finish();
ROLLBACK;
