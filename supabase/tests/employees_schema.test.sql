BEGIN;

-- Deallocate any prepared statements to reset the session
DEALLOCATE ALL;

-- Format the output for TAP testing
\pset format unaligned
\pset tuples_only true
\pset pager off

-- Revert all changes on failure
\set ON_ERROR_ROLLBACK 1
\set ON_ERROR_STOP true

-- Load the pgtap extension
SELECT plan(14);

-- Test 1: Verify employees table exists
SELECT has_table('public', 'employees', 'employees table should exist');

-- Test 2: Verify id is the primary key
SELECT col_is_pk('public', 'employees', 'id', 'id should be the primary key');

-- Test 3-14: Verify all columns exist with correct types
SELECT has_column('public', 'employees', 'id', 'id column should exist');
SELECT has_column('public', 'employees', 'organization_id', 'organization_id column should exist');
SELECT has_column('public', 'employees', 'user_account_id', 'user_account_id column should exist');
SELECT has_column('public', 'employees', 'first_name', 'first_name column should exist');
SELECT has_column('public', 'employees', 'last_name', 'last_name column should exist');
SELECT has_column('public', 'employees', 'profile_image', 'profile_image column should exist');
SELECT has_column('public', 'employees', 'employment_status', 'employment_status column should exist');
SELECT has_column('public', 'employees', 'job_title', 'job_title column should exist');
SELECT has_column('public', 'employees', 'department', 'department column should exist');
SELECT has_column('public', 'employees', 'created_at', 'created_at column should exist');
SELECT has_column('public', 'employees', 'updated_at', 'updated_at column should exist');
SELECT has_column('public', 'employees', 'address', 'address column should exist');

-- Finish the tests
SELECT * FROM finish();

ROLLBACK;
