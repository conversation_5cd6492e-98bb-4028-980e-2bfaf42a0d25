BEGIN;

-- Deallocate any prepared statements to reset the session
DEALLOCATE ALL;

-- Format the output for TAP testing
\pset format unaligned
\pset tuples_only true
\pset pager off

-- Revert all changes on failure
\set ON_ERROR_ROLLBACK 1
\set ON_ERROR_STOP true

-- Load the pgtap extension
SELECT plan(2);

-- Test 1: Verify user_roles table exists
SELECT has_table('public', 'user_roles', 'user_roles table should exist');

-- Test 2: Verify id is the primary key
SELECT col_is_pk('public', 'user_roles', 'id', 'id should be the primary key');

-- Finish the tests
SELECT * FROM finish();

ROLLBACK;
