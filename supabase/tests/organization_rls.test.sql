BEGIN;

-- Load the pgtap extension
SELECT plan(12);

-- Set up test data
INSERT INTO organizations (id, name, status)
VALUES
  ('11111111-1111-1111-1111-111111111111', 'Test Organization 1', 'active'),
  ('*************-2222-2222-************', 'Test Organization 2', 'active');

INSERT INTO locations (id, organization_id, name, status)
VALUES
  ('*************-3333-3333-************', '11111111-1111-1111-1111-111111111111', 'Test Location 1', 'active'),
  ('44444444-4444-4444-4444-444444444444', '*************-2222-2222-************', 'Test Location 2', 'active');

INSERT INTO rooms (id, location_id, organization_id, name, status)
VALUES
  ('*************-5555-5555-************', '*************-3333-3333-************', '11111111-1111-1111-1111-111111111111', 'Test Room 1', 'active'),
  ('66666666-6666-6666-6666-666666666666', '44444444-4444-4444-4444-444444444444', '*************-2222-2222-************', 'Test Room 2', 'active');

INSERT INTO business_hours (id, organization_id, location_id, day_of_week, start_time, end_time)
VALUES
  ('*************-7777-7777-************', '11111111-1111-1111-1111-111111111111', '*************-3333-3333-************', 1, '09:00', '17:00'),
  ('88888888-8888-8888-8888-888888888888', '*************-2222-2222-************', '44444444-4444-4444-4444-444444444444', 1, '09:00', '17:00');

-- Create test users in auth.users table
INSERT INTO auth.users (id, email)
VALUES
  ('*************-9999-9999-************', '<EMAIL>'),
  ('aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee', '<EMAIL>');

-- Test 1: Anonymous users cannot access organizations
SET LOCAL ROLE anon;
SELECT is_empty(
  'SELECT * FROM organizations',
  'Anonymous users cannot access organizations'
);

-- Test 2: User from Organization 1 can only see Organization 1
SET LOCAL ROLE authenticated;
SET LOCAL "request.jwt.claim.sub" TO '*************-9999-9999-************';
-- Simulate user_roles entry for this user
INSERT INTO user_roles (id, user_id, role, organization_id)
VALUES ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '*************-9999-9999-************', 'Director', '11111111-1111-1111-1111-111111111111');

SELECT results_eq(
  'SELECT id FROM organizations',
  ARRAY['11111111-1111-1111-1111-111111111111']::UUID[],
  'User from Organization 1 can only see Organization 1'
);

-- Test 3: User from Organization 1 can only see locations from Organization 1
SELECT results_eq(
  'SELECT id FROM locations',
  ARRAY['*************-3333-3333-************']::UUID[],
  'User from Organization 1 can only see locations from Organization 1'
);

-- Test 4: User from Organization 1 can only see rooms from Organization 1
SELECT results_eq(
  'SELECT id FROM rooms',
  ARRAY['*************-5555-5555-************']::UUID[],
  'User from Organization 1 can only see rooms from Organization 1'
);

-- Test 5: User from Organization 1 can only see business hours from Organization 1
SELECT results_eq(
  'SELECT id FROM business_hours',
  ARRAY['*************-7777-7777-************']::UUID[],
  'User from Organization 1 can only see business hours from Organization 1'
);

-- Test 6: Director from Organization 1 can update Organization 1
SELECT lives_ok(
  $$
    UPDATE organizations
    SET name = 'Updated Organization 1'
    WHERE id = '11111111-1111-1111-1111-111111111111'
    RETURNING name
  $$,
  'Director from Organization 1 can update Organization 1'
);

-- Test 7: Director from Organization 1 cannot update Organization 2
SELECT is_empty(
  $$
    UPDATE organizations
    SET name = 'Should Not Update'
    WHERE id = '*************-2222-2222-************'
    RETURNING name
  $$,
  'Director from Organization 1 cannot update Organization 2'
);

-- Test 8: Director from Organization 1 can insert a location for Organization 1
SELECT lives_ok(
  $$
    INSERT INTO locations (organization_id, name, status)
    VALUES ('11111111-1111-1111-1111-111111111111', 'New Location', 'active')
    RETURNING name
  $$,
  'Director from Organization 1 can insert a location for Organization 1'
);

-- Test 9: Director from Organization 1 cannot insert a location for Organization 2
SELECT throws_ok(
  $$
    INSERT INTO locations (organization_id, name, status)
    VALUES ('*************-2222-2222-************', 'Should Not Insert', 'active')
    RETURNING name
  $$,
  'new row violates row-level security policy for table "locations"',
  'Director from Organization 1 cannot insert a location for Organization 2'
);

-- Test 10: Director from Organization 1 can insert a room for Organization 1
SELECT lives_ok(
  $$
    INSERT INTO rooms (location_id, organization_id, name, status)
    VALUES ('*************-3333-3333-************', '11111111-1111-1111-1111-111111111111', 'New Room', 'active')
    RETURNING name
  $$,
  'Director from Organization 1 can insert a room for Organization 1'
);

-- Test 11: Director from Organization 1 cannot insert a room for Organization 2
SELECT throws_ok(
  $$
    INSERT INTO rooms (location_id, organization_id, name, status)
    VALUES ('44444444-4444-4444-4444-444444444444', '*************-2222-2222-************', 'Should Not Insert', 'active')
    RETURNING name
  $$,
  'new row violates row-level security policy for table "rooms"',
  'Director from Organization 1 cannot insert a room for Organization 2'
);

-- Test 12: Director from Organization 1 can insert business hours for Organization 1
SELECT lives_ok(
  $$
    INSERT INTO business_hours (organization_id, location_id, day_of_week, start_time, end_time)
    VALUES ('11111111-1111-1111-1111-111111111111', '*************-3333-3333-************', 2, '09:00', '17:00')
    RETURNING day_of_week
  $$,
  'Director from Organization 1 can insert business hours for Organization 1'
);

-- Finish the tests
SELECT * FROM finish();

ROLLBACK;
