BEGIN;

-- Load the pgtap extension
SELECT plan(10);

-- Set up test data
INSERT INTO organizations (id, name, status)
VALUES ('11111111-1111-1111-1111-111111111111', 'Test Organization', 'active');

-- Create test user in auth.users table
INSERT INTO auth.users (id, email)
VALUES ('99999999-9999-9999-9999-999999999999', '<EMAIL>');

-- Set up authenticated user with proper role
SET LOCAL ROLE authenticated;
SET LOCAL "request.jwt.claim.sub" TO '99999999-9999-9999-9999-999999999999';
INSERT INTO user_roles (id, user_id, role, organization_id)
VALUES ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '99999999-9999-9999-9999-999999999999', 'Director', '11111111-1111-1111-1111-111111111111');

-- Test 1: Create an employee
SELECT lives_ok(
  $$
    INSERT INTO employees (
      organization_id,
      first_name,
      last_name,
      employment_status,
      job_title,
      department,
      address
    )
    VALUES (
      '11111111-1111-1111-1111-111111111111',
      'John',
      'Doe',
      'active',
      'Manager',
      'HR',
      '{"street": "123 Main St", "city": "Anytown", "state": "CA", "postal_code": "12345", "country": "USA"}'::jsonb
    )
    RETURNING id
  $$,
  'Should be able to create an employee'
);

-- Store the ID of the created employee for later tests
PREPARE get_employee_id AS
SELECT id FROM employees WHERE first_name = 'John' AND last_name = 'Doe';

-- Test 2: Read the created employee
SELECT results_eq(
  'SELECT first_name, last_name FROM employees WHERE first_name = ''John'' AND last_name = ''Doe''',
  $$VALUES ('John'::text, 'Doe'::text)$$,
  'Should be able to read the created employee'
);

-- Test 3: Update the employee's job title
SELECT lives_ok(
  $$
    UPDATE employees
    SET job_title = 'Senior Manager'
    WHERE first_name = 'John' AND last_name = 'Doe'
    RETURNING job_title
  $$,
  'Should be able to update the employee''s job title'
);

-- Test 4: Verify the update was successful
SELECT results_eq(
  'SELECT job_title FROM employees WHERE first_name = ''John'' AND last_name = ''Doe''',
  $$VALUES ('Senior Manager'::text)$$,
  'The job title should be updated to Senior Manager'
);

-- Test 5: Update the employee's department
SELECT lives_ok(
  $$
    UPDATE employees
    SET department = 'Executive'
    WHERE first_name = 'John' AND last_name = 'Doe'
    RETURNING department
  $$,
  'Should be able to update the employee''s department'
);

-- Test 6: Verify the department update was successful
SELECT results_eq(
  'SELECT department FROM employees WHERE first_name = ''John'' AND last_name = ''Doe''',
  $$VALUES ('Executive'::text)$$,
  'The department should be updated to Executive'
);

-- Test 7: Update the employee's employment status
SELECT lives_ok(
  $$
    UPDATE employees
    SET employment_status = 'inactive'
    WHERE first_name = 'John' AND last_name = 'Doe'
    RETURNING employment_status
  $$,
  'Should be able to update the employee''s employment status'
);

-- Test 8: Verify the employment status update was successful
SELECT results_eq(
  'SELECT employment_status FROM employees WHERE first_name = ''John'' AND last_name = ''Doe''',
  $$VALUES ('inactive'::text)$$,
  'The employment status should be updated to inactive'
);

-- Test 9: Delete the employee
SELECT lives_ok(
  $$
    DELETE FROM employees
    WHERE first_name = 'John' AND last_name = 'Doe'
    RETURNING id
  $$,
  'Should be able to delete the employee'
);

-- Test 10: Verify the employee was deleted
SELECT is_empty(
  'SELECT * FROM employees WHERE first_name = ''John'' AND last_name = ''Doe''',
  'The employee should be deleted'
);

-- Finish the tests
SELECT * FROM finish();

ROLLBACK;
