-- Sample organization data
INSERT INTO organizations (id, name, status, phones, emails, address, website_url, logo_url, settings)
VALUES
  ('********-0000-0000-0000-********0001', 'RQRSDA Montreal', 'active',
   '{"main": "************", "fax": "************"}',
   '{"info": "<EMAIL>", "support": "<EMAIL>"}',
   '123 Rue Principale, Montreal, QC H3A 1B1',
   'https://rqrsda-mtl.org',
   'https://example.com/logos/rqrsda-mtl.png',
   '{"theme": {"primary": "#4B9CD3", "secondary": "#F5A623"}, "features": {"scheduling": true, "reporting": true}}')
ON CONFLICT (id) DO NOTHING;

-- Sample location data
INSERT INTO locations (id, organization_id, name, status, phones, emails, address)
VALUES
  ('********-0000-0000-0000-********0002', '********-0000-0000-0000-********0001', 'Centre-ville', 'active',
   '{"main": "************", "reception": "************"}',
   '{"info": "<EMAIL>"}',
   '456 Boulevard Saint-Laurent, Montreal, QC H2Y 2Y5')
ON CONFLICT (id) DO NOTHING;

-- Sample room data
INSERT INTO rooms (id, location_id, organization_id, name, description, capacity, status)
VALUES
  ('********-0000-0000-0000-********0003', '********-0000-0000-0000-********0002', '********-0000-0000-0000-********0001',
   'Salle A', 'Grande salle pour les visites familiales', 8, 'active'),
  ('********-0000-0000-0000-********0004', '********-0000-0000-0000-********0002', '********-0000-0000-0000-********0001',
   'Salle B', 'Petite salle pour les visites individuelles', 4, 'active')
ON CONFLICT (id) DO NOTHING;

-- Sample business hours data
INSERT INTO business_hours (id, organization_id, location_id, day_of_week, start_time, end_time, is_closed)
VALUES
  ('********-0000-0000-0000-********0005', '********-0000-0000-0000-********0001', NULL, 1, '09:00', '17:00', false),
  ('********-0000-0000-0000-********0006', '********-0000-0000-0000-********0001', NULL, 2, '09:00', '17:00', false),
  ('********-0000-0000-0000-********0007', '********-0000-0000-0000-********0001', NULL, 3, '09:00', '17:00', false),
  ('********-0000-0000-0000-********0008', '********-0000-0000-0000-********0001', NULL, 4, '09:00', '17:00', false),
  ('********-0000-0000-0000-********0009', '********-0000-0000-0000-********0001', NULL, 5, '09:00', '17:00', false),
  ('********-0000-0000-0000-********0010', '********-0000-0000-0000-********0001', NULL, 6, '10:00', '14:00', false),
  ('********-0000-0000-0000-********0011', '********-0000-0000-0000-********0001', NULL, 0, '00:00', '23:59', true)
ON CONFLICT (id) DO NOTHING;

-- Create RQRSDA employees (without linking to user accounts)
DO $$
DECLARE
    org_id UUID := '********-0000-0000-0000-********0001';
    employee_id_1 UUID;
    employee_id_2 UUID;
    employee_id_3 UUID;
    employee_id_4 UUID;
    employee_id_5 UUID;
    employee_id_6 UUID;
    employee_id_7 UUID;
    employee_id_8 UUID;
BEGIN
    -- Create Social Worker
    INSERT INTO employees (
        first_name,
        last_name,
        employment_status,
        job_title,
        department,
        organization_id,
        profile_image,
        date_of_birth,
        gender,
        address,
        employee_id,
        hire_date,
        specializations,
        certifications,
        education,
        emails,
        phones,
        created_at,
        updated_at
    ) VALUES (
        'Marie',
        'Tremblay',
        'active',
        'Social Worker',
        'Family Services',
        org_id,
        'https://randomuser.me/api/portraits/women/32.jpg',
        '1985-06-15',
        'Female',
        '123 Rue Principale, Montréal, QC H2X 1Y6, Canada',
        'SW-2023-001',
        '2023-01-15',
        ARRAY['Family Counseling', 'Crisis Intervention', 'Child Welfare'],
        ARRAY[
            '{"name": "Licensed Social Worker", "issuer": "Quebec Association of Social Workers", "date_issued": "2020-05-10", "expiry_date": "2025-05-10"}'::jsonb,
            '{"name": "Crisis Intervention Specialist", "issuer": "Canadian Crisis Response Institute", "date_issued": "2021-03-22", "expiry_date": "2024-03-22"}'::jsonb
        ],
        ARRAY[
            '{"degree": "Master of Social Work", "institution": "Université de Montréal", "year_completed": "2018"}'::jsonb,
            '{"degree": "Bachelor of Social Work", "institution": "McGill University", "year_completed": "2016"}'::jsonb
        ],
        ARRAY[
            '{"email": "<EMAIL>", "type": "Work", "primary": true}'::jsonb,
            '{"email": "<EMAIL>", "type": "Personal", "primary": false}'::jsonb
        ],
        ARRAY[
            '{"number": "************", "type": "Work", "primary": true}'::jsonb,
            '{"number": "************", "type": "Mobile", "primary": false}'::jsonb
        ],
        NOW(),
        NOW()
    )
    RETURNING id INTO employee_id_1;

    -- Create Program Coordinator
    INSERT INTO employees (
        first_name,
        last_name,
        employment_status,
        job_title,
        department,
        organization_id,
        profile_image,
        date_of_birth,
        gender,
        address,
        employee_id,
        hire_date,
        specializations,
        certifications,
        education,
        emails,
        phones,
        created_at,
        updated_at
    ) VALUES (
        'Sophie',
        'Bergeron',
        'active',
        'Program Coordinator',
        'Community Outreach',
        org_id,
        'https://randomuser.me/api/portraits/women/68.jpg',
        '1979-03-12',
        'Female',
        '789 Avenue du Parc, Montréal, QC H2W 1S8, Canada',
        'PC-2020-007',
        '2020-02-01',
        ARRAY['Program Development', 'Community Engagement', 'Grant Writing'],
        ARRAY[
            '{"name": "Project Management Professional", "issuer": "Project Management Institute", "date_issued": "2018-04-30", "expiry_date": "2024-04-30"}'::jsonb
        ],
        ARRAY[
            '{"degree": "Master of Public Administration", "institution": "Concordia University", "year_completed": "2012"}'::jsonb,
            '{"degree": "Bachelor of Social Sciences", "institution": "University of Ottawa", "year_completed": "2010"}'::jsonb
        ],
        ARRAY[
            '{"email": "<EMAIL>", "type": "Work", "primary": true}'::jsonb
        ],
        ARRAY[
            '{"number": "************", "type": "Work", "primary": true}'::jsonb,
            '{"number": "************", "type": "Mobile", "primary": false}'::jsonb
        ],
        NOW(),
        NOW()
    )
    RETURNING id INTO employee_id_2;

    -- Create Executive Director
    INSERT INTO employees (
        first_name,
        last_name,
        employment_status,
        job_title,
        department,
        organization_id,
        profile_image,
        date_of_birth,
        gender,
        address,
        employee_id,
        hire_date,
        specializations,
        certifications,
        education,
        emails,
        phones,
        created_at,
        updated_at
    ) VALUES (
        'Robert',
        'Gagnon',
        'active',
        'Executive Director',
        'Administration',
        org_id,
        'https://randomuser.me/api/portraits/men/22.jpg',
        '1972-11-05',
        'Male',
        '101 Rue Sherbrooke, Montréal, QC H2X 1C3, Canada',
        'ED-2018-001',
        '2018-07-01',
        ARRAY['Strategic Planning', 'Nonprofit Management', 'Fundraising'],
        ARRAY[
            '{"name": "Certified Nonprofit Executive", "issuer": "Nonprofit Leadership Alliance", "date_issued": "2016-09-15", "expiry_date": "2026-09-15"}'::jsonb
        ],
        ARRAY[
            '{"degree": "MBA", "institution": "HEC Montréal", "year_completed": "2005"}'::jsonb,
            '{"degree": "Bachelor of Social Work", "institution": "Université Laval", "year_completed": "2000"}'::jsonb
        ],
        ARRAY[
            '{"email": "<EMAIL>", "type": "Work", "primary": true}'::jsonb
        ],
        ARRAY[
            '{"number": "************", "type": "Work", "primary": true}'::jsonb
        ],
        NOW(),
        NOW()
    )
    RETURNING id INTO employee_id_3;

    -- Create Case Manager
    INSERT INTO employees (
        first_name,
        last_name,
        employment_status,
        job_title,
        department,
        organization_id,
        profile_image,
        date_of_birth,
        gender,
        address,
        employee_id,
        hire_date,
        specializations,
        certifications,
        education,
        emails,
        phones,
        created_at,
        updated_at
    ) VALUES (
        'Jean',
        'Dubois',
        'active',
        'Case Manager',
        'Family Services',
        org_id,
        'https://randomuser.me/api/portraits/men/45.jpg',
        '1983-09-22',
        'Male',
        '456 Boulevard Saint-Laurent, Montréal, QC H2Y 2Y5, Canada',
        'CM-2021-003',
        '2021-05-10',
        ARRAY['Case Management', 'Family Reunification', 'Resource Coordination'],
        ARRAY[
            '{"name": "Certified Case Manager", "issuer": "Commission for Case Manager Certification", "date_issued": "2019-06-15", "expiry_date": "2024-06-15"}'::jsonb
        ],
        ARRAY[
            '{"degree": "Master of Social Work", "institution": "McGill University", "year_completed": "2015"}'::jsonb,
            '{"degree": "Bachelor of Psychology", "institution": "Université de Montréal", "year_completed": "2012"}'::jsonb
        ],
        ARRAY[
            '{"email": "<EMAIL>", "type": "Work", "primary": true}'::jsonb
        ],
        ARRAY[
            '{"number": "************", "type": "Work", "primary": true}'::jsonb,
            '{"number": "************", "type": "Mobile", "primary": false}'::jsonb
        ],
        NOW(),
        NOW()
    )
    RETURNING id INTO employee_id_4;

    -- Create Family Therapist
    INSERT INTO employees (
        first_name,
        last_name,
        employment_status,
        job_title,
        department,
        organization_id,
        profile_image,
        date_of_birth,
        gender,
        address,
        employee_id,
        hire_date,
        specializations,
        certifications,
        education,
        emails,
        phones,
        created_at,
        updated_at
    ) VALUES (
        'Isabelle',
        'Lavoie',
        'active',
        'Family Therapist',
        'Clinical Services',
        org_id,
        'https://randomuser.me/api/portraits/women/56.jpg',
        '1980-04-18',
        'Female',
        '789 Rue Saint-Denis, Montréal, QC H2X 3L2, Canada',
        'FT-2019-005',
        '2019-09-15',
        ARRAY['Family Therapy', 'Trauma-Informed Care', 'Child Development'],
        ARRAY[
            '{"name": "Licensed Marriage and Family Therapist", "issuer": "Quebec Order of Psychologists", "date_issued": "2017-11-20", "expiry_date": "2027-11-20"}'::jsonb,
            '{"name": "Trauma-Focused Cognitive Behavioral Therapy", "issuer": "Medical University of South Carolina", "date_issued": "2018-03-10", "expiry_date": null}'::jsonb
        ],
        ARRAY[
            '{"degree": "Master of Psychology", "institution": "Université de Montréal", "year_completed": "2014"}'::jsonb,
            '{"degree": "Bachelor of Psychology", "institution": "Université Laval", "year_completed": "2011"}'::jsonb
        ],
        ARRAY[
            '{"email": "<EMAIL>", "type": "Work", "primary": true}'::jsonb,
            '{"email": "<EMAIL>", "type": "Personal", "primary": false}'::jsonb
        ],
        ARRAY[
            '{"number": "************", "type": "Work", "primary": true}'::jsonb,
            '{"number": "************", "type": "Mobile", "primary": false}'::jsonb
        ],
        NOW(),
        NOW()
    )
    RETURNING id INTO employee_id_5;

    -- Create Supervised Visit Coordinator
    INSERT INTO employees (
        first_name,
        last_name,
        employment_status,
        job_title,
        department,
        organization_id,
        profile_image,
        date_of_birth,
        gender,
        address,
        employee_id,
        hire_date,
        specializations,
        certifications,
        education,
        emails,
        phones,
        created_at,
        updated_at
    ) VALUES (
        'Michel',
        'Lemieux',
        'active',
        'Supervised Visit Coordinator',
        'Visitation Services',
        org_id,
        'https://randomuser.me/api/portraits/men/67.jpg',
        '1988-12-03',
        'Male',
        '234 Avenue Mont-Royal, Montréal, QC H2T 1P3, Canada',
        'SVC-2022-002',
        '2022-03-01',
        ARRAY['Supervised Visitation', 'Child Safety', 'Family Dynamics'],
        ARRAY[
            '{"name": "Child Safety Specialist", "issuer": "Canadian Child Welfare Association", "date_issued": "2021-05-12", "expiry_date": "2026-05-12"}'::jsonb
        ],
        ARRAY[
            '{"degree": "Bachelor of Social Work", "institution": "Université du Québec à Montréal", "year_completed": "2019"}'::jsonb
        ],
        ARRAY[
            '{"email": "<EMAIL>", "type": "Work", "primary": true}'::jsonb
        ],
        ARRAY[
            '{"number": "************", "type": "Work", "primary": true}'::jsonb,
            '{"number": "************", "type": "Mobile", "primary": false}'::jsonb
        ],
        NOW(),
        NOW()
    )
    RETURNING id INTO employee_id_6;

    -- Create Administrative Assistant
    INSERT INTO employees (
        first_name,
        last_name,
        employment_status,
        job_title,
        department,
        organization_id,
        profile_image,
        date_of_birth,
        gender,
        address,
        employee_id,
        hire_date,
        specializations,
        certifications,
        education,
        emails,
        phones,
        created_at,
        updated_at
    ) VALUES (
        'Nathalie',
        'Roy',
        'active',
        'Administrative Assistant',
        'Administration',
        org_id,
        'https://randomuser.me/api/portraits/women/89.jpg',
        '1990-07-25',
        'Female',
        '567 Rue Beaubien, Montréal, QC H2S 1S7, Canada',
        'AA-2021-008',
        '2021-11-15',
        ARRAY['Office Management', 'Client Reception', 'Records Management'],
        ARRAY[
            '{"name": "Office Administration Certificate", "issuer": "Champlain College", "date_issued": "2020-06-30", "expiry_date": null}'::jsonb
        ],
        ARRAY[
            '{"degree": "DEC in Office Administration", "institution": "Cégep du Vieux Montréal", "year_completed": "2020"}'::jsonb
        ],
        ARRAY[
            '{"email": "<EMAIL>", "type": "Work", "primary": true}'::jsonb
        ],
        ARRAY[
            '{"number": "************", "type": "Work", "primary": true}'::jsonb
        ],
        NOW(),
        NOW()
    )
    RETURNING id INTO employee_id_7;

    -- Create Outreach Worker
    INSERT INTO employees (
        first_name,
        last_name,
        employment_status,
        job_title,
        department,
        organization_id,
        profile_image,
        date_of_birth,
        gender,
        address,
        employee_id,
        hire_date,
        specializations,
        certifications,
        education,
        emails,
        phones,
        created_at,
        updated_at
    ) VALUES (
        'Ahmed',
        'Hassan',
        'active',
        'Outreach Worker',
        'Community Outreach',
        org_id,
        'https://randomuser.me/api/portraits/men/34.jpg',
        '1986-02-14',
        'Male',
        '890 Rue Saint-Urbain, Montréal, QC H2Z 1Y6, Canada',
        'OW-2020-004',
        '2020-08-15',
        ARRAY['Community Engagement', 'Cultural Mediation', 'Resource Navigation'],
        ARRAY[
            '{"name": "Cultural Competency Certificate", "issuer": "Canadian Council for Refugees", "date_issued": "2019-10-05", "expiry_date": null}'::jsonb
        ],
        ARRAY[
            '{"degree": "Bachelor of Social Work", "institution": "McGill University", "year_completed": "2017"}'::jsonb,
            '{"degree": "Certificate in Immigration and Refugee Studies", "institution": "Université de Montréal", "year_completed": "2018"}'::jsonb
        ],
        ARRAY[
            '{"email": "<EMAIL>", "type": "Work", "primary": true}'::jsonb,
            '{"email": "<EMAIL>", "type": "Personal", "primary": false}'::jsonb
        ],
        ARRAY[
            '{"number": "************", "type": "Work", "primary": true}'::jsonb,
            '{"number": "************", "type": "Mobile", "primary": false}'::jsonb
        ],
        NOW(),
        NOW()
    )
    RETURNING id INTO employee_id_8;

    -- Log the number of employees created
    RAISE NOTICE 'Created 8 RQRSDA employees with proper context';
END $$;

-- Sample contact data for the RQRSDA Montreal organization
-- Organization ID: ********-0000-0000-0000-********0001

-- Family contacts
INSERT INTO contacts (id, organization_id, name, status, email, phone, address)
VALUES
  ('********-0000-0000-0000-********0101', '********-0000-0000-0000-********0001',
   'Jean Tremblay', 'active',
   '{"personal": "<EMAIL>"}',
   '{"mobile": "************", "home": "************"}',
   '123 Rue Saint-Denis, Montreal, QC H2X 3L9'),

  ('********-0000-0000-0000-********0102', '********-0000-0000-0000-********0001',
   'Marie Tremblay', 'active',
   '{"personal": "<EMAIL>", "work": "<EMAIL>"}',
   '{"mobile": "************"}',
   '456 Boulevard Saint-Laurent, Montreal, QC H2Y 2Y5'),

  ('********-0000-0000-0000-********0103', '********-0000-0000-0000-********0001',
   'Sophie Lavoie', 'active',
   '{"personal": "<EMAIL>"}',
   '{"mobile": "************", "home": "************"}',
   '789 Avenue du Mont-Royal, Montreal, QC H2J 1X1'),

  ('********-0000-0000-0000-********0104', '********-0000-0000-0000-********0001',
   'Robert Gagnon', 'inactive',
   '{"personal": "<EMAIL>"}',
   '{"mobile": "************"}',
   '321 Rue Sherbrooke, Montreal, QC H2L 1E3'),

  ('********-0000-0000-0000-********0105', '********-0000-0000-0000-********0001',
   'Isabelle Côté', 'active',
   '{"personal": "<EMAIL>", "work": "<EMAIL>"}',
   '{"mobile": "************", "home": "************"}',
   '654 Rue Sainte-Catherine, Montreal, QC H3B 1A7')
ON CONFLICT (id) DO NOTHING;

-- Professional contacts
INSERT INTO contacts (id, organization_id, name, status, email, phone, address)
VALUES
  ('********-0000-0000-0000-********0201', '********-0000-0000-0000-********0001',
   'Dr. Michel Bergeron', 'active',
   '{"work": "<EMAIL>", "personal": "<EMAIL>"}',
   '{"work": "************", "mobile": "************"}',
   'Montreal General Hospital, 1650 Cedar Ave, Montreal, QC H3G 1A4'),

  ('********-0000-0000-0000-********0202', '********-0000-0000-0000-********0001',
   'Me. Julie Lemieux', 'active',
   '{"work": "<EMAIL>"}',
   '{"work": "************", "mobile": "************"}',
   '1000 Place Jean-Paul-Riopelle, Montreal, QC H2Z 1H2'),

  ('********-0000-0000-0000-********0203', '********-0000-0000-0000-********0001',
   'Sarah Thompson', 'active',
   '{"work": "<EMAIL>"}',
   '{"work": "************", "mobile": "************"}',
   '2100 Boulevard Edouard-Montpetit, Montreal, QC H3T 1J4'),

  ('********-0000-0000-0000-********0204', '********-0000-0000-0000-********0001',
   'Pierre Morin', 'inactive',
   '{"work": "<EMAIL>", "personal": "<EMAIL>"}',
   '{"work": "************", "mobile": "************"}',
   '3600 Rue University, Montreal, QC H3A 2B2'),

  ('********-0000-0000-0000-********0205', '********-0000-0000-0000-********0001',
   'Dr. Fatima Hassan', 'active',
   '{"work": "<EMAIL>"}',
   '{"work": "************", "mobile": "************"}',
   'Montreal Children''s Hospital, 1001 Decarie Blvd, Montreal, QC H4A 3J1')
ON CONFLICT (id) DO NOTHING;

-- Organization contacts
INSERT INTO contacts (id, organization_id, name, status, email, phone, address)
VALUES
  ('********-0000-0000-0000-********0301', '********-0000-0000-0000-********0001',
   'Centre Jeunesse de Montréal', 'active',
   '{"info": "<EMAIL>", "referrals": "<EMAIL>"}',
   '{"main": "************", "fax": "************"}',
   '1001 Boulevard de Maisonneuve Est, Montreal, QC H2L 4P9'),

  ('********-0000-0000-0000-********0302', '********-0000-0000-0000-********0001',
   'CLSC Plateau-Mont-Royal', 'active',
   '{"info": "<EMAIL>"}',
   '{"main": "************", "fax": "************"}',
   '4625 Avenue de Lorimier, Montreal, QC H2H 2B4'),

  ('********-0000-0000-0000-********0303', '********-0000-0000-0000-********0001',
   'Maison des Familles', 'active',
   '{"info": "<EMAIL>"}',
   '{"main": "************", "mobile": "************"}',
   '3245 Avenue Émile-Duployé, Montreal, QC H1Z 1G9'),

  ('********-0000-0000-0000-********0304', '********-0000-0000-0000-********0001',
   'École Primaire Saint-Jean', 'active',
   '{"info": "<EMAIL>"}',
   '{"main": "************", "fax": "************"}',
   '4200 Rue Drolet, Montreal, QC H2W 2L5'),

  ('********-0000-0000-0000-********0305', '********-0000-0000-0000-********0001',
   'Tribunal de la Famille', 'active',
   '{"info": "<EMAIL>"}',
   '{"main": "************", "fax": "************"}',
   '1 Rue Notre-Dame Est, Montreal, QC H2Y 1B6')
ON CONFLICT (id) DO NOTHING;

-- Other contacts
INSERT INTO contacts (id, organization_id, name, status, email, phone, address)
VALUES
  ('********-0000-0000-0000-********0401', '********-0000-0000-0000-********0001',
   'Interprète Services', 'active',
   '{"info": "<EMAIL>"}',
   '{"main": "************", "mobile": "************"}',
   '2100 Rue Sainte-Catherine Ouest, Montreal, QC H3H 1M6'),

  ('********-0000-0000-0000-********0402', '********-0000-0000-0000-********0001',
   'Transport Adapté Montréal', 'active',
   '{"info": "<EMAIL>"}',
   '{"main": "************", "dispatch": "************"}',
   '3600 Boulevard Crémazie Est, Montreal, QC H2M 1L9'),

  ('********-0000-0000-0000-********0403', '********-0000-0000-0000-********0001',
   'Sécurité Vigilance', 'active',
   '{"info": "<EMAIL>"}',
   '{"main": "************", "emergency": "************"}',
   '1234 Rue Ontario Est, Montreal, QC H2L 1R7'),

  ('********-0000-0000-0000-********0404', '********-0000-0000-0000-********0001',
   'Garderie Les Petits Pas', 'active',
   '{"info": "<EMAIL>"}',
   '{"main": "************"}',
   '5678 Avenue du Parc, Montreal, QC H2V 4H4'),

  ('********-0000-0000-0000-********0405', '********-0000-0000-0000-********0001',
   'Banque Alimentaire Montréal', 'active',
   '{"info": "<EMAIL>"}',
   '{"main": "************"}',
   '9012 Rue Saint-Urbain, Montreal, QC H2N 1Y6')
ON CONFLICT (id) DO NOTHING;
