-- Add SystemAdmin role to user_roles table
-- This migration adds the SystemAdmin role to the user_roles table and creates
-- functions to check for system admin permissions

-- 1. Update the CHECK constraint to include 'SystemAdmin'
ALTER TABLE user_roles
  DROP CONSTRAINT user_roles_role_check,
  ADD CONSTRAINT user_roles_role_check
    CHECK (role IN ('Director', 'Coordinator', 'SocialWorker', 'SystemAdmin'));

-- 2. Create a function to check if a user is a SystemAdmin
CREATE OR REPLACE FUNCTION public.is_system_admin()
RETURNS BOOLEAN AS $$
DECLARE
  is_admin BOOLEAN;
BEGIN
  SELECT EXISTS (
    SELECT 1 FROM user_roles
    WHERE user_id = auth.uid()
    AND role = 'SystemAdmin'
  ) INTO is_admin;

  RETURN is_admin;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Update the RLS policy for user_roles to allow <PERSON><PERSON>dmins to manage roles
DROP POLICY IF EXISTS "Only Directors can manage roles" ON user_roles;

CREATE POLICY "Directors and SystemA<PERSON>mins can manage roles"
  ON user_roles
  FOR ALL
  USING (public.is_director() OR public.is_system_admin());

-- 4. Create a policy to allow SystemAdmins to view all user roles
CREATE POLICY "SystemAdmins can view all user roles"
  ON user_roles
  FOR SELECT
  USING (public.is_system_admin());
