-- Update the disable_rls_for_tests function to include automation.drafts
CREATE OR REP<PERSON>CE FUNCTION disable_rls_for_tests()
RETURNS VOID AS $$
BEGIN
    -- Disable RLS for contacts table
    ALTER TABLE contacts DISABLE ROW LEVEL SECURITY;

    -- Disable RLS for contact_relationships table
    ALTER TABLE contact_relationships DISABLE ROW LEVEL SECURITY;

    -- Disable RLS for contact_history table
    ALTER TABLE contact_history DISABLE ROW LEVEL SECURITY;
    
    -- Disable RLS for automation.drafts table
    ALTER TABLE automation.drafts DISABLE ROW LEVEL SECURITY;
END;
$$ LANGUAGE plpgsql;

-- Update the enable_rls_for_tests function to include automation.drafts
CREATE OR REPLACE FUNCTION enable_rls_for_tests()
RETURNS VOID AS $$
BEGIN
    -- Enable RLS for contacts table
    ALTER TABLE contacts ENABLE ROW LEVEL SECURITY;

    -- Enable RLS for contact_relationships table
    ALTER TABLE contact_relationships ENABLE ROW LEVEL SECURITY;

    -- Enable RLS for contact_history table
    ALTER TABLE contact_history ENABLE ROW LEVEL SECURITY;
    
    -- Enable RLS for automation.drafts table
    ALTER TABLE automation.drafts ENABLE ROW LEVEL SECURITY;
END;
$$ LANGUAGE plpgsql;
