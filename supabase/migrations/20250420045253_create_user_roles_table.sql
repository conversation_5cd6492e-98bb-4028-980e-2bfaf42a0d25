-- Create user_roles table and policies

-- Create the user_roles table
CREATE TABLE user_roles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) NOT NULL,
  role TEXT NOT NULL CHECK (role IN ('Director', 'Coordinator', 'SocialWorker')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id)
);

-- Create a function to check if a user is a Director
CREATE OR REPLACE FUNCTION public.is_director()
RETURNS BOOLEAN AS $$
DECLARE
  is_director BOOLEAN;
BEGIN
  SELECT EXISTS (
    SELECT 1 FROM user_roles
    WHERE user_id = auth.uid()
    AND role = 'Director'
  ) INTO is_director;

  RETUR<PERSON> is_director;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Enable Row Level Security
ALTER TABLE user_roles ENABLE ROW LEVEL SECURITY;

-- Users can read their own role
CREATE POLICY "Users can read their own role"
  ON user_roles
  FOR SELECT
  USING (user_id = auth.uid());

-- Only Directors can manage roles
CREATE POLICY "Only Directors can manage roles"
  ON user_roles
  FOR ALL
  USING (public.is_director());