-- Create extension for UUID generation if not exists
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Organizations table
CREATE TABLE organizations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  status TEXT NOT NULL CHECK (status IN ('active', 'inactive', 'suspended')) DEFAULT 'active',
  phones JSONB DEFAULT '{}',  -- Format: {"home": "************", "work": "************"}
  emails JSONB DEFAULT '{}',  -- Format: {"main": "<EMAIL>", "support": "<EMAIL>"}
  address TEXT,               -- Full address as a single string
  website_url TEXT,
  logo_url TEXT,
  settings JSONB DEFAULT '{}', -- Organization settings
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Locations table
CREATE TABLE locations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  organization_id UUID REFERENCES organizations(id) NOT NULL,
  name TEXT NOT NULL,
  status TEXT NOT NULL CHECK (status IN ('active', 'inactive')) DEFAULT 'active',
  phones JSONB DEFAULT '{}',  -- Format: {"main": "************", "fax": "************"}
  emails JSONB DEFAULT '{}',  -- Format: {"info": "<EMAIL>"}
  address TEXT,               -- Full address as a single string
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Rooms table
CREATE TABLE rooms (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  location_id UUID REFERENCES locations(id) NOT NULL,
  organization_id UUID REFERENCES organizations(id) NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  capacity INTEGER,
  status TEXT NOT NULL CHECK (status IN ('active', 'inactive', 'maintenance')) DEFAULT 'active',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Business hours table
CREATE TABLE business_hours (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  organization_id UUID REFERENCES organizations(id) NOT NULL,
  location_id UUID REFERENCES locations(id),
  day_of_week INTEGER NOT NULL CHECK (day_of_week BETWEEN 0 AND 6),
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  is_closed BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  CONSTRAINT valid_time_range CHECK (start_time < end_time)
);

-- Add indexes for foreign keys and frequently queried fields
CREATE INDEX idx_locations_org_id ON locations(organization_id);
CREATE INDEX idx_rooms_loc_id ON rooms(location_id);
CREATE INDEX idx_rooms_org_id ON rooms(organization_id);
CREATE INDEX idx_business_hours_org_id ON business_hours(organization_id);
CREATE INDEX idx_business_hours_loc_id ON business_hours(location_id);

-- Enable Row Level Security (RLS) on all tables
-- RLS ensures that users can only access data from their own organization
-- This is a critical component of the multi-tenant architecture
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE rooms ENABLE ROW LEVEL SECURITY;
ALTER TABLE business_hours ENABLE ROW LEVEL SECURITY;

-- Create a function to check if a user belongs to an organization
-- This function is used by RLS policies to determine if a user has access to an organization's data
-- It checks the user_roles table to see if the current user has any role in the specified organization
-- SECURITY DEFINER ensures the function runs with the privileges of the creator, not the caller
CREATE OR REPLACE FUNCTION public.user_belongs_to_organization(org_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  belongs BOOLEAN;
BEGIN
  SELECT EXISTS (
    SELECT 1 FROM user_roles
    WHERE user_id = auth.uid()  -- auth.uid() returns the ID of the currently authenticated user
    AND organization_id = org_id
  ) INTO belongs;

  RETURN belongs;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to check if a user is a Director of a specific organization
-- This function is used by RLS policies to determine if a user has Director-level permissions
-- Directors can manage (create, update, delete) data for their own organization
-- Other roles (Coordinator, SocialWorker) typically have read-only access
-- SECURITY DEFINER ensures the function runs with the privileges of the creator, not the caller
CREATE OR REPLACE FUNCTION public.user_is_director_of_organization(org_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  is_director BOOLEAN;
BEGIN
  SELECT EXISTS (
    SELECT 1 FROM user_roles
    WHERE user_id = auth.uid()  -- auth.uid() returns the ID of the currently authenticated user
    AND organization_id = org_id
    AND role = 'Director'  -- Only Directors have management permissions
  ) INTO is_director;

  RETURN is_director;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Organizations table policies
-- These policies control access to the organizations table based on user role and organization membership

-- All users can view organizations they belong to
CREATE POLICY "Users can view their own organization"
  ON organizations FOR SELECT
  USING (public.user_belongs_to_organization(id));

-- Only Directors can update their own organization's information
CREATE POLICY "Directors can update their own organization"
  ON organizations FOR UPDATE
  USING (public.user_is_director_of_organization(id));

-- Only Directors can insert new organizations (typically only for their own organization)
CREATE POLICY "Directors can insert organizations"
  ON organizations FOR INSERT
  WITH CHECK (public.user_is_director_of_organization(id));

-- No one can delete organizations through the API (this requires admin intervention)
CREATE POLICY "Users cannot delete organizations"
  ON organizations FOR DELETE
  USING (false);

-- Locations table policies
-- These policies control access to the locations table based on user role and organization membership

-- All users can view locations belonging to their organization
CREATE POLICY "Users can view their organization locations"
  ON locations FOR SELECT
  USING (public.user_belongs_to_organization(organization_id));

-- Only Directors can manage (create, update, delete) locations for their organization
-- The FOR ALL clause applies this policy to all operations (INSERT, UPDATE, DELETE)
CREATE POLICY "Directors can manage their organization locations"
  ON locations FOR ALL
  USING (public.user_is_director_of_organization(organization_id));

-- Rooms table policies
-- These policies control access to the rooms table based on user role and organization membership

-- All users can view rooms belonging to their organization
CREATE POLICY "Users can view their organization rooms"
  ON rooms FOR SELECT
  USING (public.user_belongs_to_organization(organization_id));

-- Only Directors can manage (create, update, delete) rooms for their organization
-- The FOR ALL clause applies this policy to all operations (INSERT, UPDATE, DELETE)
CREATE POLICY "Directors can manage their organization rooms"
  ON rooms FOR ALL
  USING (public.user_is_director_of_organization(organization_id));

-- Business hours table policies
-- These policies control access to the business_hours table based on user role and organization membership

-- All users can view business hours for their organization
CREATE POLICY "Users can view their organization business hours"
  ON business_hours FOR SELECT
  USING (public.user_belongs_to_organization(organization_id));

-- Only Directors can manage (create, update, delete) business hours for their organization
-- The FOR ALL clause applies this policy to all operations (INSERT, UPDATE, DELETE)
CREATE POLICY "Directors can manage their organization business hours"
  ON business_hours FOR ALL
  USING (public.user_is_director_of_organization(organization_id));