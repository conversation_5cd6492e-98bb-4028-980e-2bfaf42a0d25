-- Create contacts table
CREATE TABLE contacts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  organization_id UUID NOT NULL REFERENCES organizations(id),
  name TEXT NOT NULL,
  email JSONB DEFAULT '{}',  -- Format: {"personal": "<EMAIL>", "work": "<EMAIL>"}
  phone JSONB DEFAULT '{}',  -- Format: {"mobile": "************", "home": "************"}
  address TEXT,
  status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX contacts_organization_id_idx ON contacts(organization_id);
CREATE INDEX contacts_name_idx ON contacts(name);
CREATE INDEX contacts_status_idx ON contacts(status);

-- Add validation constraints for JSONB fields
ALTER TABLE contacts ADD CONSTRAINT validate_contact_emails
  CHECK (public.validate_emails_jsonb(email));

ALTER TABLE contacts ADD CONSTRAINT validate_contact_phones
  CHECK (public.validate_phones_jsonb(phone));

-- Create contact relationships table
CREATE TABLE contact_relationships (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  organization_id UUID NOT NULL REFERENCES organizations(id),
  subject_contact_id UUID NOT NULL REFERENCES contacts(id),
  related_contact_id UUID NOT NULL REFERENCES contacts(id),
  relationship TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  -- Ensure subject_contact_id and related_contact_id are not the same
  CONSTRAINT different_contacts CHECK (subject_contact_id <> related_contact_id)
);

-- Create indexes for contact relationships
CREATE INDEX contact_relationships_organization_id_idx ON contact_relationships(organization_id);
CREATE INDEX contact_relationships_subject_contact_id_idx ON contact_relationships(subject_contact_id);
CREATE INDEX contact_relationships_related_contact_id_idx ON contact_relationships(related_contact_id);
CREATE INDEX contact_relationships_relationship_idx ON contact_relationships(relationship);
CREATE INDEX contact_relationships_status_idx ON contact_relationships(status);

-- Create contact history table for audit tracking
CREATE TABLE contact_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  organization_id UUID NOT NULL REFERENCES organizations(id),
  contact_id UUID NOT NULL REFERENCES contacts(id),
  user_id UUID NOT NULL REFERENCES auth.users(id),
  action TEXT NOT NULL,
  changes JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for contact history
CREATE INDEX contact_history_organization_id_idx ON contact_history(organization_id);
CREATE INDEX contact_history_contact_id_idx ON contact_history(contact_id);
CREATE INDEX contact_history_user_id_idx ON contact_history(user_id);
CREATE INDEX contact_history_action_idx ON contact_history(action);

-- Create history tracking function
CREATE OR REPLACE FUNCTION contact_history_trigger_function()
RETURNS TRIGGER AS $$
DECLARE
  current_user_id UUID;
BEGIN
  -- Try to get the current user ID, default to a system user ID if not available
  BEGIN
    current_user_id := auth.uid();
  EXCEPTION WHEN OTHERS THEN
    -- Use a default system user ID when auth.uid() is not available (e.g., during migrations)
    current_user_id := '00000000-0000-0000-0000-000000000000'::UUID;
  END;

  IF TG_OP = 'INSERT' THEN
    INSERT INTO contact_history (organization_id, contact_id, user_id, action, changes)
    VALUES (NEW.organization_id, NEW.id, current_user_id, 'INSERT', to_jsonb(NEW));
  ELSIF TG_OP = 'UPDATE' THEN
    INSERT INTO contact_history (organization_id, contact_id, user_id, action, changes)
    VALUES (NEW.organization_id, NEW.id, current_user_id, 'UPDATE', jsonb_build_object(
      'old', to_jsonb(OLD),
      'new', to_jsonb(NEW)
    ));
  ELSIF TG_OP = 'DELETE' THEN
    INSERT INTO contact_history (organization_id, contact_id, user_id, action, changes)
    VALUES (OLD.organization_id, OLD.id, current_user_id, 'DELETE', to_jsonb(OLD));
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for history tracking
CREATE TRIGGER contact_after_insert
AFTER INSERT ON contacts
FOR EACH ROW EXECUTE FUNCTION contact_history_trigger_function();

CREATE TRIGGER contact_after_update
AFTER UPDATE ON contacts
FOR EACH ROW EXECUTE FUNCTION contact_history_trigger_function();

CREATE TRIGGER contact_after_delete
AFTER DELETE ON contacts
FOR EACH ROW EXECUTE FUNCTION contact_history_trigger_function();