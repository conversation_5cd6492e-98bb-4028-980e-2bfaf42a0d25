-- Enable <PERSON><PERSON> on contacts table
ALTER TABLE contacts ENABLE ROW LEVEL SECURITY;

-- Enable <PERSON><PERSON> on contact_relationships table
ALTER TABLE contact_relationships ENABLE ROW LEVEL SECURITY;

-- Enable <PERSON><PERSON> on contact_history table
ALTER TABLE contact_history ENABLE ROW LEVEL SECURITY;

-- Policy for contacts table - all operations
CREATE POLICY contacts_organization_isolation ON contacts
  FOR ALL
  USING (organization_id IN (
    SELECT organization_id FROM user_roles WHERE user_id = auth.uid()
  ));

-- Policy for contact_relationships table - all operations
CREATE POLICY contact_relationships_organization_isolation ON contact_relationships
  FOR ALL
  USING (organization_id IN (
    SELECT organization_id FROM user_roles WHERE user_id = auth.uid()
  ));

-- Policy for contact_history table - all operations
CREATE POLICY contact_history_organization_isolation ON contact_history
  FOR ALL
  USING (organization_id IN (
    SELECT organization_id FROM user_roles WHERE user_id = auth.uid()
  ));

-- Ensure users cannot change the organization_id of a contact
CREATE POLICY contacts_no_org_change ON contacts
  FOR UPDATE
  USING (
    organization_id IN (
      SELECT organization_id FROM user_roles WHERE user_id = auth.uid()
    )
  );

-- Ensure users can only insert contacts for their own organization
CREATE POLICY contacts_insert_org_check ON contacts
  FOR INSERT
  WITH CHECK (
    organization_id IN (
      SELECT organization_id FROM user_roles WHERE user_id = auth.uid()
    )
  );

-- Ensure users cannot change the organization_id of a contact relationship
CREATE POLICY contact_relationships_no_org_change ON contact_relationships
  FOR UPDATE
  USING (
    organization_id IN (
      SELECT organization_id FROM user_roles WHERE user_id = auth.uid()
    )
  );

-- Ensure users can only insert contact relationships for their own organization
CREATE POLICY contact_relationships_insert_org_check ON contact_relationships
  FOR INSERT
  WITH CHECK (
    organization_id IN (
      SELECT organization_id FROM user_roles WHERE user_id = auth.uid()
    )
  );

-- Ensure users cannot change the organization_id of a contact history record
CREATE POLICY contact_history_no_org_change ON contact_history
  FOR UPDATE
  USING (
    organization_id IN (
      SELECT organization_id FROM user_roles WHERE user_id = auth.uid()
    )
  );

-- Ensure users can only insert contact history records for their own organization
CREATE POLICY contact_history_insert_org_check ON contact_history
  FOR INSERT
  WITH CHECK (
    organization_id IN (
      SELECT organization_id FROM user_roles WHERE user_id = auth.uid()
    )
  );