-- Create validation function for contact history changes JSONB structure
CREATE OR R<PERSON>LACE FUNCTION public.validate_contact_history_changes(changes JSONB)
RETURNS BOOLEAN AS $$
DECLARE
  valid BOOLEAN := TRUE;
  action_type TEXT;
BEGIN
  -- If null, it's invalid
  IF changes IS NULL THEN
    RETURN FALSE;
  END IF;

  -- If not an object, it's invalid
  IF jsonb_typeof(changes) != 'object' THEN
    RETURN FALSE;
  END IF;

  -- Extract action type
  IF changes ? 'action' THEN
    action_type := changes->>'action';
  ELSE
    -- Action field is required
    RETURN FALSE;
  END IF;

  -- Validate based on action type
  CASE action_type
    WHEN 'INSERT' THEN
      -- Must have 'data', 'timestamp', and 'user_id' fields
      IF NOT (changes ? 'data' AND changes ? 'timestamp' AND changes ? 'user_id') THEN
        RETURN FALSE;
      END IF;

      -- 'data' must be an object
      IF jsonb_typeof(changes->'data') != 'object' THEN
        RETURN FALSE;
      END IF;

    WHEN 'UPDATE' THEN
      -- Must have 'old', 'new', 'changed_fields', 'timestamp', and 'user_id' fields
      IF NOT (changes ? 'old' AND changes ? 'new' AND changes ? 'changed_fields' AND
              changes ? 'timestamp' AND changes ? 'user_id') THEN
        RETURN FALSE;
      END IF;

      -- 'old' and 'new' must be objects
      IF jsonb_typeof(changes->'old') != 'object' OR jsonb_typeof(changes->'new') != 'object' THEN
        RETURN FALSE;
      END IF;

      -- 'changed_fields' must be an array
      IF jsonb_typeof(changes->'changed_fields') != 'array' THEN
        RETURN FALSE;
      END IF;

    WHEN 'DELETE' THEN
      -- Must have 'data', 'timestamp', and 'user_id' fields
      IF NOT (changes ? 'data' AND changes ? 'timestamp' AND changes ? 'user_id') THEN
        RETURN FALSE;
      END IF;

      -- 'data' must be an object
      IF jsonb_typeof(changes->'data') != 'object' THEN
        RETURN FALSE;
      END IF;

    ELSE
      -- Invalid action type
      RETURN FALSE;
  END CASE;

  RETURN valid;
END;
$$ LANGUAGE plpgsql;

-- Add comment explaining the function
COMMENT ON FUNCTION public.validate_contact_history_changes(JSONB) IS 'Validates that a JSONB object contains a valid contact history changes structure';

-- Add constraint to contact_history table
ALTER TABLE contact_history ADD CONSTRAINT validate_contact_history_changes
  CHECK (public.validate_contact_history_changes(changes));

-- Update the contact history trigger function to use the standardized structure
CREATE OR REPLACE FUNCTION contact_history_trigger_function()
RETURNS TRIGGER AS $$
DECLARE
  current_user_id UUID;
  changed_fields JSONB := '[]'::JSONB;
BEGIN
  -- Try to get the current user ID, default to a system user ID if not available
  BEGIN
    current_user_id := auth.uid();
  EXCEPTION WHEN OTHERS THEN
    -- Use a default system user ID when auth.uid() is not available (e.g., during migrations)
    current_user_id := '00000000-0000-0000-0000-000000000000'::UUID;
  END;

  IF TG_OP = 'INSERT' THEN
    INSERT INTO contact_history (organization_id, contact_id, user_id, action, changes)
    VALUES (
      NEW.organization_id,
      NEW.id,
      current_user_id,
      'INSERT',
      jsonb_build_object(
        'action', 'INSERT',
        'data', to_jsonb(NEW),
        'timestamp', now(),
        'user_id', current_user_id
      )
    );
  ELSIF TG_OP = 'UPDATE' THEN
    -- Build array of changed field names
    SELECT jsonb_agg(key)
    INTO changed_fields
    FROM (
      SELECT key
      FROM jsonb_each(to_jsonb(NEW))
      WHERE to_jsonb(NEW) -> key <> to_jsonb(OLD) -> key
    ) t;

    INSERT INTO contact_history (organization_id, contact_id, user_id, action, changes)
    VALUES (
      NEW.organization_id,
      NEW.id,
      current_user_id,
      'UPDATE',
      jsonb_build_object(
        'action', 'UPDATE',
        'old', to_jsonb(OLD),
        'new', to_jsonb(NEW),
        'changed_fields', changed_fields,
        'timestamp', now(),
        'user_id', current_user_id
      )
    );
  ELSIF TG_OP = 'DELETE' THEN
    INSERT INTO contact_history (organization_id, contact_id, user_id, action, changes)
    VALUES (
      OLD.organization_id,
      OLD.id,
      current_user_id,
      'DELETE',
      jsonb_build_object(
        'action', 'DELETE',
        'data', to_jsonb(OLD),
        'timestamp', now(),
        'user_id', current_user_id
      )
    );
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;