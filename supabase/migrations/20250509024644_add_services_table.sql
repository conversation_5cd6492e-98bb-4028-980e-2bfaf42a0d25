-- Create services table for organization profile management
-- This migration adds the services table to store organization services

-- Services table
CREATE TABLE services (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  organization_id UUID REFERENCES organizations(id) NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  price DECIMAL(10, 2) NOT NULL DEFAULT 0.00,
  duration INTEGER NOT NULL DEFAULT 60, -- Duration in minutes
  status TEXT NOT NULL CHECK (status IN ('active', 'inactive')) DEFAULT 'active',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add index for organization_id
CREATE INDEX idx_services_org_id ON services(organization_id);

-- Enable Row Level Security (RLS)
ALTER TABLE services ENABLE ROW LEVEL SECURITY;

-- Create trigger for updated_at
CREATE TRIGGER update_services_updated_at
BEFORE UPDATE ON services
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

-- RLS Policies for services table

-- All users can view services for their organization
CREATE POLICY "Users can view their organization services"
  ON services FOR SELECT
  USING (public.user_belongs_to_organization(organization_id));

-- Only Directors can manage services for their organization
CREATE POLICY "Directors can manage their organization services"
  ON services FOR ALL
  USING (public.user_is_director_of_organization(organization_id));

-- SystemAdmins can view all services
CREATE POLICY "SystemAdmins can view all services"
  ON services FOR SELECT
  USING (public.is_system_admin());

-- SystemAdmins can manage all services
CREATE POLICY "SystemAdmins can manage all services"
  ON services FOR ALL
  USING (public.is_system_admin());

-- Add comment to the table
COMMENT ON TABLE services IS 'Stores services offered by organizations';