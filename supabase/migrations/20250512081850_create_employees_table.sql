-- Create employees table for employee management
-- This migration adds the employees table to store employee data

-- Employees table
CREATE TABLE employees (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  organization_id UUID REFERENCES organizations(id) NOT NULL,
  user_account_id UUID REFERENCES auth.users(id),
  first_name TEXT NOT NULL,
  last_name TEXT NOT NULL,
  profile_image TEXT,
  date_of_birth DATE,
  gender TEXT,
  address JSONB, -- Format: {"street": "123 Main St", "city": "Montreal", "state": "QC", "postal_code": "H1A 1A1", "country": "Canada"}
  employee_id TEXT,
  hire_date DATE,
  termination_date DATE,
  employment_status TEXT NOT NULL CHECK (employment_status IN ('active', 'inactive', 'terminated')) DEFAULT 'active',
  job_title TEXT,
  department TEXT,
  supervisor_id UUID REFERENCES employees(id),
  specializations TEXT[],
  certifications JSONB[], -- Array of certification objects
  education JSONB[], -- Array of education objects
  emails JSONB[], -- Array of email objects
  phones JSONB[], -- Array of phone objects
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for foreign keys and frequently queried fields
CREATE INDEX idx_employees_org_id ON employees(organization_id);
CREATE INDEX idx_employees_user_account_id ON employees(user_account_id);
CREATE INDEX idx_employees_supervisor_id ON employees(supervisor_id);
CREATE INDEX idx_employees_employment_status ON employees(employment_status);

-- Enable Row Level Security (RLS) on employees table
ALTER TABLE employees ENABLE ROW LEVEL SECURITY;

-- Create trigger for updated_at
CREATE TRIGGER update_employees_updated_at
BEFORE UPDATE ON employees
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

-- RLS Policies for employees table

-- All users can view employees for their organization
CREATE POLICY "Users can view their organization employees"
  ON employees FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM user_roles
      WHERE user_roles.user_id = auth.uid()
      AND user_roles.organization_id = employees.organization_id
    )
  );

-- Directors can manage employees for their organization
CREATE POLICY "Directors can manage their organization employees"
  ON employees FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM user_roles
      WHERE user_roles.user_id = auth.uid()
      AND user_roles.organization_id = employees.organization_id
      AND user_roles.role = 'Director'
    )
  );

-- SystemAdmins can view all employees
CREATE POLICY "SystemAdmins can view all employees"
  ON employees FOR SELECT
  USING (public.is_system_admin());

-- SystemAdmins can manage all employees
CREATE POLICY "SystemAdmins can manage all employees"
  ON employees FOR ALL
  USING (public.is_system_admin());

-- Comment on table and columns
COMMENT ON TABLE employees IS 'Stores employee information for organizations';
COMMENT ON COLUMN employees.id IS 'Primary key for the employee';
COMMENT ON COLUMN employees.organization_id IS 'Foreign key to the organization the employee belongs to';
COMMENT ON COLUMN employees.user_account_id IS 'Foreign key to the user account if the employee has system access';
COMMENT ON COLUMN employees.employment_status IS 'Current employment status (active, inactive, terminated)';
COMMENT ON COLUMN employees.address IS 'JSON object containing address components';
COMMENT ON COLUMN employees.certifications IS 'Array of JSON objects containing certification details';
COMMENT ON COLUMN employees.education IS 'Array of JSON objects containing education history';
COMMENT ON COLUMN employees.emails IS 'Array of JSON objects containing email addresses';
COMMENT ON COLUMN employees.phones IS 'Array of JSON objects containing phone numbers';