-- Update the handle_new_user function to include organization_id
CREATE OR <PERSON><PERSON><PERSON><PERSON> FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
  org_id UUID;
BEGIN
  -- Extract organization_id from user metadata if available
  org_id := (NEW.raw_user_meta_data->>'organization_id')::UUID;

  INSERT INTO public.user_profiles (
    id,
    first_name,
    last_name,
    email,
    phone,
    language,
    organization_id
  )
  VALUES (
    NEW.id,
    COALESCE(NEW.raw_user_meta_data->>'first_name', ''),
    COALESCE(NEW.raw_user_meta_data->>'last_name', ''),
    NEW.email,
    NEW.raw_user_meta_data->>'phone',
    COALESCE(NEW.raw_user_meta_data->>'language', 'fr'),
    org_id
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- No need to recreate the trigger as it's already pointing to the function
-- and will use the updated version automatically