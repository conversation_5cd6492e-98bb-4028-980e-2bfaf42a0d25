-- Create validation functions for JSONB structures

-- Function to validate contact emails JSONB structure
-- Expected format: {"type": "<EMAIL>", ...}
-- Example: {"main": "<EMAIL>", "support": "<EMAIL>"}
CREATE OR REPLACE FUNCTION public.validate_emails_jsonb(emails JSONB)
RETURNS BOOLEAN AS $$
DECLARE
  email_regex TEXT := '^[A-Za-z0-9._%-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,4}$';
  key TEXT;
  val TEXT;
  valid BOOLEAN := TRUE;
BEGIN
  -- If null, consider it valid (for optional fields)
  IF emails IS NULL THEN
    RETURN TRUE;
  END IF;

  -- If not an object, it's invalid
  IF jsonb_typeof(emails) != 'object' THEN
    RETURN FALSE;
  END IF;

  -- Check each key-value pair
  FOR key, val IN SELECT * FROM jsonb_each_text(emails) LOOP
    -- Validate email format using regex
    IF val !~ email_regex THEN
      valid := FALSE;
      EXIT;
    END IF;
  END LOOP;

  RETURN valid;
END;
$$ LANGUAGE plpgsql;

-- Add comment explaining the function
COMMENT ON FUNCTION public.validate_emails_jsonb(JSONB) IS 'Validates that a JSONB object contains valid email addresses as values';

-- Function to validate contact phones JSONB structure
-- Expected format: {"type": "phone_number", ...}
-- Example: {"main": "************", "fax": "************"}
CREATE OR REPLACE FUNCTION public.validate_phones_jsonb(phones JSONB)
RETURNS BOOLEAN AS $$
DECLARE
  -- Simple regex for phone numbers (can be made more specific if needed)
  phone_regex TEXT := '^[0-9\(\)\-\+\s\.]{7,20}$';
  key TEXT;
  val TEXT;
  valid BOOLEAN := TRUE;
BEGIN
  -- If null, consider it valid (for optional fields)
  IF phones IS NULL THEN
    RETURN TRUE;
  END IF;

  -- If not an object, it's invalid
  IF jsonb_typeof(phones) != 'object' THEN
    RETURN FALSE;
  END IF;

  -- Check each key-value pair
  FOR key, val IN SELECT * FROM jsonb_each_text(phones) LOOP
    -- Validate phone format using regex
    IF val !~ phone_regex THEN
      valid := FALSE;
      EXIT;
    END IF;
  END LOOP;

  RETURN valid;
END;
$$ LANGUAGE plpgsql;

-- Add comment explaining the function
COMMENT ON FUNCTION public.validate_phones_jsonb(JSONB) IS 'Validates that a JSONB object contains valid phone numbers as values';

-- Function to validate organization settings JSONB structure
-- Expected format: Complex nested structure with theme and features
-- Example: {"theme": {"primary": "#4B9CD3"}, "features": {"scheduling": true}}
CREATE OR REPLACE FUNCTION public.validate_org_settings_jsonb(settings JSONB)
RETURNS BOOLEAN AS $$
DECLARE
  valid BOOLEAN := TRUE;
BEGIN
  -- If null, consider it valid (for optional fields)
  IF settings IS NULL THEN
    RETURN TRUE;
  END IF;

  -- If not an object, it's invalid
  IF jsonb_typeof(settings) != 'object' THEN
    RETURN FALSE;
  END IF;

  -- Check for theme object if it exists
  IF settings ? 'theme' THEN
    -- Theme should be an object
    IF jsonb_typeof(settings->'theme') != 'object' THEN
      RETURN FALSE;
    END IF;
  END IF;

  -- Check for features object if it exists
  IF settings ? 'features' THEN
    -- Features should be an object
    IF jsonb_typeof(settings->'features') != 'object' THEN
      RETURN FALSE;
    END IF;
  END IF;

  RETURN valid;
END;
$$ LANGUAGE plpgsql;

-- Add comment explaining the function
COMMENT ON FUNCTION public.validate_org_settings_jsonb(JSONB) IS 'Validates that a JSONB object contains a valid organization settings structure';

-- Add check constraints to tables using these validation functions

-- Organizations table
ALTER TABLE organizations ADD CONSTRAINT validate_org_emails
  CHECK (public.validate_emails_jsonb(emails));

ALTER TABLE organizations ADD CONSTRAINT validate_org_phones
  CHECK (public.validate_phones_jsonb(phones));

ALTER TABLE organizations ADD CONSTRAINT validate_org_settings
  CHECK (public.validate_org_settings_jsonb(settings));

-- Locations table
ALTER TABLE locations ADD CONSTRAINT validate_location_emails
  CHECK (public.validate_emails_jsonb(emails));

ALTER TABLE locations ADD CONSTRAINT validate_location_phones
  CHECK (public.validate_phones_jsonb(phones));