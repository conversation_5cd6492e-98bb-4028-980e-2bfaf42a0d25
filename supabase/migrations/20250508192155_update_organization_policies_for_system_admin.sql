-- Update organization-related RLS policies for SystemAdmin
-- This migration updates the RLS policies for organizations, locations, rooms, and business_hours
-- to allow System<PERSON>dmins to manage all organizations

-- 1. Update organizations table policies

-- Allow SystemAdmins to view all organizations
CREATE POLICY "SystemAdmins can view all organizations"
  ON organizations FOR SELECT
  USING (public.is_system_admin());

-- Allow SystemAdmins to update any organization
CREATE POLICY "SystemAdmins can update any organization"
  ON organizations FOR UPDATE
  USING (public.is_system_admin());

-- Allow SystemAdmins to insert new organizations
CREATE POLICY "SystemAdmins can insert organizations"
  ON organizations FOR INSERT
  WITH CHECK (public.is_system_admin());

-- 2. Update locations table policies

-- Allow SystemAdmins to view all locations
CREATE POLICY "SystemAdmins can view all locations"
  ON locations FOR SELECT
  USING (public.is_system_admin());

-- Allow SystemAdmins to manage all locations
CREATE POLICY "SystemAdmins can manage all locations"
  ON locations FOR ALL
  USING (public.is_system_admin());

-- 3. Update rooms table policies

-- Allow SystemAdmins to view all rooms
CREATE POLICY "SystemAdmins can view all rooms"
  ON rooms FOR SELECT
  USING (public.is_system_admin());

-- Allow SystemAdmins to manage all rooms
CREATE POLICY "SystemAdmins can manage all rooms"
  ON rooms FOR ALL
  USING (public.is_system_admin());

-- 4. Update business_hours table policies

-- Allow SystemAdmins to view all business hours
CREATE POLICY "SystemAdmins can view all business hours"
  ON business_hours FOR SELECT
  USING (public.is_system_admin());

-- Allow SystemAdmins to manage all business hours
CREATE POLICY "SystemAdmins can manage all business hours"
  ON business_hours FOR ALL
  USING (public.is_system_admin());
