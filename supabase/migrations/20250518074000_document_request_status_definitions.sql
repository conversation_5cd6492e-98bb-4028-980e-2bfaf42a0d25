-- Add comment to document the status field in the requests table
COMMENT ON COLUMN public.requests.status IS 'Request status. Possible values:
- draft: Initial state, request is being prepared and not yet submitted
- requested: Request has been submitted and is awaiting review
- waitlist: Request has been reviewed and placed on a waitlist
- approved: Request has been approved and is ready for scheduling
- rejected: Request has been rejected
- completed: Request has been fulfilled and is now complete';

-- Create a table to define request status transitions and requirements
CREATE TABLE IF NOT EXISTS public.request_status_definitions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    status TEXT UNIQUE NOT NULL,
    description TEXT NOT NULL,
    required_fields JSONB NOT NULL,
    allowed_transitions JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Enable RLS on the status definitions table
ALTER TABLE public.request_status_definitions ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for the status definitions table
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies WHERE tablename = 'request_status_definitions' AND policyname = 'Users can view request status definitions'
    ) THEN
        CREATE POLICY "Users can view request status definitions"
            ON public.request_status_definitions FOR SELECT
            USING (true);
    END IF;
END
$$;

-- Insert status definitions
INSERT INTO public.request_status_definitions (status, description, required_fields, allowed_transitions, created_at, updated_at)
VALUES
    ('draft', 
     'Initial state, request is being prepared and not yet submitted', 
     '["title", "organization_id"]'::jsonb, 
     '["requested"]'::jsonb, 
     now(), 
     now()),
     
    ('requested', 
     'Request has been submitted and is awaiting review', 
     '["title", "description", "requester_id", "service_type"]'::jsonb, 
     '["waitlist", "approved", "rejected"]'::jsonb, 
     now(), 
     now()),
     
    ('waitlist', 
     'Request has been reviewed and placed on a waitlist', 
     '["title", "description", "requester_id", "service_type", "waitlist_position"]'::jsonb, 
     '["approved", "rejected"]'::jsonb, 
     now(), 
     now()),
     
    ('approved', 
     'Request has been approved and is ready for scheduling', 
     '["title", "description", "requester_id", "service_type", "assignee_id", "approval_date"]'::jsonb, 
     '["completed", "rejected"]'::jsonb, 
     now(), 
     now()),
     
    ('rejected', 
     'Request has been rejected', 
     '["title", "description", "requester_id", "service_type", "rejection_reason", "rejection_date"]'::jsonb, 
     '[]'::jsonb, 
     now(), 
     now()),
     
    ('completed', 
     'Request has been fulfilled and is now complete', 
     '["title", "description", "requester_id", "service_type", "assignee_id", "approval_date", "completion_date"]'::jsonb, 
     '[]'::jsonb, 
     now(), 
     now())
ON CONFLICT (status) DO UPDATE
SET 
    description = EXCLUDED.description,
    required_fields = EXCLUDED.required_fields,
    allowed_transitions = EXCLUDED.allowed_transitions,
    updated_at = now();

-- Create a function to validate status transitions
CREATE OR REPLACE FUNCTION public.validate_request_status_transition()
RETURNS TRIGGER AS $$
DECLARE
    allowed_transitions JSONB;
BEGIN
    -- Skip validation if status hasn't changed
    IF OLD.status = NEW.status THEN
        RETURN NEW;
    END IF;
    
    -- Get allowed transitions for the current status
    SELECT rd.allowed_transitions INTO allowed_transitions
    FROM public.request_status_definitions rd
    WHERE rd.status = OLD.status;
    
    -- Check if the new status is in the allowed transitions
    IF NOT (allowed_transitions ? NEW.status) THEN
        RAISE EXCEPTION 'Invalid status transition from % to %', OLD.status, NEW.status;
    END IF;
    
    -- Set status update timestamp and user
    NEW.status_updated_at = now();
    NEW.status_updated_by = auth.uid();
    
    -- Set specific date fields based on the new status
    IF NEW.status = 'approved' AND OLD.status != 'approved' THEN
        NEW.approval_date = now();
    ELSIF NEW.status = 'rejected' AND OLD.status != 'rejected' THEN
        NEW.rejection_date = now();
    ELSIF NEW.status = 'completed' AND OLD.status != 'completed' THEN
        NEW.completion_date = now();
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to validate status transitions
DROP TRIGGER IF EXISTS validate_request_status_transition_trigger ON public.requests;
CREATE TRIGGER validate_request_status_transition_trigger
BEFORE UPDATE OF status ON public.requests
FOR EACH ROW
EXECUTE FUNCTION public.validate_request_status_transition();
