-- Create a function to temporarily disable <PERSON><PERSON> for tests
CREATE OR REPLACE FUNCTION disable_rls_for_tests()
RETURNS VOID AS $$
BEGIN
    -- Disable RLS for contacts table
    ALTER TABLE contacts DISABLE ROW LEVEL SECURITY;

    -- Disable RLS for contact_relationships table
    ALTER TABLE contact_relationships DISABLE ROW LEVEL SECURITY;

    -- Disable RLS for contact_history table
    ALTER TABLE contact_history DISABLE ROW LEVEL SECURITY;
END;
$$ LANGUAGE plpgsql;

-- Create a function to re-enable RLS after tests
CREATE OR REPLACE FUNCTION enable_rls_for_tests()
RETURNS VOID AS $$
BEGIN
    -- Enable RLS for contacts table
    ALTER TABLE contacts ENABLE ROW LEVEL SECURITY;

    -- Enable RLS for contact_relationships table
    ALTER TABLE contact_relationships ENABLE ROW LEVEL SECURITY;

    -- Enable RLS for contact_history table
    ALTER TABLE contact_history ENABLE ROW LEVEL SECURITY;
END;
$$ LANGUAGE plpgsql;