-- Create automation_notifications table in the automation schema
CREATE TABLE automation.notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  type TEXT NOT NULL,
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  data JSONB NOT NULL DEFAULT '{}'::jsonb,
  read BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Add comment to the table
COMMENT ON TABLE automation.notifications IS 'Stores user notifications related to workflow events';

-- <PERSON><PERSON> indexes
CREATE INDEX idx_automation_notifications_user_id ON automation.notifications(user_id);
CREATE INDEX idx_automation_notifications_organization_id ON automation.notifications(organization_id);
CREATE INDEX idx_automation_notifications_type ON automation.notifications(type);
CREATE INDEX idx_automation_notifications_read ON automation.notifications(read);
CREATE INDEX idx_automation_notifications_created_at ON automation.notifications(created_at);

-- Set up Row Level Security
ALTER TABLE automation.notifications ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Users can only view their own notifications within their organization
CREATE POLICY "Users can view their own notifications"
  ON automation.notifications
  FOR SELECT
  USING (
    auth.uid() = user_id AND
    EXISTS (
      SELECT 1 FROM user_roles
      WHERE user_id = auth.uid()
      AND organization_id = automation.notifications.organization_id
    )
  );

-- Users can insert their own notifications within their organization
CREATE POLICY "Users can insert their own notifications"
  ON automation.notifications
  FOR INSERT
  WITH CHECK (
    auth.uid() = user_id AND
    EXISTS (
      SELECT 1 FROM user_roles
      WHERE user_id = auth.uid()
      AND organization_id = automation.notifications.organization_id
    )
  );

-- Users can update their own notifications within their organization
CREATE POLICY "Users can update their own notifications"
  ON automation.notifications
  FOR UPDATE
  USING (
    auth.uid() = user_id AND
    EXISTS (
      SELECT 1 FROM user_roles
      WHERE user_id = auth.uid()
      AND organization_id = automation.notifications.organization_id
    )
  );

-- Users can delete their own notifications within their organization
CREATE POLICY "Users can delete their own notifications"
  ON automation.notifications
  FOR DELETE
  USING (
    auth.uid() = user_id AND
    EXISTS (
      SELECT 1 FROM user_roles
      WHERE user_id = auth.uid()
      AND organization_id = automation.notifications.organization_id
    )
  );

-- System admins can view all notifications
CREATE POLICY "System admins can view all notifications"
  ON automation.notifications
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM user_roles
      WHERE user_id = auth.uid()
      AND role = 'SystemAdmin'
    )
  );

-- Directors can view all notifications within their organization
CREATE POLICY "Directors can view all notifications within their organization"
  ON automation.notifications
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM user_roles
      WHERE user_id = auth.uid()
      AND role = 'Director'
      AND organization_id = automation.notifications.organization_id
    )
  );

-- Add updated_at trigger
CREATE TRIGGER update_automation_notifications_updated_at
  BEFORE UPDATE ON automation.notifications
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();
