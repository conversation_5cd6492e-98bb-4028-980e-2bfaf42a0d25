-- Create request table
CREATE TABLE IF NOT EXISTS public.requests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    description TEXT,
    status TEXT NOT NULL DEFAULT 'draft',
    requester_id UUID REFERENCES auth.users(id),
    assignee_id UUID REFERENCES auth.users(id),
    priority TEXT,
    service_type TEXT,
    rejection_reason TEXT,
    waitlist_position INTEGER,
    approval_date TIMESTAMP WITH TIME ZONE,
    rejection_date TIMESTAMP WITH TIME ZONE,
    completion_date TIMESTAMP WITH TIME ZONE,
    status_updated_at TIMESTAMP WITH TIME ZONE,
    status_updated_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Create indexes for foreign keys
CREATE INDEX IF NOT EXISTS idx_requests_organization_id ON public.requests(organization_id);
CREATE INDEX IF NOT EXISTS idx_requests_requester_id ON public.requests(requester_id);
CREATE INDEX IF NOT EXISTS idx_requests_assignee_id ON public.requests(assignee_id);
CREATE INDEX IF NOT EXISTS idx_requests_status ON public.requests(status);

-- Create request history table
CREATE TABLE IF NOT EXISTS public.request_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    request_id UUID NOT NULL REFERENCES public.requests(id) ON DELETE CASCADE,
    previous_status TEXT,
    new_status TEXT,
    changes JSONB,
    action TEXT NOT NULL,
    user_id UUID REFERENCES auth.users(id),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Create index for request_id
CREATE INDEX IF NOT EXISTS idx_request_history_request_id ON public.request_history(request_id);
CREATE INDEX IF NOT EXISTS idx_request_history_user_id ON public.request_history(user_id);

-- Create request metadata table
CREATE TABLE IF NOT EXISTS public.request_metadata (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    request_id UUID NOT NULL REFERENCES public.requests(id) ON DELETE CASCADE,
    court_judgment_reference TEXT,
    court_judgment_date DATE,
    court_judgment_details TEXT,
    court_judgment_document_url TEXT,
    service_requirements JSONB DEFAULT '{}'::JSONB,
    family_availability JSONB DEFAULT '{}'::JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    UNIQUE(request_id)
);

-- Create indexes for request_metadata
CREATE INDEX IF NOT EXISTS idx_request_metadata_request_id ON public.request_metadata(request_id);
CREATE INDEX IF NOT EXISTS idx_request_metadata_service_requirements ON public.request_metadata USING GIN (service_requirements);
CREATE INDEX IF NOT EXISTS idx_request_metadata_family_availability ON public.request_metadata USING GIN (family_availability);

-- Create a junction table for request-contact relationships
CREATE TABLE IF NOT EXISTS public.request_contacts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    request_id UUID NOT NULL REFERENCES public.requests(id) ON DELETE CASCADE,
    contact_id UUID NOT NULL REFERENCES public.contacts(id) ON DELETE CASCADE,
    relationship_type TEXT NOT NULL,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    UNIQUE(request_id, contact_id, relationship_type)
);

-- Create indexes for the junction table
CREATE INDEX IF NOT EXISTS idx_request_contacts_request_id ON public.request_contacts(request_id);
CREATE INDEX IF NOT EXISTS idx_request_contacts_contact_id ON public.request_contacts(contact_id);
CREATE INDEX IF NOT EXISTS idx_request_contacts_relationship_type ON public.request_contacts(relationship_type);

-- Enable Row Level Security
ALTER TABLE public.requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.request_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.request_metadata ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.request_contacts ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for requests
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies WHERE tablename = 'requests' AND policyname = 'Users can view requests in their organization'
    ) THEN
        CREATE POLICY "Users can view requests in their organization"
            ON public.requests FOR SELECT
            USING (organization_id IN (
                SELECT organization_id FROM public.user_roles WHERE user_id = auth.uid()
            ));
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policies WHERE tablename = 'requests' AND policyname = 'Users can insert requests in their organization'
    ) THEN
        CREATE POLICY "Users can insert requests in their organization"
            ON public.requests FOR INSERT
            WITH CHECK (organization_id IN (
                SELECT organization_id FROM public.user_roles WHERE user_id = auth.uid()
            ));
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policies WHERE tablename = 'requests' AND policyname = 'Users can update requests in their organization'
    ) THEN
        CREATE POLICY "Users can update requests in their organization"
            ON public.requests FOR UPDATE
            USING (organization_id IN (
                SELECT organization_id FROM public.user_roles WHERE user_id = auth.uid()
            ));
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policies WHERE tablename = 'requests' AND policyname = 'Users can delete requests in their organization'
    ) THEN
        CREATE POLICY "Users can delete requests in their organization"
            ON public.requests FOR DELETE
            USING (organization_id IN (
                SELECT organization_id FROM public.user_roles WHERE user_id = auth.uid()
            ));
    END IF;
END
$$;

-- Create RLS policies for request_history
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies WHERE tablename = 'request_history' AND policyname = 'Users can view request history in their organization'
    ) THEN
        CREATE POLICY "Users can view request history in their organization"
            ON public.request_history FOR SELECT
            USING (request_id IN (
                SELECT id FROM public.requests WHERE organization_id IN (
                    SELECT organization_id FROM public.user_roles WHERE user_id = auth.uid()
                )
            ));
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policies WHERE tablename = 'request_history' AND policyname = 'Users can insert request history in their organization'
    ) THEN
        CREATE POLICY "Users can insert request history in their organization"
            ON public.request_history FOR INSERT
            WITH CHECK (request_id IN (
                SELECT id FROM public.requests WHERE organization_id IN (
                    SELECT organization_id FROM public.user_roles WHERE user_id = auth.uid()
                )
            ));
    END IF;
END
$$;

-- Create RLS policies for request_metadata
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies WHERE tablename = 'request_metadata' AND policyname = 'Users can view request metadata in their organization'
    ) THEN
        CREATE POLICY "Users can view request metadata in their organization"
            ON public.request_metadata FOR SELECT
            USING (request_id IN (
                SELECT id FROM public.requests WHERE organization_id IN (
                    SELECT organization_id FROM public.user_roles WHERE user_id = auth.uid()
                )
            ));
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policies WHERE tablename = 'request_metadata' AND policyname = 'Users can insert request metadata in their organization'
    ) THEN
        CREATE POLICY "Users can insert request metadata in their organization"
            ON public.request_metadata FOR INSERT
            WITH CHECK (request_id IN (
                SELECT id FROM public.requests WHERE organization_id IN (
                    SELECT organization_id FROM public.user_roles WHERE user_id = auth.uid()
                )
            ));
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policies WHERE tablename = 'request_metadata' AND policyname = 'Users can update request metadata in their organization'
    ) THEN
        CREATE POLICY "Users can update request metadata in their organization"
            ON public.request_metadata FOR UPDATE
            USING (request_id IN (
                SELECT id FROM public.requests WHERE organization_id IN (
                    SELECT organization_id FROM public.user_roles WHERE user_id = auth.uid()
                )
            ));
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policies WHERE tablename = 'request_metadata' AND policyname = 'Users can delete request metadata in their organization'
    ) THEN
        CREATE POLICY "Users can delete request metadata in their organization"
            ON public.request_metadata FOR DELETE
            USING (request_id IN (
                SELECT id FROM public.requests WHERE organization_id IN (
                    SELECT organization_id FROM public.user_roles WHERE user_id = auth.uid()
                )
            ));
    END IF;
END
$$;

-- Create RLS policies for request_contacts
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies WHERE tablename = 'request_contacts' AND policyname = 'Users can view request contacts in their organization'
    ) THEN
        CREATE POLICY "Users can view request contacts in their organization"
            ON public.request_contacts FOR SELECT
            USING (request_id IN (
                SELECT id FROM public.requests WHERE organization_id IN (
                    SELECT organization_id FROM public.user_roles WHERE user_id = auth.uid()
                )
            ));
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policies WHERE tablename = 'request_contacts' AND policyname = 'Users can insert request contacts in their organization'
    ) THEN
        CREATE POLICY "Users can insert request contacts in their organization"
            ON public.request_contacts FOR INSERT
            WITH CHECK (request_id IN (
                SELECT id FROM public.requests WHERE organization_id IN (
                    SELECT organization_id FROM public.user_roles WHERE user_id = auth.uid()
                )
            ));
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policies WHERE tablename = 'request_contacts' AND policyname = 'Users can update request contacts in their organization'
    ) THEN
        CREATE POLICY "Users can update request contacts in their organization"
            ON public.request_contacts FOR UPDATE
            USING (request_id IN (
                SELECT id FROM public.requests WHERE organization_id IN (
                    SELECT organization_id FROM public.user_roles WHERE user_id = auth.uid()
                )
            ));
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policies WHERE tablename = 'request_contacts' AND policyname = 'Users can delete request contacts in their organization'
    ) THEN
        CREATE POLICY "Users can delete request contacts in their organization"
            ON public.request_contacts FOR DELETE
            USING (request_id IN (
                SELECT id FROM public.requests WHERE organization_id IN (
                    SELECT organization_id FROM public.user_roles WHERE user_id = auth.uid()
                )
            ));
    END IF;
END
$$;

-- Create a function to track request metadata changes
CREATE OR REPLACE FUNCTION public.track_request_metadata_changes()
RETURNS TRIGGER AS $$
DECLARE
    changes_json JSONB;
    action_type TEXT;
BEGIN
    -- Determine action type
    IF TG_OP = 'INSERT' THEN
        action_type := 'metadata_created';
        changes_json := to_jsonb(NEW);
    ELSIF TG_OP = 'UPDATE' THEN
        action_type := 'metadata_updated';

        -- Build a JSONB object with only the changed fields
        changes_json := '{}'::JSONB;

        -- Compare each field and add to changes if different
        IF NEW.court_judgment_reference IS DISTINCT FROM OLD.court_judgment_reference THEN
            changes_json := changes_json || jsonb_build_object('court_judgment_reference',
                jsonb_build_object('old', OLD.court_judgment_reference, 'new', NEW.court_judgment_reference));
        END IF;

        IF NEW.court_judgment_date IS DISTINCT FROM OLD.court_judgment_date THEN
            changes_json := changes_json || jsonb_build_object('court_judgment_date',
                jsonb_build_object('old', OLD.court_judgment_date, 'new', NEW.court_judgment_date));
        END IF;

        IF NEW.court_judgment_details IS DISTINCT FROM OLD.court_judgment_details THEN
            changes_json := changes_json || jsonb_build_object('court_judgment_details',
                jsonb_build_object('old', OLD.court_judgment_details, 'new', NEW.court_judgment_details));
        END IF;

        IF NEW.court_judgment_document_url IS DISTINCT FROM OLD.court_judgment_document_url THEN
            changes_json := changes_json || jsonb_build_object('court_judgment_document_url',
                jsonb_build_object('old', OLD.court_judgment_document_url, 'new', NEW.court_judgment_document_url));
        END IF;

        IF NEW.service_requirements IS DISTINCT FROM OLD.service_requirements THEN
            changes_json := changes_json || jsonb_build_object('service_requirements',
                jsonb_build_object('old', OLD.service_requirements, 'new', NEW.service_requirements));
        END IF;

        IF NEW.family_availability IS DISTINCT FROM OLD.family_availability THEN
            changes_json := changes_json || jsonb_build_object('family_availability',
                jsonb_build_object('old', OLD.family_availability, 'new', NEW.family_availability));
        END IF;

        -- If no changes detected, don't create a history entry
        IF changes_json = '{}'::JSONB THEN
            RETURN NULL;
        END IF;
    ELSIF TG_OP = 'DELETE' THEN
        action_type := 'metadata_deleted';
        changes_json := to_jsonb(OLD);
    END IF;

    -- Insert into history table
    INSERT INTO public.request_history (
        request_id,
        changes,
        action,
        user_id
    ) VALUES (
        NEW.request_id,
        changes_json,
        action_type,
        auth.uid()
    );

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for metadata changes
CREATE TRIGGER track_request_metadata_insert
AFTER INSERT ON public.request_metadata
FOR EACH ROW
EXECUTE FUNCTION public.track_request_metadata_changes();

CREATE TRIGGER track_request_metadata_update
AFTER UPDATE ON public.request_metadata
FOR EACH ROW
EXECUTE FUNCTION public.track_request_metadata_changes();

-- Create a function to get request history with organization context
CREATE OR REPLACE FUNCTION public.get_request_history_with_context(p_request_id UUID)
RETURNS TABLE (
    id UUID,
    request_id UUID,
    previous_status TEXT,
    new_status TEXT,
    changes JSONB,
    action TEXT,
    user_id UUID,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE,
    user_name TEXT,
    user_email TEXT,
    organization_id UUID
)
LANGUAGE sql
SECURITY DEFINER
SET search_path = public
AS $$
    SELECT
        rh.id,
        rh.request_id,
        rh.previous_status,
        rh.new_status,
        rh.changes,
        rh.action,
        rh.user_id,
        rh.notes,
        rh.created_at,
        u.raw_user_meta_data->>'name' AS user_name,
        u.email AS user_email,
        r.organization_id
    FROM
        public.request_history rh
    JOIN
        public.requests r ON rh.request_id = r.id
    LEFT JOIN
        auth.users u ON rh.user_id = u.id
    WHERE
        rh.request_id = p_request_id
    ORDER BY
        rh.created_at DESC;
$$;