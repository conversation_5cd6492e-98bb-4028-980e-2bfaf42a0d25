-- Create automation_executions table in the automation schema
CREATE TABLE automation.executions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  workflow_type TEXT NOT NULL,
  status TEXT NOT NULL CHECK (status IN ('pending', 'running', 'completed', 'failed', 'cancelled')) DEFAULT 'pending',
  data JSONB NOT NULL DEFAULT '{}'::jsonb,
  result JSONB DEFAULT NULL,
  error JSONB DEFAULT NULL,
  started_at TIMESTAMPTZ DEFAULT NULL,
  completed_at TIMESTAMPTZ DEFAULT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Add comment to the table
COMMENT ON TABLE automation.executions IS 'Stores workflow execution records for tracking and auditing';

-- <PERSON><PERSON> indexes
CREATE INDEX idx_automation_executions_user_id ON automation.executions(user_id);
CREATE INDEX idx_automation_executions_organization_id ON automation.executions(organization_id);
CREATE INDEX idx_automation_executions_workflow_type ON automation.executions(workflow_type);
CREATE INDEX idx_automation_executions_status ON automation.executions(status);
CREATE INDEX idx_automation_executions_created_at ON automation.executions(created_at);

-- Set up Row Level Security
ALTER TABLE automation.executions ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Users can only view their own executions within their organization
CREATE POLICY "Users can view their own executions"
  ON automation.executions
  FOR SELECT
  USING (
    auth.uid() = user_id AND
    EXISTS (
      SELECT 1 FROM user_roles
      WHERE user_id = auth.uid()
      AND organization_id = automation.executions.organization_id
    )
  );

-- Users can insert their own executions within their organization
CREATE POLICY "Users can insert their own executions"
  ON automation.executions
  FOR INSERT
  WITH CHECK (
    auth.uid() = user_id AND
    EXISTS (
      SELECT 1 FROM user_roles
      WHERE user_id = auth.uid()
      AND organization_id = automation.executions.organization_id
    )
  );

-- Users can update their own executions within their organization
CREATE POLICY "Users can update their own executions"
  ON automation.executions
  FOR UPDATE
  USING (
    auth.uid() = user_id AND
    EXISTS (
      SELECT 1 FROM user_roles
      WHERE user_id = auth.uid()
      AND organization_id = automation.executions.organization_id
    )
  );

-- Users can delete their own executions within their organization
CREATE POLICY "Users can delete their own executions"
  ON automation.executions
  FOR DELETE
  USING (
    auth.uid() = user_id AND
    EXISTS (
      SELECT 1 FROM user_roles
      WHERE user_id = auth.uid()
      AND organization_id = automation.executions.organization_id
    )
  );

-- System admins can view all executions
CREATE POLICY "System admins can view all executions"
  ON automation.executions
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM user_roles
      WHERE user_id = auth.uid()
      AND role = 'SystemAdmin'
    )
  );

-- Directors can view all executions within their organization
CREATE POLICY "Directors can view all executions within their organization"
  ON automation.executions
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM user_roles
      WHERE user_id = auth.uid()
      AND role = 'Director'
      AND organization_id = automation.executions.organization_id
    )
  );

-- Add updated_at trigger
CREATE TRIGGER update_automation_executions_updated_at
  BEFORE UPDATE ON automation.executions
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();
