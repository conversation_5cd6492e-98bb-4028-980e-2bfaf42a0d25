-- Create employee availability tables for employee availability management
-- This migration adds tables to store employee availability data, time-off requests, and exceptions

-- Employee availability table for regular working hours and recurring patterns
CREATE TABLE employee_availability (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  employee_id UUID REFERENCES employees(id) NOT NULL,
  organization_id UUID REFERENCES organizations(id) NOT NULL,
  day_of_week INTEGER NOT NULL CHECK (day_of_week BETWEEN 0 AND 6), -- 0 = Sunday, 6 = Saturday
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  is_recurring BOOLEAN DEFAULT TRUE,
  recurrence_pattern TEXT, -- Optional pattern for complex recurrences (e.g., 'every 2 weeks')
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id),
  updated_by UUID REFERENCES auth.users(id),
  CONSTRAINT valid_time_range CHECK (start_time < end_time)
);

-- Employee time-off table for vacation and time-off requests
CREATE TABLE employee_time_off (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  employee_id UUID REFERENCES employees(id) NOT NULL,
  organization_id UUID REFERENCES organizations(id) NOT NULL,
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  start_time TIME,
  end_time TIME,
  type TEXT NOT NULL CHECK (type IN ('vacation', 'sick_leave', 'personal_leave', 'other')),
  status TEXT NOT NULL CHECK (status IN ('pending', 'approved', 'rejected', 'cancelled')) DEFAULT 'pending',
  description TEXT,
  approved_by UUID REFERENCES auth.users(id),
  approved_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id),
  updated_by UUID REFERENCES auth.users(id),
  CONSTRAINT valid_date_range CHECK (start_date <= end_date),
  CONSTRAINT valid_time_range_if_provided CHECK (
    (start_time IS NULL AND end_time IS NULL) OR
    (start_time IS NOT NULL AND end_time IS NOT NULL AND start_time < end_time)
  )
);

-- Employee availability exceptions table for one-time exceptions to regular availability
CREATE TABLE employee_availability_exceptions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  employee_id UUID REFERENCES employees(id) NOT NULL,
  organization_id UUID REFERENCES organizations(id) NOT NULL,
  exception_date DATE NOT NULL,
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  is_available BOOLEAN NOT NULL DEFAULT FALSE, -- FALSE = unavailable, TRUE = available (override)
  reason TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id),
  updated_by UUID REFERENCES auth.users(id),
  CONSTRAINT valid_time_range CHECK (start_time < end_time)
);

-- Add indexes for foreign keys and frequently queried fields
CREATE INDEX idx_employee_availability_employee_id ON employee_availability(employee_id);
CREATE INDEX idx_employee_availability_org_id ON employee_availability(organization_id);
CREATE INDEX idx_employee_availability_day_of_week ON employee_availability(day_of_week);

CREATE INDEX idx_employee_time_off_employee_id ON employee_time_off(employee_id);
CREATE INDEX idx_employee_time_off_org_id ON employee_time_off(organization_id);
CREATE INDEX idx_employee_time_off_date_range ON employee_time_off(start_date, end_date);
CREATE INDEX idx_employee_time_off_status ON employee_time_off(status);

CREATE INDEX idx_employee_availability_exceptions_employee_id ON employee_availability_exceptions(employee_id);
CREATE INDEX idx_employee_availability_exceptions_org_id ON employee_availability_exceptions(organization_id);
CREATE INDEX idx_employee_availability_exceptions_date ON employee_availability_exceptions(exception_date);

-- Enable Row Level Security (RLS) on all tables
ALTER TABLE employee_availability ENABLE ROW LEVEL SECURITY;
ALTER TABLE employee_time_off ENABLE ROW LEVEL SECURITY;
ALTER TABLE employee_availability_exceptions ENABLE ROW LEVEL SECURITY;

-- Create triggers for updated_at
CREATE TRIGGER update_employee_availability_updated_at
BEFORE UPDATE ON employee_availability
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_employee_time_off_updated_at
BEFORE UPDATE ON employee_time_off
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_employee_availability_exceptions_updated_at
BEFORE UPDATE ON employee_availability_exceptions
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

-- RLS Policies for employee_availability table

-- All users can view availability for employees in their organization
CREATE POLICY "Users can view availability for employees in their organization"
  ON employee_availability FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM user_roles
      WHERE user_roles.user_id = auth.uid()
      AND user_roles.organization_id = employee_availability.organization_id
    )
  );

-- Directors can manage availability for all employees in their organization
CREATE POLICY "Directors can manage availability for all employees in their organization"
  ON employee_availability FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM user_roles
      WHERE user_roles.user_id = auth.uid()
      AND user_roles.organization_id = employee_availability.organization_id
      AND user_roles.role = 'Director'
    )
  );

-- Employees can manage their own availability
CREATE POLICY "Employees can manage their own availability"
  ON employee_availability FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM employees
      WHERE employees.id = employee_availability.employee_id
      AND employees.user_account_id = auth.uid()
    )
  );

-- RLS Policies for employee_time_off table

-- All users can view time-off for employees in their organization
CREATE POLICY "Users can view time-off for employees in their organization"
  ON employee_time_off FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM user_roles
      WHERE user_roles.user_id = auth.uid()
      AND user_roles.organization_id = employee_time_off.organization_id
    )
  );

-- Directors can manage time-off for all employees in their organization
CREATE POLICY "Directors can manage time-off for all employees in their organization"
  ON employee_time_off FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM user_roles
      WHERE user_roles.user_id = auth.uid()
      AND user_roles.organization_id = employee_time_off.organization_id
      AND user_roles.role = 'Director'
    )
  );

-- Employees can request time-off for themselves
CREATE POLICY "Employees can request time-off for themselves"
  ON employee_time_off FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM employees
      WHERE employees.id = employee_time_off.employee_id
      AND employees.user_account_id = auth.uid()
    )
  );

-- Employees can view and update their own time-off requests
CREATE POLICY "Employees can view and update their own time-off requests"
  ON employee_time_off FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM employees
      WHERE employees.id = employee_time_off.employee_id
      AND employees.user_account_id = auth.uid()
    )
  );

CREATE POLICY "Employees can update their own time-off requests"
  ON employee_time_off FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM employees
      WHERE employees.id = employee_time_off.employee_id
      AND employees.user_account_id = auth.uid()
    )
    AND employee_time_off.status = 'pending'
  );

-- RLS Policies for employee_availability_exceptions table

-- All users can view availability exceptions for employees in their organization
CREATE POLICY "Users can view availability exceptions for employees in their organization"
  ON employee_availability_exceptions FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM user_roles
      WHERE user_roles.user_id = auth.uid()
      AND user_roles.organization_id = employee_availability_exceptions.organization_id
    )
  );

-- Directors can manage availability exceptions for all employees in their organization
CREATE POLICY "Directors can manage availability exceptions for all employees in their organization"
  ON employee_availability_exceptions FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM user_roles
      WHERE user_roles.user_id = auth.uid()
      AND user_roles.organization_id = employee_availability_exceptions.organization_id
      AND user_roles.role = 'Director'
    )
  );

-- Employees can manage their own availability exceptions
CREATE POLICY "Employees can manage their own availability exceptions"
  ON employee_availability_exceptions FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM employees
      WHERE employees.id = employee_availability_exceptions.employee_id
      AND employees.user_account_id = auth.uid()
    )
  );

-- Comment on tables and columns
COMMENT ON TABLE employee_availability IS 'Stores employee regular working hours and recurring availability patterns';
COMMENT ON TABLE employee_time_off IS 'Stores employee time-off requests such as vacation and sick leave';
COMMENT ON TABLE employee_availability_exceptions IS 'Stores one-time exceptions to employee regular availability';
