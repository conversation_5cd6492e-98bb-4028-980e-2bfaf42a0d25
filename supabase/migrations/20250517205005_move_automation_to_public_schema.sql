-- Move automation.executions to public schema
CREATE TABLE public.executions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  workflow_type TEXT NOT NULL,
  status TEXT NOT NULL,
  data JSONB NOT NULL DEFAULT '{}'::jsonb,
  result JSONB,
  error TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Add comment to the executions table
COMMENT ON TABLE public.executions IS 'Stores workflow execution records';

-- Create indexes for executions
CREATE INDEX idx_executions_user_id ON public.executions(user_id);
CREATE INDEX idx_executions_organization_id ON public.executions(organization_id);
CREATE INDEX idx_executions_workflow_type ON public.executions(workflow_type);
CREATE INDEX idx_executions_status ON public.executions(status);

-- Set up Row Level Security for executions
ALTER TABLE public.executions ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for executions
-- Users can only view their own executions within their organization
CREATE POLICY "Users can view their own executions"
  ON public.executions
  FOR SELECT
  USING (
    auth.uid() = user_id AND
    EXISTS (
      SELECT 1 FROM user_roles
      WHERE user_id = auth.uid()
      AND organization_id = public.executions.organization_id
    )
  );

-- Users can insert their own executions within their organization
CREATE POLICY "Users can insert their own executions"
  ON public.executions
  FOR INSERT
  WITH CHECK (
    auth.uid() = user_id AND
    EXISTS (
      SELECT 1 FROM user_roles
      WHERE user_id = auth.uid()
      AND organization_id = public.executions.organization_id
    )
  );

-- Users can update their own executions within their organization
CREATE POLICY "Users can update their own executions"
  ON public.executions
  FOR UPDATE
  USING (
    auth.uid() = user_id AND
    EXISTS (
      SELECT 1 FROM user_roles
      WHERE user_id = auth.uid()
      AND organization_id = public.executions.organization_id
    )
  );

-- System admins can view all executions
CREATE POLICY "System admins can view all executions"
  ON public.executions
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM user_roles
      WHERE user_id = auth.uid()
      AND role = 'SystemAdmin'
    )
  );

-- Add updated_at trigger for executions
CREATE TRIGGER update_executions_updated_at
  BEFORE UPDATE ON public.executions
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

-- Move automation.notifications to public schema
CREATE TABLE public.notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  type TEXT NOT NULL,
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  data JSONB NOT NULL DEFAULT '{}'::jsonb,
  read BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Add comment to the notifications table
COMMENT ON TABLE public.notifications IS 'Stores user notifications';

-- Create indexes for notifications
CREATE INDEX idx_notifications_user_id ON public.notifications(user_id);
CREATE INDEX idx_notifications_organization_id ON public.notifications(organization_id);
CREATE INDEX idx_notifications_type ON public.notifications(type);
CREATE INDEX idx_notifications_read ON public.notifications(read);

-- Set up Row Level Security for notifications
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for notifications
-- Users can only view their own notifications within their organization
CREATE POLICY "Users can view their own notifications"
  ON public.notifications
  FOR SELECT
  USING (
    auth.uid() = user_id AND
    EXISTS (
      SELECT 1 FROM user_roles
      WHERE user_id = auth.uid()
      AND organization_id = public.notifications.organization_id
    )
  );

-- Users can insert notifications for themselves within their organization
CREATE POLICY "Users can insert their own notifications"
  ON public.notifications
  FOR INSERT
  WITH CHECK (
    auth.uid() = user_id AND
    EXISTS (
      SELECT 1 FROM user_roles
      WHERE user_id = auth.uid()
      AND organization_id = public.notifications.organization_id
    )
  );

-- Users can update their own notifications within their organization
CREATE POLICY "Users can update their own notifications"
  ON public.notifications
  FOR UPDATE
  USING (
    auth.uid() = user_id AND
    EXISTS (
      SELECT 1 FROM user_roles
      WHERE user_id = auth.uid()
      AND organization_id = public.notifications.organization_id
    )
  );

-- System admins can view all notifications
CREATE POLICY "System admins can view all notifications"
  ON public.notifications
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM user_roles
      WHERE user_id = auth.uid()
      AND role = 'SystemAdmin'
    )
  );

-- Add updated_at trigger for notifications
CREATE TRIGGER update_notifications_updated_at
  BEFORE UPDATE ON public.notifications
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

-- Migrate data from automation.executions to public.executions
INSERT INTO public.executions (id, user_id, organization_id, workflow_type, status, data, result, error, created_at, updated_at)
SELECT id, user_id, organization_id, workflow_type, status, data, result, error, created_at, updated_at
FROM automation.executions;

-- Migrate data from automation.notifications to public.notifications
INSERT INTO public.notifications (id, user_id, organization_id, type, title, message, data, read, created_at, updated_at)
SELECT id, user_id, organization_id, type, title, message, data, read, created_at, updated_at
FROM automation.notifications;

-- Drop the automation schema and all its objects
DROP SCHEMA automation CASCADE;

-- Update the disable_rls_for_tests function to use public schema tables
CREATE OR REPLACE FUNCTION disable_rls_for_tests()
RETURNS VOID AS $$
BEGIN
    -- Disable RLS for contacts table
    ALTER TABLE contacts DISABLE ROW LEVEL SECURITY;

    -- Disable RLS for contact_relationships table
    ALTER TABLE contact_relationships DISABLE ROW LEVEL SECURITY;

    -- Disable RLS for contact_history table
    ALTER TABLE contact_history DISABLE ROW LEVEL SECURITY;
    
    -- Disable RLS for drafts table
    ALTER TABLE public.drafts DISABLE ROW LEVEL SECURITY;
    
    -- Disable RLS for executions table
    ALTER TABLE public.executions DISABLE ROW LEVEL SECURITY;
    
    -- Disable RLS for notifications table
    ALTER TABLE public.notifications DISABLE ROW LEVEL SECURITY;
END;
$$ LANGUAGE plpgsql;

-- Update the enable_rls_for_tests function to use public schema tables
CREATE OR REPLACE FUNCTION enable_rls_for_tests()
RETURNS VOID AS $$
BEGIN
    -- Enable RLS for contacts table
    ALTER TABLE contacts ENABLE ROW LEVEL SECURITY;

    -- Enable RLS for contact_relationships table
    ALTER TABLE contact_relationships ENABLE ROW LEVEL SECURITY;

    -- Enable RLS for contact_history table
    ALTER TABLE contact_history ENABLE ROW LEVEL SECURITY;
    
    -- Enable RLS for drafts table
    ALTER TABLE public.drafts ENABLE ROW LEVEL SECURITY;
    
    -- Enable RLS for executions table
    ALTER TABLE public.executions ENABLE ROW LEVEL SECURITY;
    
    -- Enable RLS for notifications table
    ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;
END;
$$ LANGUAGE plpgsql;
