-- Add organization_id column to contact_history table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'contact_history'
        AND column_name = 'organization_id'
    ) THEN
        ALTER TABLE contact_history ADD COLUMN organization_id UUID NOT NULL REFERENCES organizations(id);
    END IF;
END $$;

-- Create index for organization_id if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM pg_indexes
        WHERE tablename = 'contact_history'
        AND indexname = 'contact_history_organization_id_idx'
    ) THEN
        CREATE INDEX contact_history_organization_id_idx ON contact_history(organization_id);
    END IF;
END $$;

-- Update the contact history trigger function to include organization_id
CREATE OR REPLACE FUNCTION contact_history_trigger_function()
RETURNS TRIGGER AS $$
DECLARE
  current_user_id UUID;
BEGIN
  -- Try to get the current user ID, default to a system user ID if not available
  BEGIN
    current_user_id := auth.uid();
  EXCEPTION WHEN OTHERS THEN
    -- Use a default system user ID when auth.uid() is not available (e.g., during migrations)
    current_user_id := '00000000-0000-0000-0000-000000000000'::UUID;
  END;

  IF TG_OP = 'INSERT' THEN
    INSERT INTO contact_history (organization_id, contact_id, user_id, action, changes)
    VALUES (NEW.organization_id, NEW.id, current_user_id, 'INSERT', to_jsonb(NEW));
  ELSIF TG_OP = 'UPDATE' THEN
    INSERT INTO contact_history (organization_id, contact_id, user_id, action, changes)
    VALUES (NEW.organization_id, NEW.id, current_user_id, 'UPDATE', jsonb_build_object(
      'old', to_jsonb(OLD),
      'new', to_jsonb(NEW)
    ));
  ELSIF TG_OP = 'DELETE' THEN
    INSERT INTO contact_history (organization_id, contact_id, user_id, action, changes)
    VALUES (OLD.organization_id, OLD.id, current_user_id, 'DELETE', to_jsonb(OLD));
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;