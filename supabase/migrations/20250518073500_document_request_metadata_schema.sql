-- Add comments to document the JSONB schema for service_requirements
COMMENT ON COLUMN public.request_metadata.service_requirements IS 'JSON schema for service requirements:
{
  "frequency": {
    "type": "string",
    "enum": ["weekly", "biweekly", "monthly", "custom"],
    "description": "How often the service is required"
  },
  "duration": {
    "type": "integer",
    "description": "Duration of each service session in minutes"
  },
  "specialRequirements": {
    "type": "array",
    "items": {
      "type": "string"
    },
    "description": "List of special requirements for the service"
  },
  "preferredDays": {
    "type": "array",
    "items": {
      "type": "string",
      "enum": ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"]
    },
    "description": "Preferred days of the week for service"
  },
  "preferredTimeOfDay": {
    "type": "string",
    "enum": ["morning", "afternoon", "evening"],
    "description": "Preferred time of day for service"
  },
  "notes": {
    "type": "string",
    "description": "Additional notes about service requirements"
  }
}';

-- Add comments to document the JSONB schema for family_availability
COMMENT ON COLUMN public.request_metadata.family_availability IS 'JSON schema for family availability:
{
  "weekdayAvailability": {
    "monday": {
      "available": true|false,
      "timeSlots": [
        {
          "start": "09:00",
          "end": "12:00"
        }
      ]
    },
    "tuesday": { /* same structure as monday */ },
    "wednesday": { /* same structure as monday */ },
    "thursday": { /* same structure as monday */ },
    "friday": { /* same structure as monday */ },
    "saturday": { /* same structure as monday */ },
    "sunday": { /* same structure as monday */ }
  },
  "specificDates": [
    {
      "date": "2025-06-15",
      "available": true|false,
      "timeSlots": [
        {
          "start": "09:00",
          "end": "12:00"
        }
      ]
    }
  ],
  "notes": "Additional notes about availability"
}';
