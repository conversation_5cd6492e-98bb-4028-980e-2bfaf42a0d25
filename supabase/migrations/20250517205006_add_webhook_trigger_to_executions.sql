-- Create extension for HTT<PERSON> requests if not exists
CREATE EXTENSION IF NOT EXISTS http;

-- Create a function to send webhook notifications
CREATE OR REPLACE FUNCTION public.notify_execution_webhook()
RETURNS TRIGGER AS $$
DECLARE
  webhook_url TEXT := 'https://infinisoft.app.n8n.cloud/webhook-test/webhoook';
  payload JSONB;
BEGIN
  -- Create the payload from the NEW record
  payload := jsonb_build_object(
    'id', NEW.id,
    'user_id', NEW.user_id,
    'organization_id', NEW.organization_id,
    'workflow_type', NEW.workflow_type,
    'status', NEW.status,
    'data', NEW.data,
    'created_at', NEW.created_at
  );

  -- Make the HTTP POST request to the webhook URL
  PERFORM http_post(
    webhook_url,
    payload::text,
    'application/json'
  );

  -- Return the NEW record to continue with the insert
  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    -- Log the error but allow the insert to proceed
    RAISE NOTICE 'Error sending webhook: %', SQLERRM;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add comment explaining the function
COMMENT ON FUNCTION public.notify_execution_webhook() IS 'Trigger function to send a webhook notification when a new execution record is inserted';

-- Create the trigger on the executions table
CREATE TRIGGER executions_after_insert_webhook
AFTER INSERT ON public.executions
FOR EACH ROW
EXECUTE FUNCTION public.notify_execution_webhook();

-- Add comment explaining the trigger
COMMENT ON TRIGGER executions_after_insert_webhook ON public.executions IS 'Trigger to send webhook notifications when new execution records are created';