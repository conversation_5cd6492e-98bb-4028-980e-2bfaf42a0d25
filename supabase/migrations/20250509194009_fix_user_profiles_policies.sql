-- Fix the typo in the user_profiles policy for organization admins
-- Drop the existing policy with the typo
DROP POLICY IF EXISTS "Organization admins can read profiles in their organization" ON public.user_profiles;

-- Recreate the policy with the correct role names (case-sensitive)
CREATE POLICY "Organization admins can read profiles in their organization"
  ON public.user_profiles
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.user_roles
      WHERE user_roles.user_id = auth.uid()
      AND user_roles.organization_id = user_profiles.organization_id
      AND user_roles.role IN ('Director', 'SystemAdmin')
    )
  );

-- Add a policy to allow Directors to read all user profiles in their organization
-- This uses the public.user_is_director_of_organization function
CREATE POLICY "Directors can read all profiles in their organization"
  ON public.user_profiles
  FOR SELECT
  USING (
    public.user_is_director_of_organization(organization_id)
  );