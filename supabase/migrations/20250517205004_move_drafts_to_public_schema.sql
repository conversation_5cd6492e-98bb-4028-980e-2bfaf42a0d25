-- Create drafts table in public schema
CREATE TABLE public.drafts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  workflow_type TEXT NOT NULL,
  current_step TEXT NOT NULL,
  data JSONB NOT NULL DEFAULT '{}'::jsonb,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Add comment to the table
COMMENT ON TABLE public.drafts IS 'Stores incomplete wizard data for resumable workflows';

-- <PERSON>reate indexes
CREATE INDEX idx_drafts_user_id ON public.drafts(user_id);
CREATE INDEX idx_drafts_organization_id ON public.drafts(organization_id);
CREATE INDEX idx_drafts_workflow_type ON public.drafts(workflow_type);
CREATE INDEX idx_drafts_current_step ON public.drafts(current_step);

-- Set up Row Level Security
ALTER TABLE public.drafts ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Users can only view their own drafts within their organization
CREATE POLICY "Users can view their own drafts"
  ON public.drafts
  FOR SELECT
  USING (
    auth.uid() = user_id AND
    EXISTS (
      SELECT 1 FROM user_roles
      WHERE user_id = auth.uid()
      AND organization_id = public.drafts.organization_id
    )
  );

-- Users can insert their own drafts within their organization
CREATE POLICY "Users can insert their own drafts"
  ON public.drafts
  FOR INSERT
  WITH CHECK (
    auth.uid() = user_id AND
    EXISTS (
      SELECT 1 FROM user_roles
      WHERE user_id = auth.uid()
      AND organization_id = public.drafts.organization_id
    )
  );

-- Users can update their own drafts within their organization
CREATE POLICY "Users can update their own drafts"
  ON public.drafts
  FOR UPDATE
  USING (
    auth.uid() = user_id AND
    EXISTS (
      SELECT 1 FROM user_roles
      WHERE user_id = auth.uid()
      AND organization_id = public.drafts.organization_id
    )
  );

-- Users can delete their own drafts within their organization
CREATE POLICY "Users can delete their own drafts"
  ON public.drafts
  FOR DELETE
  USING (
    auth.uid() = user_id AND
    EXISTS (
      SELECT 1 FROM user_roles
      WHERE user_id = auth.uid()
      AND organization_id = public.drafts.organization_id
    )
  );

-- System admins can view all drafts
CREATE POLICY "System admins can view all drafts"
  ON public.drafts
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM user_roles
      WHERE user_id = auth.uid()
      AND role = 'SystemAdmin'
    )
  );

-- Add updated_at trigger
CREATE TRIGGER update_drafts_updated_at
  BEFORE UPDATE ON public.drafts
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

-- Migrate data from automation.drafts to public.drafts
INSERT INTO public.drafts (id, user_id, organization_id, workflow_type, current_step, data, created_at, updated_at)
SELECT id, user_id, organization_id, workflow_type, current_step, data, created_at, updated_at
FROM automation.drafts;

-- Drop the automation.drafts table
DROP TABLE automation.drafts;

-- Update the disable_rls_for_tests function to use public.drafts instead of automation.drafts
CREATE OR REPLACE FUNCTION disable_rls_for_tests()
RETURNS VOID AS $$
BEGIN
    -- Disable RLS for contacts table
    ALTER TABLE contacts DISABLE ROW LEVEL SECURITY;

    -- Disable RLS for contact_relationships table
    ALTER TABLE contact_relationships DISABLE ROW LEVEL SECURITY;

    -- Disable RLS for contact_history table
    ALTER TABLE contact_history DISABLE ROW LEVEL SECURITY;
    
    -- Disable RLS for drafts table
    ALTER TABLE public.drafts DISABLE ROW LEVEL SECURITY;
END;
$$ LANGUAGE plpgsql;

-- Update the enable_rls_for_tests function to use public.drafts instead of automation.drafts
CREATE OR REPLACE FUNCTION enable_rls_for_tests()
RETURNS VOID AS $$
BEGIN
    -- Enable RLS for contacts table
    ALTER TABLE contacts ENABLE ROW LEVEL SECURITY;

    -- Enable RLS for contact_relationships table
    ALTER TABLE contact_relationships ENABLE ROW LEVEL SECURITY;

    -- Enable RLS for contact_history table
    ALTER TABLE contact_history ENABLE ROW LEVEL SECURITY;
    
    -- Enable RLS for drafts table
    ALTER TABLE public.drafts ENABLE ROW LEVEL SECURITY;
END;
$$ LANGUAGE plpgsql;
