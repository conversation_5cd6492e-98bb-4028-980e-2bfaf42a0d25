-- Create automation schema
CREATE SCHEMA IF NOT EXISTS automation;

-- Enable Row Level Security
ALTER SCHEMA automation OWNER TO postgres;

-- Create automation_drafts table
CREATE TABLE automation.drafts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  workflow_type TEXT NOT NULL,
  current_step TEXT NOT NULL,
  data JSONB NOT NULL DEFAULT '{}'::jsonb,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Add comment to the table
COMMENT ON TABLE automation.drafts IS 'Stores incomplete wizard data for resumable workflows';

-- Create indexes
CREATE INDEX idx_automation_drafts_user_id ON automation.drafts(user_id);
CREATE INDEX idx_automation_drafts_organization_id ON automation.drafts(organization_id);
CREATE INDEX idx_automation_drafts_workflow_type ON automation.drafts(workflow_type);
CREATE INDEX idx_automation_drafts_current_step ON automation.drafts(current_step);

-- Set up Row Level Security
ALTER TABLE automation.drafts ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Users can only view their own drafts within their organization
CREATE POLICY "Users can view their own drafts"
  ON automation.drafts
  FOR SELECT
  USING (
    auth.uid() = user_id AND
    EXISTS (
      SELECT 1 FROM user_roles
      WHERE user_id = auth.uid()
      AND organization_id = automation.drafts.organization_id
    )
  );

-- Users can insert their own drafts within their organization
CREATE POLICY "Users can insert their own drafts"
  ON automation.drafts
  FOR INSERT
  WITH CHECK (
    auth.uid() = user_id AND
    EXISTS (
      SELECT 1 FROM user_roles
      WHERE user_id = auth.uid()
      AND organization_id = automation.drafts.organization_id
    )
  );

-- Users can update their own drafts within their organization
CREATE POLICY "Users can update their own drafts"
  ON automation.drafts
  FOR UPDATE
  USING (
    auth.uid() = user_id AND
    EXISTS (
      SELECT 1 FROM user_roles
      WHERE user_id = auth.uid()
      AND organization_id = automation.drafts.organization_id
    )
  );

-- Users can delete their own drafts within their organization
CREATE POLICY "Users can delete their own drafts"
  ON automation.drafts
  FOR DELETE
  USING (
    auth.uid() = user_id AND
    EXISTS (
      SELECT 1 FROM user_roles
      WHERE user_id = auth.uid()
      AND organization_id = automation.drafts.organization_id
    )
  );

-- System admins can view all drafts
CREATE POLICY "System admins can view all drafts"
  ON automation.drafts
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM user_roles
      WHERE user_id = auth.uid()
      AND role = 'SystemAdmin'
    )
  );

-- Add updated_at trigger
CREATE TRIGGER update_automation_drafts_updated_at
  BEFORE UPDATE ON automation.drafts
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();
