-- Create a table to store application settings
CREATE TABLE IF NOT EXISTS public.app_settings (
  key TEXT PRIMARY KEY,
  value TEXT NOT NULL,
  description TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Add webhook URL to settings
INSERT INTO public.app_settings (key, value, description)
VALUES
  ('webhook_url', 'https://infinisoft.app.n8n.cloud/webhook/webhoook', 'URL for the n8n webhook endpoint')
ON CONFLICT (key)
DO UPDATE SET value = EXCLUDED.value, updated_at = now();

-- Drop existing trigger and function if they exist
DROP TRIGGER IF EXISTS executions_after_insert_webhook ON public.executions;
DROP FUNCTION IF EXISTS public.notify_execution_webhook();

-- Create extension for HTTP requests if not exists
CREATE EXTENSION IF NOT EXISTS http;

-- Create the function with URL from settings table
CREATE OR REPLACE FUNCTION public.notify_execution_webhook()
RETURNS TRIGGER AS $$
DECLARE
  webhook_url TEXT;
  payload JSONB;
BEGIN
  -- Get webhook URL from settings
  SELECT value INTO webhook_url FROM public.app_settings WHERE key = 'webhook_url';

  -- If no URL found, use default
  IF webhook_url IS NULL THEN
    webhook_url := 'https://infinisoft.app.n8n.cloud/webhook/webhoook';
  END IF;

  -- Create the payload from the NEW record
  payload := jsonb_build_object(
    'id', NEW.id,
    'user_id', NEW.user_id,
    'organization_id', NEW.organization_id,
    'workflow_type', NEW.workflow_type,
    'status', NEW.status,
    'data', NEW.data,
    'created_at', NEW.created_at
  );

  -- Make the HTTP POST request to the webhook URL
  PERFORM http_post(
    webhook_url,
    payload::text,
    'application/json'
  );

  -- Return the NEW record to continue with the insert
  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    -- Log the error but allow the insert to proceed
    RAISE NOTICE 'Error sending webhook: %', SQLERRM;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add comment explaining the function
COMMENT ON FUNCTION public.notify_execution_webhook() IS 'Trigger function to send a webhook notification when a new execution record is inserted';

-- Create the trigger on the executions table
CREATE TRIGGER executions_after_insert_webhook
AFTER INSERT ON public.executions
FOR EACH ROW
EXECUTE FUNCTION public.notify_execution_webhook();

-- Add comment explaining the trigger
COMMENT ON TRIGGER executions_after_insert_webhook ON public.executions IS 'Trigger to send webhook notifications when new execution records are created';

-- Enable realtime for drafts, executions, and notifications tables
ALTER TABLE public.drafts REPLICA IDENTITY FULL;
ALTER TABLE public.executions REPLICA IDENTITY FULL;
ALTER TABLE public.notifications REPLICA IDENTITY FULL;

-- Add updated_at trigger for app_settings
CREATE TRIGGER update_app_settings_updated_at
BEFORE UPDATE ON public.app_settings
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

-- Add comment on app_settings table
COMMENT ON TABLE public.app_settings IS 'Stores application-wide settings and configuration values';