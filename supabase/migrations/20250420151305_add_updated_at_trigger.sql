-- Create a function to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add comment explaining the function
COMMENT ON FUNCTION public.update_updated_at_column() IS 'Trigger function to automatically update the updated_at column with the current timestamp whenever a row is updated';

-- Create triggers for each table with updated_at column

-- Organizations table
CREATE TRIGGER update_organizations_updated_at
BEFORE UPDATE ON organizations
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

-- Locations table
CREATE TRIGGER update_locations_updated_at
BEFORE UPDATE ON locations
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

-- Rooms table
CREATE TRIGGER update_rooms_updated_at
BEFORE UPDATE ON rooms
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

-- Business hours table
CREATE TRIGGER update_business_hours_updated_at
BEFORE UPDATE ON business_hours
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

-- Note: user_roles table doesn't have an updated_at column, so no trigger is needed