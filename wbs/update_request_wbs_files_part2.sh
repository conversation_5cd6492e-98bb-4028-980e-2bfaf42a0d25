#!/bin/bash

# Function to update a file with a to-do list
update_file() {
  local file="$1"
  local content="$2"

  # Create directory if it doesn't exist
  mkdir -p "$(dirname "$file")"

  echo -e "# Tasks List\n\n$content" > "$file"
  echo "Updated: $file"
}

cd wbs/request

# 5. UI Components
update_file "5. UI Components/5.1 Request Forms/5.1.1 Create Form" "- Create RequestForm component
- Add form fields for all required request properties
- Implement form validation
- Add error handling and display
- Connect form to create server action
- Add loading state during submission
- Implement responsive design"

update_file "5. UI Components/5.1 Request Forms/5.1.2 Edit Form" "- Create RequestEditForm component
- Pre-populate form with existing request data
- Implement form validation
- Add error handling and display
- Connect form to update server action
- Add loading state during submission
- Implement responsive design"

update_file "5. UI Components/5.1 Request Forms/5.1.3 Wizard Steps" "- Create RequestWizard component
- Implement multi-step form with navigation
- Create separate components for each wizard step
- Add validation for each step
- Implement progress indicator
- Add ability to save draft between steps
- Connect to create/update server actions"

update_file "5. UI Components/5.2 Request Display/5.2.1 Detail View" "- Create RequestDetail component
- Display all request properties in organized sections
- Add conditional rendering based on request status
- Implement responsive layout
- Add action buttons based on permissions and status
- Include related data (contacts, documents, etc.)"

update_file "5. UI Components/5.2 Request Display/5.2.2 Status Badge" "- Create RequestStatusBadge component
- Display different colors based on status
- Add appropriate icons for each status
- Implement tooltip with status description
- Make component reusable across the application"

update_file "5. UI Components/5.2 Request Display/5.2.3 History Timeline" "- Create RequestHistoryTimeline component
- Display history entries in chronological order
- Highlight status changes
- Show user attribution for each change
- Implement expandable details for each entry
- Add filtering options by change type"

update_file "5. UI Components/5.3 Request Lists/5.3.1 Table View" "- Create RequestTable component
- Display requests in tabular format with key columns
- Implement sortable columns
- Add status badges
- Make rows clickable to navigate to detail view
- Implement responsive design with column priority"

update_file "5. UI Components/5.3 Request Lists/5.3.2 Filters" "- Create RequestFilters component
- Add status filter dropdown
- Implement date range filter
- Add search input for text search
- Create assignee filter dropdown
- Make filters collapsible on mobile
- Connect filters to list server action"

update_file "5. UI Components/5.3 Request Lists/5.3.3 Sorting" "- Implement column sorting in RequestTable
- Add sort indicators to column headers
- Create sort parameter handling
- Connect sorting to list server action
- Preserve sort state across page refreshes"

update_file "5. UI Components/5.4 Workflow Components/5.4.1 Status Transition Buttons" "- Create RequestStatusActions component
- Display appropriate action buttons based on current status
- Implement permission checks for each action
- Add confirmation dialogs for irreversible actions
- Connect buttons to workflow server actions
- Show loading state during transitions"

update_file "5. UI Components/5.4 Workflow Components/5.4.2 Approval Form" "- Create RequestApprovalForm component
- Add fields for approval notes
- Implement validation
- Connect to approve server action
- Add loading state during submission
- Show success/error messages"

update_file "5. UI Components/5.4 Workflow Components/5.4.3 Rejection Form" "- Create RequestRejectionForm component
- Add required field for rejection reason
- Implement validation
- Connect to reject server action
- Add loading state during submission
- Show success/error messages"

# 6. Pages Implementation
update_file "6. Pages Implementation/6.1 List Page/6.1.1 Main Component" "- Create list page component
- Implement RequestTable component
- Add RequestFilters component
- Include pagination controls
- Add create button with permission check
- Implement responsive layout"

update_file "6. Pages Implementation/6.1 List Page/6.1.2 Loading State" "- Create loading.tsx file
- Implement skeleton UI for table
- Add shimmer effect for better UX
- Match layout of the actual list page"

update_file "6. Pages Implementation/6.1 List Page/6.1.3 Error Handling" "- Create error.tsx file
- Display user-friendly error message
- Add retry button
- Log detailed error information
- Provide contact support option"

update_file "6. Pages Implementation/6.2 Create Page/6.2.1 Main Component" "- Create create page component
- Implement RequestForm or RequestWizard
- Add breadcrumb navigation
- Add cancel button to return to list
- Implement responsive layout"

update_file "6. Pages Implementation/6.2 Create Page/6.2.2 Loading State" "- Create loading.tsx file
- Implement skeleton UI for form
- Add shimmer effect for better UX
- Match layout of the actual create page"

update_file "6. Pages Implementation/6.2 Create Page/6.2.3 Error Handling" "- Create error.tsx file
- Display user-friendly error message
- Add retry button
- Log detailed error information
- Provide contact support option"

update_file "6. Pages Implementation/6.3 View Page/6.3.1 Main Component" "- Create view page component
- Implement RequestDetail component
- Add RequestStatusActions component
- Include RequestHistoryTimeline component
- Add edit button with permission check
- Implement responsive layout"

update_file "6. Pages Implementation/6.3 View Page/6.3.2 Loading State" "- Create loading.tsx file
- Implement skeleton UI for detail view
- Add shimmer effect for better UX
- Match layout of the actual view page"

update_file "6. Pages Implementation/6.3 View Page/6.3.3 Error Handling" "- Create error.tsx file
- Display user-friendly error message
- Add retry button
- Log detailed error information
- Provide contact support option"

update_file "6. Pages Implementation/6.3 View Page/6.3.4 Not Found State" "- Create not-found.tsx file
- Display user-friendly not found message
- Add button to return to list page
- Log the attempted access for debugging"

update_file "6. Pages Implementation/6.4 Edit Page/6.4.1 Main Component" "- Create edit page component
- Implement RequestEditForm component
- Add breadcrumb navigation
- Add cancel button to return to view page
- Implement responsive layout"

update_file "6. Pages Implementation/6.4 Edit Page/6.4.2 Loading State" "- Create loading.tsx file
- Implement skeleton UI for edit form
- Add shimmer effect for better UX
- Match layout of the actual edit page"

update_file "6. Pages Implementation/6.4 Edit Page/6.4.3 Error Handling" "- Create error.tsx file
- Display user-friendly error message
- Add retry button
- Log detailed error information
- Provide contact support option"

update_file "6. Pages Implementation/6.4 Edit Page/6.4.4 Not Found State" "- Create not-found.tsx file
- Display user-friendly not found message
- Add button to return to list page
- Log the attempted access for debugging"

update_file "6. Pages Implementation/6.5 History Page/6.5.1 Main Component" "- Create history page component
- Implement RequestHistoryTimeline component with full details
- Add filters for history types
- Add breadcrumb navigation
- Add button to return to view page
- Implement responsive layout"

update_file "6. Pages Implementation/6.5 History Page/6.5.2 Loading State" "- Create loading.tsx file
- Implement skeleton UI for history timeline
- Add shimmer effect for better UX
- Match layout of the actual history page"

update_file "6. Pages Implementation/6.5 History Page/6.5.3 Error Handling" "- Create error.tsx file
- Display user-friendly error message
- Add retry button
- Log detailed error information
- Provide contact support option"

# 7. Authorization
update_file "7. Authorization/7.1 Permission Definitions/7.1.1 View Permissions" "- Define request:view permission
- Update ConfigurationService with permission
- Document permission purpose and scope
- Assign permission to appropriate roles"

update_file "7. Authorization/7.1 Permission Definitions/7.1.2 Create Permissions" "- Define request:create permission
- Update ConfigurationService with permission
- Document permission purpose and scope
- Assign permission to appropriate roles"

update_file "7. Authorization/7.1 Permission Definitions/7.1.3 Edit Permissions" "- Define request:edit permission
- Update ConfigurationService with permission
- Document permission purpose and scope
- Assign permission to appropriate roles"

update_file "7. Authorization/7.1 Permission Definitions/7.1.4 Delete Permissions" "- Define request:delete permission
- Update ConfigurationService with permission
- Document permission purpose and scope
- Assign permission to appropriate roles"

update_file "7. Authorization/7.1 Permission Definitions/7.1.5 Workflow Permissions" "- Define request:workflow permission
- Update ConfigurationService with permission
- Document permission purpose and scope
- Assign permission to appropriate roles"

update_file "7. Authorization/7.2 Role-Based Access/7.2.1 Director Access" "- Assign all request permissions to Director role
- Document Director capabilities for requests
- Implement any Director-specific UI elements
- Test Director access to all request features"

update_file "7. Authorization/7.2 Role-Based Access/7.2.2 Coordinator Access" "- Assign appropriate request permissions to Coordinator role
- Document Coordinator capabilities for requests
- Implement any Coordinator-specific UI elements
- Test Coordinator access to permitted request features"

update_file "7. Authorization/7.2 Role-Based Access/7.2.3 Social Worker Access" "- Assign limited request permissions to Social Worker role
- Document Social Worker capabilities for requests
- Implement any Social Worker-specific UI elements
- Test Social Worker access to permitted request features"

update_file "7. Authorization/7.3 Route Protection/7.3.1 Page-Level Guards" "- Update routePermissions.ts with request routes
- Add requiredPermission decorator to page components
- Implement redirect for unauthorized access
- Test all route protections"

update_file "7. Authorization/7.3 Route Protection/7.3.2 Action-Level Guards" "- Add permission checks to all server actions
- Implement requiredPermission decorator for actions
- Return appropriate error for unauthorized actions
- Test all action protections"

# 8. Integration
update_file "8. Integration/8.1 n8n Workflow Integration/8.1.1 Status Change Triggers" "- Create webhook endpoints for status changes
- Implement n8n workflow for each status transition
- Document webhook payload structure
- Test all status change triggers"

update_file "8. Integration/8.1 n8n Workflow Integration/8.1.2 Notification Workflows" "- Create n8n workflows for email notifications
- Implement in-app notification generation
- Configure notification templates
- Test all notification workflows"

update_file "8. Integration/8.1 n8n Workflow Integration/8.1.3 Document Generation" "- Create n8n workflow for document generation
- Configure document templates
- Implement document storage and linking
- Test document generation workflow"

update_file "8. Integration/8.2 Case File Integration/8.2.1 Case File Creation" "- Implement case file creation from completed requests
- Transfer relevant data from request to case file
- Create relationships between request and case file
- Test case file creation process"

update_file "8. Integration/8.2 Case File Integration/8.2.2 Data Transfer" "- Define data mapping between request and case file
- Implement data transformation if needed
- Transfer contacts and documents
- Test data integrity after transfer"

update_file "8. Integration/8.3 Contact Integration/8.3.1 Contact Selection" "- Create contact selector component
- Implement contact search functionality
- Allow adding multiple contacts with roles
- Test contact selection and association"

update_file "8. Integration/8.3 Contact Integration/8.3.2 Relationship Mapping" "- Map contact relationships to request roles
- Implement relationship visualization
- Transfer relationships to case file when completed
- Test relationship mapping"

# 9. Testing
update_file "9. Testing/9.1 Unit Tests/9.1.1 Service Tests" "- Create tests for RequestService methods
- Create tests for RequestWorkflowService methods
- Create tests for RequestHistoryService methods
- Implement mocks for database and dependencies
- Test error handling and edge cases"

update_file "9. Testing/9.1 Unit Tests/9.1.2 Action Tests" "- Create tests for management actions
- Create tests for workflow actions
- Create tests for history actions
- Implement mocks for services
- Test error handling and edge cases"

update_file "9. Testing/9.1 Unit Tests/9.1.3 Component Tests" "- Create tests for form components
- Create tests for display components
- Create tests for list components
- Create tests for workflow components
- Test component rendering and interactions"

update_file "9. Testing/9.2 Integration Tests/9.2.1 Workflow Tests" "- Test complete request workflow from creation to completion
- Test all status transitions
- Test validation rules
- Test notifications
- Test case file creation"

update_file "9. Testing/9.2 Integration Tests/9.2.2 RLS Policy Tests" "- Test organization isolation
- Test permission-based access control
- Test cross-organization access prevention
- Test system admin access"

update_file "9. Testing/9.3 E2E Tests/9.3.1 Request Lifecycle" "- Create E2E test for complete request lifecycle
- Test UI interactions
- Test data persistence
- Test navigation between pages
- Test error scenarios"

update_file "9. Testing/9.3 E2E Tests/9.3.2 Permission Tests" "- Create E2E tests for different user roles
- Test permission-based UI adaptations
- Test unauthorized access attempts
- Test permission boundaries"

echo "Updated second batch of files successfully!"
