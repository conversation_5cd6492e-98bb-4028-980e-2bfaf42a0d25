#!/bin/bash

# <PERSON>ript to traverse a directory recursively, read files in numerical order,
# and present their content for execution one by one

# Function to extract the numeric prefix from a filename
get_numeric_prefix() {
    local filename=$(basename "$1")
    if [[ $filename =~ ^([0-9]+[\.]) ]]; then
        echo "${BASH_REMATCH[1]}" | sed 's/\.//'
    else
        echo "999999" # Default high number for files without numeric prefix
    fi
}

# Function to process a single file
process_file() {
    local file="$1"
    echo "========================================================"
    echo "ISSUE: $(basename "$file")"
    echo "========================================================"
    echo ""
    cat "$file"
    echo ""
    echo "========================================================"
    echo "Before implementing, make sur you understand and follow the AI codebase guide found in docs/ai-codebase-guide.md"
    echo "Implement the above tasks."
    echo "Before proceeding to next issue, make sure to:"
    echo "- npm run build successfully"
    echo "- Fix all errors reported by npm run build"
    echo "- Step back and review your code making sure it not out of scope"
    echo "- Run migrations for local if required"
    echo "Are you 100% sure your code respect the AI codebase guide?"
    echo "Type 'DONE' when completed to proceed to the next task."
    echo "========================================================"
    
    # Wait for user to type DONE
    while true; do
        read -p "> " response
        if [[ "$response" == "DONE" ]]; then
            break
        else
            echo "Type 'DONE' when you have completed the task."
        fi
    done
}

# Main function to traverse directory and process files
traverse_and_process() {
    local dir="$1"
    
    # Check if directory exists
    if [ ! -d "$dir" ]; then
        echo "Error: Directory '$dir' does not exist."
        exit 1
    fi
    
    # Find all files recursively and sort them
    local files=()
    while IFS= read -r -d '' file; do
        # Only include regular files, not directories
        if [ -f "$file" ]; then
            files+=("$file")
        fi
    done < <(find "$dir" -type f -print0)
    
    # Sort files by numeric prefix
    IFS=$'\n' sorted_files=($(for file in "${files[@]}"; do
        echo "$(get_numeric_prefix "$file")|$file"
    done | sort -n | cut -d'|' -f2))
    unset IFS
    
    # Process each file in order
    for file in "${sorted_files[@]}"; do
        process_file "$file"
    done
    
    echo "All tasks completed!"
}

# Check if directory argument is provided
if [ $# -eq 0 ]; then
    echo "Usage: $0 <directory>"
    exit 1
fi

# Start processing
traverse_and_process "$1"
