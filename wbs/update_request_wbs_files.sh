#!/bin/bash

# Function to update a file with a to-do list
update_file() {
  local file="$1"
  local content="$2"

  # Create directory if it doesn't exist
  mkdir -p "$(dirname "$file")"

  echo -e "# Tasks List\n\n$content" > "$file"
  echo "Updated: $file"
}

cd wbs/request

# 1. Database Schema
update_file "1. Database Schema/1.1 Request Table/1.1.1 Core Fields" "- Define UUID primary key field
- Add organization_id foreign key field
- Add title and description text fields
- Add status field with appropriate constraints
- Add requester_id and assignee_id foreign keys
- Add priority field with constraints
- Add service_type field
- Add created_at and updated_at timestamp fields"

update_file "1. Database Schema/1.1 Request Table/1.1.2 Status Management" "- Define status enum values (draft, requested, waitlist, approved, rejected, completed)
- Add status field with CHECK constraint
- Add status-specific fields (rejection_reason, waitlist_position, etc.)
- Create indexes for status field for efficient filtering"

update_file "1. Database Schema/1.1 Request Table/1.1.3 Relationships" "- Create foreign key to organizations table
- Create foreign key to users table for requester
- Create foreign key to users table for assignee
- Set up cascade rules for updates and deletes
- Create indexes for all foreign key fields"

update_file "1. Database Schema/1.1 Request Table/1.1.4 RLS Policies" "- Enable row-level security on the table
- Create policy for SELECT operations based on organization_id
- Create policy for INSERT operations with permission checks
- Create policy for UPDATE operations with permission checks
- Create policy for DELETE operations with permission checks"

update_file "1. Database Schema/1.2 Request History Table/1.2.1 Change Tracking" "- Define UUID primary key field
- Add request_id foreign key field
- Add previous_status and new_status fields
- Add changes JSONB field to store detailed changes
- Add action field to describe the change type"

update_file "1. Database Schema/1.2 Request History Table/1.2.2 User Attribution" "- Add user_id foreign key field
- Add created_at timestamp field
- Add notes field for user comments
- Create indexes for user_id field"

update_file "1. Database Schema/1.2 Request History Table/1.2.3 RLS Policies" "- Enable row-level security on the table
- Create policy for SELECT operations based on organization_id
- Create policy for INSERT operations with permission checks
- Create indexes for efficient policy enforcement"

update_file "1. Database Schema/1.3 Request Metadata Table/1.3.1 Court Judgments" "- Add court_judgment_reference field
- Add court_judgment_date field
- Add court_judgment_details field
- Create appropriate indexes for these fields"

update_file "1. Database Schema/1.3 Request Metadata Table/1.3.2 Service Requirements" "- Define service_requirements JSONB field structure
- Include service frequency, duration, and special requirements
- Document the JSONB schema for service requirements
- Create GIN index for JSONB field if needed"

update_file "1. Database Schema/1.3 Request Metadata Table/1.3.3 Family Availability" "- Define family_availability JSONB field structure
- Include weekday availability and specific dates
- Document the JSONB schema for family availability
- Create GIN index for JSONB field if needed"

update_file "1. Database Schema/1.3 Request Metadata Table/1.3.4 RLS Policies" "- Enable row-level security on the table
- Create policy for SELECT operations based on organization_id
- Create policy for INSERT operations with permission checks
- Create policy for UPDATE operations with permission checks"

update_file "1. Database Schema/1.4 Request Status Table/1.4.1 Status Definitions" "- Define all possible request statuses
- Document the meaning and purpose of each status
- Define which fields are required for each status
- Define which actions are allowed in each status"

update_file "1. Database Schema/1.4 Request Status Table/1.4.2 Transition Rules" "- Define allowed status transitions
- Document conditions for each transition
- Define validation rules for each transition
- Create triggers or functions to enforce transition rules"

# 2. Domain Structure
update_file "2. Domain Structure/2.1 Folder Setup/2.1.1 Domain Configuration" "- Create domain.ts configuration file
- Define domain ID constant (request)
- Define domain name constant (Request)
- Define base path constant (/protected/request)
- Define domain permissions object
- Export domain configuration object"

update_file "2. Domain Structure/2.1 Folder Setup/2.1.2 Types Definition" "- Create types/index.ts file
- Define RequestStatus type
- Define Request type from database schema
- Define RequestInsert and RequestUpdate types
- Define RequestHistory type
- Define RequestWithRelations interface
- Define RequestListParams interface"

update_file "2. Domain Structure/2.1 Folder Setup/2.1.3 Services Structure" "- Create services directory structure
- Create RequestService.ts file
- Create RequestWorkflowService.ts file
- Create RequestHistoryService.ts file
- Set up service class structure with static methods
- Import necessary dependencies"

update_file "2. Domain Structure/2.2 Features Organization/2.2.1 Management Feature" "- Create management feature directory
- Set up actions subdirectory
- Set up components subdirectory
- Set up pages subdirectory with list, create, view, edit
- Create feature-specific types and services if needed"

update_file "2. Domain Structure/2.2 Features Organization/2.2.2 Workflow Feature" "- Create workflow feature directory
- Set up actions subdirectory for workflow actions
- Set up components subdirectory for workflow UI
- Set up pages subdirectory for workflow pages
- Create feature-specific types and services if needed"

update_file "2. Domain Structure/2.2 Features Organization/2.2.3 History Feature" "- Create history feature directory
- Set up actions subdirectory for history actions
- Set up components subdirectory for history UI
- Set up pages subdirectory for history pages
- Create feature-specific types and services if needed"

# 3. Services Implementation
update_file "3. Services Implementation/3.1 Request Service/3.1.1 CRUD Operations" "- Implement create method
- Implement getById method with optional relations
- Implement update method
- Implement delete method
- Implement list method with pagination and filtering
- Add proper error handling and logging
- Return standardized ServiceResponse objects"

update_file "3. Services Implementation/3.1 Request Service/3.1.2 Status Management" "- Implement changeStatus method
- Add validation for status transitions
- Create helper methods for specific transitions
- Implement status-specific business logic
- Add proper error handling and logging"

update_file "3. Services Implementation/3.1 Request Service/3.1.3 History Tracking" "- Implement createHistoryEntry method
- Add automatic history tracking to all update operations
- Track status changes with previous and new values
- Track field changes in JSONB format
- Include user attribution in history entries"

update_file "3. Services Implementation/3.2 Request Workflow Service/3.2.1 State Transitions" "- Implement submitRequest method
- Implement approveRequest method
- Implement rejectRequest method
- Implement waitlistRequest method
- Implement completeRequest method
- Add validation for each transition"

update_file "3. Services Implementation/3.2 Request Workflow Service/3.2.2 Validation Rules" "- Implement validateForSubmission method
- Implement validateForApproval method
- Implement validateForRejection method
- Implement validateForWaitlist method
- Implement validateForCompletion method
- Create reusable validation helpers"

update_file "3. Services Implementation/3.2 Request Workflow Service/3.2.3 Notification Triggers" "- Implement sendSubmissionNotification method
- Implement sendApprovalNotification method
- Implement sendRejectionNotification method
- Implement sendWaitlistNotification method
- Implement sendCompletionNotification method
- Integrate with NotificationService"

update_file "3. Services Implementation/3.3 Request History Service/3.3.1 History Retrieval" "- Implement getHistoryByRequestId method
- Add pagination and filtering options
- Include user details in history entries
- Format history entries for display
- Add proper error handling and logging"

update_file "3. Services Implementation/3.3 Request History Service/3.3.2 Change Comparison" "- Implement compareChanges method
- Create helpers to format different types of changes
- Implement diffing logic for complex fields
- Format changes for display in UI
- Handle special cases like status changes"

# 4. Server Actions
update_file "4. Server Actions/4.1 Management Actions/4.1.1 Create Request" "- Create server action function with 'use server' directive
- Accept form data or structured input
- Validate input data
- Call RequestService.create method
- Handle errors and return appropriate response
- Add revalidatePath for cache invalidation
- Redirect to appropriate page on success"

update_file "4. Server Actions/4.1 Management Actions/4.1.2 View Request" "- Create server action function with 'use server' directive
- Accept request ID parameter
- Call RequestService.getById method with relations
- Handle not found errors
- Format data for UI consumption
- Return data with proper typing"

update_file "4. Server Actions/4.1 Management Actions/4.1.3 Update Request" "- Create server action function with 'use server' directive
- Accept request ID and form data
- Validate input data
- Call RequestService.update method
- Handle errors and return appropriate response
- Add revalidatePath for cache invalidation
- Redirect to appropriate page on success"

update_file "4. Server Actions/4.1 Management Actions/4.1.4 Delete Request" "- Create server action function with 'use server' directive
- Accept request ID parameter
- Call RequestService.delete method
- Handle errors and return appropriate response
- Add revalidatePath for cache invalidation
- Redirect to list page on success"

update_file "4. Server Actions/4.1 Management Actions/4.1.5 List Requests" "- Create server action function with 'use server' directive
- Accept pagination and filtering parameters
- Call RequestService.list method
- Format response for UI consumption
- Handle errors and return appropriate response
- Support search, filtering, and sorting"

update_file "4. Server Actions/4.2 Workflow Actions/4.2.1 Submit Request" "- Create server action function with 'use server' directive
- Accept request ID parameter
- Call RequestWorkflowService.submitRequest method
- Handle validation errors
- Add revalidatePath for cache invalidation
- Return success/error status with message"

update_file "4. Server Actions/4.2 Workflow Actions/4.2.2 Approve Request" "- Create server action function with 'use server' directive
- Accept request ID parameter
- Call RequestWorkflowService.approveRequest method
- Handle validation errors
- Add revalidatePath for cache invalidation
- Return success/error status with message"

update_file "4. Server Actions/4.2 Workflow Actions/4.2.3 Reject Request" "- Create server action function with 'use server' directive
- Accept request ID and rejection reason
- Call RequestWorkflowService.rejectRequest method
- Handle validation errors
- Add revalidatePath for cache invalidation
- Return success/error status with message"

update_file "4. Server Actions/4.2 Workflow Actions/4.2.4 Waitlist Request" "- Create server action function with 'use server' directive
- Accept request ID and waitlist position
- Call RequestWorkflowService.waitlistRequest method
- Handle validation errors
- Add revalidatePath for cache invalidation
- Return success/error status with message"

update_file "4. Server Actions/4.2 Workflow Actions/4.2.5 Complete Request" "- Create server action function with 'use server' directive
- Accept request ID and completion notes
- Call RequestWorkflowService.completeRequest method
- Handle validation errors
- Add revalidatePath for cache invalidation
- Return success/error status with message"

update_file "4. Server Actions/4.3 History Actions/4.3.1 View History" "- Create server action function with 'use server' directive
- Accept request ID parameter
- Call RequestHistoryService.getHistoryByRequestId method
- Format history entries for display
- Handle errors and return appropriate response
- Support pagination if needed"

update_file "4. Server Actions/4.3 History Actions/4.3.2 Compare Changes" "- Create server action function with 'use server' directive
- Accept history entry ID or before/after entries
- Call RequestHistoryService.compareChanges method
- Format comparison results for display
- Handle errors and return appropriate response"

# Continue with the rest of the files in the next script
echo "Updated first batch of files successfully!"
