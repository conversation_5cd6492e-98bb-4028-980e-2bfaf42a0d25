# Database Seeding Scripts

This directory contains scripts for seeding the database with initial data.

## Available Scripts

### `seed-account-employee.ts`

This script creates user accounts for employees in the database and links them to the corresponding employee records.

#### Usage

```bash
# Run with local environment (.env.local)
npx ts-node scripts/db/seed/seed-account-employee.ts

# Run with cloud environment (.env)
npx ts-node scripts/db/seed/seed-account-employee.ts --env=cloud

# Show help
npx ts-node scripts/db/seed/seed-account-employee.ts --help
```

#### What it does

1. Creates a default organization if it doesn't exist
2. Creates a system admin user if it doesn't exist
3. Fetches all employees from the database
4. For each employee:
   - Creates a user account if it doesn't exist
   - Creates a user profile if it doesn't exist
   - Assigns the appropriate role based on job title
   - Links the user account to the employee record

#### Environment Variables

The script requires the following environment variables to be set in either `.env.local` or `.env`:

- `NEXT_PUBLIC_SUPABASE_URL`: The URL of your Supabase project
- `SUPABASE_SERVICE_ROLE_KEY`: The service role key for your Supabase project

#### Options

- `--env=<environment>`: Specify which environment file to use
  - `local`: Use `.env.local` (default)
  - `cloud`: Use `.env`
- `--help`, `-h`: Show help message
