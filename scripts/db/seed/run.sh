#!/usr/bin/env bash
#
# run.sh
#
# This script seeds the database with initial data.
#
# Usage:
#   ./scripts/db/seed/run.sh [--env=local|cloud]
#
# Options:
#   --env=local    Use local environment (.env.local) - default
#   --env=cloud    Use cloud environment (.env)
#

# Exit on error, undefined variables, and propagate pipe errors
set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
  echo -e "${BLUE}[INFO]${NC} $1" >&2
}

log_success() {
  echo -e "${GREEN}[SUCCESS]${NC} $1" >&2
}

log_warning() {
  echo -e "${YELLOW}[WARNING]${NC} $1" >&2
}

log_error() {
  echo -e "${RED}[ERROR]${NC} $1" >&2
}

# Function to seed account and employee data
seed_account_employee() {
  local env_arg="$1"
  log_info "Seeding account and employee data with environment: $env_arg..."

  if ts-node --compiler-options '{"module":"CommonJS"}' ./scripts/db/seed/seed-account-employee.ts --env="$env_arg"; then
    log_success "Account and employee data seeded successfully"
    return 0
  else
    log_error "Failed to seed account and employee data"
    return 1
  fi
}

# Function to display login credentials in an ASCII table
display_login_credentials() {
  log_info "Available login credentials:"
  echo ""
  echo "┌───────────────────────────────┬──────────────┬───────────────────────┐"
  echo "│ Email                         │ Password     │ Role                  │"
  echo "├───────────────────────────────┼──────────────┼───────────────────────┤"
  echo "│ <EMAIL>          │ Password123! │ System Administrator  │"
  echo "├───────────────────────────────┼──────────────┼───────────────────────┤"
  echo "│ <EMAIL>      │ Password123! │ Director              │"
  echo "├───────────────────────────────┼──────────────┼───────────────────────┤"
  echo "│ <EMAIL>    │ Password123! │ Coordinator           │"
  echo "├───────────────────────────────┼──────────────┼───────────────────────┤"
  echo "│ <EMAIL>     │ Password123! │ Coordinator           │"
  echo "├───────────────────────────────┼──────────────┼───────────────────────┤"
  echo "│ <EMAIL>     │ Password123! │ Social Worker         │"
  echo "├───────────────────────────────┼──────────────┼───────────────────────┤"
  echo "│ <EMAIL>        │ Password123! │ Social Worker         │"
  echo "├───────────────────────────────┼──────────────┼───────────────────────┤"
  echo "│ <EMAIL>    │ Password123! │ Social Worker         │"
  echo "├───────────────────────────────┼──────────────┼───────────────────────┤"
  echo "│ <EMAIL>       │ Password123! │ Social Worker         │"
  echo "├───────────────────────────────┼──────────────┼───────────────────────┤"
  echo "│ <EMAIL>       │ Password123! │ Social Worker         │"
  echo "└───────────────────────────────┴──────────────┴───────────────────────┘"
  echo ""
  log_info "Next steps:"
  log_info "1. Run 'npm run dev' to start the application"
  log_info "2. Open http://localhost:3000/en/auth/signin in your browser"
  log_info "3. Log in with any of the credentials above"
  echo ""
}

# Parse command line arguments
parse_args() {
  local env_arg="local"  # Default to local environment

  for arg in "$@"; do
    case $arg in
      --env=*)
        env_arg="${arg#*=}"
        if [[ "$env_arg" != "local" && "$env_arg" != "cloud" ]]; then
          log_error "Invalid environment: $env_arg. Must be 'local' or 'cloud'."
          exit 1
        fi
        ;;
      --help|-h)
        echo "Usage: $0 [--env=local|cloud]"
        echo ""
        echo "Options:"
        echo "  --env=local    Use local environment (.env.local) - default"
        echo "  --env=cloud    Use cloud environment (.env)"
        echo "  --help, -h     Show this help message"
        exit 0
        ;;
    esac
  done

  echo "$env_arg"
}

# Main function
main() {
  log_info "Starting database seeding process..."

  # Parse command line arguments
  local env_arg
  env_arg=$(parse_args "$@")
  log_info "Using environment: $env_arg"

  # Seed account and employee data
  if ! seed_account_employee "$env_arg"; then
    log_error "Database seeding failed"
    return 1
  fi

  log_success "Database seeding completed successfully!"
  log_info "Your database is now populated with initial data"

  # Display login credentials if not disabled
  if [ "${DISPLAY_CREDENTIALS:-true}" != "false" ]; then
    display_login_credentials
  fi

  return 0
}

# Run main function with all arguments
main "$@"