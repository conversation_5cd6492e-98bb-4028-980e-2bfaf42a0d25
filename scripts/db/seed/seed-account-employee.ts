import { createClient } from "@supabase/supabase-js";
import dotenv from "dotenv";

// Parse command line arguments
const args = process.argv.slice(2);
const helpArg = args.includes('--help') || args.includes('-h');

if (helpArg) {
  console.log(`
Usage: ts-node seed-account-employee.ts [options]

Options:
  --env=<environment>  Specify which environment file to use:
                       'local' - Use .env.local (default)
                       'cloud' - Use .env
  --help, -h           Show this help message

Examples:
  ts-node seed-account-employee.ts                 # Uses .env.local
  ts-node seed-account-employee.ts --env=cloud     # Uses .env
  `);
  process.exit(0);
}

const envArg = args.find(arg => arg.startsWith('--env='));
const envFile = envArg ? envArg.split('=')[1] : 'local';

// Load environment variables from the appropriate file
const envPath = envFile === 'cloud' ? '.env' : '.env.local';
console.log(`Using environment file: ${envPath}`);
dotenv.config({ path: envPath });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error("Missing environment variables");
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

// Define types for user profiles
interface NotificationPreferences {
  email: boolean;
  sms: boolean;
  inApp: boolean;
}

interface UserSeedData {
  email: string;
  password: string;
  role: string;
  firstName: string;
  lastName: string;
  phone?: string;
  language: "en" | "fr";
  notificationPreferences: NotificationPreferences;
  organizationId?: string;
  jobTitle?: string;
}

// Employee data structure from the database is inferred from the query result

async function ensureDefaultOrganization() {
  console.log("Ensuring default organization exists...");

  // Check if the organizations table exists
  const { error: tableError } = await supabase
    .from("organizations")
    .select("id")
    .limit(1);

  if (tableError && !tableError.message.includes("relation") && !tableError.message.includes("does not exist")) {
    console.error("Error checking organizations table:", tableError);
    return false;
  }

  // If table doesn't exist, we'll skip this step
  if (tableError && (tableError.message.includes("relation") || tableError.message.includes("does not exist"))) {
    console.warn("Organizations table doesn't exist yet, skipping organization creation");
    return false;
  }

  // Check if default organization exists
  const { error: orgError } = await supabase
    .from("organizations")
    .select("id")
    .eq("id", "00000000-0000-0000-0000-000000000001")
    .single();

  if (orgError && !orgError.message.includes("No rows found")) {
    console.error("Error checking default organization:", orgError);
    return false;
  }

  // Create default organization if it doesn't exist
  if (orgError && orgError.message.includes("No rows found")) {
    const { error: createOrgError } = await supabase
      .from("organizations")
      .insert({
        id: "00000000-0000-0000-0000-000000000001",
        name: "RQRSDA Montreal",
        status: "active",
      });

    if (createOrgError) {
      console.error("Error creating default organization:", createOrgError);
      return false;
    }

    console.log("Created default organization");
  } else {
    console.log("Default organization already exists");
  }

  return true;
}

async function getEmployeeData() {
  console.log("Fetching employee data...");

  const { data: employees, error } = await supabase
    .from("employees")
    .select("id, first_name, last_name, job_title, emails, phones");

  if (error) {
    console.error("Error fetching employees:", error);
    return [];
  }

  return employees || [];
}

async function getEmployeeRole(jobTitle: string): Promise<string> {
  // Map job titles to roles
  const roleMap: Record<string, string> = {
    "Executive Director": "Director",
    "Program Coordinator": "Coordinator",
    "Social Worker": "SocialWorker",
    "Case Manager": "SocialWorker",
    "Family Therapist": "SocialWorker",
    "Supervised Visit Coordinator": "Coordinator",
    "Administrative Assistant": "SocialWorker", // Changed from Staff to SocialWorker
    "Outreach Worker": "SocialWorker"
  };

  return roleMap[jobTitle] || "SocialWorker"; // Default to SocialWorker instead of Staff
}

async function seedUsers() {
  console.log("Seeding users and roles...");

  // Ensure default organization exists
  const organizationReady = await ensureDefaultOrganization();
  if (!organizationReady) {
    console.warn("Proceeding without verifying organization. Some operations might fail.");
  }

  // Create system admin user
  const sysAdmin: UserSeedData = {
    email: "<EMAIL>",
    password: "Password123!",
    role: "SystemAdmin",
    firstName: "System",
    lastName: "Administrator",
    phone: "+****************",
    language: "en",
    notificationPreferences: {
      email: true,
      sms: false,
      inApp: true,
    }
  };

  // Check if sysadmin already exists
  const { data: existingSysAdmin } = await supabase
    .from("user_roles")
    .select("user_id")
    .eq("role", sysAdmin.role);

  if (!existingSysAdmin || existingSysAdmin.length === 0) {
    // Create sysadmin user
    const { data: authUser, error: createError } = await supabase.auth.admin.createUser({
      email: sysAdmin.email,
      password: sysAdmin.password,
      email_confirm: true,
      user_metadata: {
        first_name: sysAdmin.firstName,
        last_name: sysAdmin.lastName,
        phone: sysAdmin.phone,
        language: sysAdmin.language,
        notification_preferences: sysAdmin.notificationPreferences,
      },
    });

    if (createError) {
      console.error(`Error creating system admin user:`, createError);
    } else {
      console.log(`Created system admin user: ${sysAdmin.email}`);

      // Check if profile exists
      const { error: profileError } = await supabase
        .from("user_profiles")
        .select("id")
        .eq("id", authUser.user.id)
        .single();

      if (profileError) {
        // Create profile manually
        const { error: insertProfileError } = await supabase.from("user_profiles").insert({
          id: authUser.user.id,
          first_name: sysAdmin.firstName,
          last_name: sysAdmin.lastName,
          email: sysAdmin.email,
          phone: sysAdmin.phone,
          language: sysAdmin.language,
          notification_preferences: sysAdmin.notificationPreferences,
        });

        if (insertProfileError) {
          console.error(`Error creating profile for system admin:`, insertProfileError);
        } else {
          console.log(`Manually created profile for system admin`);
        }
      }

      // Assign role
      const { error: roleError } = await supabase.from("user_roles").insert({
        user_id: authUser.user.id,
        role: sysAdmin.role,
      });

      if (roleError) {
        console.error(`Error assigning role to system admin:`, roleError);
      } else {
        console.log(`Assigned role ${sysAdmin.role} to system admin`);
      }
    }
  } else {
    console.log("System admin user already exists, skipping...");
  }

  // Get employee data from the database
  const employees = await getEmployeeData();
  console.log(`Found ${employees.length} employees to create accounts for`);

  // Create user accounts for each employee
  for (const employee of employees) {
    // Extract primary email from employee data
    const primaryEmail = employee.emails.find((e: any) => e.primary)?.email;
    if (!primaryEmail) {
      console.warn(`Employee ${employee.first_name} ${employee.last_name} has no primary email, skipping...`);
      continue;
    }

    // Extract primary phone from employee data
    const primaryPhone = employee.phones.find((p: any) => p.primary)?.number;

    // Determine role based on job title
    const role = await getEmployeeRole(employee.job_title);

    // Create user data
    const userData: UserSeedData = {
      email: primaryEmail,
      password: "Password123!",
      role: role,
      firstName: employee.first_name,
      lastName: employee.last_name,
      phone: primaryPhone,
      language: "fr", // Default to French for RQRSDA employees
      notificationPreferences: {
        email: true,
        sms: !!primaryPhone,
        inApp: true,
      },
      organizationId: "00000000-0000-0000-0000-000000000001",
      jobTitle: employee.job_title
    };

    // Check if user already exists
    const { data: existingUser } = await supabase.auth.admin.listUsers();

    // Find user with matching email
    const matchingUser = existingUser.users.find(user => user.email === userData.email);

    if (matchingUser) {
      const existingUserId = matchingUser.id;
      console.log(`User with email ${userData.email} already exists, checking if linked to employee...`);

      // Check if employee is already linked to a user account
      const { data: linkedEmployee } = await supabase
        .from("employees")
        .select("user_account_id")
        .eq("id", employee.id)
        .single();

      if (linkedEmployee && linkedEmployee.user_account_id) {
        console.log(`Employee ${employee.first_name} ${employee.last_name} already linked to a user account, skipping...`);
        continue;
      }

      // Link existing user to employee
      const { error: updateError } = await supabase
        .from("employees")
        .update({ user_account_id: existingUserId })
        .eq("id", employee.id);

      if (updateError) {
        console.error(`Error linking existing user to employee ${employee.first_name} ${employee.last_name}:`, updateError);
      } else {
        console.log(`Linked existing user to employee ${employee.first_name} ${employee.last_name}`);
      }

      continue;
    }

    // Create user with metadata
    const { data: authUser, error: createError } = await supabase.auth.admin.createUser({
      email: userData.email,
      password: userData.password,
      email_confirm: true,
      user_metadata: {
        first_name: userData.firstName,
        last_name: userData.lastName,
        phone: userData.phone,
        language: userData.language,
        notification_preferences: userData.notificationPreferences,
      },
    });

    if (createError) {
      console.error(`Error creating user ${userData.email}:`, createError);
      continue;
    }

    console.log(`Created user: ${userData.email}`);

    // Verify user_profile was created by the trigger
    const { data: profile, error: profileError } = await supabase
      .from("user_profiles")
      .select("*")
      .eq("id", authUser.user.id)
      .single();

    if (profileError) {
      console.error(`Error verifying profile for ${userData.email}:`, profileError);

      // If profile wasn't created by trigger, create it manually
      const { error: insertProfileError } = await supabase.from("user_profiles").insert({
        id: authUser.user.id,
        first_name: userData.firstName,
        last_name: userData.lastName,
        email: userData.email,
        phone: userData.phone,
        language: userData.language,
        notification_preferences: userData.notificationPreferences,
        organization_id: userData.organizationId,
      });

      if (insertProfileError) {
        console.error(`Error creating profile for ${userData.email}:`, insertProfileError);
      } else {
        console.log(`Manually created profile for ${userData.email}`);
      }
    } else {
      console.log(`Profile created by trigger for ${userData.email}`);

      // Update profile with additional fields if needed
      if (profile && (
        profile.phone !== userData.phone ||
        profile.language !== userData.language ||
        profile.organization_id !== userData.organizationId
      )) {
        const { error: updateProfileError } = await supabase
          .from("user_profiles")
          .update({
            phone: userData.phone,
            language: userData.language,
            notification_preferences: userData.notificationPreferences,
            organization_id: userData.organizationId,
          })
          .eq("id", authUser.user.id);

        if (updateProfileError) {
          console.error(`Error updating profile for ${userData.email}:`, updateProfileError);
        } else {
          console.log(`Updated profile for ${userData.email}`);
        }
      }
    }

    // Assign role
    const { error: roleError } = await supabase.from("user_roles").insert({
      user_id: authUser.user.id,
      role: userData.role,
      organization_id: userData.organizationId || null,
    });

    if (roleError) {
      console.error(`Error assigning role to ${userData.email}:`, roleError);
      continue;
    }

    console.log(`Assigned role ${userData.role} to ${userData.email}`);

    // Link user account to employee
    const { error: linkError } = await supabase
      .from("employees")
      .update({ user_account_id: authUser.user.id })
      .eq("id", employee.id);

    if (linkError) {
      console.error(`Error linking user account to employee ${userData.firstName} ${userData.lastName}:`, linkError);
    } else {
      console.log(`Linked user account to employee ${userData.firstName} ${userData.lastName}`);
    }
  }

  console.log("Seeding completed!");
}

seedUsers().catch((err) => {
  console.error("Seeding failed:", err);
  process.exit(1);
});
