#!/usr/bin/env bash
#
# run.sh
#
# This script resets the Supabase database and runs the seed script.
#
# Usage:
#   ./scripts/db/reset/run.sh
#

# Exit on error, undefined variables, and propagate pipe errors
set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
  echo -e "${BLUE}[INFO]${NC} $1" >&2
}

log_success() {
  echo -e "${GREEN}[SUCCESS]${NC} $1" >&2
}

log_warning() {
  echo -e "${YELLOW}[WARNING]${NC} $1" >&2
}

log_error() {
  echo -e "${RED}[ERROR]${NC} $1" >&2
}

# Function to reset the database
reset_database() {
  log_info "Resetting Supabase database..."
  log_info "This will delete all data and apply migrations"
  log_info "Running 'npx supabase db reset'..."

  if npx supabase db reset; then
    log_success "Database reset successfully"
    return 0
  else
    log_error "Failed to reset database"
    return 1
  fi
}

# Function to seed the database
seed_database() {
  log_info "Seeding database with initial data..."

  # The seed script will display the credentials table, so we don't need to display it again
  export DISPLAY_CREDENTIALS=false

  if ./scripts/db/seed/run.sh; then
    log_success "Database seeded successfully"
    return 0
  else
    log_error "Failed to seed database"
    return 1
  fi
}

# Function to display login credentials in an ASCII table
display_login_credentials() {
  log_info "Available login credentials:"
  echo ""
  echo "┌───────────────────────────────┬──────────────┬───────────────────────┐"
  echo "│ Email                         │ Password     │ Role                  │"
  echo "├───────────────────────────────┼──────────────┼───────────────────────┤"
  echo "│ <EMAIL>          │ Password123! │ System Administrator  │"
  echo "├───────────────────────────────┼──────────────┼───────────────────────┤"
  echo "│ <EMAIL>      │ Password123! │ Director              │"
  echo "├───────────────────────────────┼──────────────┼───────────────────────┤"
  echo "│ <EMAIL>    │ Password123! │ Coordinator           │"
  echo "├───────────────────────────────┼──────────────┼───────────────────────┤"
  echo "│ <EMAIL>     │ Password123! │ Coordinator           │"
  echo "├───────────────────────────────┼──────────────┼───────────────────────┤"
  echo "│ <EMAIL>     │ Password123! │ Social Worker         │"
  echo "├───────────────────────────────┼──────────────┼───────────────────────┤"
  echo "│ <EMAIL>        │ Password123! │ Social Worker         │"
  echo "├───────────────────────────────┼──────────────┼───────────────────────┤"
  echo "│ <EMAIL>    │ Password123! │ Social Worker         │"
  echo "├───────────────────────────────┼──────────────┼───────────────────────┤"
  echo "│ <EMAIL>       │ Password123! │ Social Worker         │"
  echo "├───────────────────────────────┼──────────────┼───────────────────────┤"
  echo "│ <EMAIL>       │ Password123! │ Social Worker         │"
  echo "└───────────────────────────────┴──────────────┴───────────────────────┘"
  echo ""
  log_info "Next steps:"
  log_info "1. Run 'npm run dev' to start the application"
  log_info "2. Open http://localhost:3000/en/auth/signin in your browser"
  log_info "3. Log in with any of the credentials above"
  echo ""
}

# Main function
main() {
  log_info "Starting database reset process..."

  # Step 1: Reset the database
  if ! reset_database; then
    log_error "Database reset failed"
    return 1
  fi

  # Step 2: Seed the database
  if ! seed_database; then
    log_warning "Database seeding failed, but reset was successful"
    log_info "You may need to run 'npm run db:seed' manually"
    return 1
  fi

  log_success "Database reset and seed completed successfully!"
  log_info "Your database is now ready to use"

  # Display login credentials
  display_login_credentials

  return 0
}

# Run main function
main
