#!/bin/bash

# Script to guide developers on how to extend the request wizard
# This script provides instructions for adding new steps to the wizard

# Colors for better readability
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Function to display section headers
section() {
  echo -e "\n${BLUE}==== $1 ====${NC}\n"
}

# Function to display steps
step() {
  echo -e "${GREEN}$1.${NC} $2"
}

# Function to display notes
note() {
  echo -e "${YELLOW}NOTE:${NC} $1"
}

# Function to display warnings
warning() {
  echo -e "${RED}WARNING:${NC} $1"
}

# Function to display code examples
code() {
  echo -e "${PURPLE}$1${NC}"
}

# Clear the screen
clear

# Display header
echo -e "${BLUE}============================================${NC}"
echo -e "${BLUE}      Extending the Request Wizard          ${NC}"
echo -e "${BLUE}============================================${NC}"
echo ""
echo "This guide will help you understand how to extend the request wizard with new steps."
echo ""

# Overview section
section "OVERVIEW"
echo "The request wizard is designed to be extensible. You can add new steps to collect additional information."
echo "The current wizard has 4 steps:"
step "1" "Basic Information"
step "2" "Service Requirements"
step "3" "Family Availability"
step "4" "Review"
echo ""
note "Adding a new step involves creating several files and updating existing ones."
note "This guide will walk you through the process step by step."
echo ""

# Ask if the user wants to continue
read -p "Press Enter to continue or Ctrl+C to exit..."

# Step 1: Plan your new step
section "STEP 1: PLAN YOUR NEW STEP"
echo "Before writing any code, plan what information your new step will collect:"
echo ""
step "1" "Determine what information you need to collect"
step "2" "Design the form fields and validation rules"
step "3" "Decide where in the wizard flow the step should appear"
step "4" "Plan how the collected data will be stored and used"
echo ""
note "It's important to have a clear plan before implementing the step."
warning "Make sure the information you're collecting is necessary and relevant."
echo ""

# Ask if the user wants to continue
read -p "Press Enter to continue or Ctrl+C to exit..."

# Step 2: Add translations
section "STEP 2: ADD TRANSLATIONS"
echo "Add translations for your new step in the i18n files:"
echo ""
step "1" "Open src/lib/i18n/locales/en.json and fr.json"
step "2" "Add translations under the 'request.wizard' section"
step "3" "Add step name, title, and any field labels"
echo ""
echo "Example for English (en.json):"
code '{
  "request": {
    "wizard": {
      "steps": {
        "newStep": "New Step"
      },
      "stepTitles": {
        "newStep": "New Step Information"
      },
      "newStep": {
        "field1": "Field 1",
        "field2": "Field 2",
        "notes": "Additional Notes"
      }
    }
  }
}'
echo ""
note "Make sure to add translations for all user-facing text."
note "Follow the existing structure to maintain consistency."
echo ""

# Ask if the user wants to continue
read -p "Press Enter to continue or Ctrl+C to exit..."

# Step 3: Create the form component
section "STEP 3: CREATE THE FORM COMPONENT"
echo "Create a form component for your new step:"
echo ""
step "1" "Create a new directory: src/app/[lang]/protected/request/(features)/wizard/(pages)/[draftId]/new-step/components/"
step "2" "Create a new file: NewStepForm.tsx in this directory"
step "3" "Implement the form with the necessary fields"
echo ""
echo "Basic structure of the form component:"
code '"use client";

import { useActionState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { saveNewStep } from "@/app/[lang]/protected/request/(features)/wizard/actions";
import { ActionState } from "@/lib/types/responses";
import { AutomationDraft } from "@/app/[lang]/protected/automation-firstdraft/lib/types";

interface NewStepFormProps {
  draftId: string;
  draftData: AutomationDraft;
  lang: string;
  dictionary: any;
}

export function NewStepForm({
  draftId,
  draftData,
  lang,
  dictionary,
}: NewStepFormProps) {
  // Initial state for the edit action
  const initialState: ActionState<AutomationDraft> = {
    success: true,
    error: "",
    data: null,
  };

  // Create a server action for updating the draft
  const [state, formAction, pending] = useActionState(saveNewStep, initialState);

  // Extract data from draft
  const draftDataObj = (draftData.data as Record<string, any>) || {};
  const newStepData = draftDataObj.newStep || {};

  return (
    <form action={formAction} className="space-y-4">
      {/* Display error message if there is one */}
      {state.error && (
        <Alert variant="destructive" className="mb-6">
          <AlertDescription>{state.error}</AlertDescription>
        </Alert>
      )}

      {/* Hidden fields */}
      <input type="hidden" name="id" value={draftId} />
      <input type="hidden" name="lang" value={lang} />

      {/* Form fields */}
      <div className="space-y-6">
        {/* Field 1 */}
        <div className="space-y-2">
          <Label htmlFor="field1">
            {dictionary.newStep?.field1 || "Field 1"} *
          </Label>
          <Input
            id="field1"
            name="field1"
            defaultValue={newStepData.field1 || ""}
            required
          />
        </div>

        {/* Field 2 */}
        <div className="space-y-2">
          <Label htmlFor="field2">
            {dictionary.newStep?.field2 || "Field 2"}
          </Label>
          <Input
            id="field2"
            name="field2"
            defaultValue={newStepData.field2 || ""}
          />
        </div>

        {/* Notes field */}
        <div className="space-y-2">
          <Label htmlFor="notes">
            {dictionary.newStep?.notes || "Additional Notes"}
          </Label>
          <Textarea
            id="notes"
            name="notes"
            defaultValue={newStepData.notes || ""}
            rows={4}
          />
        </div>
      </div>

      {/* Submit button */}
      <div className="flex justify-end mt-6">
        <Button type="submit" disabled={pending}>
          {pending
            ? dictionary.common?.saving || "Saving..."
            : dictionary.wizard?.buttons?.nextReview || "Next: Review"}
        </Button>
      </div>
    </form>
  );
}'
echo ""
note "Adapt the form fields to match the information you need to collect."
note "Make sure to use the dictionary for all user-facing text."
echo ""

# Ask if the user wants to continue
read -p "Press Enter to continue or Ctrl+C to exit..."

# Step 4: Create the page component
section "STEP 4: CREATE THE PAGE COMPONENT"
echo "Create a page component for your new step:"
echo ""
step "1" "Create a new file: src/app/[lang]/protected/request/(features)/wizard/(pages)/[draftId]/new-step/page.tsx"
step "2" "Implement the page component that uses your form component"
echo ""
echo "Basic structure of the page component:"
code 'import { notFound } from "next/navigation";
import { StepWizard } from "@/components/ui/step-wizard";
import { H3 } from "@/components/typography";
import { view } from "@/app/[lang]/protected/automation-firstdraft/actions/drafts/view";
import { ActionState } from "@/lib/types/responses";
import { AutomationDraft } from "@/app/[lang]/protected/automation-firstdraft/lib/types";
import { NewStepForm } from "./components/NewStepForm";
import { i18n } from "@/lib/i18n/services/I18nService";

interface NewStepPageProps {
  params: Promise<{
    draftId: string;
    lang: string;
  }>;
}

export default async function NewStepPage({ params }: NewStepPageProps) {
  // Await the params
  const { draftId, lang } = await params;

  // Get the dictionary
  const dictionary = i18n.getDictionary(lang);

  // Get the draft data
  const initialState: ActionState<AutomationDraft> = {
    success: true,
    error: "",
    data: null,
  };

  const result = await view(initialState, { id: draftId });

  // If the draft doesn\'t exist, show the not found page
  if (!result.success || !result.data) {
    notFound();
  }

  const draftData = result.data;

  // Check if this is the correct workflow type
  if (draftData.workflow_type !== "request_creation") {
    notFound();
  }

  return (
    <StepWizard
      title={dictionary.request?.wizard?.wizardTitle || "Create New Request"}
      description={dictionary.request?.wizard?.wizardDescription || "Complete the following steps to create a new request"}
      steps={[
        { label: dictionary.request?.wizard?.steps?.basicInfo || "Basic Info", completed: true },
        { label: dictionary.request?.wizard?.steps?.serviceRequirements || "Service Requirements", completed: true },
        { label: dictionary.request?.wizard?.steps?.familyAvailability || "Family Availability", completed: true },
        { label: dictionary.request?.wizard?.steps?.newStep || "New Step", completed: false },
        { label: dictionary.request?.wizard?.steps?.review || "Review", completed: false },
      ]}
      currentStep={3}
      previousUrl={`/${lang}/protected/request/wizard/${draftId}/family-availability`}
      nextUrl="#"
    >
      <div className="space-y-6">
        <H3>{dictionary.request?.wizard?.stepTitles?.newStep || "New Step Information"}</H3>
        <NewStepForm draftId={draftId} draftData={draftData} lang={lang} dictionary={dictionary.request || {}} />
      </div>
    </StepWizard>
  );
}'
echo ""
note "Adjust the step index and navigation URLs based on where your step appears in the flow."
note "Make sure to update the steps array to include your new step."
echo ""

# Ask if the user wants to continue
read -p "Press Enter to continue or Ctrl+C to exit..."

# Step 5: Create loading and error states
section "STEP 5: CREATE LOADING AND ERROR STATES"
echo "Create loading and error states for your new step:"
echo ""
step "1" "Create loading.tsx in the new-step directory"
step "2" "Create error.tsx in the new-step directory"
echo ""
echo "Example loading.tsx:"
code 'import { StepWizard } from "@/components/ui/step-wizard";
import { H3 } from "@/components/typography";
import { Skeleton } from "@/components/ui/skeleton";

export default function NewStepLoading() {
  return (
    <StepWizard
      title="Create New Request"
      description="Complete the following steps to create a new request"
      steps={[
        { label: "Basic Info", completed: true },
        { label: "Service Requirements", completed: true },
        { label: "Family Availability", completed: true },
        { label: "New Step", completed: false },
        { label: "Review", completed: false },
      ]}
      currentStep={3}
      previousUrl="#"
      nextUrl="#"
    >
      <div className="space-y-6">
        <H3>New Step Information</H3>
        <div className="space-y-6">
          {/* Field 1 skeleton */}
          <div className="space-y-2">
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-10 w-full" />
          </div>

          {/* Field 2 skeleton */}
          <div className="space-y-2">
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-10 w-full" />
          </div>

          {/* Notes field skeleton */}
          <div className="space-y-2">
            <Skeleton className="h-4 w-28" />
            <Skeleton className="h-32 w-full" />
          </div>

          {/* Button skeleton */}
          <div className="flex justify-end mt-6">
            <Skeleton className="h-10 w-32" />
          </div>
        </div>
      </div>
    </StepWizard>
  );
}'
echo ""
echo "Example error.tsx:"
code '"use client";

import { ErrorDisplay } from "@/components/ui/error-display";

export default function NewStepError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  return <ErrorDisplay error={error} reset={reset} />;
}'
echo ""
note "The loading state should match the structure of your form."
note "The error component is standard and can be reused across steps."
echo ""

# Ask if the user wants to continue
read -p "Press Enter to continue or Ctrl+C to exit..."

# Step 6: Create the server action
section "STEP 6: CREATE THE SERVER ACTION"
echo "Create a server action to save the data from your new step:"
echo ""
step "1" "Create a new file: src/app/[lang]/protected/request/(features)/wizard/actions/saveNewStep.ts"
step "2" "Implement the server action to save the form data"
echo ""
echo "Example server action:"
code '"use server";

import { revalidatePath } from "next/cache";
import { redirect } from "next/navigation";
import { logger } from "@/lib/logger/services/LoggerService";
import { ActionState } from "@/lib/types/responses";
import { DraftService } from "@/app/[lang]/protected/automation-firstdraft/lib/services";
import { AutomationDraft } from "@/app/[lang]/protected/automation-firstdraft/lib/types";
import { auth } from "@/lib/authentication/services/AuthenticationService";
import DOMAIN_CONFIG from "../../../lib/config/domain";

export const saveNewStep = async (
  _prevState: ActionState<AutomationDraft>,
  formData: FormData
): Promise<ActionState<AutomationDraft>> => {
  // Get form data
  const draftId = formData.get("id") as string;
  const field1 = formData.get("field1") as string;
  const field2 = formData.get("field2") as string;
  const notes = formData.get("notes") as string;
  const lang = (formData.get("lang") as string) || "en";

  // Flag to track if we should redirect
  let shouldRedirect = false;
  let result;

  try {
    // Get current user
    const user = await auth.getCurrentUser();
    if (!user) {
      return {
        success: false,
        error: "User not authenticated",
        data: null,
      };
    }

    // Validate required fields
    if (!draftId) {
      return {
        success: false,
        error: "Draft ID is required",
        data: null,
      };
    }

    if (!field1) {
      return {
        success: false,
        error: "Field 1 is required",
        data: null,
      };
    }

    // Get the current draft
    const draftResult = await DraftService.getDraft(draftId);
    if (!draftResult.success || !draftResult.data) {
      logger.error(`Error getting draft: ${draftResult.error}`);
      return {
        success: false,
        error: `Failed to get draft: ${draftResult.message}`,
        data: null,
      };
    }

    // Get current data and merge with new data
    const currentData = (draftResult.data.data as Record<string, any>) || {};
    const updatedData = {
      ...currentData,
      newStep: {
        field1,
        field2,
        notes,
      },
    };

    // Create draft update object
    const draft = {
      current_step: "review",
      data: updatedData,
    };

    // Update draft
    result = await DraftService.updateDraft(draftId, draft);

    if (!result.success) {
      logger.error(`Error updating draft: ${result.error}`);
      return {
        success: false,
        error: `Failed to update draft: ${result.message}`,
        data: null,
      };
    }

    // Revalidate path
    revalidatePath(`/${lang}${DOMAIN_CONFIG.basePath}`);

    // Set redirect flag
    shouldRedirect = true;

    // Return success state (this will only be used if redirect doesn\'t happen)
    return {
      success: true,
      error: "",
      data: result.data,
    };
  } catch (error) {
    logger.error(`Unexpected error updating new step: ${error}`);
    return {
      success: false,
      error: `Unexpected error updating new step: ${error}`,
      data: null,
    };
  } finally {
    // Redirect outside of try-catch if successful
    if (shouldRedirect && result && result.data) {
      redirect(`/${lang}/protected/request/wizard/${draftId}/review`);
    }
  }
};'
echo ""
note "Adapt the form field processing to match your form fields."
note "Update the redirect URL to point to the next step in the flow."
echo ""

# Ask if the user wants to continue
read -p "Press Enter to continue or Ctrl+C to exit..."

# Step 7: Update the review page
section "STEP 7: UPDATE THE REVIEW PAGE"
echo "Update the review page to include your new step data:"
echo ""
step "1" "Open src/app/[lang]/protected/request/(features)/wizard/(pages)/[draftId]/review/components/ReviewForm.tsx"
step "2" "Add a new card to display the data from your new step"
echo ""
echo "Example card to add to the review form:"
code '{/* New Step Review */}
<Card>
  <CardHeader className="pb-3">
    <CardTitle className="text-lg font-medium">
      {dictionary.wizard?.steps?.newStep || "New Step"}
    </CardTitle>
  </CardHeader>
  <CardContent className="space-y-4">
    <div>
      <H4 className="text-sm font-medium text-muted-foreground">
        {dictionary.newStep?.field1 || "Field 1"}
      </H4>
      <p>{newStepData.field1 || "-"}</p>
    </div>
    <div>
      <H4 className="text-sm font-medium text-muted-foreground">
        {dictionary.newStep?.field2 || "Field 2"}
      </H4>
      <p>{newStepData.field2 || "-"}</p>
    </div>
    <div>
      <H4 className="text-sm font-medium text-muted-foreground">
        {dictionary.newStep?.notes || "Additional Notes"}
      </H4>
      <p className="whitespace-pre-wrap">{newStepData.notes || "-"}</p>
    </div>
    <div className="flex justify-end">
      <Link
        href={`/${lang}/protected/request/wizard/${draftId}/new-step`}
        className="text-sm text-blue-600 hover:underline"
      >
        {dictionary.common?.edit || "Edit"}
      </Link>
    </div>
  </CardContent>
</Card>'
echo ""
note "Make sure to extract the new step data from the draft data object."
note "Add the card in the appropriate position in the review form."
echo ""

# Ask if the user wants to continue
read -p "Press Enter to continue or Ctrl+C to exit..."

# Step 8: Update the completeRequestCreation action
section "STEP 8: UPDATE THE COMPLETE REQUEST CREATION ACTION"
echo "Update the completeRequestCreation action to include your new step data:"
echo ""
step "1" "Open src/app/[lang]/protected/request/(features)/wizard/actions/completeRequestCreation.ts"
step "2" "Extract the new step data from the draft"
step "3" "Include it in the metadata or history entry"
echo ""
echo "Example code to add:"
code '// Extract data from draft
const basicInfo = draftData.basicInfo || {};
const serviceRequirements = draftData.serviceRequirements || {};
const familyAvailability = draftData.familyAvailability || {};
const newStepData = draftData.newStep || {};'
echo ""
note "Make sure to include the new step data in the metadata or history entry."
note "This ensures the data is saved with the request."
echo ""

# Ask if the user wants to continue
read -p "Press Enter to continue or Ctrl+C to exit..."

# Step 9: Update navigation in other steps
section "STEP 9: UPDATE NAVIGATION IN OTHER STEPS"
echo "Update the navigation in other steps to include your new step:"
echo ""
step "1" "Update the family-availability page to navigate to your new step"
step "2" "Update the steps array in all wizard pages to include your new step"
echo ""
echo "Example update to family-availability page:"
code 'redirect(`/${lang}/protected/request/wizard/${draftId}/new-step`);'
echo ""
note "Make sure to update the navigation flow to include your new step."
note "Update the steps array in all wizard pages to maintain consistency."
echo ""

# Ask if the user wants to continue
read -p "Press Enter to continue or Ctrl+C to exit..."

# Step 10: Test your new step
section "STEP 10: TEST YOUR NEW STEP"
echo "Test your new step thoroughly:"
echo ""
step "1" "Start a new request and navigate through the wizard"
step "2" "Test form validation and error handling"
step "3" "Test navigation between steps"
step "4" "Test the review page displays your new step data correctly"
step "5" "Test submitting the request with your new step data"
echo ""
note "Testing is crucial to ensure your new step works correctly."
warning "Make sure to test edge cases and error scenarios."
echo ""

# Conclusion
section "CONCLUSION"
echo "By following these steps, you can extend the request wizard with new steps to collect additional information."
echo "Remember to maintain consistency with the existing wizard design and user experience."
echo ""
echo "For more information, refer to the application documentation or contact the development team."
echo ""

echo -e "${BLUE}============================================${NC}"
echo -e "${BLUE}      End of Wizard Extension Guide         ${NC}"
echo -e "${BLUE}============================================${NC}"
