#!/bin/bash

# Project name
PROJECT_NAME="RQRSDA Platform Development"

# Function to add issue to project
add_to_project() {
    local issue_number=$1
    echo "Adding issue #$issue_number to project '$PROJECT_NAME'"
    gh issue edit $issue_number --add-project "$PROJECT_NAME"
    echo "Issue #$issue_number added to project"
}

# Add all Milestone 2 issues to the project
for issue_number in 22 23 24 25 26 27 28 29 30 31 32 33 34 35 36; do
    add_to_project $issue_number
done

echo "All issues added to project"
