#!/bin/bash

# Script to add parent-child relationships for a set of issues
# Usage: ./add-relationships.sh <parent_issue_number> <child_issue_number1> [<child_issue_number2> ...]
# Example: ./add-relationships.sh 100 101 102 103
#          This will make issues 101, 102, and 103 children of issue 100

if [ $# -lt 2 ]; then
  echo "Usage: $0 <parent_issue_number> <child_issue_number1> [<child_issue_number2> ...]"
  echo "Example: $0 100 101 102 103"
  exit 1
fi

PARENT_ISSUE_NUMBER=$1
shift  # Remove the first argument (parent issue number)

echo "Adding child issues to parent issue #$PARENT_ISSUE_NUMBER..."

# Loop through all remaining arguments (child issue numbers)
for CHILD_ISSUE_NUMBER in "$@"; do
  bash scripts/add-sub-issue.sh $PARENT_ISSUE_NUMBER $CHILD_ISSUE_NUMBER
done

echo "All child issues have been added to parent issue #$PARENT_ISSUE_NUMBER."
