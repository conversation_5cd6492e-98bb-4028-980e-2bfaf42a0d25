#!/bin/bash

# Project ID for "RQRSDA Platform Development"
PROJECT_ID="PVT_kwHOAtPYAc4A3EAs"

# Function to get node ID for an issue
get_node_id() {
    local issue_number=$1
    local node_id=$(gh api graphql -f query="query { repository(owner: \"mouimet-infinisoft\", name: \"rqrsda2025\") { issue(number: $issue_number) { id } } }" | jq -r '.data.repository.issue.id')
    echo $node_id
}

# Function to add issue to project
add_to_project() {
    local issue_number=$1
    local node_id=$(get_node_id $issue_number)
    echo "Adding issue #$issue_number (ID: $node_id) to project"
    gh api graphql -f query="mutation { addProjectV2ItemById(input: { projectId: \"$PROJECT_ID\", contentId: \"$node_id\" }) { item { id } } }"
    echo "Issue #$issue_number added to project"
}

# Add all Milestone 11 issues to the project
for issue_number in 100 101 102 103 104 105 106; do
    add_to_project $issue_number
done

echo "All issues added to project"
