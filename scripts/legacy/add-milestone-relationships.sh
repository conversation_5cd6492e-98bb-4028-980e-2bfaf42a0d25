#!/bin/bash

# Script to add parent-child relationships for a milestone
# Usage: ./add-milestone-relationships.sh <milestone_number> <parent_issue_number> <child_issue_numbers...> [<parent_issue_number> <child_issue_numbers...> ...]
# Example: ./add-milestone-relationships.sh 10 94 96 97 95 98 99
#          This will add issues 96 and 97 as children of issue 94, and issues 98 and 99 as children of issue 95 for Milestone 10

if [ $# -lt 3 ]; then
  echo "Usage: $0 <milestone_number> <parent_issue_number> <child_issue_numbers...> [<parent_issue_number> <child_issue_numbers...> ...]"
  echo "Example: $0 10 94 96 97 95 98 99"
  echo "This will add issues 96 and 97 as children of issue 94, and issues 98 and 99 as children of issue 95 for Milestone 10"
  exit 1
fi

MILESTONE_NUMBER=$1
shift  # Remove the first argument (milestone number)

echo "Adding parent-child relationships for Milestone $MILESTONE_NUMBER..."

# Process parent-child relationships
while [ $# -gt 0 ]; do
  PARENT_ISSUE_NUMBER=$1
  shift  # Remove the parent issue number
  
  # Collect child issues until we hit another parent issue (which would be a number followed by a number)
  CHILD_ISSUES=()
  while [ $# -gt 0 ] && ! [[ "$1" =~ ^[0-9]+$ && "$2" =~ ^[0-9]+$ ]]; do
    CHILD_ISSUES+=("$1")
    shift
  done
  
  echo "Processing parent issue #$PARENT_ISSUE_NUMBER with ${#CHILD_ISSUES[@]} child issues: ${CHILD_ISSUES[@]}"
  
  # Add each child issue to the parent
  for CHILD_ISSUE_NUMBER in "${CHILD_ISSUES[@]}"; do
    bash scripts/add-sub-issue.sh $PARENT_ISSUE_NUMBER $CHILD_ISSUE_NUMBER
  done
done

echo "All parent-child relationships for Milestone $MILESTONE_NUMBER have been established."
