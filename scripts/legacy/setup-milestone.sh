#!/bin/bash

# <PERSON><PERSON>t to set up a milestone by adding parent-child relationships and adding issues to the project
# Usage: ./setup-milestone.sh <milestone_number> <all_issue_numbers> -- <parent_issue_number> <child_issue_numbers...> [<parent_issue_number> <child_issue_numbers...> ...]
# Example: ./setup-milestone.sh 10 94 95 96 97 98 99 -- 94 96 97 95 98 99
#          This will add all issues 94-99 to the project, and set up parent-child relationships:
#          - Issues 96 and 97 as children of issue 94
#          - Issues 98 and 99 as children of issue 95

if [ $# -lt 4 ]; then
  echo "Usage: $0 <milestone_number> <all_issue_numbers> -- <parent_issue_number> <child_issue_numbers...> [<parent_issue_number> <child_issue_numbers...> ...]"
  echo "Example: $0 10 94 95 96 97 98 99 -- 94 96 97 95 98 99"
  echo "This will add all issues 94-99 to the project, and set up parent-child relationships:"
  echo "- Issues 96 and 97 as children of issue 94"
  echo "- Issues 98 and 99 as children of issue 95"
  exit 1
fi

MILESTONE_NUMBER=$1
shift  # Remove the first argument (milestone number)

# Collect all issue numbers until we hit the separator
ALL_ISSUES=()
while [ $# -gt 0 ] && [ "$1" != "--" ]; do
  ALL_ISSUES+=("$1")
  shift
done

if [ "$1" != "--" ]; then
  echo "Error: Missing separator '--' between issue numbers and parent-child relationships"
  exit 1
fi

shift  # Remove the separator

echo "Setting up Milestone $MILESTONE_NUMBER..."

# First, add all issues to the project
if [ ${#ALL_ISSUES[@]} -gt 0 ]; then
  echo "Adding all issues to the project..."
  bash scripts/add-issues-to-project.sh "${ALL_ISSUES[@]}"
fi

# Then, set up parent-child relationships
if [ $# -gt 0 ]; then
  echo "Setting up parent-child relationships..."
  bash scripts/add-milestone-relationships.sh "$MILESTONE_NUMBER" "$@"
fi

echo "Milestone $MILESTONE_NUMBER setup completed."

# Update project-context.md to mark the milestone as complete
if [ -f "project-context.md" ]; then
  echo "Updating project-context.md..."
  
  # Find the line with the milestone and update it
  MILESTONE_LINE=$(grep -n "^$MILESTONE_NUMBER\\. .* (Epics and features not yet created)" project-context.md | cut -d: -f1)
  
  if [ -n "$MILESTONE_LINE" ]; then
    MILESTONE_TEXT=$(sed -n "${MILESTONE_LINE}p" project-context.md)
    NEW_TEXT=$(echo "$MILESTONE_TEXT" | sed 's/(Epics and features not yet created)/(Complete with all epics and features created and added to the project)/')
    
    sed -i "${MILESTONE_LINE}s/.*/$NEW_TEXT/" project-context.md
    echo "Updated project-context.md to mark Milestone $MILESTONE_NUMBER as complete."
  else
    echo "Could not find Milestone $MILESTONE_NUMBER in project-context.md or it's already marked as complete."
  fi
fi
