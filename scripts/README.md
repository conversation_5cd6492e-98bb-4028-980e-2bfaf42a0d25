# GitHub Issue Management Scripts

This directory contains simple utility scripts for managing GitHub issues. There are three main scripts that handle the core tasks:

## 1. Add Issues to Project

**Script:** `add-issues-to-project.sh`

Adds one or more issues to the GitHub project board.

```bash
./add-issues-to-project.sh <issue_number1> [<issue_number2> ...]
```

**Example:**
```bash
./add-issues-to-project.sh 100 101 102
```
This adds issues #100, #101, and #102 to the project board.

## 2. Add Sub-Issue (Parent-Child Relationship)

**Script:** `add-sub-issue.sh`

Creates a parent-child relationship between two issues.

```bash
./add-sub-issue.sh <parent_issue_number> <child_issue_number>
```

**Example:**
```bash
./add-sub-issue.sh 100 101
```
This makes issue #101 a child of issue #100.

## 3. Add Issues to Milestone

**Script:** `add-issues-to-milestone.sh`

Adds one or more issues to a specific milestone.

```bash
./add-issues-to-milestone.sh <milestone_number> <issue_number1> [<issue_number2> ...]
```

**Example:**
```bash
./add-issues-to-milestone.sh 10 100 101 102
```
This adds issues #100, #101, and #102 to milestone #10.

## Environment Variables

All scripts support the following environment variables to avoid hardcoded values:

- `GITHUB_OWNER`: The GitHub repository owner (default: detected from git config)
- `GITHUB_REPO`: The GitHub repository name (default: detected from git config)
- `GITHUB_PROJECT_ID`: The GitHub project ID (default: "PVT_kwHOAtPYAc4A3EAs")

## Usage Tips

1. You can combine these scripts to manage your issues efficiently:

```bash
# First, add issues to the project
./add-issues-to-project.sh 100 101 102 103

# Then, create parent-child relationships
./add-sub-issue.sh 100 101
./add-sub-issue.sh 100 102

# Finally, add them to a milestone
./add-issues-to-milestone.sh 10 100 101 102 103
```

2. All scripts provide helpful usage information if run without parameters.
