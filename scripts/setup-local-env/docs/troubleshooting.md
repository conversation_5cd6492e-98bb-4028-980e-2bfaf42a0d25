# Troubleshooting Guide

This guide provides solutions to common issues you might encounter when setting up and using the local development environment.

## Docker Issues

### Docker is not running

**Symptoms:**
- Error message: "Cannot connect to the Docker daemon"
- Error message: "Docker daemon is not running"

**Solutions:**
1. Start Docker Desktop (on Windows or macOS)
2. On Linux, start the Docker service:
   ```bash
   sudo systemctl start docker
   ```
3. Verify Docker is running:
   ```bash
   docker info
   ```

### Permission issues with Docker

**Symptoms:**
- Error message: "Permission denied"
- Error message: "Got permission denied while trying to connect to the Docker daemon socket"

**Solutions:**
1. Add your user to the docker group:
   ```bash
   sudo usermod -aG docker $USER
   ```
2. Log out and log back in for the changes to take effect
3. If the issue persists, run the command with sudo:
   ```bash
   sudo docker info
   ```

## Supabase Issues

### Supabase fails to start

**Symptoms:**
- Error message: "Failed to start Supabase"
- Error message: "Port 54321 is already in use"

**Solutions:**
1. Make sure no other services are using the required ports (54321-54324)
2. Stop any running Supabase instances:
   ```bash
   npm run supabase:stop
   ```
3. If the issue persists, check if there are any orphaned Docker containers:
   ```bash
   docker ps -a
   ```
4. Remove any orphaned Supabase containers:
   ```bash
   docker rm -f $(docker ps -a | grep supabase | awk '{print $1}')
   ```

### Supabase initialization fails

**Symptoms:**
- Error message: "Failed to initialize Supabase project"
- Error message: "supabase init failed"

**Solutions:**
1. Make sure you have the Supabase CLI installed:
   ```bash
   npx supabase --version
   ```
2. If the Supabase directory already exists, remove it and try again:
   ```bash
   rm -rf supabase
   npm run setup:local
   ```
3. Check if there are any permission issues with the directory:
   ```bash
   ls -la
   ```

## Environment Variables Issues

### Environment variables not set

**Symptoms:**
- Error message: "Supabase connection information is not available"
- Application fails to connect to Supabase

**Solutions:**
1. Make sure Supabase is running:
   ```bash
   npm run supabase:start
   ```
2. Set up the environment variables manually:
   ```bash
   source ./scripts/setup-local-env/utils/setup-env-vars.sh
   setup_env_vars
   ```
3. Check if the `.env.local` file exists and contains the correct values:
   ```bash
   cat .env.local
   ```

### Invalid environment variables

**Symptoms:**
- Error message: "SUPABASE_API_URL is not a valid URL"
- Application fails to connect to Supabase

**Solutions:**
1. Check the values in the `.env.local` file:
   ```bash
   cat .env.local
   ```
2. Make sure the URLs are correct (e.g., `http://localhost:54321`)
3. Set up the environment variables again:
   ```bash
   source ./scripts/setup-local-env/utils/setup-env-vars.sh
   setup_env_vars
   ```

## Next.js Issues

### Next.js fails to start

**Symptoms:**
- Error message: "Error: Cannot find module '@next/swc-darwin-arm64'"
- Error message: "Error: Cannot find module 'next'"

**Solutions:**
1. Make sure you have installed the dependencies:
   ```bash
   npm install
   ```
2. Clear the Next.js cache:
   ```bash
   rm -rf .next
   ```
3. Try running the development server again:
   ```bash
   npm run dev
   ```

### Next.js cannot connect to Supabase

**Symptoms:**
- Error message: "Error: Failed to fetch from Supabase"
- Error message: "Error: Invalid Supabase credentials"

**Solutions:**
1. Make sure Supabase is running:
   ```bash
   npm run supabase:start
   ```
2. Check if the environment variables are set correctly:
   ```bash
   cat .env.local
   ```
3. Make sure the Supabase URL and keys are correct
4. Restart the Next.js development server:
   ```bash
   npm run dev
   ```

## Testing Issues

### Tests fail to run

**Symptoms:**
- Error message: "Command not found: bats"
- Error message: "No tests found"

**Solutions:**
1. Make sure you have Bats installed:
   ```bash
   npm run test:setup
   ```
2. If the issue persists, try installing Bats manually:
   ```bash
   git clone https://github.com/bats-core/bats-core.git
   cd bats-core
   ./install.sh /usr/local
   ```
3. Check if the test files exist:
   ```bash
   ls -la scripts/setup-local-env/tests
   ```

### ShellCheck fails

**Symptoms:**
- Error message: "Command not found: shellcheck"
- Error message: "ShellCheck not installed"

**Solutions:**
1. Install ShellCheck:
   - On macOS:
     ```bash
     brew install shellcheck
     ```
   - On Ubuntu/Debian:
     ```bash
     sudo apt-get install shellcheck
     ```
   - On CentOS/RHEL:
     ```bash
     sudo yum install epel-release
     sudo yum install shellcheck
     ```
2. Run ShellCheck manually:
   ```bash
   shellcheck scripts/setup-local-env/*.sh scripts/setup-local-env/utils/*.sh
   ```

## Other Issues

### Script permissions

**Symptoms:**
- Error message: "Permission denied"
- Error message: "Command not found"

**Solutions:**
1. Make sure the scripts are executable:
   ```bash
   chmod +x scripts/setup-local-env/*.sh scripts/setup-local-env/utils/*.sh
   ```
2. Try running the script with bash explicitly:
   ```bash
   bash scripts/setup-local-env/setup-local-env.sh
   ```

### Path issues

**Symptoms:**
- Error message: "No such file or directory"
- Error message: "Cannot find module"

**Solutions:**
1. Make sure you are in the project root directory:
   ```bash
   pwd
   ```
2. Check if the files exist:
   ```bash
   ls -la scripts/setup-local-env
   ```
3. Try using absolute paths:
   ```bash
   bash "$(pwd)/scripts/setup-local-env/setup-local-env.sh"
   ```

If you encounter any other issues not covered in this guide, please contact the development team for assistance.
