# Usage Examples

This document provides examples of how to use the local development environment setup scripts.

## Basic Usage

### Setting Up the Local Development Environment

The most common use case is to set up the local development environment:

```bash
npm run setup:local
```

This will:
1. Check if Docker and Node.js are installed
2. Install missing prerequisites if needed
3. Initialize Supabase project
4. Start Supabase local instance
5. Set up environment variables in `.env.local`

### Managing Supabase

Once the local development environment is set up, you can manage Supabase with these commands:

```bash
# Start Supabase
npm run supabase:start

# Stop Supabase
npm run supabase:stop

# Reset Supabase database
npm run supabase:reset
```

## Advanced Usage

### Running Individual Scripts

You can run individual scripts directly if you need more control:

```bash
# Check prerequisites
bash scripts/setup-local-env/utils/check-prerequisites.sh

# Install prerequisites
bash scripts/setup-local-env/utils/install-prerequisites.sh

# Initialize and start Supabase
bash scripts/setup-local-env/utils/setup-supabase.sh

# Set up environment variables
bash scripts/setup-local-env/utils/setup-env-vars.sh
```

### Sourcing Scripts

You can source the scripts to use their functions in your own scripts:

```bash
# Source check-prerequisites.sh
source scripts/setup-local-env/utils/check-prerequisites.sh

# Use functions from check-prerequisites.sh
if check_docker; then
  echo "Docker is installed and running"
else
  echo "Docker is not installed or not running"
fi

if check_node; then
  echo "Node.js is installed with the correct version"
else
  echo "Node.js is not installed or has the wrong version"
fi
```

### Setting Up Environment Variables Manually

If you need to set up environment variables manually:

```bash
# Source setup-env-vars.sh
source scripts/setup-local-env/utils/setup-env-vars.sh

# Set environment variables
export SUPABASE_API_URL="http://localhost:54321"
export SUPABASE_ANON_KEY="your-anon-key"
export SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"

# Set up environment variables
setup_env_vars
```

## Testing

### Running Tests

You can run tests for the setup scripts:

```bash
# Run all tests
npm run test:setup

# Run tests with coverage
npm run test:setup:coverage
```

### Running Individual Test Files

You can run individual test files:

```bash
# Run tests for check-prerequisites.sh
npx bats scripts/setup-local-env/tests/check-prerequisites.bats

# Run tests for install-prerequisites.sh
npx bats scripts/setup-local-env/tests/install-prerequisites.bats

# Run tests for setup-supabase.sh
npx bats scripts/setup-local-env/tests/setup-supabase.bats

# Run tests for setup-env-vars.sh
npx bats scripts/setup-local-env/tests/setup-env-vars.bats

# Run tests for setup-local-env.sh
npx bats scripts/setup-local-env/tests/main.bats
```

### Running ShellCheck

You can run ShellCheck on the shell scripts:

```bash
# Run ShellCheck on all shell scripts
npm run test:shellcheck

# Run ShellCheck on a specific file
shellcheck scripts/setup-local-env/setup-local-env.sh
```

## Development Workflow

Here's a typical development workflow:

1. Set up the local development environment:
   ```bash
   npm run setup:local
   ```

2. Start the Next.js development server:
   ```bash
   npm run dev
   ```

3. Open [http://localhost:3000](http://localhost:3000) in your browser

4. Access the Supabase Studio at [http://localhost:54323](http://localhost:54323)

5. Make changes to your code

6. When you're done, stop Supabase:
   ```bash
   npm run supabase:stop
   ```

## CI/CD Integration

You can use these scripts in CI/CD pipelines:

```yaml
# Example GitHub Actions workflow
name: CI

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Set up Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18'
      - name: Install dependencies
        run: npm ci
      - name: Set up local development environment
        run: npm run setup:local
      - name: Run tests
        run: npm test
```

## Troubleshooting

If you encounter issues, see the [Troubleshooting Guide](./troubleshooting.md) for solutions to common problems.
