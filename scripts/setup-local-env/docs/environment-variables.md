# Environment Variables Documentation

This document describes the environment variables used in the local development environment.

## Overview

Environment variables are used to configure the application for different environments (development, test, production). The `setup-env-vars.sh` script sets up these environment variables for local development.

## Required Environment Variables

These environment variables are required for the application to function properly:

| Variable | Description | Example |
|----------|-------------|---------|
| `NEXT_PUBLIC_SUPABASE_URL` | The URL of the Supabase API | `http://localhost:54321` |
| `NEXT_PUBLIC_SUPABASE_ANON_KEY` | The anonymous key for Supabase | `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...` |
| `SUPABASE_SERVICE_ROLE_KEY` | The service role key for Supabase | `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...` |

## Optional Environment Variables

These environment variables are optional and have default values:

| Variable | Description | Default Value |
|----------|-------------|---------------|
| `SUPABASE_DB_URL` | The URL of the Supabase database | `postgresql://postgres:postgres@localhost:54322/postgres` |
| `SUPABASE_STUDIO_URL` | The URL of the Supabase Studio | `http://localhost:54323` |
| `NODE_ENV` | The environment (development, test, production) | `development` |
| `NEXT_PUBLIC_APP_URL` | The URL of the application | `http://localhost:3000` |
| `NEXT_PUBLIC_API_URL` | The URL of the API | `http://localhost:3000/api` |

## Environment Files

The application uses the following environment files:

- `.env.local`: Contains environment variables for local development
- `.env.local.example`: Example environment file with placeholder values

## Setting Up Environment Variables

The environment variables are set up automatically when you run the `setup-local-env.sh` script:

```bash
npm run setup:local
```

This script will:
1. Check if Docker and Node.js are installed
2. Install missing prerequisites if needed
3. Initialize Supabase project
4. Start Supabase local instance
5. Get Supabase connection information (API URL, DB URL, Studio URL, anon key, service role key)
6. Set up environment variables in `.env.local`

## How It Works

The `setup-env-vars.sh` script performs the following steps:

1. Checks if Supabase is initialized (supabase directory exists)
2. Runs `npx supabase status` to get the connection information
3. Extracts the API URL, DB URL, Studio URL, anon key, and service role key from the output
4. Validates the environment variables
5. Creates a `.env.local` file with the environment variables

## Manual Setup

If you need to set up the environment variables manually, you can run:

```bash
bash ./scripts/setup-local-env/utils/setup-env-vars.sh
```

This will create a `.env.local` file with the necessary environment variables.

**Note:** Supabase must be initialized and running for this script to work correctly.

## Troubleshooting

If you encounter issues with environment variables, check the following:

1. Make sure Supabase is initialized:
   ```bash
   ls -la supabase
   ```
   If the supabase directory doesn't exist, initialize Supabase:
   ```bash
   npx supabase init
   ```

2. Make sure Supabase is running:
   ```bash
   npx supabase status
   ```
   If Supabase is not running, start it:
   ```bash
   npm run supabase:start
   ```

3. Check the output of the Supabase status command:
   ```bash
   npx supabase status
   ```
   It should include the API URL, DB URL, Studio URL, anon key, and service role key.

4. Verify that the environment variables are set correctly:
   ```bash
   cat .env.local
   ```

5. If the environment variables are not set correctly, you can run the setup script again:
   ```bash
   bash ./scripts/setup-local-env/utils/setup-env-vars.sh
   ```

6. If you need to reset the environment variables, you can delete the `.env.local` file and run the setup script again:
   ```bash
   rm .env.local
   bash ./scripts/setup-local-env/utils/setup-env-vars.sh
   ```

## Additional Information

- The `NEXT_PUBLIC_` prefix is used for environment variables that are exposed to the client-side code.
- The environment variables are loaded automatically by Next.js when the application starts.
- You can add additional environment variables to the `.env.local` file as needed.
