#!/usr/bin/env bash
#
# install-prerequisites.sh
#
# This script installs the required prerequisites for the local development
# environment setup if they are missing.
#
# Usage:
#   source ./scripts/utils/install-prerequisites.sh
#   install_docker
#   install_node
#

# Exit on error, undefined variables, and propagate pipe errors
set -euo pipefail

# Source check-prerequisites.sh for logging functions and checks
UTILS_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "${UTILS_DIR}/check-prerequisites.sh"

# Detect OS
detect_os() {
  if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    echo "linux"
  elif [[ "$OSTYPE" == "darwin"* ]]; then
    echo "macos"
  elif [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
    echo "windows"
  else
    echo "unknown"
  fi
}

# Install Docker if not installed
install_docker() {
  # Check if Docker is already installed
  if command -v docker &> /dev/null && docker info &> /dev/null; then
    log_success "Docker is already installed and running"
    return 0
  fi

  log_info "Installing Docker..."

  # Detect OS
  OS=$(detect_os)

  case "$OS" in
    linux)
      log_info "Detected Linux OS"
      log_info "Installing Docker using convenience script..."

      # Check if curl is installed
      if ! command -v curl &> /dev/null; then
        log_error "curl is not installed"
        log_info "Please install curl and try again"
        return 1
      fi

      # Download and run Docker installation script
      curl -fsSL https://get.docker.com -o get-docker.sh

      # Check if script was downloaded successfully
      if [ ! -f get-docker.sh ]; then
        log_error "Failed to download Docker installation script"
        return 1
      fi

      # Run the script
      log_info "Running Docker installation script..."
      sh get-docker.sh

      # Clean up
      rm get-docker.sh

      # Add current user to docker group
      log_info "Adding current user to docker group..."
      sudo usermod -aG docker "$USER"
      log_info "Added $USER to docker group. You may need to log out and log back in for this to take effect."
      ;;

    macos)
      log_info "Detected macOS"
      log_info "Please install Docker Desktop for Mac manually:"
      log_info "https://docs.docker.com/desktop/mac/install/"
      return 1
      ;;

    windows)
      log_info "Detected Windows"
      log_info "Please install Docker Desktop for Windows manually:"
      log_info "https://docs.docker.com/desktop/windows/install/"
      return 1
      ;;

    *)
      log_error "Unsupported OS: $OSTYPE"
      log_info "Please install Docker manually: https://docs.docker.com/get-docker/"
      return 1
      ;;
  esac

  # Verify installation
  if command -v docker &> /dev/null; then
    log_success "Docker installed successfully"

    # Start Docker service
    log_info "Starting Docker service..."
    if [ "$OS" = "linux" ]; then
      sudo systemctl start docker
    fi

    # Verify Docker is running
    if docker info &> /dev/null; then
      log_success "Docker is running"
    else
      log_warning "Docker is installed but not running"
      log_info "Please start Docker manually"
    fi

    return 0
  else
    log_error "Docker installation failed"
    return 1
  fi
}

# Install Node.js if not installed
install_node() {
  # Check if Node.js is already installed with correct version
  if command -v node &> /dev/null; then
    NODE_VERSION=$(node -v | cut -d 'v' -f 2)
    NODE_MAJOR_VERSION=$(echo "$NODE_VERSION" | cut -d '.' -f 1)

    if [ "$NODE_MAJOR_VERSION" -ge 16 ]; then
      log_success "Node.js v$NODE_VERSION is already installed"
      return 0
    else
      log_warning "Node.js $NODE_VERSION is installed but not supported"
      log_info "Will attempt to install Node.js 16 or higher"
    fi
  fi

  log_info "Installing Node.js..."

  # Detect OS
  OS=$(detect_os)

  case "$OS" in
    linux)
      log_info "Detected Linux OS"
      log_info "Installing Node.js using nvm..."

      # Check if curl is installed
      if ! command -v curl &> /dev/null; then
        log_error "curl is not installed"
        log_info "Please install curl and try again"
        return 1
      fi

      # Install nvm
      log_info "Installing nvm..."
      curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.3/install.sh | bash

      # Source nvm
      export NVM_DIR="$HOME/.nvm"
      [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"

      # Check if nvm was installed successfully
      if ! command -v nvm &> /dev/null; then
        log_error "Failed to install nvm"
        log_info "Please install Node.js manually: https://nodejs.org/en/download/"
        return 1
      fi

      # Install latest LTS version
      log_info "Installing latest LTS version of Node.js..."
      nvm install --lts

      # Use the installed version
      nvm use --lts
      ;;

    macos)
      log_info "Detected macOS"
      log_info "Please install Node.js manually:"
      log_info "https://nodejs.org/en/download/"
      return 1
      ;;

    windows)
      log_info "Detected Windows"
      log_info "Please install Node.js manually:"
      log_info "https://nodejs.org/en/download/"
      return 1
      ;;

    *)
      log_error "Unsupported OS: $OSTYPE"
      log_info "Please install Node.js manually: https://nodejs.org/en/download/"
      return 1
      ;;
  esac

  # Verify installation
  if command -v node &> /dev/null; then
    NODE_VERSION=$(node -v | cut -d 'v' -f 2)
    log_success "Node.js $NODE_VERSION installed successfully"
    return 0
  else
    log_error "Node.js installation failed"
    return 1
  fi
}

# Install all prerequisites
install_all_prerequisites() {
  log_info "Installing all prerequisites..."

  local all_passed=true

  # Check and install Docker
  if ! check_docker; then
    log_info "Docker needs to be installed"
    if ! install_docker; then
      all_passed=false
    fi
  fi

  # Check and install Node.js
  if ! check_node; then
    log_info "Node.js needs to be installed"
    if ! install_node; then
      all_passed=false
    fi
  fi

  if [ "$all_passed" = true ]; then
    log_success "All prerequisites installed successfully"
    return 0
  else
    log_error "Failed to install some prerequisites"
    return 1
  fi
}

# If this script is run directly, install all prerequisites
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
  install_all_prerequisites
fi
