#!/usr/bin/env bash
#
# setup-supabase.sh
#
# This script initializes and starts Supabase for local development.
#
# Usage:
#   source ./scripts/utils/setup-supabase.sh
#   initialize_supabase
#   start_supabase
#

# Exit on error, undefined variables, and propagate pipe errors
set -euo pipefail

# Source check-prerequisites.sh for logging functions
UTILS_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "${UTILS_DIR}/check-prerequisites.sh"

# Global variables to store Supabase connection information
SUPABASE_API_URL=""
SUPABASE_DB_URL=""
SUPABASE_STUDIO_URL=""
SUPABASE_ANON_KEY=""
SUPABASE_SERVICE_ROLE_KEY=""

# Initialize Supabase project
initialize_supabase() {
  log_info "Initializing Supabase project..."

  # Check if Supabase is already initialized
  if [ -d "supabase" ]; then
    log_warning "Supabase project already initialized"
    return 0
  fi

  # Initialize Supabase project
  log_info "Running 'npx supabase init'..."
  local init_output
  init_output=$(npx supabase init)

  if [ $? -ne 0 ]; then
    log_error "Failed to initialize Supabase project"
    return 1
  fi

  log_success "Supabase project initialized successfully"
  return 0
}

# Start Supabase
start_supabase() {
  log_info "Starting Supabase..."

  # Check if Supabase is initialized
  if [ ! -d "supabase" ]; then
    log_error "Supabase project not initialized"
    log_info "Please run initialize_supabase first"
    return 1
  fi

  # Start Supabase and capture output
  log_info "Running 'npx supabase start'..."
  local supabase_output
  supabase_output=$(npx supabase start)

  if [ $? -ne 0 ]; then
    log_error "Failed to start Supabase"
    log_error "$supabase_output"
    return 1
  fi

  log_success "Supabase started successfully"

  # Extract connection information
  SUPABASE_API_URL=$(echo "$supabase_output" | grep -o "API URL: .*" | cut -d ' ' -f 3)
  SUPABASE_DB_URL=$(echo "$supabase_output" | grep -o "DB URL: .*" | cut -d ' ' -f 3)
  SUPABASE_STUDIO_URL=$(echo "$supabase_output" | grep -o "Studio URL: .*" | cut -d ' ' -f 3)
  SUPABASE_ANON_KEY=$(echo "$supabase_output" | grep -o "anon key: .*" | cut -d ' ' -f 3)
  SUPABASE_SERVICE_ROLE_KEY=$(echo "$supabase_output" | grep -o "service_role key: .*" | cut -d ' ' -f 3)

  # Verify that we extracted all the information
  if [ -z "$SUPABASE_API_URL" ] || [ -z "$SUPABASE_ANON_KEY" ] || [ -z "$SUPABASE_SERVICE_ROLE_KEY" ]; then
    log_error "Failed to extract Supabase connection information"
    log_error "Supabase output: $supabase_output"
    return 1
  fi

  # Export variables for other scripts to use
  export SUPABASE_API_URL
  export SUPABASE_DB_URL
  export SUPABASE_STUDIO_URL
  export SUPABASE_ANON_KEY
  export SUPABASE_SERVICE_ROLE_KEY

  log_success "Supabase started successfully"
  log_info "API URL: $SUPABASE_API_URL"
  log_info "Studio URL: $SUPABASE_STUDIO_URL"

  return 0
}

# Stop Supabase
stop_supabase() {
  log_info "Stopping Supabase..."

  # Check if Supabase is initialized
  if [ ! -d "supabase" ]; then
    log_warning "Supabase project not initialized"
    log_info "Nothing to stop"
    return 0
  fi

  # Stop Supabase
  log_info "Running 'npx supabase stop'..."
  if ! npx supabase stop; then
    log_error "Failed to stop Supabase"
    return 1
  fi

  log_success "Supabase stopped successfully"
  return 0
}

# Reset Supabase database
reset_supabase() {
  log_info "Resetting Supabase database..."

  # Check if Supabase is initialized
  if [ ! -d "supabase" ]; then
    log_error "Supabase project not initialized"
    log_info "Please run initialize_supabase first"
    return 1
  fi

  # Reset Supabase database
  log_info "Running 'npx supabase db reset'..."
  if ! npx supabase db reset; then
    log_error "Failed to reset Supabase database"
    return 1
  fi

  log_success "Supabase database reset successfully"
  return 0
}

# Setup Supabase (initialize and start)
setup_supabase() {
  log_info "Setting up Supabase..."

  # Initialize Supabase
  if ! initialize_supabase; then
    log_error "Failed to initialize Supabase"
    return 1
  fi

  # Start Supabase
  if ! start_supabase; then
    log_error "Failed to start Supabase"
    return 1
  fi

  log_success "Supabase setup completed successfully"
  return 0
}

# If this script is run directly, setup Supabase
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
  setup_supabase
fi
