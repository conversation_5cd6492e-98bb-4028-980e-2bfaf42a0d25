#!/usr/bin/env bash
#
# setup-env-vars.sh
#
# This script sets up environment variables for local development.
# It creates a .env.local file with the necessary environment variables
# for connecting to Supabase and other services.
#
# Usage:
#   source ./scripts/setup-local-env/utils/setup-env-vars.sh
#   setup_env_vars
#
# Environment Variables:
#   SUPABASE_API_URL - The URL of the Supabase API (e.g., http://localhost:54321)
#   SUPABASE_ANON_KEY - The anonymous key for Supabase
#   SUPABASE_SERVICE_ROLE_KEY - The service role key for Supabase
#   SUPABASE_DB_URL - The URL of the Supabase database (optional)
#   SUPABASE_STUDIO_URL - The URL of the Supabase Studio (optional)
#   NODE_ENV - The environment (development, test, production) (optional, defaults to development)
#

# Exit on error, undefined variables, and propagate pipe errors
set -euo pipefail

# Source check-prerequisites.sh for logging functions
UTILS_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "${UTILS_DIR}/check-prerequisites.sh"

# Validate environment variables
validate_env_vars() {
  local missing_vars=0
  local required_vars=("SUPABASE_API_URL" "SUPABASE_ANON_KEY" "SUPABASE_SERVICE_ROLE_KEY")

  for var in "${required_vars[@]}"; do
    if [ -z "${!var:-}" ]; then
      log_error "Required environment variable $var is not set"
      missing_vars=$((missing_vars + 1))
    fi
  done

  # Validate URL format for SUPABASE_API_URL
  if [ -n "${SUPABASE_API_URL:-}" ]; then
    if [[ ! "$SUPABASE_API_URL" =~ ^https?://[a-zA-Z0-9.-]+(:([0-9]+))?(/.*)? ]]; then
      log_error "SUPABASE_API_URL is not a valid URL: $SUPABASE_API_URL"
      missing_vars=$((missing_vars + 1))
    fi
  fi

  # Return 1 if any required variables are missing
  if [ $missing_vars -gt 0 ]; then
    return 1
  fi

  return 0
}

# Get Supabase connection information
get_supabase_connection_info() {
  log_info "Getting Supabase connection information..."

  # Check if Supabase is initialized
  if [ ! -d "supabase" ]; then
    log_error "Supabase is not initialized"
    log_info "Please run 'npx supabase init' first"
    return 1
  fi

  # Get Supabase status
  local supabase_status
  supabase_status=$(npx supabase status 2>&1)

  # Check if Supabase is running
  if echo "$supabase_status" | grep -q "No containers are running"; then
    log_error "Supabase is not running"
    log_info "Please run 'npx supabase start' first"
    return 1
  fi

  # Extract API URL
  SUPABASE_API_URL=$(echo "$supabase_status" | grep "API URL:" | awk '{print $3}')
  if [ -z "$SUPABASE_API_URL" ]; then
    SUPABASE_API_URL="http://localhost:54321"
    log_warning "Could not extract API URL from Supabase status, using default: $SUPABASE_API_URL"
  fi
  log_info "API URL: $SUPABASE_API_URL"

  # Extract DB URL
  SUPABASE_DB_URL=$(echo "$supabase_status" | grep "DB URL:" | awk '{print $3}')
  if [ -z "$SUPABASE_DB_URL" ]; then
    SUPABASE_DB_URL="postgresql://postgres:postgres@localhost:54322/postgres"
    log_warning "Could not extract DB URL from Supabase status, using default: $SUPABASE_DB_URL"
  fi
  log_info "DB URL: $SUPABASE_DB_URL"

  # Extract Studio URL
  SUPABASE_STUDIO_URL=$(echo "$supabase_status" | grep "Studio URL:" | awk '{print $3}')
  if [ -z "$SUPABASE_STUDIO_URL" ]; then
    SUPABASE_STUDIO_URL="http://localhost:54323"
    log_warning "Could not extract Studio URL from Supabase status, using default: $SUPABASE_STUDIO_URL"
  fi
  log_info "Studio URL: $SUPABASE_STUDIO_URL"

  # Extract anon key
  SUPABASE_ANON_KEY=$(echo "$supabase_status" | grep "anon key:" | awk '{print $3}')
  if [ -z "$SUPABASE_ANON_KEY" ]; then
    log_error "Could not extract anon key from Supabase status"
    return 1
  fi
  log_info "anon key: $SUPABASE_ANON_KEY"

  # Extract service role key
  SUPABASE_SERVICE_ROLE_KEY=$(echo "$supabase_status" | grep "service_role key:" | awk '{print $3}')
  if [ -z "$SUPABASE_SERVICE_ROLE_KEY" ]; then
    log_error "Could not extract service role key from Supabase status"
    return 1
  fi
  log_info "service_role key: $SUPABASE_SERVICE_ROLE_KEY"

  # Export variables
  export SUPABASE_API_URL
  export SUPABASE_DB_URL
  export SUPABASE_STUDIO_URL
  export SUPABASE_ANON_KEY
  export SUPABASE_SERVICE_ROLE_KEY

  log_success "Successfully retrieved Supabase connection information"
  return 0
}

# Set up environment variables
setup_env_vars() {
  log_info "Setting up environment variables..."

  # Get Supabase connection information
  if ! get_supabase_connection_info; then
    log_error "Failed to get Supabase connection information"
    return 1
  fi

  # Validate environment variables
  if ! validate_env_vars; then
    log_error "Supabase connection information is not available or invalid"
    return 1
  fi

  # Set default values for optional variables
  NODE_ENV=${NODE_ENV:-development}

  # Create .env.local file
  ENV_FILE=".env.local"

  # Check if .env.local already exists
  if [ -f "$ENV_FILE" ]; then
    log_warning "$ENV_FILE already exists"
    log_info "Backing up existing $ENV_FILE to $ENV_FILE.bak"
    cp "$ENV_FILE" "$ENV_FILE.bak"
  fi

  # Create .env.local file
  log_info "Creating $ENV_FILE..."
  cat > "$ENV_FILE" << EOF || { log_error "Failed to create $ENV_FILE"; return 1; }
# Supabase connection variables
# Generated by setup-local-env.sh on $(date)

# Supabase connection URLs
NEXT_PUBLIC_SUPABASE_URL=$SUPABASE_API_URL
NEXT_PUBLIC_SUPABASE_ANON_KEY=$SUPABASE_ANON_KEY
SUPABASE_SERVICE_ROLE_KEY=$SUPABASE_SERVICE_ROLE_KEY
SUPABASE_DB_URL=$SUPABASE_DB_URL
SUPABASE_STUDIO_URL=$SUPABASE_STUDIO_URL

# Environment
NODE_ENV=$NODE_ENV

# Application settings
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_API_URL=http://localhost:3000/api
EOF

  # Create .env.local.example file if it doesn't exist
  if [ ! -f "$ENV_FILE.example" ]; then
    log_info "Creating $ENV_FILE.example..."
    cat > "$ENV_FILE.example" << EOF || { log_warning "Failed to create $ENV_FILE.example"; }
# Supabase connection variables

# Supabase connection URLs
NEXT_PUBLIC_SUPABASE_URL=http://localhost:54321
NEXT_PUBLIC_SUPABASE_ANON_KEY=<anon-key>
SUPABASE_SERVICE_ROLE_KEY=<service-role-key>
SUPABASE_DB_URL=postgresql://postgres:postgres@localhost:54322/postgres
SUPABASE_STUDIO_URL=http://localhost:54323

# Environment
NODE_ENV=development

# Application settings
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_API_URL=http://localhost:3000/api
EOF
  fi

  # Verify that the environment variables were set correctly
  if [ ! -f "$ENV_FILE" ]; then
    log_error "Failed to create $ENV_FILE"
    return 1
  fi

  log_success "Environment variables set up successfully"
  log_info "Environment variables saved to $ENV_FILE"
  log_info "You can modify these variables in $ENV_FILE as needed"

  return 0
}

# If this script is run directly, setup environment variables
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
  setup_env_vars
fi
