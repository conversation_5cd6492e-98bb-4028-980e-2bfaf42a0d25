#!/usr/bin/env bash
#
# check-prerequisites.sh
#
# This script checks if the required prerequisites are installed for the local
# development environment setup.
#
# Usage:
#   source ./scripts/utils/check-prerequisites.sh
#   check_docker
#   check_node
#

# Exit on error, undefined variables, and propagate pipe errors
set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
  echo -e "${BLUE}[INFO]${NC} $1" >&2
}

log_success() {
  echo -e "${GREEN}[SUCCESS]${NC} $1" >&2
}

log_warning() {
  echo -e "${YELLOW}[WARNING]${NC} $1" >&2
}

log_error() {
  echo -e "${RED}[ERROR]${NC} $1" >&2
}

# Check if Docker is installed and running
check_docker() {
  log_info "Checking if Docker is installed..."
  
  if ! command -v docker &> /dev/null; then
    log_error "Docker is not installed"
    log_info "Please install Docker: https://docs.docker.com/get-docker/"
    return 1
  fi
  
  log_info "Checking if Docker is running..."
  if ! docker info &> /dev/null; then
    log_error "Docker is installed but not running"
    log_info "Please start Docker and try again"
    return 1
  fi
  
  # Check Docker Compose
  log_info "Checking if Docker Compose is available..."
  if ! docker compose version &> /dev/null; then
    log_warning "Docker Compose V2 not detected"
    
    # Check for legacy docker-compose
    if ! command -v docker-compose &> /dev/null; then
      log_error "Docker Compose is not installed"
      log_info "Please install Docker Compose: https://docs.docker.com/compose/install/"
      return 1
    else
      log_warning "Using legacy Docker Compose"
    fi
  fi
  
  log_success "Docker is installed and running correctly"
  return 0
}

# Check if Node.js is installed with correct version
check_node() {
  log_info "Checking if Node.js is installed..."
  
  if ! command -v node &> /dev/null; then
    log_error "Node.js is not installed"
    log_info "Please install Node.js: https://nodejs.org/"
    return 1
  fi
  
  # Check Node.js version
  NODE_VERSION=$(node -v | cut -d 'v' -f 2)
  NODE_MAJOR_VERSION=$(echo "$NODE_VERSION" | cut -d '.' -f 1)
  
  log_info "Detected Node.js version: $NODE_VERSION"
  
  # Require Node.js 16 or higher
  if [ "$NODE_MAJOR_VERSION" -lt 16 ]; then
    log_error "Node.js version $NODE_VERSION is not supported"
    log_info "Please install Node.js 16 or higher"
    return 1
  fi
  
  # Check npm
  log_info "Checking if npm is installed..."
  if ! command -v npm &> /dev/null; then
    log_error "npm is not installed"
    log_info "Please install npm (usually comes with Node.js)"
    return 1
  fi
  
  log_success "Node.js $NODE_VERSION and npm are installed correctly"
  return 0
}

# Check if all prerequisites are installed
check_all_prerequisites() {
  log_info "Checking all prerequisites..."
  
  local all_passed=true
  
  if ! check_docker; then
    all_passed=false
  fi
  
  if ! check_node; then
    all_passed=false
  fi
  
  if [ "$all_passed" = true ]; then
    log_success "All prerequisites are installed correctly"
    return 0
  else
    log_error "Some prerequisites are missing or misconfigured"
    return 1
  fi
}

# If this script is run directly, run all checks
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
  check_all_prerequisites
fi
