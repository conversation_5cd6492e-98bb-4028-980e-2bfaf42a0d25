#!/usr/bin/env bats
#
# Tests for setup-env-vars.sh
#

# Load test helper
load test_helper

# Setup function to run before each test
setup() {
  # Call the parent setup function
  load_script

  # Mock external commands
  mock_commands

  # Create a temporary directory for testing
  export TEST_DIR="$(mktemp -d)"
  cd "$TEST_DIR"

  # Create a mock supabase directory
  mkdir -p "supabase"

  # Mock npx supabase status command
  npx() {
    if [[ "$1" == "supabase" && "$2" == "status" ]]; then
      echo "API URL: http://localhost:54321"
      echo "DB URL: postgresql://postgres:postgres@localhost:54322/postgres"
      echo "Studio URL: http://localhost:54323"
      echo "anon key: test-anon-key"
      echo "service_role key: test-service-role-key"
      return 0
    fi
    return 1
  }
  export -f npx
}

# Teardown function to run after each test
teardown() {
  # Remove the temporary directory
  if [ -d "$TEST_DIR" ]; then
    rm -rf "$TEST_DIR"
  fi

  # Unset environment variables
  unset SUPABASE_API_URL
  unset SUPABASE_DB_URL
  unset SUPABASE_STUDIO_URL
  unset SUPABASE_ANON_KEY
  unset SUPABASE_SERVICE_ROLE_KEY
  unset TEST_DIR

  # Unset functions
  unset -f npx
}

# Test get_supabase_connection_info function
@test "get_supabase_connection_info should succeed when Supabase is initialized and running" {
  # Run get_supabase_connection_info
  run get_supabase_connection_info

  # Assert that get_supabase_connection_info succeeded
  [ "$status" -eq 0 ]

  # Assert that the output contains success message
  [[ "$output" == *"Successfully retrieved Supabase connection information"* ]]

  # We can't check the environment variables directly because they're set in a subshell
  # Instead, we'll check that the function outputs the expected values
  [[ "$output" == *"API URL: http://localhost:54321"* ]]
  [[ "$output" == *"DB URL: postgresql://postgres:postgres@localhost:54322/postgres"* ]]
  [[ "$output" == *"Studio URL: http://localhost:54323"* ]]
  [[ "$output" == *"anon key: test-anon-key"* ]]
  [[ "$output" == *"service_role key: test-service-role-key"* ]]
}

@test "get_supabase_connection_info should use default values when URLs are missing" {
  # Mock npx supabase status command with missing URLs
  npx() {
    if [[ "$1" == "supabase" && "$2" == "status" ]]; then
      echo "Some other output"
      echo "anon key: test-anon-key"
      echo "service_role key: test-service-role-key"
      return 0
    fi
    return 1
  }
  export -f npx

  # Run get_supabase_connection_info
  run get_supabase_connection_info

  # Assert that get_supabase_connection_info succeeded
  [ "$status" -eq 0 ]

  # Assert that the output contains warning messages
  [[ "$output" == *"Could not extract API URL from Supabase status, using default: http://localhost:54321"* ]]
  [[ "$output" == *"Could not extract DB URL from Supabase status, using default: postgresql://postgres:postgres@localhost:54322/postgres"* ]]
  [[ "$output" == *"Could not extract Studio URL from Supabase status, using default: http://localhost:54323"* ]]

  # Assert that the output contains the default values
  [[ "$output" == *"API URL: http://localhost:54321"* ]]
  [[ "$output" == *"DB URL: postgresql://postgres:postgres@localhost:54322/postgres"* ]]
  [[ "$output" == *"Studio URL: http://localhost:54323"* ]]
}

@test "get_supabase_connection_info should fail when Supabase is not initialized" {
  # Remove supabase directory
  rm -rf "supabase"

  # Run get_supabase_connection_info
  run get_supabase_connection_info

  # Assert that get_supabase_connection_info failed
  [ "$status" -eq 1 ]

  # Assert that the output contains error message
  [[ "$output" == *"Supabase is not initialized"* ]]
}

@test "get_supabase_connection_info should fail when Supabase is not running" {
  # Mock npx supabase status command to return error
  npx() {
    if [[ "$1" == "supabase" && "$2" == "status" ]]; then
      echo "No containers are running"
      return 1
    fi
    return 1
  }
  export -f npx

  # Run get_supabase_connection_info
  run get_supabase_connection_info

  # Assert that get_supabase_connection_info failed
  [ "$status" -eq 1 ]

  # Assert that the output contains error message
  [[ "$output" == *"Supabase is not running"* ]]
}

@test "get_supabase_connection_info should fail when anon key is missing" {
  # Mock npx supabase status command with missing anon key
  npx() {
    if [[ "$1" == "supabase" && "$2" == "status" ]]; then
      echo "API URL: http://localhost:54321"
      echo "DB URL: postgresql://postgres:postgres@localhost:54322/postgres"
      echo "Studio URL: http://localhost:54323"
      echo "service_role key: test-service-role-key"
      return 0
    fi
    return 1
  }
  export -f npx

  # Run get_supabase_connection_info
  run get_supabase_connection_info

  # Assert that get_supabase_connection_info failed
  [ "$status" -eq 1 ]

  # Assert that the output contains error message
  [[ "$output" == *"Could not extract anon key from Supabase status"* ]]
}

@test "get_supabase_connection_info should fail when service role key is missing" {
  # Mock npx supabase status command with missing service role key
  npx() {
    if [[ "$1" == "supabase" && "$2" == "status" ]]; then
      echo "API URL: http://localhost:54321"
      echo "DB URL: postgresql://postgres:postgres@localhost:54322/postgres"
      echo "Studio URL: http://localhost:54323"
      echo "anon key: test-anon-key"
      return 0
    fi
    return 1
  }
  export -f npx

  # Run get_supabase_connection_info
  run get_supabase_connection_info

  # Assert that get_supabase_connection_info failed
  [ "$status" -eq 1 ]

  # Assert that the output contains error message
  [[ "$output" == *"Could not extract service role key from Supabase status"* ]]
}

# Test validate_env_vars function
@test "validate_env_vars should succeed when all required environment variables are set" {
  # Set environment variables
  export SUPABASE_API_URL="http://localhost:54321"
  export SUPABASE_ANON_KEY="test-anon-key"
  export SUPABASE_SERVICE_ROLE_KEY="test-service-role-key"

  # Run validate_env_vars
  run validate_env_vars

  # Assert that validate_env_vars succeeded
  [ "$status" -eq 0 ]
}

@test "validate_env_vars should fail when required environment variables are not set" {
  # Unset environment variables
  unset SUPABASE_API_URL
  unset SUPABASE_ANON_KEY
  unset SUPABASE_SERVICE_ROLE_KEY

  # Run validate_env_vars
  run validate_env_vars

  # Assert that validate_env_vars failed
  [ "$status" -eq 1 ]

  # Assert that the output contains error messages
  [[ "$output" == *"Required environment variable SUPABASE_API_URL is not set"* ]]
  [[ "$output" == *"Required environment variable SUPABASE_ANON_KEY is not set"* ]]
  [[ "$output" == *"Required environment variable SUPABASE_SERVICE_ROLE_KEY is not set"* ]]
}

@test "validate_env_vars should fail when SUPABASE_API_URL is not a valid URL" {
  # Set invalid URL
  export SUPABASE_API_URL="not-a-valid-url"
  export SUPABASE_ANON_KEY="test-anon-key"
  export SUPABASE_SERVICE_ROLE_KEY="test-service-role-key"

  # Run validate_env_vars
  run validate_env_vars

  # Assert that validate_env_vars failed
  [ "$status" -eq 1 ]

  # Assert that the output contains error message
  [[ "$output" == *"SUPABASE_API_URL is not a valid URL"* ]]
}

# Test setup_env_vars function
@test "setup_env_vars should succeed when environment variables are set" {
  # Run setup_env_vars
  run setup_env_vars

  # Assert that setup_env_vars succeeded
  [ "$status" -eq 0 ]

  # Assert that the output contains success message
  [[ "$output" == *"Environment variables set up successfully"* ]]

  # Assert that .env.local file was created
  [ -f ".env.local" ]

  # Assert that .env.local.example file was created
  [ -f ".env.local.example" ]

  # Assert that .env.local contains the correct values
  grep -q "NEXT_PUBLIC_SUPABASE_URL=http://localhost:54321" ".env.local"
  grep -q "NEXT_PUBLIC_SUPABASE_ANON_KEY=test-anon-key" ".env.local"
  grep -q "SUPABASE_SERVICE_ROLE_KEY=test-service-role-key" ".env.local"
  grep -q "SUPABASE_DB_URL=postgresql://postgres:postgres@localhost:54322/postgres" ".env.local"
  grep -q "SUPABASE_STUDIO_URL=http://localhost:54323" ".env.local"
  grep -q "NODE_ENV=development" ".env.local"
  grep -q "NEXT_PUBLIC_APP_URL=http://localhost:3000" ".env.local"
  grep -q "NEXT_PUBLIC_API_URL=http://localhost:3000/api" ".env.local"
}

@test "setup_env_vars should fail when Supabase is not initialized" {
  # Remove supabase directory
  rm -rf "supabase"

  # Run setup_env_vars
  run setup_env_vars

  # Assert that setup_env_vars failed
  [ "$status" -eq 1 ]

  # Assert that the output contains error message
  [[ "$output" == *"Supabase is not initialized"* ]]
}

@test "setup_env_vars should backup existing .env.local file" {
  # Create an existing .env.local file
  echo "EXISTING=value" > ".env.local"

  # Run setup_env_vars
  run setup_env_vars

  # Assert that setup_env_vars succeeded
  [ "$status" -eq 0 ]

  # Assert that .env.local.bak file was created
  [ -f ".env.local.bak" ]

  # Assert that .env.local.bak contains the original content
  grep -q "EXISTING=value" ".env.local.bak"

  # Assert that .env.local contains the new content
  grep -q "NEXT_PUBLIC_SUPABASE_URL=http://localhost:54321" ".env.local"
}

@test "setup_env_vars should fail when .env.local file cannot be created" {
  # Mock the cat command to fail
  cat() {
    return 1
  }
  export -f cat

  # Run setup_env_vars
  run setup_env_vars

  # Assert that setup_env_vars failed
  [ "$status" -eq 1 ]

  # Unset the mock
  unset -f cat
}
