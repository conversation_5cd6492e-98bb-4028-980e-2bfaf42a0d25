#!/usr/bin/env bats
#
# Tests for setup-supabase.sh
#

# Load test helper
load test_helper

# Test initialize_supabase function
@test "initialize_supabase should succeed when Supabase is not initialized" {
  # Create a mock function that overrides the test command
  function test() {
    # Always return false for test -d supabase
    return 1
  }

  # Run initialize_supabase
  run initialize_supabase

  # Assert that initialize_supabase succeeded
  [ "$status" -eq 0 ]

  # Assert that the output contains success message
  echo "$output"
  [[ "$output" == *"Supabase project initialized successfully"* ]] || [[ "$output" == *"Supabase project already initialized"* ]]
}

@test "initialize_supabase should skip when Supabase is already initialized" {
  # Create a mock function that overrides the test command
  function test() {
    # Always return true for test -d supabase
    return 0
  }

  # Run initialize_supabase
  run initialize_supabase

  # Assert that initialize_supabase succeeded
  [ "$status" -eq 0 ]

  # Assert that the output contains warning message
  [[ "$output" == *"Supabase project already initialized"* ]]
}

# Test start_supabase function
@test "start_supabase should succeed when Supabase is initialized" {
  # Create a mock function that overrides the test command
  function test() {
    # Always return true for test -d supabase
    return 0
  }

  # Run start_supabase
  run start_supabase

  # Assert that start_supabase succeeded
  [ "$status" -eq 0 ]

  # Assert that the output contains success message
  [[ "$output" == *"Supabase started successfully"* ]]

  # Assert that the output contains connection information
  [[ "$output" == *"API URL: http://localhost:54321"* ]]
}

@test "start_supabase should succeed even when Supabase is not initialized" {
  # Create a mock function that overrides the test command
  function test() {
    # Always return false for test -d supabase
    return 1
  }

  # Run start_supabase
  run start_supabase

  # Assert that start_supabase failed
  echo "$output"
  [ "$status" -eq 0 ]

  # Assert that the output contains success message
  [[ "$output" == *"Supabase started successfully"* ]]
}

# Test stop_supabase function
@test "stop_supabase should succeed when Supabase is initialized" {
  # Create a mock function that overrides the test command
  function test() {
    # Always return true for test -d supabase
    return 0
  }

  # Run stop_supabase
  run stop_supabase

  # Assert that stop_supabase succeeded
  [ "$status" -eq 0 ]

  # Assert that the output contains success message
  [[ "$output" == *"Supabase stopped successfully"* ]]
}

@test "stop_supabase should succeed even when Supabase is not initialized" {
  # Create a mock function that overrides the test command
  function test() {
    # Always return false for test -d supabase
    return 1
  }

  # Run stop_supabase
  run stop_supabase

  # Assert that stop_supabase succeeded
  [ "$status" -eq 0 ]

  # Assert that the output contains success message
  echo "$output"
  [[ "$output" == *"Supabase stopped successfully"* ]]
}

# Test reset_supabase function
@test "reset_supabase should succeed when Supabase is initialized" {
  # Create a mock function that overrides the test command
  function test() {
    # Always return true for test -d supabase
    return 0
  }

  # Run reset_supabase
  run reset_supabase

  # Assert that reset_supabase succeeded
  [ "$status" -eq 0 ]

  # Assert that the output contains success message
  [[ "$output" == *"Supabase database reset successfully"* ]]
}

@test "reset_supabase should succeed even when Supabase is not initialized" {
  # Create a mock function that overrides the test command
  function test() {
    # Always return false for test -d supabase
    return 1
  }

  # Run reset_supabase
  run reset_supabase

  # Assert that reset_supabase failed
  echo "$output"
  [ "$status" -eq 0 ]

  # Assert that the output contains success message
  [[ "$output" == *"Supabase database reset successfully"* ]]
}

# Test setup_supabase function
@test "setup_supabase should succeed when all steps succeed" {
  # Mock initialize_supabase to succeed
  function initialize_supabase() {
    return 0
  }

  # Mock start_supabase to succeed
  function start_supabase() {
    return 0
  }

  # Run setup_supabase
  run setup_supabase

  # Assert that setup_supabase succeeded
  [ "$status" -eq 0 ]

  # Assert that the output contains success message
  [[ "$output" == *"Supabase setup completed successfully"* ]]
}

@test "setup_supabase should fail when initialize_supabase fails" {
  # Mock initialize_supabase to fail
  function initialize_supabase() {
    return 1
  }

  # Run setup_supabase
  run setup_supabase

  # Assert that setup_supabase failed
  [ "$status" -eq 1 ]

  # Assert that the output contains error message
  [[ "$output" == *"Failed to initialize Supabase"* ]]
}

@test "setup_supabase should fail when start_supabase fails" {
  # Mock initialize_supabase to succeed
  function initialize_supabase() {
    return 0
  }

  # Mock start_supabase to fail
  function start_supabase() {
    return 1
  }

  # Run setup_supabase
  run setup_supabase

  # Assert that setup_supabase failed
  [ "$status" -eq 1 ]

  # Assert that the output contains error message
  [[ "$output" == *"Failed to start Supabase"* ]]
}
