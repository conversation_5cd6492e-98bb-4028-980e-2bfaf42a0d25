#!/usr/bin/env bats
#
# Tests for the main function in setup-local-env.sh
#

# Load test helper
load test_helper

# Setup function to run before each test
setup() {
  # Define the main function directly in the test file
  main() {
    log_info "Starting local development environment setup..."

    # Step 1: Check prerequisites
    log_info "Step 1: Checking prerequisites..."
    if ! check_all_prerequisites; then
      log_warning "Some prerequisites are missing"

      # Step 2: Install missing prerequisites
      log_info "Step 2: Installing missing prerequisites..."
      if ! install_all_prerequisites; then
        log_error "Failed to install prerequisites"
        log_info "Please install the missing prerequisites manually and try again"
        return 1
      fi
    else
      log_success "All prerequisites are installed"
    fi

    # Step 3: Initialize Supabase
    log_info "Step 3: Initializing Supabase..."
    if ! initialize_supabase; then
      log_error "Failed to initialize Supabase"
      return 1
    fi

    # Step 4: Start Supabase
    log_info "Step 4: Starting Supabase..."
    if ! start_supabase; then
      log_error "Failed to start Supabase"
      return 1
    fi

    # Step 5: Set up environment variables
    log_info "Step 5: Setting up environment variables..."
    if ! setup_env_vars; then
      log_error "Failed to set up environment variables"
      return 1
    fi

    log_success "Local development environment setup completed successfully!"
    log_info "You can now run 'npm run dev' to start the application"
    log_info "Supabase Studio is available at: $SUPABASE_STUDIO_URL"

    # Print available npm scripts
    log_info "Available npm scripts:"
    log_info "  npm run setup:local     - Set up local development environment"
    log_info "  npm run supabase:start  - Start Supabase"
    log_info "  npm run supabase:stop   - Stop Supabase"
    log_info "  npm run supabase:reset  - Reset Supabase database"

    return 0
  }

  # Source the utility scripts
  source "${BATS_TEST_DIRNAME}/../utils/check-prerequisites.sh"

  # Mock external commands
  mock_commands
}

# Test main function
@test "main should succeed when all steps succeed" {
  # Mock check_all_prerequisites to succeed
  function check_all_prerequisites() {
    return 0
  }

  # Mock initialize_supabase to succeed
  function initialize_supabase() {
    return 0
  }

  # Mock start_supabase to succeed
  function start_supabase() {
    # Set up environment variables
    export SUPABASE_API_URL="http://localhost:54321"
    export SUPABASE_STUDIO_URL="http://localhost:54323"
    export SUPABASE_ANON_KEY="test-anon-key"
    export SUPABASE_SERVICE_ROLE_KEY="test-service-role-key"
    return 0
  }

  # Mock setup_env_vars to succeed
  function setup_env_vars() {
    return 0
  }

  # Run main
  run main

  # Assert that main succeeded
  [ "$status" -eq 0 ]

  # Assert that the output contains success message
  [[ "$output" == *"Local development environment setup completed successfully"* ]]
}

@test "main should install prerequisites when some are missing" {
  # Mock check_all_prerequisites to fail
  function check_all_prerequisites() {
    return 1
  }

  # Mock install_all_prerequisites to succeed
  function install_all_prerequisites() {
    return 0
  }

  # Mock initialize_supabase to succeed
  function initialize_supabase() {
    return 0
  }

  # Mock start_supabase to succeed
  function start_supabase() {
    # Set up environment variables
    export SUPABASE_API_URL="http://localhost:54321"
    export SUPABASE_STUDIO_URL="http://localhost:54323"
    export SUPABASE_ANON_KEY="test-anon-key"
    export SUPABASE_SERVICE_ROLE_KEY="test-service-role-key"
    return 0
  }

  # Mock setup_env_vars to succeed
  function setup_env_vars() {
    return 0
  }

  # Run main
  run main

  # Assert that main succeeded
  [ "$status" -eq 0 ]

  # Assert that the output contains warning message
  [[ "$output" == *"Some prerequisites are missing"* ]]

  # Assert that the output contains success message
  [[ "$output" == *"Local development environment setup completed successfully"* ]]
}

@test "main should fail when install_all_prerequisites fails" {
  # Mock check_all_prerequisites to fail
  function check_all_prerequisites() {
    return 1
  }

  # Mock install_all_prerequisites to fail
  function install_all_prerequisites() {
    return 1
  }

  # Run main
  run main

  # Assert that main failed
  [ "$status" -eq 1 ]

  # Assert that the output contains error message
  [[ "$output" == *"Failed to install prerequisites"* ]]
}

@test "main should fail when initialize_supabase fails" {
  # Mock check_all_prerequisites to succeed
  function check_all_prerequisites() {
    return 0
  }

  # Mock initialize_supabase to fail
  function initialize_supabase() {
    return 1
  }

  # Run main
  run main

  # Assert that main failed
  [ "$status" -eq 1 ]

  # Assert that the output contains error message
  [[ "$output" == *"Failed to initialize Supabase"* ]]
}

@test "main should fail when start_supabase fails" {
  # Mock check_all_prerequisites to succeed
  function check_all_prerequisites() {
    return 0
  }

  # Mock initialize_supabase to succeed
  function initialize_supabase() {
    return 0
  }

  # Mock start_supabase to fail
  function start_supabase() {
    return 1
  }

  # Run main
  run main

  # Assert that main failed
  [ "$status" -eq 1 ]

  # Assert that the output contains error message
  [[ "$output" == *"Failed to start Supabase"* ]]
}

@test "main should fail when setup_env_vars fails" {
  # Mock check_all_prerequisites to succeed
  function check_all_prerequisites() {
    return 0
  }

  # Mock initialize_supabase to succeed
  function initialize_supabase() {
    return 0
  }

  # Mock start_supabase to succeed
  function start_supabase() {
    # Set up environment variables
    export SUPABASE_API_URL="http://localhost:54321"
    export SUPABASE_STUDIO_URL="http://localhost:54323"
    export SUPABASE_ANON_KEY="test-anon-key"
    export SUPABASE_SERVICE_ROLE_KEY="test-service-role-key"
    return 0
  }

  # Mock setup_env_vars to fail
  function setup_env_vars() {
    return 1
  }

  # Run main
  run main

  # Assert that main failed
  [ "$status" -eq 1 ]

  # Assert that the output contains error message
  [[ "$output" == *"Failed to set up environment variables"* ]]
}
