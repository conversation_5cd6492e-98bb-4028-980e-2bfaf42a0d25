#!/usr/bin/env bats
#
# Tests for install-prerequisites.sh
#

# Load test helper
load test_helper

# Test detect_os function
@test "detect_os should return linux on Linux" {
  # Mock OSTYPE
  OSTYPE="linux-gnu"
  
  # Run detect_os
  result=$(detect_os)
  
  # Assert that detect_os returned linux
  [ "$result" = "linux" ]
}

@test "detect_os should return macos on macOS" {
  # Mock OSTYPE
  OSTYPE="darwin20.0"
  
  # Run detect_os
  result=$(detect_os)
  
  # Assert that detect_os returned macos
  [ "$result" = "macos" ]
}

@test "detect_os should return windows on Windows" {
  # Mock OSTYPE
  OSTYPE="msys"
  
  # Run detect_os
  result=$(detect_os)
  
  # Assert that detect_os returned windows
  [ "$result" = "windows" ]
}

@test "detect_os should return unknown on unknown OS" {
  # Mock OSTYPE
  OSTYPE="unknown"
  
  # Run detect_os
  result=$(detect_os)
  
  # Assert that detect_os returned unknown
  [ "$result" = "unknown" ]
}

# Test install_docker function
@test "install_docker should skip when Docker is already installed" {
  # Mock command -v docker to succeed
  function command() {
    if [[ "$2" == "docker" ]]; then
      return 0
    fi
    command "$@"
  }
  
  # Mock docker info to succeed
  function docker() {
    if [[ "$1" == "info" ]]; then
      return 0
    fi
    command docker "$@"
  }
  
  # Run install_docker
  run install_docker
  
  # Assert that install_docker succeeded
  [ "$status" -eq 0 ]
  
  # Assert that the output contains success message
  [[ "$output" == *"Docker is already installed and running"* ]]
}

@test "install_docker should fail on unsupported OS" {
  # Mock command -v docker to fail
  function command() {
    if [[ "$2" == "docker" ]]; then
      return 1
    fi
    command "$@"
  }
  
  # Mock detect_os to return unknown
  function detect_os() {
    echo "unknown"
  }
  
  # Run install_docker
  run install_docker
  
  # Assert that install_docker failed
  [ "$status" -eq 1 ]
  
  # Assert that the output contains error message
  [[ "$output" == *"Unsupported OS"* ]]
}

# Test install_node function
@test "install_node should skip when Node.js is already installed with correct version" {
  # Mock command -v node to succeed
  function command() {
    if [[ "$2" == "node" ]]; then
      return 0
    fi
    command "$@"
  }
  
  # Mock node -v to return a valid version
  function node() {
    if [[ "$1" == "-v" ]]; then
      echo "v18.15.0"
      return 0
    fi
    command node "$@"
  }
  
  # Run install_node
  run install_node
  
  # Assert that install_node succeeded
  [ "$status" -eq 0 ]
  
  # Assert that the output contains success message
  [[ "$output" == *"Node.js v18.15.0 is already installed"* ]]
}

@test "install_node should fail on unsupported OS" {
  # Mock command -v node to fail
  function command() {
    if [[ "$2" == "node" ]]; then
      return 1
    fi
    command "$@"
  }
  
  # Mock detect_os to return unknown
  function detect_os() {
    echo "unknown"
  }
  
  # Run install_node
  run install_node
  
  # Assert that install_node failed
  [ "$status" -eq 1 ]
  
  # Assert that the output contains error message
  [[ "$output" == *"Unsupported OS"* ]]
}

# Test install_all_prerequisites function
@test "install_all_prerequisites should succeed when all prerequisites are already installed" {
  # Mock check_docker to succeed
  function check_docker() {
    return 0
  }
  
  # Mock check_node to succeed
  function check_node() {
    return 0
  }
  
  # Run install_all_prerequisites
  run install_all_prerequisites
  
  # Assert that install_all_prerequisites succeeded
  [ "$status" -eq 0 ]
  
  # Assert that the output contains success message
  [[ "$output" == *"All prerequisites installed successfully"* ]]
}

@test "install_all_prerequisites should install missing prerequisites" {
  # Mock check_docker to fail
  function check_docker() {
    return 1
  }
  
  # Mock check_node to fail
  function check_node() {
    return 1
  }
  
  # Mock install_docker to succeed
  function install_docker() {
    return 0
  }
  
  # Mock install_node to succeed
  function install_node() {
    return 0
  }
  
  # Run install_all_prerequisites
  run install_all_prerequisites
  
  # Assert that install_all_prerequisites succeeded
  [ "$status" -eq 0 ]
  
  # Assert that the output contains success message
  [[ "$output" == *"All prerequisites installed successfully"* ]]
}

@test "install_all_prerequisites should fail when some prerequisites fail to install" {
  # Mock check_docker to fail
  function check_docker() {
    return 1
  }
  
  # Mock check_node to fail
  function check_node() {
    return 1
  }
  
  # Mock install_docker to succeed
  function install_docker() {
    return 0
  }
  
  # Mock install_node to fail
  function install_node() {
    return 1
  }
  
  # Run install_all_prerequisites
  run install_all_prerequisites
  
  # Assert that install_all_prerequisites failed
  [ "$status" -eq 1 ]
  
  # Assert that the output contains error message
  [[ "$output" == *"Failed to install some prerequisites"* ]]
}
