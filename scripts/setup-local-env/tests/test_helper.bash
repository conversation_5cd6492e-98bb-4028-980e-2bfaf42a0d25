#!/usr/bin/env bash
#
# Test helper functions for Bats tests
#

# Set up the test environment
setup() {
  # Load the script being tested
  load_script

  # Mock external commands
  mock_commands
}

# Load the script being tested
load_script() {
  # Get the name of the test file
  local test_file="${BATS_TEST_FILENAME##*/}"
  local script_name="${test_file%.bats}.sh"

  # Source the script
  if [ -f "${BATS_TEST_DIRNAME}/../utils/${script_name}" ]; then
    source "${BATS_TEST_DIRNAME}/../utils/${script_name}"
  elif [ -f "${BATS_TEST_DIRNAME}/../${script_name}" ]; then
    source "${BATS_TEST_DIRNAME}/../${script_name}"
  else
    echo "Error: Could not find script ${script_name}" >&2
    return 1
  fi

  # Special case for setup-local-env.sh
  if [ "$script_name" = "setup-local-env.sh" ]; then
    # Source utility scripts
    source "${BATS_TEST_DIRNAME}/../utils/check-prerequisites.sh"
    source "${BATS_TEST_DIRNAME}/../utils/install-prerequisites.sh"
    source "${BATS_TEST_DIRNAME}/../utils/setup-supabase.sh"
    source "${BATS_TEST_DIRNAME}/../utils/setup-env-vars.sh"
  fi
}

# Mock external commands
mock_commands() {
  # Create a temporary directory for mock commands
  export MOCK_BIN_DIR="$(mktemp -d)"
  export PATH="${MOCK_BIN_DIR}:${PATH}"

  # Mock docker command
  mock_docker

  # Mock node command
  mock_node

  # Mock npm command
  mock_npm

  # Mock npx command
  mock_npx

  # Mock test command
  mock_test
}

# Mock docker command
mock_docker() {
  cat > "${MOCK_BIN_DIR}/docker" << 'EOF'
#!/usr/bin/env bash
if [[ "$1" == "info" ]]; then
  # Simulate docker info
  echo "Docker info output"
  exit 0
elif [[ "$1" == "compose" && "$2" == "version" ]]; then
  # Simulate docker compose version
  echo "Docker Compose version v2.15.1"
  exit 0
else
  # Pass through to real docker if needed
  command docker "$@"
fi
EOF
  chmod +x "${MOCK_BIN_DIR}/docker"
}

# Mock node command
mock_node() {
  cat > "${MOCK_BIN_DIR}/node" << 'EOF'
#!/usr/bin/env bash
if [[ "$1" == "-v" ]]; then
  # Simulate node -v
  echo "v18.15.0"
  exit 0
else
  # Pass through to real node if needed
  command node "$@"
fi
EOF
  chmod +x "${MOCK_BIN_DIR}/node"
}

# Mock npm command
mock_npm() {
  cat > "${MOCK_BIN_DIR}/npm" << 'EOF'
#!/usr/bin/env bash
# Simulate npm
echo "npm command: $@"
exit 0
EOF
  chmod +x "${MOCK_BIN_DIR}/npm"
}

# Mock npx command
mock_npx() {
  cat > "${MOCK_BIN_DIR}/npx" << 'EOF'
#!/usr/bin/env bash
if [[ "$1" == "supabase" && "$2" == "init" ]]; then
  # Simulate supabase init
  mkdir -p supabase
  echo "Supabase project initialized successfully"
  exit 0
elif [[ "$1" == "supabase" && "$2" == "start" ]]; then
  # Simulate supabase start
  echo "Started supabase local development setup."
  echo "API URL: http://localhost:54321"
  echo "DB URL: postgresql://postgres:postgres@localhost:54322/postgres"
  echo "Studio URL: http://localhost:54323"
  echo "Inbucket URL: http://localhost:54324"
  echo "anon key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhtdXB0cHBsZnZpaWZyYndtbXR1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE2MTY1MjQ3MjcsImV4cCI6MTkzMjEwMDcyN30.hfZzKU7Lv8Q3l8qz7hfLJQm9mAJ_7dwMzGKzPX0Fims"
  echo "service_role key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhtdXB0cHBsZnZpaWZyYndtbXR1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTYxNjUyNDcyNywiZXhwIjoxOTMyMTAwNzI3fQ.wQlJlM7IHfgfVXwPp8w_D2FB6VkOLHzZmLxrv_LvOEc"
  exit 0
elif [[ "$1" == "supabase" && "$2" == "stop" ]]; then
  # Simulate supabase stop
  echo "Supabase stopped successfully"
  exit 0
elif [[ "$1" == "supabase" && "$2" == "db" && "$3" == "reset" ]]; then
  # Simulate supabase db reset
  echo "Supabase database reset successfully"
  exit 0
else
  # Pass through to real npx if needed
  command npx "$@"
fi
EOF
  chmod +x "${MOCK_BIN_DIR}/npx"
}

# Mock test command
mock_test() {
  cat > "${MOCK_BIN_DIR}/test" << 'EOF'
#!/usr/bin/env bash
if [[ "$1" == "-d" && "$2" == "supabase" ]]; then
  # This will be overridden by the test function in each test
  exit 0
else
  # Pass through to real test command
  command test "$@"
fi
EOF
  chmod +x "${MOCK_BIN_DIR}/test"
}

# Teardown the test environment
teardown() {
  # Remove the temporary directory for mock commands
  if [ -d "${MOCK_BIN_DIR}" ]; then
    rm -rf "${MOCK_BIN_DIR}"
  fi
}

# Assert that a command succeeds
assert_success() {
  "$@"
  local status=$?
  [ $status -eq 0 ] || {
    echo "Command failed with exit status $status: $*" >&2
    return 1
  }
}

# Assert that a command fails
assert_failure() {
  "$@"
  local status=$?
  [ $status -ne 0 ] || {
    echo "Command succeeded, but expected failure: $*" >&2
    return 1
  }
}

# Assert that a file exists
assert_file_exists() {
  [ -f "$1" ] || {
    echo "File does not exist: $1" >&2
    return 1
  }
}

# Assert that a directory exists
assert_dir_exists() {
  [ -d "$1" ] || {
    echo "Directory does not exist: $1" >&2
    return 1
  }
}

# Assert that a file contains a string
assert_file_contains() {
  local file="$1"
  local string="$2"

  grep -q "$string" "$file" || {
    echo "File '$file' does not contain '$string'" >&2
    return 1
  }
}
