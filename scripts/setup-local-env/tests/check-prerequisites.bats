#!/usr/bin/env bats
#
# Tests for check-prerequisites.sh
#

# Load test helper
load test_helper

# Test check_docker function
@test "check_docker should succeed when Dock<PERSON> is installed and running" {
  # Mock command -v docker to succeed
  function command() {
    if [[ "$2" == "docker" ]]; then
      return 0
    fi
    command "$@"
  }
  
  # Mock docker info to succeed
  function docker() {
    if [[ "$1" == "info" ]]; then
      return 0
    elif [[ "$1" == "compose" && "$2" == "version" ]]; then
      return 0
    fi
    command docker "$@"
  }
  
  # Run check_docker
  run check_docker
  
  # Assert that check_docker succeeded
  [ "$status" -eq 0 ]
  
  # Assert that the output contains success message
  [[ "$output" == *"Docker is installed and running correctly"* ]]
}

@test "check_docker should fail when Docker is not installed" {
  # Mock command -v docker to fail
  function command() {
    if [[ "$2" == "docker" ]]; then
      return 1
    fi
    command "$@"
  }
  
  # Run check_docker
  run check_docker
  
  # Assert that check_docker failed
  [ "$status" -eq 1 ]
  
  # Assert that the output contains error message
  [[ "$output" == *"Docker is not installed"* ]]
}

@test "check_docker should fail when Dock<PERSON> is installed but not running" {
  # Mock command -v docker to succeed
  function command() {
    if [[ "$2" == "docker" ]]; then
      return 0
    fi
    command "$@"
  }
  
  # Mock docker info to fail
  function docker() {
    if [[ "$1" == "info" ]]; then
      return 1
    fi
    command docker "$@"
  }
  
  # Run check_docker
  run check_docker
  
  # Assert that check_docker failed
  [ "$status" -eq 1 ]
  
  # Assert that the output contains error message
  [[ "$output" == *"Docker is installed but not running"* ]]
}

# Test check_node function
@test "check_node should succeed when Node.js is installed with correct version" {
  # Mock command -v node to succeed
  function command() {
    if [[ "$2" == "node" ]]; then
      return 0
    elif [[ "$2" == "npm" ]]; then
      return 0
    fi
    command "$@"
  }
  
  # Mock node -v to return a valid version
  function node() {
    if [[ "$1" == "-v" ]]; then
      echo "v18.15.0"
      return 0
    fi
    command node "$@"
  }
  
  # Run check_node
  run check_node
  
  # Assert that check_node succeeded
  [ "$status" -eq 0 ]
  
  # Assert that the output contains success message
  [[ "$output" == *"Node.js 18.15.0 and npm are installed correctly"* ]]
}

@test "check_node should fail when Node.js is not installed" {
  # Mock command -v node to fail
  function command() {
    if [[ "$2" == "node" ]]; then
      return 1
    fi
    command "$@"
  }
  
  # Run check_node
  run check_node
  
  # Assert that check_node failed
  [ "$status" -eq 1 ]
  
  # Assert that the output contains error message
  [[ "$output" == *"Node.js is not installed"* ]]
}

@test "check_node should fail when Node.js version is too old" {
  # Mock command -v node to succeed
  function command() {
    if [[ "$2" == "node" ]]; then
      return 0
    elif [[ "$2" == "npm" ]]; then
      return 0
    fi
    command "$@"
  }
  
  # Mock node -v to return an old version
  function node() {
    if [[ "$1" == "-v" ]]; then
      echo "v14.17.0"
      return 0
    fi
    command node "$@"
  }
  
  # Run check_node
  run check_node
  
  # Assert that check_node failed
  [ "$status" -eq 1 ]
  
  # Assert that the output contains error message
  [[ "$output" == *"Node.js version 14.17.0 is not supported"* ]]
}

@test "check_node should fail when npm is not installed" {
  # Mock command -v node to succeed
  function command() {
    if [[ "$2" == "node" ]]; then
      return 0
    elif [[ "$2" == "npm" ]]; then
      return 1
    fi
    command "$@"
  }
  
  # Mock node -v to return a valid version
  function node() {
    if [[ "$1" == "-v" ]]; then
      echo "v18.15.0"
      return 0
    fi
    command node "$@"
  }
  
  # Run check_node
  run check_node
  
  # Assert that check_node failed
  [ "$status" -eq 1 ]
  
  # Assert that the output contains error message
  [[ "$output" == *"npm is not installed"* ]]
}

# Test check_all_prerequisites function
@test "check_all_prerequisites should succeed when all prerequisites are installed" {
  # Mock check_docker to succeed
  function check_docker() {
    return 0
  }
  
  # Mock check_node to succeed
  function check_node() {
    return 0
  }
  
  # Run check_all_prerequisites
  run check_all_prerequisites
  
  # Assert that check_all_prerequisites succeeded
  [ "$status" -eq 0 ]
  
  # Assert that the output contains success message
  [[ "$output" == *"All prerequisites are installed correctly"* ]]
}

@test "check_all_prerequisites should fail when some prerequisites are missing" {
  # Mock check_docker to fail
  function check_docker() {
    return 1
  }
  
  # Mock check_node to succeed
  function check_node() {
    return 0
  }
  
  # Run check_all_prerequisites
  run check_all_prerequisites
  
  # Assert that check_all_prerequisites failed
  [ "$status" -eq 1 ]
  
  # Assert that the output contains error message
  [[ "$output" == *"Some prerequisites are missing or misconfigured"* ]]
}
