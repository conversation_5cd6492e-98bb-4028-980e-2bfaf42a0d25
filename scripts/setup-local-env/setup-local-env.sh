#!/usr/bin/env bash
#
# setup-local-env.sh
#
# This script sets up a local development environment with Supabase CLI.
#
# Usage:
#   ./scripts/setup-local-env.sh
#

# Exit on error, undefined variables, and propagate pipe errors
set -euo pipefail

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Source utility scripts
source "${SCRIPT_DIR}/utils/check-prerequisites.sh"
source "${SCRIPT_DIR}/utils/install-prerequisites.sh"
source "${SCRIPT_DIR}/utils/setup-supabase.sh"
source "${SCRIPT_DIR}/utils/setup-env-vars.sh"

# Cleanup function
cleanup() {
  log_info "Cleaning up..."

  # Add cleanup tasks here if needed

  # Check if there was an error
  if [ $? -ne 0 ]; then
    log_error "Setup failed"
    log_info "Please check the error messages above and try again"
  fi
}

# Trap signals
trap cleanup EXIT

# Main function
main() {
  log_info "Starting local development environment setup..."

  # Step 1: Check prerequisites
  log_info "Step 1: Checking prerequisites..."
  if ! check_all_prerequisites; then
    log_warning "Some prerequisites are missing"

    # Step 2: Install missing prerequisites
    log_info "Step 2: Installing missing prerequisites..."
    if ! install_all_prerequisites; then
      log_error "Failed to install prerequisites"
      log_info "Please install the missing prerequisites manually and try again"
      return 1
    fi
  else
    log_success "All prerequisites are installed"
  fi

  # Step 3: Initialize Supabase
  log_info "Step 3: Initializing Supabase..."
  if ! initialize_supabase; then
    log_error "Failed to initialize Supabase"
    return 1
  fi

  # Step 4: Start Supabase
  log_info "Step 4: Starting Supabase..."
  if ! start_supabase; then
    log_error "Failed to start Supabase"
    return 1
  fi

  # Step 5: Set up environment variables
  log_info "Step 5: Setting up environment variables..."
  if ! setup_env_vars; then
    log_error "Failed to set up environment variables"
    return 1
  fi

  # Step 6: Seed the database
  log_info "Step 6: Seeding the database..."
  log_info "Running npm run db:seed..."
  if ! npm run db:seed; then
    log_warning "Database seeding failed. You may need to run 'npm run db:seed' manually."
  else
    log_success "Database seeded successfully!"
  fi

  log_success "Local development environment setup completed successfully!"
  log_info "You can now run 'npm run dev' to start the application"
  log_info "Supabase Studio is available at: $SUPABASE_STUDIO_URL"

  # Print available npm scripts
  log_info "Available npm scripts:"
  log_info "  npm run setup:local     - Set up local development environment"
  log_info "  npm run supabase:start  - Start Supabase"
  log_info "  npm run supabase:stop   - Stop Supabase"
  log_info "  npm run supabase:reset  - Reset Supabase database"
  log_info "  npm run db:seed         - Seed the database with initial data"

  return 0
}

# Run main function
main
