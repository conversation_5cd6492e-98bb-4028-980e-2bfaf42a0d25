#!/usr/bin/env bash
#
# Run ShellCheck on all shell scripts
#

# Exit on error, undefined variables, and propagate pipe errors
set -euo pipefail

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
  echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
  echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
  echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
  echo -e "${RED}[ERROR]${NC} $1"
}

# Install ShellCheck if not already installed
install_shellcheck() {
  log_info "Checking if ShellCheck is installed..."
  
  # Check if ShellCheck is already installed
  if command -v shellcheck &> /dev/null; then
    log_success "ShellCheck is already installed"
    return 0
  fi
  
  log_info "Installing ShellCheck..."
  
  # Detect OS
  if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Linux
    if command -v apt-get &> /dev/null; then
      # Debian/Ubuntu
      log_info "Installing ShellCheck using apt..."
      sudo apt-get update
      sudo apt-get install -y shellcheck
    elif command -v yum &> /dev/null; then
      # CentOS/RHEL
      log_info "Installing ShellCheck using yum..."
      sudo yum install -y epel-release
      sudo yum install -y shellcheck
    elif command -v dnf &> /dev/null; then
      # Fedora
      log_info "Installing ShellCheck using dnf..."
      sudo dnf install -y shellcheck
    else
      log_error "Unsupported Linux distribution"
      log_info "Please install ShellCheck manually: https://github.com/koalaman/shellcheck#installing"
      return 1
    fi
  elif [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    if command -v brew &> /dev/null; then
      log_info "Installing ShellCheck using Homebrew..."
      brew install shellcheck
    else
      log_error "Homebrew is not installed"
      log_info "Please install Homebrew first: https://brew.sh/"
      return 1
    fi
  else
    log_error "Unsupported OS: $OSTYPE"
    log_info "Please install ShellCheck manually: https://github.com/koalaman/shellcheck#installing"
    return 1
  fi
  
  # Verify installation
  if command -v shellcheck &> /dev/null; then
    log_success "ShellCheck installed successfully"
    return 0
  else
    log_error "ShellCheck installation failed"
    return 1
  fi
}

# Run ShellCheck on all shell scripts
run_shellcheck() {
  log_info "Running ShellCheck on all shell scripts..."
  
  # Find all shell scripts
  local shell_scripts=("${SCRIPT_DIR}"/*.sh "${SCRIPT_DIR}"/utils/*.sh)
  local total_scripts=${#shell_scripts[@]}
  
  log_info "Found $total_scripts shell scripts"
  
  # Run ShellCheck on each script
  local failed=0
  
  for script in "${shell_scripts[@]}"; do
    log_info "Checking $script..."
    
    if shellcheck -x "$script"; then
      log_success "No issues found in $script"
    else
      log_error "Issues found in $script"
      ((failed++))
    fi
    
    echo ""
  done
  
  if [ $failed -eq 0 ]; then
    log_success "All scripts passed ShellCheck!"
    return 0
  else
    log_error "$failed scripts failed ShellCheck"
    return 1
  fi
}

# Main function
main() {
  log_info "Running ShellCheck on shell scripts..."
  
  # Install ShellCheck
  install_shellcheck
  
  # Run ShellCheck
  run_shellcheck
  
  return 0
}

# Run main function
main
