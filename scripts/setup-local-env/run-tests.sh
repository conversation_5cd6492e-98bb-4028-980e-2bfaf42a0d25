#!/usr/bin/env bash
#
# Run tests for the setup scripts
#

# Exit on error, undefined variables, and propagate pipe errors
set -euo pipefail

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
  echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
  echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
  echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
  echo -e "${RED}[ERROR]${NC} $1"
}

# Install Bats if not already installed
install_bats() {
  log_info "Checking if Bat<PERSON> is installed..."

  # Check if <PERSON><PERSON> is already installed
  if command -v bats &> /dev/null; then
    log_success "Bats is already installed"
    return 0
  fi

  log_info "Installing Bats..."

  # Clone Bats repositories
  BATS_DIR="${SCRIPT_DIR}/tests/bats"

  # Create directory if it doesn't exist
  mkdir -p "${BATS_DIR}"

  # Clone bats-core if not already cloned
  if [ ! -d "${BATS_DIR}/bats-core" ]; then
    log_info "Cloning bats-core..."
    git clone https://github.com/bats-core/bats-core.git "${BATS_DIR}/bats-core"
  fi

  # Clone bats-support if not already cloned
  if [ ! -d "${BATS_DIR}/bats-support" ]; then
    log_info "Cloning bats-support..."
    git clone https://github.com/bats-core/bats-support.git "${BATS_DIR}/bats-support"
  fi

  # Clone bats-assert if not already cloned
  if [ ! -d "${BATS_DIR}/bats-assert" ]; then
    log_info "Cloning bats-assert..."
    git clone https://github.com/bats-core/bats-assert.git "${BATS_DIR}/bats-assert"
  fi

  # Create a bats executable
  cat > "${SCRIPT_DIR}/tests/bats.sh" << 'EOF'
#!/usr/bin/env bash
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
"${SCRIPT_DIR}/bats/bats-core/bin/bats" "$@"
EOF

  # Make it executable
  chmod +x "${SCRIPT_DIR}/tests/bats.sh"

  log_success "Bats installed successfully"
  return 0
}

# Run tests
run_tests() {
  log_info "Running tests..."

  # Find all test files
  local test_files=("${SCRIPT_DIR}"/tests/*.bats)
  local total_tests=${#test_files[@]}

  log_info "Found $total_tests test files"

  # Run tests
  "${SCRIPT_DIR}/tests/bats.sh" "${test_files[@]}"

  local result=$?

  if [ $result -eq 0 ]; then
    log_success "All tests passed!"
  else
    log_error "Some tests failed"
    return 1
  fi

  return 0
}

# Run tests with coverage
run_tests_with_coverage() {
  log_info "Running tests with coverage..."

  # Find all test files
  local test_files=("${SCRIPT_DIR}"/tests/*.bats)
  local total_tests=${#test_files[@]}

  log_info "Found $total_tests test files"

  # Create a temporary directory for coverage reports
  local coverage_dir="${SCRIPT_DIR}/coverage"
  mkdir -p "${coverage_dir}"

  # Run tests with coverage
  BATS_COVERAGE=1 "${SCRIPT_DIR}/tests/bats.sh" "${test_files[@]}"

  local result=$?

  if [ $result -eq 0 ]; then
    log_success "All tests passed!"

    # Generate coverage report
    log_info "Generating coverage report..."

    # Count total number of tests
    local total_tests=0
    for file in "${test_files[@]}"; do
      local tests_in_file=$(grep -c "^@test" "$file")
      total_tests=$((total_tests + tests_in_file))
    done

    # Count total number of functions in utility scripts
    local util_files=("${SCRIPT_DIR}"/utils/*.sh)
    local total_functions=0
    for file in "${util_files[@]}"; do
      local functions_in_file=$(grep -c "^[a-zA-Z0-9_]\+()\s*{" "$file")
      total_functions=$((total_functions + functions_in_file))
    done

    # Calculate coverage percentage
    local coverage_percentage=$((total_tests * 100 / total_functions))

    log_info "Test coverage: ${coverage_percentage}% (${total_tests}/${total_functions})"

    # Save coverage report
    echo "Test coverage: ${coverage_percentage}% (${total_tests}/${total_functions})" > "${coverage_dir}/coverage.txt"
    echo "" >> "${coverage_dir}/coverage.txt"
    echo "Test files:" >> "${coverage_dir}/coverage.txt"
    for file in "${test_files[@]}"; do
      echo "  - $(basename "$file")" >> "${coverage_dir}/coverage.txt"
    done
    echo "" >> "${coverage_dir}/coverage.txt"
    echo "Utility files:" >> "${coverage_dir}/coverage.txt"
    for file in "${util_files[@]}"; do
      echo "  - $(basename "$file")" >> "${coverage_dir}/coverage.txt"
    done

    log_success "Coverage report generated at ${coverage_dir}/coverage.txt"
  else
    log_error "Some tests failed"
    return 1
  fi

  return 0
}

# Main function
main() {
  log_info "Running tests for setup scripts..."

  # Install Bats
  install_bats

  # Check if coverage flag is provided
  if [ "${1:-}" = "--coverage" ]; then
    # Run tests with coverage
    run_tests_with_coverage
  else
    # Run tests
    run_tests
  fi

  return 0
}

# Run main function
main "$@"
