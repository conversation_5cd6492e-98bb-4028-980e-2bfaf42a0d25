# Local Development Environment Setup

This directory contains scripts for setting up the local development environment.

## Overview

The local development environment setup scripts are designed to help developers quickly set up a local development environment for the project. The scripts handle the following tasks:

- Checking for prerequisites (Docker, Node.js)
- Installing missing prerequisites
- Initializing Supabase project
- Starting Supabase local instance
- Setting up environment variables

## Directory Structure

```
scripts/setup-local-env/
├── setup-local-env.sh       # Main setup script
├── run-tests.sh             # Script to run tests
├── run-shellcheck.sh        # Script to run ShellCheck
├── README.md                # This file
├── docs/                    # Documentation
│   └── environment-variables.md  # Environment variables documentation
├── utils/                   # Utility scripts
│   ├── check-prerequisites.sh    # Check prerequisites
│   ├── install-prerequisites.sh  # Install prerequisites
│   ├── setup-supabase.sh         # Set up Supabase
│   └── setup-env-vars.sh         # Set up environment variables
└── tests/                   # Test files
    ├── check-prerequisites.bats  # Tests for check-prerequisites.sh
    ├── install-prerequisites.bats  # Tests for install-prerequisites.sh
    ├── main.bats                 # Tests for setup-local-env.sh
    ├── setup-env-vars.bats       # Tests for setup-env-vars.sh
    ├── setup-supabase.bats       # Tests for setup-supabase.sh
    └── test_helper.bash          # Test helper functions
```

## Usage

### Setting Up the Local Development Environment

To set up the local development environment, run:

```bash
npm run setup:local
```

This will:
1. Check if Docker and Node.js are installed
2. Install missing prerequisites if needed
3. Initialize Supabase project
4. Start Supabase local instance
5. Set up environment variables in `.env.local`

### Managing Supabase

The following npm scripts are available for managing Supabase:

```bash
# Start Supabase
npm run supabase:start

# Stop Supabase
npm run supabase:stop

# Reset Supabase database
npm run supabase:reset
```

### Running Tests

To run the tests for the setup scripts:

```bash
# Run all tests
npm run test:setup

# Run tests with coverage
npm run test:setup:coverage
```

### Running ShellCheck

To run ShellCheck on the shell scripts:

```bash
npm run test:shellcheck
```

## Environment Variables

The setup scripts create a `.env.local` file with the necessary environment variables for local development. See [Environment Variables Documentation](./docs/environment-variables.md) for more information.

## Troubleshooting

### Docker Issues

If you encounter issues with Docker, make sure:
- Docker is installed and running
- You have permission to run Docker commands
- Docker daemon is running

### Supabase Issues

If you encounter issues with Supabase:
- Make sure Docker is running
- Try stopping and starting Supabase:
  ```bash
  npm run supabase:stop
  npm run supabase:start
  ```
- Reset the Supabase database:
  ```bash
  npm run supabase:reset
  ```

### Environment Variables Issues

If you encounter issues with environment variables:
- Make sure Supabase is running
- Check the `.env.local` file
- Try setting up the environment variables again:
  ```bash
  source ./scripts/setup-local-env/utils/setup-env-vars.sh
  setup_env_vars
  ```

## Contributing

When contributing to the setup scripts:
- Add tests for new functionality
- Run tests and ShellCheck before committing
- Update documentation as needed
- Follow the existing code style and conventions
