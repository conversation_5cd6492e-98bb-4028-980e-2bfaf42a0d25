#!/bin/bash

# Script to add issues to a GitHub project
# Usage: ./add-issues-to-project.sh <issue_number1> [<issue_number2> ...]

# Check if issue numbers were provided
if [ $# -eq 0 ]; then
    echo "Usage: $0 <issue_number1> [<issue_number2> ...]"
    echo "Example: $0 100 101 102 103  # Adds issues #100, #101, #102, and #103 to the project"
    exit 1
fi

# Get repository information from git config or environment variables
REPO_OWNER=${GITHUB_OWNER:-$(git config --get remote.origin.url | sed -n 's/.*github.com[:\/]\([^\/]*\).*/\1/p')}
REPO_NAME=${GITHUB_REPO:-$(git config --get remote.origin.url | sed -n 's/.*\/\([^\/]*\)\.git$/\1/p')}

# If we couldn't determine the owner or repo, use defaults
REPO_OWNER=${REPO_OWNER:-"mouimet-infinisoft"}
REPO_NAME=${REPO_NAME:-"rqrsda2025"}

# Project ID - can be set via environment variable
PROJECT_ID=${GITHUB_PROJECT_ID:-"PVT_kwHOAtPYAc4A3EAs"}

# Function to get node ID for an issue
get_node_id() {
    local issue_number=$1
    echo "Getting ID for issue #$issue_number..."
    local node_id=$(gh api graphql -f query='query($owner:String!, $repo:String!, $number:Int!) { repository(owner:$owner, name:$repo) { issue(number:$number) { id } } }' -f owner="$REPO_OWNER" -f repo="$REPO_NAME" -f number=$issue_number | jq -r '.data.repository.issue.id')

    if [ -z "$node_id" ] || [ "$node_id" = "null" ]; then
        echo "Error: Could not get the ID of issue #$issue_number"
        return 1
    fi

    echo $node_id
    return 0
}

# Function to add issue to project
add_to_project() {
    local issue_number=$1
    local node_id=$(get_node_id $issue_number)

    if [ $? -ne 0 ]; then
        echo "Skipping issue #$issue_number due to error"
        return 1
    fi

    echo "Adding issue #$issue_number (ID: $node_id) to project"
    gh api graphql -f query='mutation($project:ID!, $issue:ID!) { addProjectV2ItemById(input: {projectId: $project, contentId: $issue}) { item { id } } }' -f project="$PROJECT_ID" -f issue="$node_id" 2>/dev/null

    if [ $? -eq 0 ]; then
        echo "Issue #$issue_number added to project"
        return 0
    else
        echo "Warning: Issue #$issue_number may already be in the project or there was an error"
        return 1
    fi
}

echo "Adding issues to project..."

# Add all specified issues to the project
SUCCESS_COUNT=0
FAIL_COUNT=0

for issue_number in "$@"; do
    add_to_project $issue_number
    if [ $? -eq 0 ]; then
        SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
    else
        FAIL_COUNT=$((FAIL_COUNT + 1))
    fi
done

echo "Process completed: $SUCCESS_COUNT issues added successfully, $FAIL_COUNT issues failed or were already in the project."
