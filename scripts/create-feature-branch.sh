#!/bin/bash

# Script to create a feature branch for a GitHub issue
# Usage: ./create-feature-branch.sh <issue_number> [<custom_description>]
# Example: ./create-feature-branch.sh 19
#          This will create a branch named feature/issue-19-initialize-nextjs-15-project

if [ $# -lt 1 ]; then
  echo "Usage: $0 <issue_number> [<custom_description>]"
  echo "Example: $0 19"
  exit 1
fi

# Get repository information from git config or environment variables
REPO_OWNER=${GITHUB_OWNER:-$(git config --get remote.origin.url | sed -n 's/.*github.com[:\\/]\\([^\\/]*\\).*/\\1/p')}
REPO_NAME=${GITHUB_REPO:-$(git config --get remote.origin.url | sed -n 's/.*\\/\\([^\\/]*\\)\\.git$/\\1/p')}

# If we couldn't determine the owner or repo, use defaults
REPO_OWNER=${REPO_OWNER:-"mouimet-infinisoft"}
REPO_NAME=${REPO_NAME:-"rqrsda2025"}

# Get issue number
ISSUE_NUMBER=$1

# Get issue title
echo "Getting title for issue #$ISSUE_NUMBER..."
ISSUE_TITLE=$(gh issue view $ISSUE_NUMBER --json title -q .title)

if [ -z "$ISSUE_TITLE" ]; then
  echo "Error: Could not get the title of issue #$ISSUE_NUMBER"
  exit 1
fi

# Convert issue title to lowercase and replace spaces with hyphens
BRANCH_DESCRIPTION=$(echo $ISSUE_TITLE | tr '[:upper:]' '[:lower:]' | sed 's/[^a-z0-9]/-/g' | sed 's/--*/-/g' | sed 's/^-//' | sed 's/-$//')

# Use custom description if provided
if [ $# -ge 2 ]; then
  BRANCH_DESCRIPTION=$2
fi

# Create branch name
BRANCH_NAME="feature/issue-$ISSUE_NUMBER-$BRANCH_DESCRIPTION"

# Check if branch already exists
if git show-ref --verify --quiet refs/heads/$BRANCH_NAME; then
  echo "Branch $BRANCH_NAME already exists"
else
  # Create the branch
  echo "Creating branch $BRANCH_NAME..."
  git checkout -b $BRANCH_NAME
  
  if [ $? -eq 0 ]; then
    echo "Branch $BRANCH_NAME created successfully"
  else
    echo "Error: Failed to create branch $BRANCH_NAME"
    exit 1
  fi
fi

# Link the branch to the issue
echo "Linking branch $BRANCH_NAME to issue #$ISSUE_NUMBER..."
gh issue develop $ISSUE_NUMBER --branch $BRANCH_NAME --checkout

if [ $? -eq 0 ]; then
  echo "Branch $BRANCH_NAME linked to issue #$ISSUE_NUMBER successfully"
else
  echo "Warning: Failed to link branch to issue using gh issue develop. Trying alternative method..."
  
  # Alternative method: Add a comment to the issue
  gh issue comment $ISSUE_NUMBER --body "I've created branch \`$BRANCH_NAME\` for this issue."
  
  if [ $? -eq 0 ]; then
    echo "Added comment to issue #$ISSUE_NUMBER about the branch"
  else
    echo "Error: Failed to add comment to issue #$ISSUE_NUMBER"
  fi
fi

# Push the branch to remote
echo "Pushing branch $BRANCH_NAME to remote..."
git push -u origin $BRANCH_NAME

if [ $? -eq 0 ]; then
  echo "Branch $BRANCH_NAME pushed to remote successfully"
else
  echo "Error: Failed to push branch $BRANCH_NAME to remote"
  exit 1
fi

echo "Feature branch setup complete. You are now on branch $BRANCH_NAME linked to issue #$ISSUE_NUMBER"
