#!/bin/bash

# Scaffolding script for Contact domain
# This script copies the template-single-crud structure and renames it for the Contact domain

echo "Starting Contact domain scaffolding..."

# 1. Copy the entire template structure
echo "Copying template structure..."

# Create the contact domain directory if it doesn't exist
mkdir -p src/app/\[lang\]/protected/contact

# Copy the entire template-single-crud structure to contact
cp -r src/app/\[lang\]/protected/template-single-crud/* src/app/\[lang\]/protected/contact/

# 2. Rename directories first
echo "Renaming directories..."

# Rename feature-1 directory to management
mv src/app/\[lang\]/protected/contact/\(features\)/feature-1 src/app/\[lang\]/protected/contact/\(features\)/management

# 3. Create the relationships structure (not in template)
echo "Creating relationship structure..."

# Create relationship directories and files
mkdir -p src/app/\[lang\]/protected/contact/\(features\)/management/\(pages\)/\[id\]/relationships/{list,create,\[relationshipId\]/{edit,remove}}
touch src/app/\[lang\]/protected/contact/\(features\)/management/\(pages\)/\[id\]/relationships/{layout.tsx}
touch src/app/\[lang\]/protected/contact/\(features\)/management/\(pages\)/\[id\]/relationships/list/{error.tsx,loading.tsx,page.tsx}
touch src/app/\[lang\]/protected/contact/\(features\)/management/\(pages\)/\[id\]/relationships/create/{error.tsx,loading.tsx,page.tsx}
touch src/app/\[lang\]/protected/contact/\(features\)/management/\(pages\)/\[id\]/relationships/\[relationshipId\]/edit/{error.tsx,loading.tsx,page.tsx}
touch src/app/\[lang\]/protected/contact/\(features\)/management/\(pages\)/\[id\]/relationships/\[relationshipId\]/remove/{error.tsx,loading.tsx,page.tsx}

# Create relationship actions and components
mkdir -p src/app/\[lang\]/protected/contact/\(features\)/management/\(pages\)/\[id\]/relationships/actions
mkdir -p src/app/\[lang\]/protected/contact/\(features\)/management/\(pages\)/\[id\]/relationships/components
touch src/app/\[lang\]/protected/contact/\(features\)/management/\(pages\)/\[id\]/relationships/actions/{create.ts,edit.ts,remove.ts,list.ts,index.ts}
touch src/app/\[lang\]/protected/contact/\(features\)/management/\(pages\)/\[id\]/relationships/components/{CreateForm.tsx,EditForm.tsx,RemoveForm.tsx,List.tsx,index.ts}

# 4. Create ContactRelationshipService
echo "Creating ContactRelationshipService..."
touch src/app/\[lang\]/protected/contact/\(features\)/management/lib/services/ContactRelationshipService.ts
touch src/app/\[lang\]/protected/contact/\(features\)/management/lib/types/relationships.ts

# 5. Rename files and update content
echo "Updating file contents..."

# Find all files in the contact domain
find src/app/\[lang\]/protected/contact -type f -name "*.ts*" | while read file; do
  # Replace template-single-crud with contact
  sed -i 's/template-single-crud/contact/g' "$file"
  # Replace feature-1 with management
  sed -i 's/feature-1/management/g' "$file"
  # Replace Feature1 with Contact
  sed -i 's/Feature1/Contact/g' "$file"
  # Replace Feature with Contact
  sed -i 's/Feature /Contact /g' "$file"
  # Replace feature with contact
  sed -i 's/feature/contact/g' "$file"
done

# Rename Feature1Service.ts to ContactService.ts if it exists
if [ -f "src/app/[lang]/protected/contact/(features)/management/lib/services/Feature1Service.ts" ]; then
  mv src/app/\[lang\]/protected/contact/\(features\)/management/lib/services/Feature1Service.ts src/app/\[lang\]/protected/contact/\(features\)/management/lib/services/ContactService.ts
fi

echo "Contact domain scaffolding completed!"
