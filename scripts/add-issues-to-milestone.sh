#!/bin/bash

# Script to add issues to a milestone
# Usage: ./add-issues-to-milestone.sh <milestone_number> <issue_number1> [<issue_number2> ...]

# Check if milestone number and at least one issue number were provided
if [ $# -lt 2 ]; then
    echo "Usage: $0 <milestone_number> <issue_number1> [<issue_number2> ...]"
    echo "Example: $0 10 100 101 102  # Adds issues #100, #101, and #102 to milestone #10"
    exit 1
fi

# Get repository information from git config or environment variables
REPO_OWNER=${GITHUB_OWNER:-$(git config --get remote.origin.url | sed -n 's/.*github.com[:\\/]\\([^\\/]*\\).*/\\1/p')}
REPO_NAME=${GITHUB_REPO:-$(git config --get remote.origin.url | sed -n 's/.*\\/\\([^\\/]*\\)\\.git$/\\1/p')}

# If we couldn't determine the owner or repo, use defaults
REPO_OWNER=${REPO_OWNER:-"mouimet-infinisoft"}
REPO_NAME=${REPO_NAME:-"rqrsda2025"}

# Get milestone number
MILESTONE_NUMBER=$1
shift  # Remove the first argument (milestone number)

# Get milestone ID
echo "Getting ID for milestone #$MILESTONE_NUMBER..."
MILESTONE_ID=$(gh api repos/$REPO_OWNER/$REPO_NAME/milestones --jq ".[] | select(.number == $MILESTONE_NUMBER) | .id")

if [ -z "$MILESTONE_ID" ]; then
    echo "Error: Could not find milestone #$MILESTONE_NUMBER"
    exit 1
fi

echo "Found milestone #$MILESTONE_NUMBER (ID: $MILESTONE_ID)"

# Function to add issue to milestone
add_to_milestone() {
    local issue_number=$1
    echo "Adding issue #$issue_number to milestone #$MILESTONE_NUMBER..."
    
    # Update the issue to add it to the milestone
    gh api repos/$REPO_OWNER/$REPO_NAME/issues/$issue_number -X PATCH -F milestone=$MILESTONE_ID
    
    if [ $? -eq 0 ]; then
        echo "Issue #$issue_number added to milestone #$MILESTONE_NUMBER"
        return 0
    else
        echo "Error: Failed to add issue #$issue_number to milestone #$MILESTONE_NUMBER"
        return 1
    fi
}

echo "Adding issues to milestone #$MILESTONE_NUMBER..."

# Add all specified issues to the milestone
SUCCESS_COUNT=0
FAIL_COUNT=0

for issue_number in "$@"; do
    add_to_milestone $issue_number
    if [ $? -eq 0 ]; then
        SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
    else
        FAIL_COUNT=$((FAIL_COUNT + 1))
    fi
done

echo "Process completed: $SUCCESS_COUNT issues added to milestone #$MILESTONE_NUMBER, $FAIL_COUNT issues failed."
