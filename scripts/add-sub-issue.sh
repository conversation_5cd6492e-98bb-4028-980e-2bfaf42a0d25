#!/bin/bash

# Script to add a sub-issue to a parent issue (create parent-child relationship)
# Usage: ./add-sub-issue.sh <parent_issue_number> <child_issue_number>

# Check for required arguments
if [ $# -ne 2 ]; then
  echo "Usage: $0 <parent_issue_number> <child_issue_number>"
  echo "Example: $0 100 101  # Makes issue #101 a child of issue #100"
  exit 1
fi

# Get repository information from git config or environment variables
REPO_OWNER=${GITHUB_OWNER:-$(git config --get remote.origin.url | sed -n 's/.*github.com[:\/]\([^\/]*\).*/\1/p')}
REPO_NAME=${GITHUB_REPO:-$(git config --get remote.origin.url | sed -n 's/.*\/\([^\/]*\)\.git$/\1/p')}

# If we couldn't determine the owner or repo, use defaults
REPO_OWNER=${REPO_OWNER:-"mouimet-infinisoft"}
REPO_NAME=${REPO_NAME:-"rqrsda2025"}

# Get parameters
PARENT_ISSUE_NUMBER=$1
CHILD_ISSUE_NUMBER=$2

# Get the internal ID of the child issue
echo "Getting ID for issue #$CHILD_ISSUE_NUMBER..."
CHILD_ISSUE_ID=$(gh api repos/$REPO_OWNER/$REPO_NAME/issues/$CHILD_ISSUE_NUMBER --jq .id)

if [ -z "$CHILD_ISSUE_ID" ]; then
  echo "Error: Could not get the ID of issue #$CHILD_ISSUE_NUMBER"
  exit 1
fi

echo "Adding issue #$CHILD_ISSUE_NUMBER (ID: $CHILD_ISSUE_ID) as a sub-issue to issue #$PARENT_ISSUE_NUMBER"

# Add the child issue as a sub-issue to the parent issue
RESPONSE=$(gh api repos/$REPO_OWNER/$REPO_NAME/issues/$PARENT_ISSUE_NUMBER/sub_issues -X POST -F sub_issue_id=$CHILD_ISSUE_ID)

if [ $? -eq 0 ]; then
  echo "Success! Issue #$CHILD_ISSUE_NUMBER is now a sub-issue of issue #$PARENT_ISSUE_NUMBER"
else
  echo "Error: Failed to add issue #$CHILD_ISSUE_NUMBER as a sub-issue to issue #$PARENT_ISSUE_NUMBER"
  echo "$RESPONSE"
  exit 1
fi
