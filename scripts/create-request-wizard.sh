#!/bin/bash

# Script to guide users through the request creation wizard
# This script helps users understand the request creation process

# Colors for better readability
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to display section headers
section() {
  echo -e "\n${BLUE}==== $1 ====${NC}\n"
}

# Function to display steps
step() {
  echo -e "${GREEN}$1.${NC} $2"
}

# Function to display notes
note() {
  echo -e "${YELLOW}NOTE:${NC} $1"
}

# Function to display warnings
warning() {
  echo -e "${RED}WARNING:${NC} $1"
}

# Clear the screen
clear

# Display header
echo -e "${BLUE}============================================${NC}"
echo -e "${BLUE}      Request Creation Wizard Guide         ${NC}"
echo -e "${BLUE}============================================${NC}"
echo ""
echo "This guide will help you understand the request creation process."
echo ""

# Overview section
section "OVERVIEW"
echo "The request creation wizard consists of 4 steps:"
step "1" "Basic Information - Enter the title, description, service type, and priority"
step "2" "Service Requirements - Specify frequency, duration, and other service details"
step "3" "Family Availability - Indicate when the family is available for services"
step "4" "Review - Review all information before submitting the request"
echo ""
note "You can navigate back and forth between steps to update information."
note "Your progress is saved automatically as you move between steps."
echo ""

# Ask if the user wants to continue
read -p "Press Enter to continue or Ctrl+C to exit..."

# Basic Information section
section "STEP 1: BASIC INFORMATION"
echo "In this step, you'll provide the fundamental details about the request:"
echo ""
step "1" "Title: A clear, concise title for the request"
step "2" "Description: Detailed explanation of what is being requested"
step "3" "Service Type: The type of service needed (supervised visit, exchange, etc.)"
step "4" "Priority: How urgent the request is (low, medium, high)"
echo ""
note "The title and description should be clear enough for service providers to understand the request."
note "The service type helps determine which resources will be needed."
warning "All fields marked with an asterisk (*) are required."
echo ""

# Ask if the user wants to continue
read -p "Press Enter to continue or Ctrl+C to exit..."

# Service Requirements section
section "STEP 2: SERVICE REQUIREMENTS"
echo "In this step, you'll specify the details of the service requirements:"
echo ""
step "1" "Frequency: How often the service is needed (weekly, bi-weekly, monthly, one-time)"
step "2" "Duration: How long each service session should be (in minutes)"
step "3" "Special Requirements: Any special considerations or requirements"
step "4" "Preferred Days: Which days of the week are preferred for the service"
step "5" "Preferred Time of Day: Morning, afternoon, evening, or no preference"
step "6" "Additional Notes: Any other relevant information about the service requirements"
echo ""
note "Being specific about service requirements helps with scheduling and resource allocation."
note "The availability grid allows you to select multiple time slots across the week."
echo ""

# Ask if the user wants to continue
read -p "Press Enter to continue or Ctrl+C to exit..."

# Family Availability section
section "STEP 3: FAMILY AVAILABILITY"
echo "In this step, you'll indicate when the family is available for services:"
echo ""
step "1" "General Availability: Overall availability patterns"
step "2" "Weekly Schedule: Use the availability grid to select specific time slots"
step "3" "Additional Notes: Any other relevant information about availability"
echo ""
note "The availability grid allows you to toggle time slots by clicking on them."
note "You can switch between week view and day view for more detailed selection."
note "This information helps with scheduling services at times that work for the family."
echo ""

# Ask if the user wants to continue
read -p "Press Enter to continue or Ctrl+C to exit..."

# Review section
section "STEP 4: REVIEW"
echo "In this step, you'll review all the information before submitting the request:"
echo ""
step "1" "Basic Information: Review the title, description, service type, and priority"
step "2" "Service Requirements: Review the frequency, duration, and other service details"
step "3" "Family Availability: Review the availability information"
step "4" "Submit: Submit the request when everything is correct"
echo ""
note "You can go back to any previous step to make changes if needed."
note "Once submitted, the request will be created in draft status."
note "You'll be redirected to a success page where you can view the request or create another one."
echo ""

# Ask if the user wants to continue
read -p "Press Enter to continue or Ctrl+C to exit..."

# Success page section
section "SUCCESS PAGE"
echo "After successfully submitting a request, you'll see a success page with options to:"
echo ""
step "1" "View Request: Go to the request details page to see the created request"
step "2" "Create Another Request: Start the wizard again to create another request"
step "3" "Back to Requests: Return to the requests list page"
echo ""
note "The request is created in draft status and can be edited or submitted later."
echo ""

# Conclusion
section "CONCLUSION"
echo "The request creation wizard makes it easy to create well-structured requests with all the necessary information."
echo "Remember that you can save your progress at any point and come back to complete the request later."
echo ""
echo "For more information, refer to the application documentation or contact support."
echo ""

echo -e "${BLUE}============================================${NC}"
echo -e "${BLUE}      End of Request Creation Guide         ${NC}"
echo -e "${BLUE}============================================${NC}"
