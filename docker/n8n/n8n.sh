#!/bin/bash

# Script to manage n8n Docker Compose setup

# Function to display usage information
show_usage() {
  echo "Usage: $0 [start|stop|restart|status|logs]"
  echo ""
  echo "Commands:"
  echo "  start   - Start n8n container"
  echo "  stop    - Stop n8n container"
  echo "  restart - Restart n8n container"
  echo "  status  - Show status of n8n container"
  echo "  logs    - Show logs from n8n container"
  echo ""
}

# Check if Dock<PERSON> is running
check_docker() {
  if ! docker info > /dev/null 2>&1; then
    echo "Error: Docker is not running. Please start Docker and try again."
    exit 1
  fi
}

# Check if Supabase is running
check_supabase() {
  if ! docker network inspect supabase_network_rqrsda2025 > /dev/null 2>&1; then
    echo "Warning: Supabase network not found. Make sure Supabase is running."
    echo "Run 'npm run supabase:start' to start Supabase."
    return 1
  fi
  return 0
}

# Start n8n
start_n8n() {
  echo "Starting n8n..."

  # Check if Supabase is running
  if check_supabase; then
    echo "Supabase network found. n8n will connect to Supabase services."
  else
    echo "Continuing without Supabase network. Some features may not work correctly."
  fi

  docker compose up -d
  echo "n8n is now running at http://localhost:5678"
}

# Stop n8n
stop_n8n() {
  echo "Stopping n8n..."
  docker compose down
  echo "n8n stopped"
}

# Restart n8n
restart_n8n() {
  echo "Restarting n8n..."
  docker compose restart
  echo "n8n restarted and is running at http://localhost:5678"
}

# Show n8n status
show_status() {
  echo "n8n container status:"
  docker compose ps
}

# Show n8n logs
show_logs() {
  echo "n8n logs:"
  docker compose logs
}

# Main script logic
check_docker

# Process command line arguments
if [ $# -eq 0 ]; then
  show_usage
  exit 1
fi

case "$1" in
  start)
    start_n8n
    ;;
  stop)
    stop_n8n
    ;;
  restart)
    restart_n8n
    ;;
  status)
    show_status
    ;;
  logs)
    show_logs
    ;;
  *)
    show_usage
    exit 1
    ;;
esac

exit 0
