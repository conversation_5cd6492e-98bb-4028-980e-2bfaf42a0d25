# n8n Workflow Engine

This directory contains the Docker Compose configuration for running n8n locally for development purposes.

## Overview

n8n is a workflow automation tool that will be used as part of the RQRSDA2025 platform for:
- Workflow automation
- Business process orchestration
- Integration with external services
- Notification management

## Getting Started

### Prerequisites

- <PERSON><PERSON> and Docker Compose installed on your machine
- The main application running locally

### Running n8n

To start the n8n instance:

```bash
cd docker/n8n
docker compose up -d
```

This will start n8n in detached mode. You can access the n8n editor at:

http://localhost:5678

### Stopping n8n

To stop the n8n instance:

```bash
cd docker/n8n
docker compose down
```

To stop and remove all data (including workflows):

```bash
cd docker/n8n
docker compose down -v
```

## Integration with RQRSDA2025

n8n will be integrated with the main application through:

1. Webhook triggers from the application to n8n
2. API calls from n8n back to the application
3. Database updates via Supabase

### Network Configuration

The n8n container is configured to join the same Docker network as the Supabase containers (`supabase_network_rqrsda2025`). This allows n8n to communicate directly with Supabase services using their internal Docker network names:

- Postgres database: `db:5432`
- Supabase API: `api:3000`
- Supabase Kong API Gateway: `kong:8000`

This network configuration enables n8n workflows to interact with the Supabase services without going through the public internet or localhost.

## Workflow Development

When developing workflows:

1. Create workflows in the n8n editor
2. Test workflows with sample data
3. Export workflows as JSON for version control
4. Document workflow purpose and triggers

## Production Deployment

For production, n8n should be deployed in a more robust environment with:
- Proper authentication
- SSL/TLS encryption
- Database persistence
- Monitoring and alerting

This Docker Compose setup is for local development only.
