# RQRSDA Platform Source Code

This directory contains the source code for the RQRSDA platform.

## Directory Structure

- `app/`: Next.js App Router pages and layouts
- `components/`: Reusable React components
- `lib/`: Utility functions, API clients, and other shared code
- `hooks/`: Custom React hooks
- `types/`: TypeScript type definitions
- `styles/`: Global styles and CSS modules

## Coding Standards

- Use TypeScript for all new code
- Follow the ESLint and Prettier configurations
- Use functional components with hooks
- Keep components small and focused
- Use proper type annotations
- Avoid using `any` type
- Write meaningful comments for complex logic

## Best Practices

- Use Next.js App Router features for routing and layouts
- Use server components where appropriate
- Use client components only when necessary
- Follow the React documentation for best practices
- Use proper error handling
- Write unit tests for critical functionality
