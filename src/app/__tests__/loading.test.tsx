import { render, screen } from "@testing-library/react";
import RootLoading from "../loading";

describe("RootLoading", () => {
  it("renders correctly", () => {
    render(<RootLoading />);

    // Check if loading text is displayed
    const loadingText = screen.getByText("Loading...");
    expect(loadingText).toBeTruthy();

    // Check if the loader icon is rendered
    const svg = screen.getByRole("img", { name: "Loading spinner" });
    expect(svg).toBeTruthy();
  });
});
