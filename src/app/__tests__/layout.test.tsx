import { render } from "@testing-library/react";
import RootLayout from "../layout";

// Mock window.matchMedia
Object.defineProperty(window, "matchMedia", {
  writable: true,
  value: jest.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // Deprecated
    removeListener: jest.fn(), // Deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock the ThemeProvider component
jest.mock("@/components/theme-provider", () => ({
  ThemeProvider: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="theme-provider">{children}</div>
  ),
}));

describe("RootLayout", () => {
  it("renders correctly", () => {
    const { getByTestId, getByText } = render(
      <RootLayout>
        <div>Test Content</div>
      </RootLayout>
    );

    // Check if ThemeProvider is rendered
    expect(getByTestId("theme-provider")).toBeInTheDocument();

    // Check if children are rendered
    expect(getByText("Test Content")).toBeInTheDocument();

    // Check if html has correct lang attribute
    const html = document.querySelector("html");
    expect(html).toHaveAttribute("lang", "fr");

    // Check if viewport meta tag is present
    const metaViewport = document.querySelector('meta[name="viewport"]');
    expect(metaViewport).toHaveAttribute("content", "width=device-width, initial-scale=1.0");
  });
});
