import { render, screen } from "@testing-library/react";
import RootNotFound from "../not-found";

// Mock Next.js Link component
jest.mock("next/link", () => {
  return ({ children, href }: { children: React.ReactNode; href: string }) => (
    <a href={href} data-testid="next-link">
      {children}
    </a>
  );
});

// Mock the Button component
jest.mock("@/components/ui/button", () => ({
  Button: ({
    children,
    asChild,
    variant,
  }: {
    children: React.ReactNode;
    asChild?: boolean;
    variant?: string;
  }) => (
    <button data-testid="button" data-as-child={asChild ? "true" : "false"} data-variant={variant}>
      {children}
    </button>
  ),
}));

// Mock the Card components
jest.mock("@/components/ui/card", () => ({
  Card: ({ children, className }: { children: React.ReactNode; className?: string }) => (
    <div data-testid="card" className={className}>
      {children}
    </div>
  ),
  CardContent: ({ children, className }: { children: React.ReactNode; className?: string }) => (
    <div data-testid="card-content" className={className}>
      {children}
    </div>
  ),
  CardFooter: ({ children, className }: { children: React.ReactNode; className?: string }) => (
    <div data-testid="card-footer" className={className}>
      {children}
    </div>
  ),
}));

// Mock the GoBackButton component
jest.mock("@/components/ui/go-back-button", () => ({
  GoBackButton: ({ label }: { label: string }) => (
    <button data-testid="go-back-button">{label}</button>
  ),
}));

// Mock Lucide icons
jest.mock("lucide-react", () => ({
  FileQuestion: () => <div data-testid="file-question-icon" />,
}));

describe("RootNotFound", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders correctly", () => {
    render(<RootNotFound />);

    // Check if the 404 heading is rendered with proper size
    const heading = screen.getByText("404");
    expect(heading).toBeInTheDocument();
    expect(heading.tagName).toBe("H1");

    // Check if the subheading is rendered
    expect(screen.getByText("Page Not Found")).toBeInTheDocument();

    // Check if description is displayed
    expect(
      screen.getByText("The page you are looking for doesn't exist or has been moved.")
    ).toBeInTheDocument();

    // Check if buttons are rendered
    expect(screen.getByTestId("go-back-button")).toBeInTheDocument();
    expect(screen.getByText("Return to Homepage")).toBeInTheDocument();

    // Check if icon is rendered
    expect(screen.getByTestId("file-question-icon")).toBeInTheDocument();
  });

  it("links to homepage", () => {
    render(<RootNotFound />);

    // Check if the homepage link is correct
    const homeLink = screen.getByTestId("next-link");
    expect(homeLink).toHaveAttribute("href", "/");
  });

  it("renders with proper layout", () => {
    render(<RootNotFound />);

    // Check if the container has the proper classes for centering
    const container = document.querySelector(
      ".flex.flex-col.items-center.justify-center.min-h-screen"
    );
    expect(container).toBeInTheDocument();

    // Check if card has proper styling
    const card = screen.getByTestId("card");
    expect(card).toHaveClass("border-2");

    // Check if card footer has proper flex layout
    const cardFooter = screen.getByTestId("card-footer");
    expect(cardFooter).toHaveClass("flex");
    expect(cardFooter).toHaveClass("justify-between");
  });
});
