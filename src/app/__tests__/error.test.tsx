import { render, screen, fireEvent } from "@testing-library/react";
import RootError from "../error";

// Mock console.error to prevent test output pollution
const originalConsoleError = console.error;
beforeAll(() => {
  console.error = jest.fn();
});
afterAll(() => {
  console.error = originalConsoleError;
});

describe("RootError", () => {
  const mockError = new Error("Test error message");
  const mockReset = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders error message correctly", () => {
    render(<RootError error={mockError} reset={mockReset} />);

    // Check if error message is displayed
    expect(screen.getByText("Test error message")).toBeInTheDocument();

    // Check if buttons are rendered
    expect(screen.getByText("Try again")).toBeInTheDocument();
    expect(screen.getByText("Go to homepage")).toBeInTheDocument();
  });

  it("logs error to console", () => {
    render(<RootError error={mockError} reset={mockReset} />);

    // Check if error is logged to console
    expect(console.error).toHaveBeenCalledWith("Root error:", mockError);
  });

  it("calls reset function when try again button is clicked", () => {
    render(<RootError error={mockError} reset={mockReset} />);

    // Click the try again button
    fireEvent.click(screen.getByText("Try again"));

    // Check if reset function is called
    expect(mockReset).toHaveBeenCalledTimes(1);
  });

  it("displays error digest when available", () => {
    const errorWithDigest = { ...mockError, digest: "test-digest-123" };
    render(<RootError error={errorWithDigest} reset={mockReset} />);

    // Check if error digest is displayed
    expect(screen.getByText("Error ID: test-digest-123")).toBeInTheDocument();
  });
});
