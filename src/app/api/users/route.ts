import { NextRequest, NextResponse } from "next/server";
import { UserManagementService } from "@/app/[lang]/protected/user/lib/services/UserManagementService";
import { UserRole } from "@/lib/authorization/types";
import { logger } from "@/lib/logger/services/LoggerService";
import { createServiceClient } from "@/lib/supabase/service";
/**
 * POST /api/users
 * Creates a new user
 */
export async function POST(request: NextRequest) {
  try {
    // Get the request body
    const body = await request.json();

    // Validate required fields
    if (!body.firstName) {
      return NextResponse.json({ error: "First name is required" }, { status: 400 });
    }

    if (!body.lastName) {
      return NextResponse.json({ error: "Last name is required" }, { status: 400 });
    }

    if (!body.email) {
      return NextResponse.json({ error: "Email is required" }, { status: 400 });
    }

    if (!body.role) {
      return NextResponse.json({ error: "Role is required" }, { status: 400 });
    }

    if (!body.organizationId) {
      return NextResponse.json({ error: "Organization ID is required" }, { status: 400 });
    }

    // Validate role
    const validRoles: UserRole[] = ["Director", "Coordinator", "SocialWorker", "SystemAdmin"];
    if (!validRoles.includes(body.role)) {
      return NextResponse.json({ error: "Invalid role" }, { status: 400 });
    }

    // Check authorization
    // Get the current user from the request
    const supabase = await createServiceClient();
    // const {
    //   data: { user },
    // } = await supabase.auth.getUser();

    // if (!user) {
    //   return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    // }

    // // Check if the user has permission to create users
    // // This is a simplified version - in a real app, you would use a more robust authorization check
    // const { data: roleData } = await supabase
    //   .from("user_roles")
    //   .select("role")
    //   .eq("user_id", user.id)
    //   .single();

    // if (!roleData || (roleData.role !== "Director" && roleData.role !== "SystemAdmin")) {
    //   return NextResponse.json(
    //     { error: "Forbidden: You don't have permission to create users" },
    //     { status: 403 }
    //   );
    // }

    // Create the user
    const newUser = await UserManagementService.createUser({
      firstName: body.firstName,
      lastName: body.lastName,
      email: body.email,
      phone: body.phone || undefined,
      role: body.role as UserRole,
      organizationId: body.organizationId,
      language: (body.language as "en" | "fr") || "en",
    });

    if (!newUser) {
      return NextResponse.json(
        { error: "Failed to create user. The email may already be in use." },
        { status: 500 }
      );
    }

    // Return the created user
    return NextResponse.json(newUser, { status: 201 });
  } catch (error) {
    logger.error(`Error creating user via API: ${error}`);
    return NextResponse.json({ error: `Failed to create user: ${error}` }, { status: 500 });
  }
}

/**
 * GET /api/users
 * Gets a list of users for the current user's organization
 */
export async function GET(request: NextRequest) {
  try {
    // Get the current user from the request
    const supabase = await createServiceClient();
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get the user's organization
    const { data: roleData } = await supabase
      .from("user_roles")
      .select("organization_id, role")
      .eq("user_id", user.id)
      .single();

    if (!roleData || !roleData.organization_id) {
      return NextResponse.json(
        { error: "User is not associated with an organization" },
        { status: 400 }
      );
    }

    // Check if the user has permission to list users
    if (roleData.role !== "Director" && roleData.role !== "SystemAdmin") {
      return NextResponse.json(
        { error: "Forbidden: You don't have permission to list users" },
        { status: 403 }
      );
    }

    // Get users for the organization
    const users = await UserManagementService.getUsersByOrganization(roleData.organization_id);

    // Return the users
    return NextResponse.json(users);
  } catch (error) {
    logger.error(`Error getting users via API: ${error}`);
    return NextResponse.json({ error: `Failed to get users: ${error}` }, { status: 500 });
  }
}
