import { NextRequest, NextResponse } from "next/server";
import { logger } from "@/lib/logger/services/LoggerService";
import { headers } from "next/headers";

/**
 * GET /api/test
 * Simple test endpoint to debug connection issues
 */
export async function GET(request: NextRequest) {
  try {
    // Log request headers for debugging
    const headersList = await headers();
    const host = headersList.get("host") || "unknown";
    const userAgent = headersList.get("user-agent") || "unknown";
    const referer = headersList.get("referer") || "unknown";
    const xForwardedFor = headersList.get("x-forwarded-for") || "unknown";

    // Get client IP
    const forwardedFor = request.headers.get("x-forwarded-for") || "unknown";

    logger.info(`Test API request received`);
    logger.info(`Host: ${host}`);
    logger.info(`User-Agent: ${userAgent}`);
    logger.info(`Referer: ${referer}`);
    logger.info(`X-Forwarded-For: ${xForwardedFor}`);
    logger.info(`Headers X-Forwarded-For: ${forwardedFor}`);

    // Return a simple response with connection info
    return NextResponse.json({
      message: "API connection successful",
      connectionInfo: {
        timestamp: new Date().toISOString(),
        headers: {
          host,
          userAgent,
          referer,
          xForwardedFor,
          forwardedFor,
        },
      },
    });
  } catch (error) {
    logger.error(`Error in test endpoint: ${error}`);
    return NextResponse.json({ error: `Test endpoint error: ${error}` }, { status: 500 });
  }
}
