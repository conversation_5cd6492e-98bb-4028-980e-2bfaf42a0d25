import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";
import { logger } from "@/lib/logger/services/LoggerService";
import { ProfileService } from "@/app/[lang]/protected/organization/(features)/profile/lib/services/ProfileService";

/**
 * Download document attachment via API route with session-based security
 * GET /api/documents/[id]/download
 */
export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params;

    // Validate parameters
    if (!id) {
      return NextResponse.json({ error: "Attachment ID is required" }, { status: 400 });
    }

    // Get organization context (validates session)
    const organization = await ProfileService.getCurrentOrganization();
    if (!organization) {
      return NextResponse.json({ error: "Authentication required" }, { status: 401 });
    }

    const supabase = await createClient();

    // Get attachment record with organization-level security
    const { data: attachment, error: fetchError } = await supabase
      .from("document_attachments")
      .select("*")
      .eq("id", id)
      .eq("organization_id", organization.id) // Organization-level RLS
      .eq("status", "attached")
      .single();

    if (fetchError || !attachment) {
      logger.warn(`Attachment not found or access denied: ${id}`);
      return NextResponse.json({ error: "Attachment not found" }, { status: 404 });
    }

    // Download file from storage
    const { data: fileData, error: downloadError } = await supabase.storage
      .from("document-attachments")
      .download(attachment.file_path);

    if (downloadError || !fileData) {
      logger.error(`Error downloading file: ${downloadError?.message}`);
      return NextResponse.json({ error: "Failed to download file" }, { status: 500 });
    }

    // Convert blob to array buffer for streaming
    const arrayBuffer = await fileData.arrayBuffer();

    // Determine content type
    const contentType = (attachment.metadata as any)?.mime_type || "application/octet-stream";

    // Log download activity for audit trail
    logger.info(`File downloaded: ${attachment.document_name} (${id})`);

    // Return file with proper headers for download
    return new NextResponse(arrayBuffer, {
      status: 200,
      headers: {
        "Content-Type": contentType,
        "Content-Disposition": `attachment; filename="${attachment.document_name}"`,
        "Content-Length": fileData.size.toString(),
        "Cache-Control": "private, no-cache, no-store, must-revalidate",
        Pragma: "no-cache",
        Expires: "0",
      },
    });
  } catch (error) {
    logger.error("Error in download API route:", error as Error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
