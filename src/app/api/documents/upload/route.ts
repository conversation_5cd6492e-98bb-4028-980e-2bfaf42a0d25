import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";
import { logger } from "@/lib/logger/services/LoggerService";
import { ProfileService } from "@/app/[lang]/protected/organization/(features)/profile/lib/services/ProfileService";
import { FileValidationService } from "@/app/[lang]/protected/document/lib/services/FileValidationService";
import { auth } from "@/lib/authentication/services/AuthenticationService";
import { Database } from "@/lib/types/database.types";

type DocumentAttachmentInsert = Database["public"]["Tables"]["document_attachments"]["Insert"];

/**
 * Upload document attachments via API route
 * POST /api/documents/upload
 */
export async function POST(request: NextRequest) {
  try {
    // Get current user and organization context
    const user = await auth.getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: "Authentication required" }, { status: 401 });
    }

    // Debug: Log user ID to help diagnose foreign key issue
    logger.info(`Upload attempt by user ID: ${user.id}`);

    const organization = await ProfileService.getCurrentOrganization();
    if (!organization) {
      return NextResponse.json({ error: "Organization not found" }, { status: 401 });
    }

    // Parse form data
    const formData = await request.formData();
    const files = formData.getAll("files") as File[];
    const attachedToType = formData.get("attached_to_type") as string;
    const attachedToId = formData.get("attached_to_id") as string;
    const category = formData.get("category") as string;
    const tagsString = formData.get("tags") as string;
    const description = formData.get("description") as string;

    // Validate required fields
    if (!attachedToType || !attachedToId) {
      return NextResponse.json({ error: "Entity type and ID are required" }, { status: 400 });
    }

    if (!files || files.length === 0) {
      return NextResponse.json({ error: "At least one file is required" }, { status: 400 });
    }

    // Parse tags
    const tags = tagsString
      ? tagsString
          .split(",")
          .map((tag) => tag.trim())
          .filter(Boolean)
      : [];

    // Validate files
    const validationResult = FileValidationService.validateFiles(files);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error || "File validation failed" },
        { status: 400 }
      );
    }

    const supabase = await createClient();
    const uploadedAttachments = [];
    const errors = [];

    // Upload each file
    for (const file of files) {
      try {
        // Generate unique file path with organization-based folder structure
        const fileExtension = file.name.split(".").pop();
        const fileName = `${Date.now()}-${Math.random().toString(36).substring(7)}.${fileExtension}`;
        const filePath = `${organization.id}/${attachedToType}/${attachedToId}/${fileName}`;

        // Upload file to Supabase Storage
        const { data: uploadData, error: uploadError } = await supabase.storage
          .from("documents")
          .upload(filePath, file, {
            cacheControl: "3600",
            upsert: false,
          });

        if (uploadError) {
          logger.error(`Error uploading file: ${uploadError.message}`);
          errors.push(`${file.name}: ${uploadError.message}`);
          continue;
        }

        // Create attachment record
        const attachmentData: DocumentAttachmentInsert = {
          organization_id: organization.id,
          attached_to_type: attachedToType,
          attached_to_id: attachedToId,
          document_name: file.name,
          file_path: uploadData.path,
          document_type: fileExtension?.toUpperCase() || "UNKNOWN",
          file_size: file.size,
          attachment_type: "manual",
          status: "attached",
          uploaded_by: user.id,
          metadata: {
            category: category || null,
            tags: tags || [],
            description: description || null,
            mime_type: file.type,
            original_name: file.name,
          },
        };

        const { data: attachment, error: dbError } = await supabase
          .from("document_attachments")
          .insert(attachmentData)
          .select()
          .single();

        if (dbError) {
          // Clean up uploaded file if database insert fails
          await supabase.storage.from("documents").remove([uploadData.path]);
          logger.error(`Error creating attachment record: ${dbError.message}`);
          errors.push(`${file.name}: Database error`);
          continue;
        }

        uploadedAttachments.push(attachment);
        logger.info(`File uploaded successfully: ${file.name}`);
      } catch (error) {
        logger.error(`Error processing file ${file.name}:`, error as Error);
        errors.push(`${file.name}: Processing failed`);
      }
    }

    // Return results
    if (uploadedAttachments.length === 0) {
      return NextResponse.json(
        {
          error: `All uploads failed: ${errors.join(", ")}`,
          uploadedFiles: [],
          errors,
        },
        { status: 400 }
      );
    }

    const response = {
      message: `Successfully uploaded ${uploadedAttachments.length} files`,
      uploadedFiles: uploadedAttachments,
      errors: errors.length > 0 ? errors : undefined,
    };

    logger.info(
      `Successfully uploaded ${uploadedAttachments.length} files for ${attachedToType}:${attachedToId}`
    );

    return NextResponse.json(response, { status: 201 });
  } catch (error) {
    logger.error("Error in upload API route:", error as Error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
