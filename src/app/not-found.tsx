"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card";
import { FileQuestion } from "lucide-react";
import Link from "next/link";
import { GoBackButton } from "@/components/ui/go-back-button";

/**
 * Root not found component
 * This component is displayed when a route is not found
 */
export default function RootNotFound() {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4 bg-background">
      <div className="w-full max-w-md text-center">
        <Card className="border-2">
          <CardContent className="pt-6 pb-4">
            <div className="flex justify-center mb-4">
              <div className="bg-muted rounded-full p-3">
                <FileQuestion className="h-8 w-8 text-primary" />
              </div>
            </div>
            <h1 className="text-4xl font-bold mb-2">404</h1>
            <h2 className="text-2xl font-semibold mb-4">Page Not Found</h2>
            <p className="text-muted-foreground mb-4">
              The page you are looking for doesn't exist or has been moved.
            </p>
          </CardContent>

          <CardFooter className="flex justify-between items-center gap-4 pt-0">
            <GoBackButton label="Go Back" />
            <Button asChild variant="default">
              <Link href="/">Return to Homepage</Link>
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}
