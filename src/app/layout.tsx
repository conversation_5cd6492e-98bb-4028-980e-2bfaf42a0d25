import type { <PERSON>ada<PERSON> } from "next";
import "./globals.css";
import { ThemeProvider } from "@/components/theme-provider";
import { Toaster } from "@/components/ui/sonner";

export const metadata: Metadata = {
  title: "RQRSDA 2025",
  description: "Réseau québécois des ressources en supervision des droits d'accès",
  applicationName: "RQRSDA Platform",
  authors: [{ name: "Infinisoft" }],
  keywords: ["RQRSDA", "supervision", "droits d'accès", "Quebec", "social services"],
  creator: "Infinisoft",
  publisher: "Infinisoft",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
};

/**
 * Root layout for the application
 * This is the top-level layout that wraps all pages
 */
export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="fr" suppressHydrationWarning>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      </head>
      <body className="font-sans m-0 p-0 bg-background text-foreground">
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <main className="min-h-screen">{children}</main>
          <Toaster />
        </ThemeProvider>
      </body>
    </html>
  );
}
