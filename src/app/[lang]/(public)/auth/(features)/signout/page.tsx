import { signOut } from "./actions/signout";
import { i18n } from "@/lib/i18n/services/I18nService";

export default async function SignOutPage({ params }: { params: Promise<{ lang: string }> }) {
  const { lang } = await params;
  const dictionary = i18n.getDictionary(lang);
  const authDict = dictionary.auth;

  // Sign out the user
  await signOut(lang);

  // This will not be rendered as the signOut action redirects
  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4">
      <div className="w-full max-w-md space-y-8 text-center">
        <h1 className="text-2xl font-bold">{authDict.signout.title || "Signing out..."}</h1>
      </div>
    </div>
  );
}
