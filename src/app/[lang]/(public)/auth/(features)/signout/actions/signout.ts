"use server";

import { auth } from "@/lib/authentication/services/AuthenticationService";
import { logger } from "@/lib/logger/services/LoggerService";
import { redirect } from "next/navigation";

/**
 * Server action to sign out the current user
 * @param lang The current language
 */
export async function signOut(lang: string) {
  logger.info("Signing out user");

  // Flag to track if we should proceed with redirect
  const shouldRedirect = true;

  try {
    const { error } = await auth.signOut();

    if (error) {
      logger.error("Sign out failed", error);
      // Even if there's an error, we'll still redirect to the sign-in page
    }

    logger.info("User signed out successfully");
  } catch (error) {
    logger.error("Unexpected error during sign out", error as Error);
    // Even in case of error, we still want to redirect to sign-in page
  }

  // Redirect outside of try-catch block
  if (shouldRedirect) {
    redirect(`/${lang}/auth/signin`);
  }
}
