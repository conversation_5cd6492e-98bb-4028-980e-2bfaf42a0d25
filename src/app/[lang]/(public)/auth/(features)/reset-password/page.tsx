import { Metadata } from "next";
import { i18n } from "@/lib/i18n/services/I18nService";
import { RequestResetForm } from "./components/RequestResetForm";
import { ResetPasswordForm } from "./components/ResetPasswordForm";

// Generate metadata for the page (SEO)
export async function generateMetadata({
  params,
}: {
  params: Promise<{ lang: string }>;
}): Promise<Metadata> {
  const { lang } = await params;
  const dictionary = i18n.getDictionary(lang);
  const authDict = dictionary.auth;

  return {
    title: authDict.resetPassword.pageTitle || "Reset Password",
    description: authDict.resetPassword.pageDescription || "Reset your account password",
  };
}

// Server Component page
export default async function ResetPasswordPage(props: {
  params: Promise<{ lang: string }>;
  searchParams: Promise<{ token?: string }>;
}) {
  const { params, searchParams } = props;
  const { lang } = await params;
  const dictionary = i18n.getDictionary(lang);
  const authDict = dictionary.auth;
  const { token } = await searchParams;

  // Get the reset password dictionary
  const resetPasswordDictionary = authDict.resetPassword;

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4">
      <div className="w-full max-w-md space-y-8">
        <h1 className="text-2xl font-bold text-center">
          {token
            ? resetPasswordDictionary.setNewPasswordTitle
            : resetPasswordDictionary.requestResetTitle}
        </h1>

        {token ? (
          <ResetPasswordForm dictionary={resetPasswordDictionary} lang={lang} token={token} />
        ) : (
          <RequestResetForm dictionary={resetPasswordDictionary} lang={lang} />
        )}
      </div>
    </div>
  );
}
