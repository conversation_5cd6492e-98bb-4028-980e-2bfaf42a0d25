"use server";

import { auth } from "@/lib/authentication/services/AuthenticationService";
import { logger } from "@/lib/logger/services/LoggerService";

// Define the state type
interface RequestResetState {
  success: boolean;
  error: string | null;
}

// Server Action for form submission
export async function requestReset(_prevState: RequestResetState, formData: FormData) {
  const email = formData.get("email") as string;

  logger.info(`Attempting to send password reset email to: ${email}`);

  try {
    const { error } = await auth.resetPassword(email);

    // Handle error
    if (error) {
      logger.error(`Password reset request failed: ${error.message}`);
      return {
        success: false,
        error: error.message,
      };
    }

    logger.info(`Password reset email sent successfully to: ${email}`);

    return {
      success: true,
      error: null,
    };
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error(`Unexpected error during password reset request: ${errorMessage}`);
    return {
      success: false,
      error: "An unexpected error occurred. Please try again.",
    };
  }
}
