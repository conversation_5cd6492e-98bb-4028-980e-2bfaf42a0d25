"use client";

import { useActionState } from "react";
import { resetPassword } from "../actions/reset-password";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import Link from "next/link";

// Define a proper type for the dictionary
interface ResetPasswordDictionary {
  passwordLabel?: string;
  confirmPasswordLabel?: string;
  resetButton?: string;
  backToSignIn?: string;
  passwordMismatch?: string;
  [key: string]: string | undefined;
}

export function ResetPasswordForm({
  dictionary,
  lang,
  token,
}: {
  dictionary: ResetPasswordDictionary;
  lang: string;
  token: string;
}) {
  // Initialize form state with the server action
  const [state, formAction, pending] = useActionState(resetPassword, {
    success: false,
    error: null,
    passwordMismatch: false,
  });

  return (
    <div className="space-y-6">
      {state.success ? (
        <div className="space-y-6">
          <div className="p-4 bg-green-100 border border-green-400 text-green-700 rounded">
            Your password has been reset successfully.
          </div>
          <div className="text-center">
            <Link href={`/${lang}/auth/signin`} className="text-blue-600 hover:text-blue-500">
              {dictionary.backToSignIn || "Back to sign in"}
            </Link>
          </div>
        </div>
      ) : (
        <form action={formAction} className="space-y-6">
          <input type="hidden" name="token" value={token} />
          <input type="hidden" name="lang" value={lang} />

          {state.error && (
            <div className="p-3 bg-red-100 border border-red-400 text-red-700 rounded">
              {state.error}
            </div>
          )}

          {state.passwordMismatch && (
            <div className="p-3 bg-red-100 border border-red-400 text-red-700 rounded">
              {dictionary.passwordMismatch || "Passwords do not match"}
            </div>
          )}

          <div className="space-y-2">
            <label htmlFor="password" className="block text-sm font-medium">
              {dictionary.passwordLabel || "New password"}
            </label>
            <Input id="password" name="password" type="password" required minLength={8} />
          </div>

          <div className="space-y-2">
            <label htmlFor="confirmPassword" className="block text-sm font-medium">
              {dictionary.confirmPasswordLabel || "Confirm new password"}
            </label>
            <Input
              id="confirmPassword"
              name="confirmPassword"
              type="password"
              required
              minLength={8}
            />
          </div>

          <Button type="submit" className="w-full" disabled={pending}>
            {pending ? "Resetting..." : "Reset password"}
          </Button>

          <div className="text-center mt-4">
            <Link
              href={`/${lang}/auth/signin`}
              className="text-sm text-blue-600 hover:text-blue-500"
            >
              {dictionary.backToSignIn || "Back to sign in"}
            </Link>
          </div>
        </form>
      )}
    </div>
  );
}
