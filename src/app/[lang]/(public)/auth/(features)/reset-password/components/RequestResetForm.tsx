"use client";

import { useActionState } from "react";
import { requestReset } from "../actions/request-reset";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import Link from "next/link";

// Define a proper type for the dictionary
interface ResetPasswordDictionary {
  emailLabel?: string;
  requestButton?: string;
  backToSignIn?: string;
  [key: string]: string | undefined;
}

export function RequestResetForm({
  dictionary,
  lang,
}: {
  dictionary: ResetPasswordDictionary;
  lang: string;
}) {
  // Initialize form state with the server action
  const [state, formAction, pending] = useActionState(requestReset, {
    success: false,
    error: null,
  });

  return (
    <div className="space-y-6">
      {state.success ? (
        <div className="p-4 bg-green-100 border border-green-400 text-green-700 rounded">
          Password reset link sent to your email. Please check your inbox.
        </div>
      ) : (
        <form action={formAction} className="space-y-6">
          <input type="hidden" name="lang" value={lang} />

          {state.error && (
            <div className="p-3 bg-red-100 border border-red-400 text-red-700 rounded">
              {state.error}
            </div>
          )}

          <div className="space-y-2">
            <label htmlFor="email" className="block text-sm font-medium">
              {dictionary.emailLabel || "Email address"}
            </label>
            <Input id="email" name="email" type="email" autoComplete="email" required />
          </div>

          <Button type="submit" className="w-full" disabled={pending}>
            {pending ? "Sending..." : "Send reset link"}
          </Button>

          <div className="text-center mt-4">
            <Link
              href={`/${lang}/auth/signin`}
              className="text-sm text-blue-600 hover:text-blue-500"
            >
              {dictionary.backToSignIn || "Back to sign in"}
            </Link>
          </div>
        </form>
      )}
    </div>
  );
}
