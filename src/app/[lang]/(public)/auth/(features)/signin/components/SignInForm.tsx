"use client";

import { useActionState } from "react";
import { signIn } from "../actions/signin";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import Link from "next/link";

// Define a proper type for the dictionary
interface SignInFormDictionary {
  emailLabel?: string;
  passwordLabel?: string;
  forgotPassword?: string;
  signInButton?: string;
  signingIn?: string;
  [key: string]: string | undefined;
}

export function SignInForm({
  dictionary,
  lang,
}: {
  dictionary: SignInFormDictionary;
  lang: string;
}) {
  // Initialize form state with the server action
  const [state, formAction, pending] = useActionState(signIn, {
    success: false,
    error: "",
  });

  return (
    <form action={formAction} className="space-y-6">
      <input type="hidden" name="lang" value={lang} />

      {state.error && (
        <div className="p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          {state.error}
        </div>
      )}

      <div className="space-y-2">
        <label htmlFor="email" className="block text-sm font-medium">
          {dictionary.emailLabel || "Email address"}
        </label>
        <Input id="email" name="email" type="email" autoComplete="email" required />
      </div>

      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <label htmlFor="password" className="block text-sm font-medium">
            {dictionary.passwordLabel || "Password"}
          </label>
          <Link
            href={`/${lang}/auth/reset-password`}
            className="text-sm text-blue-600 hover:text-blue-500"
          >
            {dictionary.forgotPassword || "Forgot password?"}
          </Link>
        </div>
        <Input
          id="password"
          name="password"
          type="password"
          autoComplete="current-password"
          required
        />
      </div>

      <Button type="submit" className="w-full" disabled={pending}>
        {pending ? "Signing In..." : "Sign In"}
      </Button>
    </form>
  );
}
