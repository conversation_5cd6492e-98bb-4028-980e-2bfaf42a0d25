// Define a proper type for the dictionary
interface SignInDictionary {
  title?: string;
  subtitle?: string;
  [key: string]: string | undefined;
}

// Server component for the sign-in header
export function SignInHeader({ dictionary }: { dictionary: SignInDictionary }) {
  return (
    <div className="text-center">
      <h1 className="text-2xl font-bold">{dictionary.title || "Sign in to your account"}</h1>
      <p className="mt-2 text-sm text-gray-600">
        {dictionary.subtitle || "Enter your credentials to access your account"}
      </p>
    </div>
  );
}
