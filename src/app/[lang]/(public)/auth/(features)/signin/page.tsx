import { SignInForm } from "./components/SignInForm";
import { SignInHeader } from "./components/SignInHeader";
import { i18n } from "@/lib/i18n/services/I18nService";
import { Metadata } from "next";

// Generate metadata for the page (SEO)
export async function generateMetadata({
  params,
}: {
  params: Promise<{ lang: string }>;
}): Promise<Metadata> {
  const { lang } = await params;
  const dictionary = i18n.getDictionary(lang);
  const authDict = dictionary.auth;

  return {
    title: authDict.signin.pageTitle || "Sign In",
    description: authDict.signin.pageDescription || "Sign in to your account",
  };
}

// Server Component page
export default async function SignInPage({ params }: { params: Promise<{ lang: string }> }) {
  const { lang } = await params;
  const dictionary = i18n.getDictionary(lang);
  const authDict = dictionary.auth;

  // Get the signin dictionary
  const signinDictionary = authDict.signin;

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4">
      <div className="w-full max-w-md space-y-8">
        <SignInHeader dictionary={signinDictionary} />
        <SignInForm dictionary={signinDictionary} lang={lang} />
      </div>
    </div>
  );
}
