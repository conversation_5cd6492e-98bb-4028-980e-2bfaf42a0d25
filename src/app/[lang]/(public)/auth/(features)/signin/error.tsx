"use client";

import { useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";

export default function SignInError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    console.error(error);
  }, [error]);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4">
      <div className="w-full max-w-md space-y-6 text-center">
        <h1 className="text-2xl font-bold text-red-600">Error</h1>
        <p className="text-gray-600">{error.message || "An error occurred during sign in"}</p>
        <div className="flex space-x-4 justify-center">
          <Button onClick={reset} variant="outline">
            Try Again
          </Button>
          <Link href={`/`}>
            <Button>Go Home</Button>
          </Link>
        </div>
      </div>
    </div>
  );
}
