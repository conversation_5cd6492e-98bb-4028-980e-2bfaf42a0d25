"use client";

import React, { useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { HistoryTimeline } from "@/components/ui/history-timeline";
import { DocumentAttachment } from "@/components/ui/document-attachment";
import { TaskProgress } from "@/components/ui/task-progress";
import { showNotification } from "@/components/ui/notification";

export default function AdditionalPage() {
  // Sample data for history timeline
  const historyItems = [
    {
      id: "1",
      timestamp: "2023-01-01T12:00:00Z",
      user: {
        id: "user1",
        name: "<PERSON>",
      },
      action: "create" as const,
      summary: "Contact created",
    },
    {
      id: "2",
      timestamp: "2023-01-02T14:30:00Z",
      user: {
        id: "user2",
        name: "<PERSON>",
      },
      action: "update" as const,
      changes: [
        { field: "name", oldValue: "<PERSON>", newValue: "<PERSON>" },
        { field: "email", oldValue: "<EMAIL>", newValue: "<EMAIL>" },
      ],
    },
    {
      id: "3",
      timestamp: "2023-01-03T09:15:00Z",
      user: {
        id: "user3",
        name: "Bob Johnson",
      },
      action: "update" as const,
      changes: [{ field: "phone", oldValue: "************", newValue: "************" }],
    },
  ];

  // Sample data for documents
  const documents = [
    {
      id: "doc1",
      name: "Contract.pdf",
      type: "application/pdf",
      size: 1024 * 1024 * 2.5, // 2.5 MB
      uploadedBy: "John Doe",
      uploadedAt: "2023-01-01T12:00:00Z",
      url: "#",
    },
    {
      id: "doc2",
      name: "Profile.jpg",
      type: "image/jpeg",
      size: 1024 * 512, // 512 KB
      uploadedBy: "Jane Smith",
      uploadedAt: "2023-01-02T14:30:00Z",
      url: "#",
    },
    {
      id: "doc3",
      name: "Report.docx",
      type: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      size: 1024 * 1024 * 1.2, // 1.2 MB
      uploadedBy: "Bob Johnson",
      uploadedAt: "2023-01-03T09:15:00Z",
      url: "#",
    },
  ];

  // Sample data for task progress
  const taskSteps = [
    { id: "step1", name: "Collecting data", status: "completed" as const },
    { id: "step2", name: "Processing data", status: "running" as const },
    { id: "step3", name: "Generating PDF", status: "pending" as const },
  ];

  // Sample data for notifications
  const notifications = [
    {
      id: "notif1",
      title: "New message",
      description: "You have a new message from John Doe",
      type: "info" as const,
      timestamp: "10:30 AM",
      read: false,
    },
    {
      id: "notif2",
      title: "Task completed",
      description: "Your report has been generated successfully",
      type: "success" as const,
      timestamp: "Yesterday",
      read: true,
    },
    {
      id: "notif3",
      title: "Error",
      description: "Failed to upload document",
      type: "error" as const,
      timestamp: "2 days ago",
      read: false,
    },
  ];

  // Function to show notifications
  const handleShowNotification = (type: "success" | "error" | "info" | "warning" | "action") => {
    switch (type) {
      case "success":
        showNotification({
          title: "Success",
          description: "Operation completed successfully",
          type: "success",
        });
        break;
      case "error":
        showNotification({
          title: "Error",
          description: "An error occurred while processing your request",
          type: "error",
        });
        break;
      case "info":
        showNotification({
          title: "Information",
          description: "This is an informational message",
          type: "info",
        });
        break;
      case "warning":
        showNotification({
          title: "Warning",
          description: "This action may have consequences",
          type: "warning",
        });
        break;
      case "action":
        showNotification({
          title: "Action Required",
          description: "Please review and approve the pending request",
          type: "info",
          action: {
            label: "Review",
            onClick: () => alert("Review action clicked"),
          },
        });
        break;
    }
  };

  return (
    <div className="space-y-12">
      <h2 className="text-2xl font-semibold mb-4">Additional UI Patterns</h2>

      <section className="space-y-4">
        <h3 className="text-xl font-semibold">History Timeline</h3>
        <Card>
          <CardHeader>
            <CardTitle>History Timeline Example</CardTitle>
            <CardDescription>
              Display a timeline of changes to an entity with collapsible details
            </CardDescription>
          </CardHeader>
          <CardContent>
            <HistoryTimeline title="Contact History" items={historyItems} />
          </CardContent>
          <CardFooter className="flex flex-col items-start">
            <p className="text-sm text-muted-foreground mb-2">Usage:</p>
            <pre className="bg-muted p-2 rounded-md text-xs w-full overflow-auto">
              {`<HistoryTimeline
  title="Contact History"
  items={[
    {
      id: "1",
      timestamp: "2023-01-01T12:00:00Z",
      user: {
        id: "user1",
        name: "John Doe",
      },
      action: "create",
      summary: "Contact created",
    },
    {
      id: "2",
      timestamp: "2023-01-02T14:30:00Z",
      user: {
        id: "user2",
        name: "Jane Smith",
      },
      action: "update",
      changes: [
        { field: "name", oldValue: "John Smith", newValue: "John Doe" },
        { field: "email", oldValue: "<EMAIL>", newValue: "<EMAIL>" },
      ],
    }
  ]}
/>`}
            </pre>
          </CardFooter>
        </Card>
      </section>

      <section className="space-y-4">
        <h3 className="text-xl font-semibold">Document Attachment</h3>
        <Card>
          <CardHeader>
            <CardTitle>Document Attachment Example</CardTitle>
            <CardDescription>
              Upload, view, and manage documents attached to an entity
            </CardDescription>
          </CardHeader>
          <CardContent>
            <DocumentAttachment
              title="Contact Documents"
              description="Documents attached to this contact"
              documents={documents}
              onUpload={async (files) => {
                console.warn("Upload files:", files);
                return Promise.resolve();
              }}
              onDelete={async (id) => {
                console.warn("Delete document:", id);
                return Promise.resolve();
              }}
              onView={async (doc) => {
                console.warn("View document:", doc);
                return Promise.resolve();
              }}
              onDownload={async (doc) => {
                console.warn("Download document:", doc);
                return Promise.resolve();
              }}
            />
          </CardContent>
          <CardFooter className="flex flex-col items-start">
            <p className="text-sm text-muted-foreground mb-2">Usage:</p>
            <pre className="bg-muted p-2 rounded-md text-xs w-full overflow-auto">
              {`<DocumentAttachment
  title="Contact Documents"
  description="Documents attached to this contact"
  documents={documents}
  onUpload={handleUpload}
  onDelete={handleDelete}
  onView={handleView}
  onDownload={handleDownload}
/>`}
            </pre>
          </CardFooter>
        </Card>
      </section>

      <section className="space-y-4">
        <h3 className="text-xl font-semibold">Task Progress</h3>
        <Card>
          <CardHeader>
            <CardTitle>Task Progress Example</CardTitle>
            <CardDescription>
              Display progress of asynchronous tasks with steps and actions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <TaskProgress
              taskId="task-123"
              title="Generating Report"
              description="Please wait while we generate your report"
              status={"running" as const}
              progress={65}
              steps={taskSteps}
              onCancel={() => console.warn("Cancel task")}
            />
          </CardContent>
          <CardFooter className="flex flex-col items-start">
            <p className="text-sm text-muted-foreground mb-2">Usage:</p>
            <pre className="bg-muted p-2 rounded-md text-xs w-full overflow-auto">
              {`<TaskProgress
  taskId="task-123"
  title="Generating Report"
  description="Please wait while we generate your report"
  status="running"
  progress={65}
  steps={[
    { id: "step1", name: "Collecting data", status: "completed" },
    { id: "step2", name: "Processing data", status: "running" },
    { id: "step3", name: "Generating PDF", status: "pending" },
  ]}
  onCancel={handleCancel}
/>`}
            </pre>
          </CardFooter>
        </Card>
      </section>

      <section className="space-y-4">
        <h3 className="text-xl font-semibold">Notifications</h3>
        <Card>
          <CardHeader>
            <CardTitle>Notifications Example</CardTitle>
            <CardDescription>Display toast notifications and notification center</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <h4 className="text-sm font-medium mb-2">Toast Notifications</h4>
              <div className="flex flex-wrap gap-2">
                <Button onClick={() => handleShowNotification("success")}>Success</Button>
                <Button onClick={() => handleShowNotification("error")}>Error</Button>
                <Button onClick={() => handleShowNotification("info")}>Info</Button>
                <Button onClick={() => handleShowNotification("warning")}>Warning</Button>
                <Button onClick={() => handleShowNotification("action")}>With Action</Button>
              </div>
            </div>

            <div>
              <h4 className="text-sm font-medium mb-2">Notification Center</h4>
              <div className="p-4 border rounded-md">
                <p className="text-muted-foreground">Notification Center component removed</p>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex flex-col items-start">
            <p className="text-sm text-muted-foreground mb-2">Usage:</p>
            <pre className="bg-muted p-2 rounded-md text-xs w-full overflow-auto">
              {`// Show a notification
showNotification({
  title: "Success",
  description: "Operation completed successfully",
  type: "success",
  action: {
    label: "View",
    onClick: handleView,
  },
});`}
            </pre>
          </CardFooter>
        </Card>
      </section>
    </div>
  );
}
