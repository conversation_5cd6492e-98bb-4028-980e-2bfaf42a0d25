"use client";

import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { AppShell, Sidebar, Navbar } from "@/components/ui/app-shell";
import {
  Home,
  Users,
  Settings,
  Calendar,
  FileText,
  BarChart,
  LogOut,
  User,
  Bell,
} from "lucide-react";
import { showNotification } from "@/components/ui/notification";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

export default function AppShellPage() {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [notificationsCount, setNotificationsCount] = useState(3);

  const sidebarItems = [
    {
      title: "Dashboard",
      href: "#dashboard",
      icon: <Home className="h-4 w-4" />,
      active: true,
    },
    {
      title: "Users",
      icon: <Users className="h-4 w-4" />,
      children: [
        {
          title: "List",
          href: "#users",
        },
        {
          title: "Create",
          href: "#users/create",
        },
      ],
    },
    {
      title: "Calendar",
      href: "#calendar",
      icon: <Calendar className="h-4 w-4" />,
    },
    {
      title: "Reports",
      href: "#reports",
      icon: <FileText className="h-4 w-4" />,
    },
    {
      title: "Analytics",
      href: "#analytics",
      icon: <BarChart className="h-4 w-4" />,
    },
    {
      title: "Settings",
      href: "#settings",
      icon: <Settings className="h-4 w-4" />,
    },
  ];

  const navbarItems = [
    { title: "Dashboard", href: "#dashboard", active: true },
    { title: "Users", href: "#users" },
    { title: "Reports", href: "#reports" },
  ];

  const userMenuActions = [
    { label: "Profile", href: "#profile", icon: <User className="h-4 w-4" /> },
    { label: "Settings", href: "#settings", icon: <Settings className="h-4 w-4" /> },
    {
      label: "Logout",
      onClick: () => {
        showNotification({
          title: "Logged out",
          description: "You have been logged out successfully",
          type: "info",
        });
      },
      icon: <LogOut className="h-4 w-4" />,
    },
  ];

  const handleNotificationsClick = () => {
    showNotification({
      title: "Notifications",
      description: `You have ${notificationsCount} unread notifications`,
      type: "info",
    });
    setNotificationsCount(0);
  };

  return (
    <div className="space-y-12">
      <h2 className="text-2xl font-semibold mb-4">App Shell</h2>

      <section className="space-y-4">
        <h3 className="text-xl font-semibold">Sidebar</h3>
        <Card>
          <CardHeader>
            <CardTitle>Sidebar Component</CardTitle>
            <CardDescription>Navigation sidebar with collapsible sections</CardDescription>
          </CardHeader>
          <CardContent className="h-[400px] border rounded-md overflow-hidden">
            <Sidebar
              title="My App"
              children={undefined} // items={sidebarItems}
              // collapsed={sidebarCollapsed}
              // onToggle={() => setSidebarCollapsed(!sidebarCollapsed)}
            />
          </CardContent>
          <CardFooter className="flex flex-col items-start">
            <p className="text-sm text-muted-foreground mb-2">Usage:</p>
            <pre className="bg-muted p-2 rounded-md text-xs w-full overflow-auto">
              {`<Sidebar
  title="My App"
  logo={<Logo />}
  items={[
    {
      title: "Dashboard",
      href: "/dashboard",
      icon: <HomeIcon />,
      active: true,
    },
    {
      title: "Users",
      icon: <UsersIcon />,
      children: [
        {
          title: "List",
          href: "/users",
        },
        {
          title: "Create",
          href: "/users/create",
        },
      ],
    },
  ]}
  collapsed={sidebarCollapsed}
  onToggle={toggleSidebar}
/>`}
            </pre>
          </CardFooter>
        </Card>
      </section>

      <section className="space-y-4">
        <h3 className="text-xl font-semibold">Navbar</h3>
        <Card>
          <CardHeader>
            <CardTitle>Navbar Component</CardTitle>
            <CardDescription>Top navigation bar with user menu and notifications</CardDescription>
          </CardHeader>
          <CardContent className="border rounded-md overflow-hidden">
            <Navbar />
          </CardContent>
          <CardFooter className="flex flex-col items-start">
            <p className="text-sm text-muted-foreground mb-2">Usage:</p>
            <pre className="bg-muted p-2 rounded-md text-xs w-full overflow-auto">
              {`<Navbar
  title="My App"
  logo={<Logo />}
  items={[
    { title: "Dashboard", href: "/dashboard", active: true },
    { title: "Users", href: "/users" },
    { title: "Settings", href: "/settings" },
  ]}
  user={{
    name: "John Doe",
    email: "<EMAIL>",
  }}
  userMenuActions={[
    { label: "Profile", href: "/profile", icon: <UserIcon /> },
    { label: "Logout", onClick: handleLogout, icon: <LogOutIcon /> },
  ]}
  onSidebarToggle={toggleSidebar}
  showThemeToggle
  showNotifications
  unreadNotificationsCount={3}
/>`}
            </pre>
          </CardFooter>
        </Card>
      </section>

      <section className="space-y-4">
        <h3 className="text-xl font-semibold">Complete App Shell</h3>
        <Card>
          <CardHeader>
            <CardTitle>App Shell Component</CardTitle>
            <CardDescription>
              Combined sidebar and navbar for a complete application shell
            </CardDescription>
          </CardHeader>
          <CardContent className="h-[500px] border rounded-md overflow-hidden">
            <AppShell
              sidebar={
                <Sidebar title="My App">
                  <nav className="px-4 py-2 space-y-1">
                    {sidebarItems.map((item) => {
                      const Icon = item.icon?.type;
                      return (
                        <div key={item.title}>
                          <a
                            href={item.href || "#"}
                            className={`flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium ${
                              item.active
                                ? "bg-primary text-primary-foreground"
                                : "hover:bg-accent hover:text-accent-foreground"
                            }`}
                          >
                            {item.icon}
                            <span>{item.title}</span>
                          </a>
                          {item.children && (
                            <div className="ml-6 mt-1 space-y-1">
                              {item.children.map((child) => (
                                <a
                                  key={child.title}
                                  href={child.href || "#"}
                                  className="flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground"
                                >
                                  <span>{child.title}</span>
                                </a>
                              ))}
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </nav>
                </Sidebar>
              }
              navbar={
                <Navbar
                  left={<h2 className="text-xl font-bold">Dashboard</h2>}
                  right={
                    <div className="flex items-center gap-4">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={handleNotificationsClick}
                        className="relative"
                      >
                        <Bell className="h-5 w-5" />
                        {notificationsCount > 0 && (
                          <span className="absolute top-0 right-0 h-4 w-4 rounded-full bg-primary text-[10px] font-medium text-primary-foreground flex items-center justify-center">
                            {notificationsCount}
                          </span>
                        )}
                      </Button>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                            <Avatar className="h-8 w-8">
                              <AvatarFallback>JD</AvatarFallback>
                            </Avatar>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>John Doe</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          {userMenuActions.map((action) => (
                            <DropdownMenuItem key={action.label} onClick={action.onClick}>
                              {action.icon}
                              <span className="ml-2">{action.label}</span>
                            </DropdownMenuItem>
                          ))}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  }
                />
              }
            >
              <div className="p-6">
                <h1 className="text-2xl font-bold mb-4">Dashboard</h1>
                <p className="mb-4">Welcome to the application dashboard.</p>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {[1, 2, 3].map((i) => (
                    <Card key={i}>
                      <CardHeader>
                        <CardTitle>Card {i}</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p>This is a sample card in the dashboard.</p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            </AppShell>
          </CardContent>
          <CardFooter className="flex flex-col items-start">
            <p className="text-sm text-muted-foreground mb-2">Usage:</p>
            <pre className="bg-muted p-2 rounded-md text-xs w-full overflow-auto">
              {`<AppShell
  sidebar={
    <Sidebar title="My App">
      <nav className="px-4 py-2 space-y-1">
        {sidebarItems.map((item) => (
          <a
            key={item.title}
            href={item.href}
            className="flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium"
          >
            {item.icon}
            <span>{item.title}</span>
          </a>
        ))}
      </nav>
    </Sidebar>
  }
  navbar={
    <Navbar
      left={<h2 className="text-xl font-bold">Dashboard</h2>}
      right={
        <div className="flex items-center gap-4">
          <NotificationsButton count={3} onClick={handleNotifications} />
          <UserMenu user={user} actions={userMenuActions} />
        </div>
      }
    />
  }
>
  <div className="p-6">
    <h1>Main Content</h1>
    {/* Your application content goes here */}
  </div>
</AppShell>`}
            </pre>
          </CardFooter>
        </Card>
      </section>
    </div>
  );
}
