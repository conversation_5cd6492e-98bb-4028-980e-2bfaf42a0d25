"use client";

import React from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

export default function ColorsAndAnimationsPage() {
  return (
    <div className="space-y-12">
      <h2 className="text-3xl font-semibold mb-4">Colors, Radius, and Animations</h2>

      <Tabs defaultValue="colors">
        <TabsList className="mb-4">
          <TabsTrigger value="colors">Colors</TabsTrigger>
          <TabsTrigger value="radius">Border Radius</TabsTrigger>
          <TabsTrigger value="animations">Animations & Transitions</TabsTrigger>
        </TabsList>

        <TabsContent value="colors" className="space-y-8">
          <section>
            <h3 className="text-xl font-semibold mb-4">Theme Colors</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <Card className="overflow-hidden">
                <div
                  className="h-24 flex items-center justify-center"
                  style={{ backgroundColor: `var(--primary)`, color: "white" }}
                >
                  <span className="font-medium">Primary Text</span>
                </div>
                <CardContent className="p-4">
                  <p className="text-sm font-medium">Primary</p>
                  <p className="text-xs text-muted-foreground">var(--primary)</p>
                  <p className="text-xs text-muted-foreground mt-1">oklch(0.623 0.214 259.815)</p>
                </CardContent>
              </Card>

              <Card className="overflow-hidden">
                <div className="h-24" style={{ backgroundColor: `var(--secondary)` }}></div>
                <CardContent className="p-4">
                  <p className="text-sm font-medium">Secondary</p>
                  <p className="text-xs text-muted-foreground">var(--secondary)</p>
                  <p className="text-xs text-muted-foreground mt-1">oklch(0.967 0.001 286.375)</p>
                </CardContent>
              </Card>

              <Card className="overflow-hidden">
                <div className="h-24" style={{ backgroundColor: `var(--accent)` }}></div>
                <CardContent className="p-4">
                  <p className="text-sm font-medium">Accent</p>
                  <p className="text-xs text-muted-foreground">var(--accent)</p>
                  <p className="text-xs text-muted-foreground mt-1">oklch(0.967 0.001 286.375)</p>
                </CardContent>
              </Card>

              <Card className="overflow-hidden">
                <div className="h-24" style={{ backgroundColor: `var(--muted)` }}></div>
                <CardContent className="p-4">
                  <p className="text-sm font-medium">Muted</p>
                  <p className="text-xs text-muted-foreground">var(--muted)</p>
                  <p className="text-xs text-muted-foreground mt-1">oklch(0.967 0.001 286.375)</p>
                </CardContent>
              </Card>

              <Card className="overflow-hidden">
                <div className="h-24" style={{ backgroundColor: `var(--background)` }}></div>
                <CardContent className="p-4">
                  <p className="text-sm font-medium">Background</p>
                  <p className="text-xs text-muted-foreground">var(--background)</p>
                  <p className="text-xs text-muted-foreground mt-1">oklch(1 0 0)</p>
                </CardContent>
              </Card>

              <Card className="overflow-hidden">
                <div
                  className="h-24 flex items-center justify-center"
                  style={{ backgroundColor: `var(--foreground)`, color: "white" }}
                >
                  <span>Foreground</span>
                </div>
                <CardContent className="p-4">
                  <p className="text-sm font-medium">Foreground</p>
                  <p className="text-xs text-muted-foreground">var(--foreground)</p>
                  <p className="text-xs text-muted-foreground mt-1">oklch(0.141 0.005 285.823)</p>
                </CardContent>
              </Card>
            </div>
          </section>

          <section>
            <h3 className="text-xl font-semibold mb-4">Primary Color Scale</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
              {[50, 100, 200, 300, 400, 500, 600, 700, 800, 900].map((weight) => (
                <Card key={weight} className="overflow-hidden">
                  <div
                    className="h-24"
                    style={{ backgroundColor: `var(--primary-${weight})` }}
                  ></div>
                  <CardContent className="p-4">
                    <p className="text-sm font-medium">Primary {weight}</p>
                    <p className="text-xs text-muted-foreground">var(--primary-{weight})</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </section>

          <section>
            <h3 className="text-xl font-semibold mb-4">Contrast Examples</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>Primary Button</CardTitle>
                  <CardDescription>White text on primary background</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex flex-wrap gap-2">
                    <Button>Primary Button</Button>
                    <Button variant="outline">Outline Button</Button>
                    <Button variant="secondary">Secondary Button</Button>
                    <Button variant="destructive">Destructive Button</Button>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Text on Color</CardTitle>
                  <CardDescription>Text contrast examples</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 gap-2">
                    <div className="p-4 bg-primary text-primary-foreground rounded-md">
                      This is text on primary background
                    </div>
                    <div className="p-4 bg-secondary text-secondary-foreground rounded-md">
                      This is text on secondary background
                    </div>
                    <div className="p-4 bg-destructive text-destructive-foreground rounded-md">
                      This is text on destructive background
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </section>

          <section>
            <h3 className="text-xl font-semibold mb-4">Status Colors</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {["success", "warning", "info", "destructive"].map((status) => (
                <Card key={status} className="overflow-hidden">
                  <div
                    className="h-24 flex items-center justify-center"
                    style={{ backgroundColor: `var(--${status})`, color: "white" }}
                  >
                    <span className="font-medium capitalize">{status} Text</span>
                  </div>
                  <CardContent className="p-4">
                    <p className="text-sm font-medium capitalize">{status}</p>
                    <p className="text-xs text-muted-foreground">var(--{status})</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </section>

          <section>
            <h3 className="text-xl font-semibold mb-4">UI Colors</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {[
                "background",
                "foreground",
                "card",
                "card-foreground",
                "border",
                "input",
                "ring",
              ].map((color) => (
                <Card key={color} className="overflow-hidden">
                  <div className="h-24" style={{ backgroundColor: `var(--${color})` }}></div>
                  <CardContent className="p-4">
                    <p className="text-sm font-medium capitalize">{color}</p>
                    <p className="text-xs text-muted-foreground">var(--{color})</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </section>
        </TabsContent>

        <TabsContent value="radius" className="space-y-8">
          <section>
            <h3 className="text-xl font-semibold mb-4">Border Radius</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {[
                { name: "xs", value: "var(--radius-xs)", size: "4px" },
                { name: "sm", value: "var(--radius-sm)", size: "6px" },
                { name: "md", value: "var(--radius-md)", size: "8px" },
                { name: "DEFAULT", value: "var(--radius)", size: "10px" },
                { name: "lg", value: "var(--radius-lg)", size: "12px" },
                { name: "xl", value: "var(--radius-xl)", size: "16px" },
                { name: "2xl", value: "var(--radius-2xl)", size: "24px" },
                { name: "full", value: "var(--radius-full)", size: "9999px" },
              ].map((radius) => (
                <Card key={radius.name} className="overflow-hidden">
                  <div className="p-4 flex justify-center">
                    <div
                      className="h-24 w-24 bg-primary"
                      style={{ borderRadius: radius.value }}
                    ></div>
                  </div>
                  <CardContent className="p-4">
                    <p className="text-sm font-medium">radius-{radius.name}</p>
                    <p className="text-xs text-muted-foreground">{radius.size}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </section>

          <section>
            <h3 className="text-xl font-semibold mb-4">Border Radius Examples</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>Buttons with Different Radius</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex flex-wrap gap-2">
                    <Button className="rounded-none">None</Button>
                    <Button className="rounded-xs">Extra Small</Button>
                    <Button className="rounded-sm">Small</Button>
                    <Button className="rounded">Default</Button>
                    <Button className="rounded-lg">Large</Button>
                    <Button className="rounded-xl">Extra Large</Button>
                    <Button className="rounded-2xl">2XL</Button>
                    <Button className="rounded-full">Full</Button>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Cards with Different Radius</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-2">
                    <div className="bg-muted p-4 rounded-none">None</div>
                    <div className="bg-muted p-4 rounded-xs">Extra Small</div>
                    <div className="bg-muted p-4 rounded-sm">Small</div>
                    <div className="bg-muted p-4 rounded">Default</div>
                    <div className="bg-muted p-4 rounded-lg">Large</div>
                    <div className="bg-muted p-4 rounded-xl">Extra Large</div>
                    <div className="bg-muted p-4 rounded-2xl">2XL</div>
                    <div className="bg-muted p-4 rounded-full">Full</div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </section>
        </TabsContent>

        <TabsContent value="animations" className="space-y-8">
          <section>
            <h3 className="text-xl font-semibold mb-4">Transitions</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>Transition Durations</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex flex-wrap gap-2">
                    <Button
                      className="transition-colors hover:bg-primary-600"
                      style={{ transitionDuration: "var(--duration-fast)" }}
                    >
                      Fast (150ms)
                    </Button>
                    <Button
                      className="transition-colors hover:bg-primary-600"
                      style={{ transitionDuration: "var(--duration-normal)" }}
                    >
                      Normal (250ms)
                    </Button>
                    <Button
                      className="transition-colors hover:bg-primary-600"
                      style={{ transitionDuration: "var(--duration-slow)" }}
                    >
                      Slow (350ms)
                    </Button>
                    <Button
                      className="transition-colors hover:bg-primary-600"
                      style={{ transitionDuration: "var(--duration-slower)" }}
                    >
                      Slower (500ms)
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Transition Easing</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex flex-wrap gap-2">
                    <Button
                      className="transition-transform hover:translate-y-[-4px]"
                      style={{
                        transitionDuration: "var(--duration-normal)",
                        transitionTimingFunction: "var(--ease-in)",
                      }}
                    >
                      Ease In
                    </Button>
                    <Button
                      className="transition-transform hover:translate-y-[-4px]"
                      style={{
                        transitionDuration: "var(--duration-normal)",
                        transitionTimingFunction: "var(--ease-out)",
                      }}
                    >
                      Ease Out
                    </Button>
                    <Button
                      className="transition-transform hover:translate-y-[-4px]"
                      style={{
                        transitionDuration: "var(--duration-normal)",
                        transitionTimingFunction: "var(--ease-in-out)",
                      }}
                    >
                      Ease In Out
                    </Button>
                    <Button
                      className="transition-transform hover:translate-y-[-4px]"
                      style={{
                        transitionDuration: "var(--duration-normal)",
                        transitionTimingFunction: "var(--ease-bounce)",
                      }}
                    >
                      Bounce
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </section>

          <section>
            <h3 className="text-xl font-semibold mb-4">Animations</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>Fade Animations</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex flex-wrap gap-2">
                    <Badge className="animate-fade-in">Fade In</Badge>
                    <Badge className="animate-pulse">Pulse</Badge>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Slide Animations</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex flex-wrap gap-2">
                    <Badge className="animate-slide-in-from-top">From Top</Badge>
                    <Badge className="animate-slide-in-from-bottom">From Bottom</Badge>
                    <Badge className="animate-slide-in-from-left">From Left</Badge>
                    <Badge className="animate-slide-in-from-right">From Right</Badge>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Other Animations</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex flex-wrap gap-2">
                    <Badge className="animate-scale-in">Scale In</Badge>
                    <Badge className="animate-spin">Spin</Badge>
                    <Badge className="animate-bounce">Bounce</Badge>
                  </div>
                </CardContent>
              </Card>
            </div>
          </section>

          <section>
            <h3 className="text-xl font-semibold mb-4">Navigation Transitions</h3>
            <Card>
              <CardHeader>
                <CardTitle>Navigation Items</CardTitle>
                <CardDescription>
                  Hover over the navigation items to see the transitions
                </CardDescription>
              </CardHeader>
              <CardContent>
                <nav className="flex space-x-4">
                  <a href="#" className="nav-item text-foreground">
                    Home
                  </a>
                  <a href="#" className="nav-item text-foreground">
                    About
                  </a>
                  <a href="#" className="nav-item text-foreground active">
                    Services
                  </a>
                  <a href="#" className="nav-item text-foreground">
                    Contact
                  </a>
                </nav>
              </CardContent>
            </Card>
          </section>
        </TabsContent>
      </Tabs>
    </div>
  );
}
