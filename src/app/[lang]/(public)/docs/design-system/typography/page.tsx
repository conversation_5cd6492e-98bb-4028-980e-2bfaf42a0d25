"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  H1,
  H2,
  H3,
  H4,
  H5,
  <PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  Lead,
  PageTitle,
  SectionTitle,
  SubsectionTitle,
  CardTitle as TypographyCardTitle,
} from "@/components/typography";

export default function TypographyPage() {
  return (
    <div className="space-y-8">
      <SectionTitle description="Our typography system provides consistent text styles across the application. This page demonstrates the different typography styles and their usage guidelines.">
        Typography System
      </SectionTitle>

      <Tabs defaultValue="hierarchy" className="w-full">
        <TabsList className="w-full justify-start">
          <TabsTrigger value="hierarchy">Heading Hierarchy</TabsTrigger>
          <TabsTrigger value="body">Body Text</TabsTrigger>
          <TabsTrigger value="colors">Text Colors</TabsTrigger>
          <TabsTrigger value="usage">Usage Guidelines</TabsTrigger>
        </TabsList>

        <TabsContent value="hierarchy" className="space-y-6 mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Heading Hierarchy</CardTitle>
            </CardHeader>
            <CardContent className="space-y-8">
              <div className="space-y-2">
                <h1 className="text-3xl font-bold">Page Title (h1)</h1>
                <p className="text-sm text-muted-foreground">
                  text-3xl font-bold (36px) - Used for main page titles
                </p>
                <div className="h-px bg-border my-4" />
              </div>

              <div className="space-y-2">
                <h2 className="text-2xl font-semibold">Section Title (h2)</h2>
                <p className="text-sm text-muted-foreground">
                  text-2xl font-semibold (30px) - Used for major section headings
                </p>
                <div className="h-px bg-border my-4" />
              </div>

              <div className="space-y-2">
                <h3 className="text-xl font-semibold">Subsection Title (h3)</h3>
                <p className="text-sm text-muted-foreground">
                  text-xl font-semibold (24px) - Used for subsection headings
                </p>
                <div className="h-px bg-border my-4" />
              </div>

              <div className="space-y-2">
                <h4 className="text-lg font-medium">Card Title (h4)</h4>
                <p className="text-sm text-muted-foreground">
                  text-lg font-medium (18px) - Used for card titles and minor sections
                </p>
                <div className="h-px bg-border my-4" />
              </div>

              <div className="space-y-2">
                <h5 className="text-base font-medium">Small Title (h5)</h5>
                <p className="text-sm text-muted-foreground">
                  text-base font-medium (16px) - Used for small titles and labels
                </p>
              </div>
            </CardContent>
            <CardFooter className="flex flex-col items-start">
              <p className="text-sm text-muted-foreground mb-2">Usage:</p>
              <pre className="bg-muted p-2 rounded-md text-xs w-full overflow-auto">
                {`<h1 className="text-3xl font-bold">Page Title</h1>
<h2 className="text-2xl font-semibold">Section Title</h2>
<h3 className="text-xl font-semibold">Subsection Title</h3>
<h4 className="text-lg font-medium">Card Title</h4>
<h5 className="text-base font-medium">Small Title</h5>`}
              </pre>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="body" className="space-y-6 mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Body Text</CardTitle>
            </CardHeader>
            <CardContent className="space-y-8">
              <div className="space-y-2">
                <p className="text-base">Body Text (Regular)</p>
                <p className="text-sm text-muted-foreground">
                  text-base (16px) - Used for main body text
                </p>
                <div className="h-px bg-border my-4" />
              </div>

              <div className="space-y-2">
                <p className="text-sm">Small Text</p>
                <p className="text-sm text-muted-foreground">
                  text-sm (14px) - Used for secondary text, captions, and helper text
                </p>
                <div className="h-px bg-border my-4" />
              </div>

              <div className="space-y-2">
                <p className="text-xs">Tiny Text</p>
                <p className="text-sm text-muted-foreground">
                  text-xs (12px) - Used for very small text, footnotes, and legal text
                </p>
              </div>
            </CardContent>
            <CardFooter className="flex flex-col items-start">
              <p className="text-sm text-muted-foreground mb-2">Usage:</p>
              <pre className="bg-muted p-2 rounded-md text-xs w-full overflow-auto">
                {`<p className="text-base">Body Text (Regular)</p>
<p className="text-sm">Small Text</p>
<p className="text-xs">Tiny Text</p>`}
              </pre>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="colors" className="space-y-6 mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Text Colors</CardTitle>
            </CardHeader>
            <CardContent className="space-y-8">
              <div className="space-y-2">
                <p className="text-foreground">Primary Text</p>
                <p className="text-sm text-muted-foreground">
                  text-foreground - Used for main text content
                </p>
                <div className="h-px bg-border my-4" />
              </div>

              <div className="space-y-2">
                <p className="text-muted-foreground">Secondary Text</p>
                <p className="text-sm text-muted-foreground">
                  text-muted-foreground - Used for secondary text, descriptions, and helper text
                </p>
                <div className="h-px bg-border my-4" />
              </div>

              <div className="space-y-2">
                <p className="text-primary">Accent Text</p>
                <p className="text-sm text-muted-foreground">
                  text-primary - Used for links and emphasized text
                </p>
                <div className="h-px bg-border my-4" />
              </div>

              <div className="space-y-2">
                <p className="text-destructive">Destructive Text</p>
                <p className="text-sm text-muted-foreground">
                  text-destructive - Used for errors and destructive actions
                </p>
              </div>
            </CardContent>
            <CardFooter className="flex flex-col items-start">
              <p className="text-sm text-muted-foreground mb-2">Usage:</p>
              <pre className="bg-muted p-2 rounded-md text-xs w-full overflow-auto">
                {`<p className="text-foreground">Primary Text</p>
<p className="text-muted-foreground">Secondary Text</p>
<p className="text-primary">Accent Text</p>
<p className="text-destructive">Destructive Text</p>`}
              </pre>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="usage" className="space-y-6 mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Usage Guidelines</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <h3 className="text-xl font-semibold">Page Structure</h3>
              <ul className="list-disc pl-6 space-y-2">
                <li>
                  Use <span className="font-mono text-sm">h1 (text-3xl)</span> for main page titles
                </li>
                <li>
                  Use <span className="font-mono text-sm">h2 (text-2xl)</span> for major section
                  headings
                </li>
                <li>
                  Use <span className="font-mono text-sm">h3 (text-xl)</span> for subsection
                  headings
                </li>
                <li>
                  Use <span className="font-mono text-sm">h4 (text-lg)</span> for card titles
                </li>
                <li>
                  Use <span className="font-mono text-sm">h5 (text-base)</span> for small titles and
                  labels
                </li>
              </ul>

              <h3 className="text-xl font-semibold mt-6">Hierarchy</h3>
              <ul className="list-disc pl-6 space-y-2">
                <li>Maintain proper heading hierarchy (h1 → h2 → h3 → h4 → h5)</li>
                <li>Don't skip heading levels (e.g., don't go from h2 to h4)</li>
                <li>Use only one h1 per page</li>
              </ul>

              <h3 className="text-xl font-semibold mt-6">Text Colors</h3>
              <ul className="list-disc pl-6 space-y-2">
                <li>
                  Use <span className="font-mono text-sm">text-foreground</span> for main text
                  content
                </li>
                <li>
                  Use <span className="font-mono text-sm">text-muted-foreground</span> for secondary
                  text
                </li>
                <li>
                  Use <span className="font-mono text-sm">text-primary</span> for links and
                  emphasized text
                </li>
                <li>
                  Use <span className="font-mono text-sm">text-destructive</span> for errors and
                  warnings
                </li>
              </ul>

              <h3 className="text-xl font-semibold mt-6">Responsive Typography</h3>
              <p>
                Our typography system is responsive by default. The text sizes are defined in rem
                units, which scale based on the user's browser settings. This ensures that text
                remains readable across different screen sizes and devices.
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <section className="space-y-4 mt-8">
        <H3>Example Page Structure (Manual Classes)</H3>
        <Card>
          <CardContent className="p-6">
            <div className="border border-dashed rounded-md p-6">
              <h1 className="text-3xl font-bold mb-4">Page Title (h1)</h1>
              <p className="text-muted-foreground mb-8">Page description text goes here.</p>

              <h2 className="text-2xl font-semibold mb-4">Section Title (h2)</h2>
              <p className="mb-6">This is regular body text that explains this section.</p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div className="border rounded-md p-4">
                  <h3 className="text-xl font-semibold mb-2">Subsection Title (h3)</h3>
                  <p className="mb-4">More detailed explanation about this subsection.</p>

                  <h4 className="text-lg font-medium mb-2">Card Title (h4)</h4>
                  <p className="text-sm text-muted-foreground">
                    Secondary text that provides additional context.
                  </p>
                </div>

                <div className="border rounded-md p-4">
                  <h3 className="text-xl font-semibold mb-2">Another Subsection (h3)</h3>
                  <p className="mb-4">More detailed explanation about this subsection.</p>

                  <h5 className="text-base font-medium mb-2">Small Title (h5)</h5>
                  <p className="text-xs">Tiny text used for footnotes or additional information.</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </section>

      <section className="space-y-4 mt-8">
        <H3>Typography Components</H3>
        <Card>
          <CardHeader>
            <CardTitle>Typography Component Library</CardTitle>
          </CardHeader>
          <CardContent className="space-y-8">
            <div className="space-y-2">
              <H1>Page Title (H1 Component)</H1>
              <Muted>Uses the H1 component which applies text-3xl font-bold</Muted>
              <div className="h-px bg-border my-4" />
            </div>

            <div className="space-y-2">
              <H2>Section Title (H2 Component)</H2>
              <Muted>Uses the H2 component which applies text-2xl font-semibold</Muted>
              <div className="h-px bg-border my-4" />
            </div>

            <div className="space-y-2">
              <H3>Subsection Title (H3 Component)</H3>
              <Muted>Uses the H3 component which applies text-xl font-semibold</Muted>
              <div className="h-px bg-border my-4" />
            </div>

            <div className="space-y-2">
              <H4>Card Title (H4 Component)</H4>
              <Muted>Uses the H4 component which applies text-lg font-medium</Muted>
              <div className="h-px bg-border my-4" />
            </div>

            <div className="space-y-2">
              <H5>Small Title (H5 Component)</H5>
              <Muted>Uses the H5 component which applies text-base font-medium</Muted>
              <div className="h-px bg-border my-4" />
            </div>

            <div className="space-y-2">
              <P>Body Text (P Component)</P>
              <Muted>Uses the P component which applies text-base</Muted>
              <div className="h-px bg-border my-4" />
            </div>

            <div className="space-y-2">
              <Small>Small Text (Small Component)</Small>
              <Muted>Uses the Small component which applies text-sm</Muted>
              <div className="h-px bg-border my-4" />
            </div>

            <div className="space-y-2">
              <Tiny>Tiny Text (Tiny Component)</Tiny>
              <Muted>Uses the Tiny component which applies text-xs</Muted>
              <div className="h-px bg-border my-4" />
            </div>

            <div className="space-y-2">
              <Muted>Muted Text (Muted Component)</Muted>
              <Small>Uses the Muted component which applies text-sm text-muted-foreground</Small>
              <div className="h-px bg-border my-4" />
            </div>

            <div className="space-y-2">
              <Lead>Lead Text (Lead Component)</Lead>
              <Small>Uses the Lead component which applies text-lg text-muted-foreground</Small>
            </div>
          </CardContent>
          <CardFooter className="flex flex-col items-start">
            <p className="text-sm text-muted-foreground mb-2">Usage:</p>
            <pre className="bg-muted p-2 rounded-md text-xs w-full overflow-auto">
              {`import { H1, H2, H3, H4, H5, P, Small, Tiny, Muted, Lead } from "@/components/typography";

// Then use them in your components
<H1>Page Title</H1>
<H2>Section Title</H2>
<H3>Subsection Title</H3>
<P>Regular paragraph text</P>
<Muted>Secondary text with muted color</Muted>`}
            </pre>
          </CardFooter>
        </Card>
      </section>

      <section className="space-y-4 mt-8">
        <H3>Combined Typography Components</H3>
        <Card>
          <CardHeader>
            <CardTitle>Combined Title Components</CardTitle>
          </CardHeader>
          <CardContent className="space-y-8">
            <div className="space-y-2">
              <PageTitle description="This component combines an H1 with a description in Lead style">
                Page Title with Description
              </PageTitle>
              <div className="h-px bg-border my-4" />
            </div>

            <div className="space-y-2">
              <SectionTitle description="This component combines an H2 with a description in Muted style">
                Section Title with Description
              </SectionTitle>
              <div className="h-px bg-border my-4" />
            </div>

            <div className="space-y-2">
              <SubsectionTitle description="This component combines an H3 with a description in Muted style">
                Subsection Title with Description
              </SubsectionTitle>
              <div className="h-px bg-border my-4" />
            </div>

            <div className="space-y-2">
              <TypographyCardTitle description="This component combines an H4 with a description in Muted style">
                Card Title with Description
              </TypographyCardTitle>
            </div>
          </CardContent>
          <CardFooter className="flex flex-col items-start">
            <p className="text-sm text-muted-foreground mb-2">Usage:</p>
            <pre className="bg-muted p-2 rounded-md text-xs w-full overflow-auto">
              {`import {
  PageTitle,
  SectionTitle,
  SubsectionTitle,
  CardTitle
} from "@/components/typography";

// Then use them in your components
<PageTitle description="Page description text">Page Title</PageTitle>
<SectionTitle description="Section description">Section Title</SectionTitle>
<SubsectionTitle description="Subsection description">Subsection Title</SubsectionTitle>
<CardTitle description="Card description">Card Title</CardTitle>`}
            </pre>
          </CardFooter>
        </Card>
      </section>

      <section className="space-y-4 mt-8">
        <H3>Example Page Structure (Using Components)</H3>
        <Card>
          <CardContent className="p-6">
            <div className="border border-dashed rounded-md p-6">
              <PageTitle description="Page description text goes here.">Page Title</PageTitle>

              <SectionTitle>Section Title</SectionTitle>
              <P className="mb-6">This is regular body text that explains this section.</P>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div className="border rounded-md p-4">
                  <SubsectionTitle>Subsection Title</SubsectionTitle>
                  <P className="mb-4">More detailed explanation about this subsection.</P>

                  <TypographyCardTitle>Card Title</TypographyCardTitle>
                  <Muted>Secondary text that provides additional context.</Muted>
                </div>

                <div className="border rounded-md p-4">
                  <SubsectionTitle>Another Subsection</SubsectionTitle>
                  <P className="mb-4">More detailed explanation about this subsection.</P>

                  <H5 className="mb-2">Small Title</H5>
                  <Tiny>Tiny text used for footnotes or additional information.</Tiny>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </section>
    </div>
  );
}
