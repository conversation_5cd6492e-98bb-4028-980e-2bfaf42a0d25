"use client";

import React from "react";
import { <PERSON>Header } from "@/components";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { BackButton } from "@/components/navigation/BackButton";

export default function ComponentsPage() {
  return (
    <div className="space-y-8">
      <h2 className="text-2xl font-semibold mb-4">Components</h2>

      <section className="space-y-4">
        <h3 className="text-xl font-semibold">PageHeader</h3>
        <Card>
          <CardContent className="pt-6">
            <PageHeader
              title="Page Title"
              description="This is a description of the page content."
              actions={
                <div className="flex gap-2">
                  <Button variant="outline">Secondary Action</Button>
                  <Button>Primary Action</Button>
                </div>
              }
            />
            <div className="mt-6 p-4 border border-dashed rounded-md">
              <p>Page content would go here</p>
            </div>
          </CardContent>
          <CardFooter className="flex flex-col items-start">
            <p className="text-sm text-muted-foreground mb-2">Usage:</p>
            <pre className="bg-muted p-2 rounded-md text-xs w-full overflow-auto">
              {`<PageHeader 
  title="Page Title" 
  description="This is a description of the page content."
  actions={
    <div className="flex gap-2">
      <Button variant="outline">Secondary Action</Button>
      <Button>Primary Action</Button>
    </div>
  }
/>`}
            </pre>
          </CardFooter>
        </Card>
      </section>

      <section className="space-y-4">
        <h3 className="text-xl font-semibold">BackButton</h3>
        <Card>
          <CardContent className="pt-6">
            <BackButton href="#" />
          </CardContent>
          <CardFooter className="flex flex-col items-start">
            <p className="text-sm text-muted-foreground mb-2">Usage:</p>
            <pre className="bg-muted p-2 rounded-md text-xs w-full overflow-auto">
              {`<BackButton href="/path/to/previous/page" />`}
            </pre>
          </CardFooter>
        </Card>
      </section>

      <section className="space-y-4">
        <h3 className="text-xl font-semibold">Card Components</h3>
        <Card>
          <CardHeader>
            <CardTitle>Card Title</CardTitle>
            <CardDescription>Card description goes here</CardDescription>
          </CardHeader>
          <CardContent>
            <p>This is the main content of the card.</p>
          </CardContent>
          <CardFooter>
            <Button>Card Action</Button>
          </CardFooter>
        </Card>
      </section>

      <section className="space-y-4">
        <h3 className="text-xl font-semibold">Tabs</h3>
        <Card>
          <CardContent className="pt-6">
            <Tabs defaultValue="tab1">
              <TabsList>
                <TabsTrigger value="tab1">Tab 1</TabsTrigger>
                <TabsTrigger value="tab2">Tab 2</TabsTrigger>
                <TabsTrigger value="tab3">Tab 3</TabsTrigger>
              </TabsList>
              <TabsContent value="tab1">
                <div className="p-4 mt-4 border border-dashed rounded-md">
                  <p>Content for Tab 1</p>
                </div>
              </TabsContent>
              <TabsContent value="tab2">
                <div className="p-4 mt-4 border border-dashed rounded-md">
                  <p>Content for Tab 2</p>
                </div>
              </TabsContent>
              <TabsContent value="tab3">
                <div className="p-4 mt-4 border border-dashed rounded-md">
                  <p>Content for Tab 3</p>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </section>
    </div>
  );
}
