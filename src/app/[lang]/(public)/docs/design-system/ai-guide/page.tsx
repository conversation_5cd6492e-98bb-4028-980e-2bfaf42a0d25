"use client";

import React from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  H1,
  H2,
  H3,
  H4,
  <PERSON>,
  <PERSON>,
  Mu<PERSON>,
  Lead,
  PageTitle,
  SectionTitle,
  SubsectionTitle,
} from "@/components/typography";

export default function AIGuidePage() {
  return (
    <div className="space-y-8">
      <PageTitle description="A comprehensive guide to the design system for AI assistants">
        AI Assistant Guide to Design System
      </PageTitle>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="w-full justify-start">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="principles">Design Principles</TabsTrigger>
          <TabsTrigger value="components">Component Usage</TabsTrigger>
          <TabsTrigger value="layouts">Layout Patterns</TabsTrigger>
          <TabsTrigger value="typography">Typography System</TabsTrigger>
          <TabsTrigger value="examples">Example Patterns</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6 mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Design System Purpose and Goals</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <P>
                This design system is built to provide a consistent, accessible, and efficient UI
                framework for building business applications. It's based on Next.js 15, React 19,
                and Shadcn/Tailwind 4, with a focus on server-first architecture using React Server
                Components.
              </P>

              <H3>Key Goals</H3>
              <ul className="list-disc pl-6 space-y-2">
                <li>Maintain consistent UI/UX across the application</li>
                <li>Optimize for business workflows and data-heavy interfaces</li>
                <li>Support multi-language (French first, then English)</li>
                <li>Ensure accessibility compliance</li>
                <li>Prioritize performance with server-first architecture</li>
                <li>Support role-specific dashboards and views</li>
              </ul>

              <H3>How to Use This Guide</H3>
              <P>
                When asked to create or modify UI components, refer to this guide to understand the
                design patterns, component usage, and layout structures. The design system follows
                specific patterns for different types of pages (list views, detail views, forms,
                etc.) that should be consistently applied.
              </P>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="principles" className="space-y-6 mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Design Principles</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <H3>1. Server-First Architecture</H3>
              <P>
                The application embraces React Server Components and Server Actions. Avoid
                client-side state management when possible. Use uncontrolled forms with
                useActionState for server actions.
              </P>

              <H3>2. Consistent Layout Patterns</H3>
              <P>Use standardized layout components for common patterns:</P>
              <ul className="list-disc pl-6 space-y-2">
                <li>
                  <strong>ListLayout</strong>: For displaying collections of items
                </li>
                <li>
                  <strong>DetailLayout</strong>: For viewing details of a single item
                </li>
                <li>
                  <strong>FormLayout</strong>: For create/edit forms
                </li>
                <li>
                  <strong>ContentLayout</strong>: For general content sections
                </li>
                <li>
                  <strong>SidebarLayout</strong>: For layouts with a sidebar
                </li>
              </ul>

              <H3>3. Typography Hierarchy</H3>
              <P>
                Follow the established typography hierarchy using the typography components. Page
                titles should be larger than section titles, which should be larger than subsection
                titles.
              </P>

              <H3>4. Interaction Patterns</H3>
              <ul className="list-disc pl-6 space-y-2">
                <li>Use clickable rows in list views that navigate to detail pages</li>
                <li>Use modals for quick actions, drawers for quick information viewing</li>
                <li>Use right sidebars for calendars and supplementary information</li>
                <li>Use step form wizards for multi-step processes</li>
              </ul>

              <H3>5. Multi-Tenant Architecture</H3>
              <P>
                All entities require organization_id fields for row-level security and
                multi-tenancy. This should be reflected in the UI by showing only relevant data to
                the current user's organization.
              </P>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="components" className="space-y-6 mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Component Usage Guidelines</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <H3>Typography Components</H3>
              <P>
                Always use the typography components instead of raw HTML elements with Tailwind
                classes:
              </P>
              <ul className="list-disc pl-6 space-y-2">
                <li>
                  <code>{"<H1>, <H2>, <H3>, <H4>, <H5>"}</code> for headings
                </li>
                <li>
                  <code>{"<P>, <Small>, <Tiny>, <Muted>, <Lead>"}</code> for text
                </li>
                <li>
                  <code>{"<PageTitle>, <SectionTitle>, <SubsectionTitle>, <CardTitle>"}</code> for
                  combined title+description
                </li>
              </ul>

              <H3>Layout Components</H3>
              <P>Use the appropriate layout component for each scenario:</P>
              <ul className="list-disc pl-6 space-y-2">
                <li>
                  <code>{"<FormLayout>"}</code> for all forms
                </li>
                <li>
                  <code>{"<DetailLayout>"}</code> for viewing entity details
                </li>
                <li>
                  <code>{"<ListLayout>"}</code> for lists of items
                </li>
                <li>
                  <code>{"<ContentLayout>"}</code> for general content sections
                </li>
                <li>
                  <code>{"<SidebarLayout>"}</code> for layouts with a sidebar
                </li>
              </ul>

              <H3>UI Components</H3>
              <P>Use Shadcn UI components with consistent patterns:</P>
              <ul className="list-disc pl-6 space-y-2">
                <li>
                  Use <code>Card</code> components for content containers
                </li>
                <li>
                  Use <code>Table</code> for structured data display
                </li>
                <li>
                  Use <code>Tabs</code> for organizing related content
                </li>
                <li>
                  Use <code>Dialog</code> for modals and <code>Drawer</code> for side panels
                </li>
                <li>
                  Use <code>Form</code> components for all input forms
                </li>
              </ul>

              <H3>Data Display</H3>
              <P>For data-heavy interfaces:</P>
              <ul className="list-disc pl-6 space-y-2">
                <li>Use tables for structured data with many columns</li>
                <li>Use cards for visual representation of entities</li>
                <li>Use lists for simple collections</li>
                <li>Don't display status badges like 'active' to end users</li>
                <li>Prefer content to break to new lines rather than showing scrollbars</li>
              </ul>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="layouts" className="space-y-6 mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Layout Patterns</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <H3>Page Structure</H3>
              <P>Pages follow a consistent structure:</P>
              <ul className="list-disc pl-6 space-y-2">
                <li>
                  Routes organized with public routes under src/app/[lang]/(public) and
                  authenticated routes under src/app/[lang]/(protected)
                </li>
                <li>Domain-specific services in domain-level lib folders</li>
                <li>Shared code in src/lib with consistent structure</li>
                <li>Include loading.tsx and error.tsx files in page folders</li>
              </ul>

              <H3>List View Pattern</H3>
              <P>List views should:</P>
              <ul className="list-disc pl-6 space-y-2">
                <li>
                  Use <code>ListLayout</code> component
                </li>
                <li>Include a header with title and action buttons</li>
                <li>Use clickable rows that navigate to detail view</li>
                <li>Include pagination if needed</li>
                <li>Support filtering and sorting where appropriate</li>
              </ul>

              <H3>Detail View Pattern</H3>
              <P>Detail views should:</P>
              <ul className="list-disc pl-6 space-y-2">
                <li>
                  Use <code>DetailLayout</code> component
                </li>
                <li>Emphasize the entity's name as the main heading</li>
                <li>Use tabs to organize different aspects of the entity</li>
                <li>Include action buttons for edit, delete, etc.</li>
                <li>Show related entities in a structured way</li>
              </ul>

              <H3>Form Pattern</H3>
              <P>Forms should:</P>
              <ul className="list-disc pl-6 space-y-2">
                <li>
                  Use <code>FormLayout</code> component
                </li>
                <li>Group related fields together</li>
                <li>Use server actions with useActionState</li>
                <li>Include proper validation and error handling</li>
                <li>Have consistent button placement (cancel on left, submit on right)</li>
              </ul>

              <H3>Multi-Step Form Pattern</H3>
              <P>Multi-step forms should:</P>
              <ul className="list-disc pl-6 space-y-2">
                <li>Be implemented as layouts with nested pages</li>
                <li>Show clear progress indication</li>
                <li>Allow navigation between steps</li>
                <li>Validate each step before proceeding</li>
                <li>Use React Server Components for each step</li>
              </ul>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="typography" className="space-y-6 mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Typography System</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <H3>Typography Components</H3>
              <P>Always use typography components instead of raw HTML elements:</P>
              <ul className="list-disc pl-6 space-y-2">
                <li>
                  <code>{"<H1>"}</code> - Page Title (text-3xl font-bold)
                </li>
                <li>
                  <code>{"<H2>"}</code> - Section Title (text-2xl font-semibold)
                </li>
                <li>
                  <code>{"<H3>"}</code> - Subsection Title (text-xl font-semibold)
                </li>
                <li>
                  <code>{"<H4>"}</code> - Card Title (text-lg font-medium)
                </li>
                <li>
                  <code>{"<H5>"}</code> - Small Title (text-base font-medium)
                </li>
                <li>
                  <code>{"<P>"}</code> - Body Text (text-base)
                </li>
                <li>
                  <code>{"<Small>"}</code> - Small Text (text-sm)
                </li>
                <li>
                  <code>{"<Tiny>"}</code> - Tiny Text (text-xs)
                </li>
                <li>
                  <code>{"<Muted>"}</code> - Secondary Text (text-sm text-muted-foreground)
                </li>
                <li>
                  <code>{"<Lead>"}</code> - Large Body Text (text-lg text-muted-foreground)
                </li>
              </ul>

              <H3>Combined Typography Components</H3>
              <P>Use these components for common patterns:</P>
              <ul className="list-disc pl-6 space-y-2">
                <li>
                  <code>{'<PageTitle description="...">'}</code> - H1 with Lead description
                </li>
                <li>
                  <code>{'<SectionTitle description="...">'}</code> - H2 with Muted description
                </li>
                <li>
                  <code>{'<SubsectionTitle description="...">'}</code> - H3 with Muted description
                </li>
                <li>
                  <code>{'<CardTitle description="...">'}</code> - H4 with Muted description
                </li>
              </ul>

              <H3>Typography Hierarchy</H3>
              <P>Follow these guidelines for hierarchy:</P>
              <ul className="list-disc pl-6 space-y-2">
                <li>Use only one H1 per page (main page title)</li>
                <li>Use H2 for major sections</li>
                <li>Use H3 for subsections within H2 sections</li>
                <li>Use H4 for card titles and minor sections</li>
                <li>Don't skip levels (e.g., don't go from H2 to H4)</li>
              </ul>

              <H3>Text Colors</H3>
              <P>Use these color classes consistently:</P>
              <ul className="list-disc pl-6 space-y-2">
                <li>
                  <code>text-foreground</code> - Primary text
                </li>
                <li>
                  <code>text-muted-foreground</code> - Secondary text
                </li>
                <li>
                  <code>text-primary</code> - Accent text (links, emphasis)
                </li>
                <li>
                  <code>text-destructive</code> - Error text
                </li>
              </ul>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="examples" className="space-y-6 mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Example Patterns</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <H3>List View Example</H3>
              <P>When creating a list view, follow this pattern:</P>
              <pre className="bg-muted p-4 rounded-md text-xs overflow-auto">
                {`// List view pattern
export default function ContactListPage() {
  return (
    <ListLayout
      header={
        <div className="flex justify-between items-center">
          <PageTitle>Contacts</PageTitle>
          <Button asChild>
            <Link href="/contacts/create">Add Contact</Link>
          </Button>
        </div>
      }
      footer={<Pagination />}
    >
      <Table>
        <TableHeader>...</TableHeader>
        <TableBody>
          {contacts.map(contact => (
            <TableRow key={contact.id} asChild>
              <Link href={\`/contacts/\${contact.id}\`}>
                <TableCell>{contact.name}</TableCell>
                <TableCell>{contact.email}</TableCell>
              </Link>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </ListLayout>
  )
}`}
              </pre>

              <H3>Detail View Example</H3>
              <P>When creating a detail view, follow this pattern:</P>
              <pre className="bg-muted p-4 rounded-md text-xs overflow-auto">
                {`// Detail view pattern
export default function ContactDetailPage() {
  return (
    <DetailLayout
      title={contact.name}
      description="Contact details"
      tabs={[
        {
          value: "details",
          label: "Details",
          content: <ContactDetails contact={contact} />
        },
        {
          value: "relationships",
          label: "Relationships",
          content: <ContactRelationships contactId={contact.id} />
        }
      ]}
      footer={
        <div className="flex justify-between">
          <Button variant="outline" asChild>
            <Link href="/contacts">Back to List</Link>
          </Button>
          <div className="flex gap-2">
            <Button variant="outline" asChild>
              <Link href={\`/contacts/\${contact.id}/edit\`}>Edit</Link>
            </Button>
            <Button variant="destructive" asChild>
              <Link href={\`/contacts/\${contact.id}/remove\`}>Remove</Link>
            </Button>
          </div>
        </div>
      }
    />
  )
}`}
              </pre>

              <H3>Form Example</H3>
              <P>When creating a form, follow this pattern:</P>
              <pre className="bg-muted p-4 rounded-md text-xs overflow-auto">
                {`// Form pattern
"use client";

export default function ContactCreateForm() {
  const [state, formAction] = useActionState(createContact, {
    success: false,
    error: null
  });
  
  const pending = useFormStatus().pending;
  
  return (
    <FormLayout
      title="Create Contact"
      description="Add a new contact to the system"
      formAction={formAction}
      error={state.error}
      actions={
        <div className="flex justify-between w-full">
          <Button variant="outline" asChild>
            <Link href="/contacts">Cancel</Link>
          </Button>
          <Button type="submit" disabled={pending}>
            {pending ? "Creating..." : "Create"}
          </Button>
        </div>
      }
    >
      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="name">Name</Label>
          <Input id="name" name="name" required />
        </div>
        <div className="space-y-2">
          <Label htmlFor="email">Email</Label>
          <Input id="email" name="email" type="email" required />
        </div>
      </div>
    </FormLayout>
  )
}`}
              </pre>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
