import React from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import Link from "next/link";

interface DesignSystemPageProps {
  params: Promise<{
    lang: string;
  }>;
}

export default async function DesignSystemPage({ params }: DesignSystemPageProps) {
  const { lang } = await params;

  return (
    <div className="space-y-6">
      <h2 className="text-3xl font-semibold mb-4">Design System Overview</h2>

      <p className="text-muted-foreground mb-6">
        This section showcases the design system components, layouts, and patterns used throughout
        the application. Browse the different sections to see examples of each element.
      </p>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Typography</CardTitle>
            <CardDescription>Text styles and hierarchy</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="mb-4">
              Explore the typography system including heading hierarchy, body text, and text colors.
            </p>
            <Link
              href={`/${lang}/design-system/typography`}
              className="text-primary hover:underline"
            >
              View Typography →
            </Link>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Components</CardTitle>
            <CardDescription>Individual UI components</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="mb-4">
              View individual UI components like buttons, cards, and form elements.
            </p>
            <Link
              href={`/${lang}/design-system/components`}
              className="text-primary hover:underline"
            >
              View Components →
            </Link>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Layouts</CardTitle>
            <CardDescription>Page and content layouts</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="mb-4">
              Explore different layout patterns for pages, forms, lists, and detail views.
            </p>
            <Link href={`/${lang}/design-system/layouts`} className="text-primary hover:underline">
              View Layouts →
            </Link>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Patterns</CardTitle>
            <CardDescription>Common UI patterns</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="mb-4">
              See common UI patterns like CRUD operations, navigation, and error states.
            </p>
            <Link href={`/${lang}/design-system/patterns`} className="text-primary hover:underline">
              View Patterns →
            </Link>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Advanced Patterns</CardTitle>
            <CardDescription>Interactive UI patterns</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="mb-4">
              Explore advanced UI patterns like modals, drawers, step wizards, and secondary
              sidebars.
            </p>
            <Link href={`/${lang}/design-system/advanced`} className="text-primary hover:underline">
              View Advanced Patterns →
            </Link>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Additional Patterns</CardTitle>
            <CardDescription>Specialized UI patterns</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="mb-4">
              Explore specialized UI patterns like history timelines, document attachments, task
              progress, and notifications.
            </p>
            <Link
              href={`/${lang}/design-system/additional`}
              className="text-primary hover:underline"
            >
              View Additional Patterns →
            </Link>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
