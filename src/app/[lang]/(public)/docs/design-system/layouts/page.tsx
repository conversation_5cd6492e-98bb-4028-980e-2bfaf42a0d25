"use client";

import React from "react";
import { ContentLayout, DetailLayout, FormLayout, ListLayout } from "@/components/layouts";
import { Button } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

export default function LayoutsPage() {
  return (
    <div className="space-y-12">
      <h2 className="text-2xl font-semibold mb-4">Layout Components</h2>

      <section className="space-y-4">
        <h3 className="text-xl font-semibold">ContentLayout</h3>
        <div className="space-y-6">
          <ContentLayout
            title="Content Layout Example"
            description="This is a basic content layout with title, description, content, and footer."
            footer={<Button>Action Button</Button>}
          >
            <div className="p-4 border border-dashed rounded-md">
              <p>This is the main content area.</p>
              <p className="mt-2">You can put any content here.</p>
            </div>
          </ContentLayout>

          <Card>
            <CardHeader>
              <CardTitle>Usage</CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="bg-muted p-2 rounded-md text-xs overflow-auto">
                {`<ContentLayout
  title="Content Layout Example"
  description="This is a basic content layout with title, description, content, and footer."
  footer={<Button>Action Button</Button>}
>
  <div className="p-4 border border-dashed rounded-md">
    <p>This is the main content area.</p>
    <p className="mt-2">You can put any content here.</p>
  </div>
</ContentLayout>`}
              </pre>
            </CardContent>
          </Card>
        </div>
      </section>

      <section className="space-y-4">
        <h3 className="text-xl font-semibold">FormLayout</h3>
        <div className="space-y-6">
          <FormLayout
            title="Form Layout Example"
            description="This is a form layout with title, description, form fields, and actions."
            error="This is an example error message."
            actions={
              <div className="flex justify-between w-full">
                <Button variant="outline">Cancel</Button>
                <Button type="submit">Submit</Button>
              </div>
            }
          >
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Name</Label>
                <Input id="name" placeholder="Enter your name" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input id="email" type="email" placeholder="Enter your email" />
              </div>
            </div>
          </FormLayout>

          <Card>
            <CardHeader>
              <CardTitle>Usage</CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="bg-muted p-2 rounded-md text-xs overflow-auto">
                {`<FormLayout
  title="Form Layout Example"
  description="This is a form layout with title, description, form fields, and actions."
  error="This is an example error message."
  actions={
    <div className="flex justify-between w-full">
      <Button variant="outline">Cancel</Button>
      <Button type="submit">Submit</Button>
    </div>
  }
>
  <div className="space-y-4">
    <div className="space-y-2">
      <Label htmlFor="name">Name</Label>
      <Input id="name" placeholder="Enter your name" />
    </div>
    <div className="space-y-2">
      <Label htmlFor="email">Email</Label>
      <Input id="email" type="email" placeholder="Enter your email" />
    </div>
  </div>
</FormLayout>`}
              </pre>
            </CardContent>
          </Card>
        </div>
      </section>

      <section className="space-y-4">
        <h3 className="text-xl font-semibold">ListLayout</h3>
        <div className="space-y-6">
          <ListLayout
            header={
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-2xl font-semibold">Users</h2>
                <Button>Add User</Button>
              </div>
            }
            footer={
              <div className="flex justify-between items-center">
                <p className="text-sm text-muted-foreground">Showing 3 of 10 users</p>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm">
                    Previous
                  </Button>
                  <Button variant="outline" size="sm">
                    Next
                  </Button>
                </div>
              </div>
            }
          >
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow>
                  <TableCell>John Doe</TableCell>
                  <TableCell><EMAIL></TableCell>
                  <TableCell>Admin</TableCell>
                  <TableCell className="text-right">
                    <Button variant="outline" size="sm">
                      View
                    </Button>
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>Jane Smith</TableCell>
                  <TableCell><EMAIL></TableCell>
                  <TableCell>User</TableCell>
                  <TableCell className="text-right">
                    <Button variant="outline" size="sm">
                      View
                    </Button>
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>Bob Johnson</TableCell>
                  <TableCell><EMAIL></TableCell>
                  <TableCell>User</TableCell>
                  <TableCell className="text-right">
                    <Button variant="outline" size="sm">
                      View
                    </Button>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </ListLayout>

          <Card>
            <CardHeader>
              <CardTitle>Usage</CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="bg-muted p-2 rounded-md text-xs overflow-auto">
                {`<ListLayout
  header={
    <div className="flex justify-between items-center mb-4">
      <h2 className="text-2xl font-semibold">Users</h2>
      <Button>Add User</Button>
    </div>
  }
  footer={
    <div className="flex justify-between items-center">
      <p className="text-sm text-muted-foreground">Showing 3 of 10 users</p>
      <div className="flex gap-2">
        <Button variant="outline" size="sm">Previous</Button>
        <Button variant="outline" size="sm">Next</Button>
      </div>
    </div>
  }
>
  <Table>
    {/* Table content */}
  </Table>
</ListLayout>`}
              </pre>
            </CardContent>
          </Card>
        </div>
      </section>

      <section className="space-y-4">
        <h3 className="text-xl font-semibold">DetailLayout</h3>
        <div className="space-y-6">
          <DetailLayout
            title="User Details"
            description="View and manage user information"
            tabs={[
              {
                value: "details",
                label: "Details",
                content: (
                  <div className="space-y-4 py-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm font-medium">Name</p>
                        <p className="text-sm text-muted-foreground">John Doe</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Email</p>
                        <p className="text-sm text-muted-foreground"><EMAIL></p>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Role</p>
                        <p className="text-sm text-muted-foreground">Admin</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Status</p>
                        <p className="text-sm text-muted-foreground">Active</p>
                      </div>
                    </div>
                  </div>
                ),
              },
              {
                value: "history",
                label: "History",
                content: (
                  <div className="py-4">
                    <p>User activity history would go here.</p>
                  </div>
                ),
              },
            ]}
            footer={
              <div className="flex justify-between w-full">
                <Button variant="outline">Back</Button>
                <div className="flex gap-2">
                  <Button variant="outline">Edit</Button>
                  <Button variant="destructive">Delete</Button>
                </div>
              </div>
            }
          />

          <Card>
            <CardHeader>
              <CardTitle>Usage</CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="bg-muted p-2 rounded-md text-xs overflow-auto">
                {`<DetailLayout
  title="User Details"
  description="View and manage user information"
  tabs={[
    {
      value: "details",
      label: "Details",
      content: (
        <div className="space-y-4 py-4">
          {/* Details content */}
        </div>
      ),
    },
    {
      value: "history",
      label: "History",
      content: (
        <div className="py-4">
          {/* History content */}
        </div>
      ),
    },
  ]}
  footer={
    <div className="flex justify-between w-full">
      <Button variant="outline">Back</Button>
      <div className="flex gap-2">
        <Button variant="outline">Edit</Button>
        <Button variant="destructive">Delete</Button>
      </div>
    </div>
  }
/>`}
              </pre>
            </CardContent>
          </Card>
        </div>
      </section>
    </div>
  );
}
