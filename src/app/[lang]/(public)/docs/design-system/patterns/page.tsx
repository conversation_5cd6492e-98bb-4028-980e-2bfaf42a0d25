"use client";

import React from "react";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { PageHeader } from "@/components";
import { ErrorTemplate, LoadingTemplate, NotFoundTemplate } from "@/components/templates";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

export default function PatternsPage() {
  return (
    <div className="space-y-12">
      <h2 className="text-3xl font-semibold mb-4">UI Patterns</h2>

      <section className="space-y-4">
        <h3 className="text-xl font-semibold">CRUD Pattern</h3>
        <Card>
          <CardHeader>
            <CardTitle>CRUD Operations</CardTitle>
            <CardDescription>
              Standard patterns for Create, Read, Update, Delete operations
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="list">
              <TabsList>
                <TabsTrigger value="list">List</TabsTrigger>
                <TabsTrigger value="create">Create</TabsTrigger>
                <TabsTrigger value="view">View</TabsTrigger>
                <TabsTrigger value="edit">Edit</TabsTrigger>
                <TabsTrigger value="delete">Delete</TabsTrigger>
              </TabsList>

              <TabsContent value="list" className="pt-4">
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <h2 className="text-xl font-semibold">Users</h2>
                    <Button>Add User</Button>
                  </div>
                  <Card>
                    <CardContent className="p-0">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Name</TableHead>
                            <TableHead>Email</TableHead>
                            <TableHead>Role</TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          <TableRow>
                            <TableCell>John Doe</TableCell>
                            <TableCell><EMAIL></TableCell>
                            <TableCell>Admin</TableCell>
                            <TableCell className="text-right">
                              <Button variant="outline" size="sm">
                                View
                              </Button>
                            </TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell>Jane Smith</TableCell>
                            <TableCell><EMAIL></TableCell>
                            <TableCell>User</TableCell>
                            <TableCell className="text-right">
                              <Button variant="outline" size="sm">
                                View
                              </Button>
                            </TableCell>
                          </TableRow>
                        </TableBody>
                      </Table>
                    </CardContent>
                    <CardFooter className="flex justify-between">
                      <p className="text-sm text-muted-foreground">Showing 2 of 10 users</p>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          Previous
                        </Button>
                        <Button variant="outline" size="sm">
                          Next
                        </Button>
                      </div>
                    </CardFooter>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="create" className="pt-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Create User</CardTitle>
                    <CardDescription>Add a new user to the system</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p>Form fields would go here</p>
                  </CardContent>
                  <CardFooter className="flex justify-between">
                    <Button variant="outline">Cancel</Button>
                    <Button>Create</Button>
                  </CardFooter>
                </Card>
              </TabsContent>

              <TabsContent value="view" className="pt-4">
                <Card>
                  <CardHeader>
                    <CardTitle>User Details</CardTitle>
                    <CardDescription>View user information</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm font-medium">Name</p>
                        <p className="text-sm text-muted-foreground">John Doe</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Email</p>
                        <p className="text-sm text-muted-foreground"><EMAIL></p>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-between">
                    <Button variant="outline">Back</Button>
                    <div className="flex gap-2">
                      <Button variant="outline">Edit</Button>
                      <Button variant="destructive">Delete</Button>
                    </div>
                  </CardFooter>
                </Card>
              </TabsContent>

              <TabsContent value="edit" className="pt-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Edit User</CardTitle>
                    <CardDescription>Modify user information</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p>Form fields with existing values would go here</p>
                  </CardContent>
                  <CardFooter className="flex justify-between">
                    <Button variant="outline">Cancel</Button>
                    <Button>Save Changes</Button>
                  </CardFooter>
                </Card>
              </TabsContent>

              <TabsContent value="delete" className="pt-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Delete User</CardTitle>
                    <CardDescription>Are you sure you want to delete this user?</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p>
                      This action cannot be undone. The user will be permanently removed from the
                      system.
                    </p>
                  </CardContent>
                  <CardFooter className="flex justify-between">
                    <Button variant="outline">Cancel</Button>
                    <Button variant="destructive">Delete</Button>
                  </CardFooter>
                </Card>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </section>

      <section className="space-y-4">
        <h3 className="text-xl font-semibold">Error States</h3>
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Error Template</CardTitle>
              <CardDescription>Standard error display for error.tsx files</CardDescription>
            </CardHeader>
            <CardContent>
              <ErrorTemplate
                error={new Error("Example error")}
                reset={() => {}}
                title="Error Loading Data"
                message="There was a problem loading the requested data."
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Loading Template</CardTitle>
              <CardDescription>Standard loading display for loading.tsx files</CardDescription>
            </CardHeader>
            <CardContent>
              <LoadingTemplate title="Loading Data" type="table" itemCount={3} />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Not Found Template</CardTitle>
              <CardDescription>Standard not found display for not-found.tsx files</CardDescription>
            </CardHeader>
            <CardContent>
              <NotFoundTemplate
                title="Resource Not Found"
                description="The requested resource could not be found."
                backText="Back to Home"
              />
            </CardContent>
          </Card>
        </div>
      </section>

      <section className="space-y-4">
        <h3 className="text-xl font-semibold">Page Structure</h3>
        <Card>
          <CardContent className="pt-6">
            <div className="border border-dashed rounded-md p-6">
              <PageHeader
                title="Page Title"
                description="Page description goes here."
                actions={<Button>Action</Button>}
              />

              <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="md:col-span-2">
                  <Card>
                    <CardHeader>
                      <CardTitle>Main Content</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p>Primary content area</p>
                    </CardContent>
                  </Card>
                </div>

                <div>
                  <Card>
                    <CardHeader>
                      <CardTitle>Sidebar</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p>Secondary content or navigation</p>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <p className="text-sm text-muted-foreground">
              Standard page structure with main content and sidebar
            </p>
          </CardFooter>
        </Card>
      </section>
    </div>
  );
}
