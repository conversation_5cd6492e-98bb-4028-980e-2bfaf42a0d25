"use client";

import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { RichTextEditor, DocumentPreview } from "@/components/ui/rich-text-editor";
import { showNotification } from "@/components/ui/notification";

export default function RichTextEditorPage() {
  const [content, setContent] = useState<string>(`
    <h1>Sample Document</h1>
    <p>This is a sample document created with the rich text editor.</p>
    <h2>Features</h2>
    <ul>
      <li>Rich text formatting</li>
      <li>Image embedding</li>
      <li>Links</li>
      <li>Lists</li>
      <li>Tables</li>
      <li>Code blocks</li>
    </ul>
    <h2>Example Table</h2>
    <table>
      <thead>
        <tr>
          <th>Name</th>
          <th>Role</th>
          <th>Department</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>John Doe</td>
          <td>Manager</td>
          <td>Sales</td>
        </tr>
        <tr>
          <td>Jane Smith</td>
          <td>Developer</td>
          <td>Engineering</td>
        </tr>
        <tr>
          <td>Bob Johnson</td>
          <td>Designer</td>
          <td>Marketing</td>
        </tr>
      </tbody>
    </table>
    <h2>Code Example</h2>
    <pre><code>function greeting(name) {
  return \`Hello, \${name}!\`;
}

console.log(greeting('World'));</code></pre>
    <blockquote>
      <p>This is a blockquote. It can be used to highlight important information or quotes.</p>
    </blockquote>
  `);

  const handleContentChange = (html: string) => {
    setContent(html);
  };

  const handlePrint = () => {
    showNotification({
      title: "Print Document",
      description: "Printing document...",
      type: "info",
    });
    window.print();
  };

  const handleDownload = () => {
    showNotification({
      title: "Download Document",
      description: "Document downloaded successfully",
      type: "success",
    });

    // Create a Blob from the HTML content
    const blob = new Blob([content], { type: "text/html" });
    const url = URL.createObjectURL(blob);

    // Create a link and trigger download
    const a = document.createElement("a");
    a.href = url;
    a.download = "document.html";
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleShare = () => {
    showNotification({
      title: "Share Document",
      description: "Document sharing dialog opened",
      type: "info",
    });
  };

  return (
    <div className="space-y-12">
      <h2 className="text-3xl font-semibold mb-4">Rich Text Editor</h2>

      <section className="space-y-4">
        <h3 className="text-xl font-semibold">Rich Text Editor Component</h3>
        <Card>
          <CardHeader>
            <CardTitle>Rich Text Editor</CardTitle>
            <CardDescription>
              A rich text editor for creating and editing formatted content
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="editor" className="w-full">
              <TabsList className="mb-4">
                <TabsTrigger value="editor">Editor</TabsTrigger>
                <TabsTrigger value="preview">Preview</TabsTrigger>
                <TabsTrigger value="split">Split View</TabsTrigger>
              </TabsList>
              <TabsContent value="editor" className="w-full">
                <RichTextEditor
                  initialContent={content}
                  onChange={handleContentChange}
                  placeholder="Start writing..."
                  showToolbar
                  showBubbleMenu
                  showFloatingMenu
                  height="400px"
                />
              </TabsContent>
              <TabsContent value="preview">
                <DocumentPreview
                  content={content}
                  title="Sample Document"
                  description="Created with the rich text editor"
                  author="John Doe"
                  date={new Date()}
                  onPrint={handlePrint}
                  onDownload={handleDownload}
                  onShare={handleShare}
                />
              </TabsContent>
              <TabsContent value="split" className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium mb-2">Editor</h4>
                  <RichTextEditor
                    initialContent={content}
                    onChange={handleContentChange}
                    placeholder="Start writing..."
                    showToolbar
                    showBubbleMenu
                    showFloatingMenu
                    height="400px"
                  />
                </div>
                <div>
                  <h4 className="text-sm font-medium mb-2">Preview</h4>
                  <DocumentPreview
                    content={content}
                    title="Sample Document"
                    description="Created with the rich text editor"
                    author="John Doe"
                    date={new Date()}
                    showHeader={false}
                    showFooter={false}
                  />
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
          <CardFooter className="flex flex-col items-start">
            <p className="text-sm text-muted-foreground mb-2">Usage:</p>
            <pre className="bg-muted p-2 rounded-md text-xs w-full overflow-auto">
              {`<RichTextEditor
  initialContent="<p>Hello World!</p>"
  onChange={handleContentChange}
  placeholder="Start writing..."
  showToolbar
  showBubbleMenu
  height="300px"
/>

<DocumentPreview
  content={htmlContent}
  title="Project Report"
  description="Quarterly project status report"
  author="John Doe"
  date={new Date()}
  onPrint={handlePrint}
  onDownload={handleDownload}
/>`}
            </pre>
          </CardFooter>
        </Card>
      </section>

      <section className="space-y-4">
        <h3 className="text-xl font-semibold">Use Cases</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card>
            <CardHeader>
              <CardTitle>Report Writing</CardTitle>
              <CardDescription>Create detailed reports with rich formatting</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                The rich text editor is perfect for creating detailed reports with headings, lists,
                tables, and images. Social workers can document their observations and findings in a
                structured format.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Case Notes</CardTitle>
              <CardDescription>Document case notes with formatting</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Social workers can use the editor to take detailed notes during supervised visits,
                highlighting important observations and including structured information.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Templates</CardTitle>
              <CardDescription>Create and use report templates</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Administrators can create standardized templates for different types of reports,
                ensuring consistency across the organization.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Document Sharing</CardTitle>
              <CardDescription>Share and print formatted documents</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Documents created with the editor can be easily shared with other stakeholders,
                printed for physical records, or downloaded for offline use.
              </p>
            </CardContent>
          </Card>
        </div>
      </section>
    </div>
  );
}
