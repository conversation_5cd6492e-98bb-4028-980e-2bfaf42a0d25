"use client";

import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { SidebarLayout } from "@/components/layouts";
import { Modal } from "@/components/ui/modal";
import { Drawer } from "@/components/ui/drawer";
import { StepWizard } from "@/components/ui/step-wizard";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";

export default function AdvancedPage() {
  // State for modal
  const [isModalOpen, setIsModalOpen] = useState(false);

  // State for drawer
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  return (
    <div className="space-y-12">
      <h2 className="text-2xl font-semibold mb-4">Advanced UI Patterns</h2>

      <section className="space-y-4">
        <h3 className="text-xl font-semibold">Modal for Quick Actions</h3>
        <Card>
          <CardHeader>
            <CardTitle>Modal Example</CardTitle>
            <CardDescription>
              Use modals for quick interactions like adding a relationship or confirming a deletion
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={() => setIsModalOpen(true)}>Open Modal</Button>

            <Modal
              open={isModalOpen}
              onClose={() => setIsModalOpen(false)}
              title="Add Relationship"
              description="Add a new relationship to this contact"
              footer={
                <div className="flex justify-end gap-2 w-full">
                  <Button variant="outline" onClick={() => setIsModalOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={() => setIsModalOpen(false)}>Add</Button>
                </div>
              }
            >
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="contact">Related Contact</Label>
                  <Select>
                    <SelectTrigger id="contact">
                      <SelectValue placeholder="Select a contact" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="john">John Doe</SelectItem>
                      <SelectItem value="jane">Jane Smith</SelectItem>
                      <SelectItem value="bob">Bob Johnson</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="relationship">Relationship Type</Label>
                  <Select>
                    <SelectTrigger id="relationship">
                      <SelectValue placeholder="Select relationship type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="parent">Parent</SelectItem>
                      <SelectItem value="child">Child</SelectItem>
                      <SelectItem value="sibling">Sibling</SelectItem>
                      <SelectItem value="spouse">Spouse</SelectItem>
                      <SelectItem value="lawyer">Lawyer</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </Modal>
          </CardContent>
          <CardFooter className="flex flex-col items-start">
            <p className="text-sm text-muted-foreground mb-2">Usage:</p>
            <pre className="bg-muted p-2 rounded-md text-xs w-full overflow-auto">
              {`<Modal
  open={isModalOpen}
  onClose={() => setIsModalOpen(false)}
  title="Add Relationship"
  description="Add a new relationship to this contact"
  footer={
    <div className="flex justify-end gap-2 w-full">
      <Button variant="outline" onClick={() => setIsModalOpen(false)}>Cancel</Button>
      <Button onClick={() => setIsModalOpen(false)}>Add</Button>
    </div>
  }
>
  <div className="space-y-4">
    {/* Form content */}
  </div>
</Modal>`}
            </pre>
          </CardFooter>
        </Card>
      </section>

      <section className="space-y-4">
        <h3 className="text-xl font-semibold">Drawer for Quick Information</h3>
        <Card>
          <CardHeader>
            <CardTitle>Drawer Example</CardTitle>
            <CardDescription>
              Use drawers for showing supplementary information without leaving the current context
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={() => setIsDrawerOpen(true)}>Open Drawer</Button>

            <Drawer open={isDrawerOpen} onClose={() => setIsDrawerOpen(false)} direction="right">
              <div className="p-4 space-y-4">
                <div className="flex items-center gap-4">
                  <Avatar>
                    <AvatarFallback>JD</AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="text-lg font-semibold">John Doe</h3>
                    <p className="text-sm text-muted-foreground">Family Member</p>
                  </div>
                </div>

                <div className="space-y-2">
                  <p className="text-sm font-medium">Email</p>
                  <p className="text-sm text-muted-foreground"><EMAIL></p>
                </div>

                <div className="space-y-2">
                  <p className="text-sm font-medium">Phone</p>
                  <p className="text-sm text-muted-foreground">(*************</p>
                </div>

                <div className="mt-6">
                  <Button className="w-full">View Full Details</Button>
                </div>
              </div>
            </Drawer>
          </CardContent>
          <CardFooter className="flex flex-col items-start">
            <p className="text-sm text-muted-foreground mb-2">Usage:</p>
            <pre className="bg-muted p-2 rounded-md text-xs w-full overflow-auto">
              {`<Drawer
  open={isDrawerOpen}
  onClose={() => setIsDrawerOpen(false)}
  direction="right"
  title="Contact Preview"
>
  <div className="p-4 space-y-4">
    {/* Drawer content */}
  </div>
</Drawer>`}
            </pre>
          </CardFooter>
        </Card>
      </section>

      <section className="space-y-4">
        <h3 className="text-xl font-semibold">Secondary Sidebar for Calendar/Scheduling</h3>
        <Card>
          <CardHeader>
            <CardTitle>Secondary Sidebar Example</CardTitle>
            <CardDescription>
              Use a secondary sidebar for scheduling assistants with calendars
            </CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            <div className="border rounded-md overflow-hidden" style={{ height: "500px" }}>
              <SidebarLayout
                sidebar={
                  <div className="p-4 space-y-4">
                    <div className="sticky top-0 bg-background p-2 rounded-md mb-4">
                      <h3 className="text-lg font-semibold">Calendar</h3>
                    </div>

                    <Card>
                      <CardContent className="p-2">
                        <Calendar mode="single" />
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader className="p-3">
                        <CardTitle className="text-sm">Available Slots</CardTitle>
                      </CardHeader>
                      <CardContent className="p-3 pt-0">
                        <div className="space-y-2">
                          <Button variant="outline" size="sm" className="w-full justify-start">
                            9:00 AM - 10:00 AM
                          </Button>
                          <Button variant="outline" size="sm" className="w-full justify-start">
                            10:30 AM - 11:30 AM
                          </Button>
                          <Button variant="outline" size="sm" className="w-full justify-start">
                            1:00 PM - 2:00 PM
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                }
                sidebarPosition="right"
                sidebarWidth="320px"
              >
                <div className="p-6">
                  <h2 className="text-2xl font-semibold mb-4">Schedule Visit</h2>
                  <Card>
                    <CardHeader>
                      <CardTitle>Visit Details</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor="title">Title</Label>
                          <Input id="title" placeholder="Enter visit title" />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="description">Description</Label>
                          <Input id="description" placeholder="Enter visit description" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </SidebarLayout>
            </div>
          </CardContent>
          <CardFooter className="flex flex-col items-start">
            <p className="text-sm text-muted-foreground mb-2">Usage:</p>
            <pre className="bg-muted p-2 rounded-md text-xs w-full overflow-auto">
              {`<SidebarLayout
  sidebar={
    <div className="p-4 space-y-4">
      {/* Calendar and time slots */}
    </div>
  }
  sidebarPosition="right"
  sidebarWidth="320px"
>
  <div className="p-6">
    {/* Main content */}
  </div>
</SidebarLayout>`}
            </pre>
          </CardFooter>
        </Card>
      </section>

      <section className="space-y-4">
        <h3 className="text-xl font-semibold">Step Wizard for Multi-Step Forms</h3>
        <Card>
          <CardHeader>
            <CardTitle>Step Wizard Example</CardTitle>
            <CardDescription>
              Use step wizards for breaking complex forms into manageable steps
            </CardDescription>
          </CardHeader>
          <CardContent>
            <StepWizard
              title="Create New Employee"
              description="Complete the following steps to create a new employee"
              steps={[
                { label: "Basic Info", completed: true },
                { label: "Contact", completed: false },
                { label: "Role", completed: false },
              ]}
              currentStep={1}
              previousUrl="#"
              nextUrl="#"
            >
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Contact Details</h3>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input id="email" type="email" placeholder="Enter email address" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone</Label>
                    <Input id="phone" placeholder="Enter phone number" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="address">Address</Label>
                    <Input id="address" placeholder="Enter address" />
                  </div>
                </div>
              </div>
            </StepWizard>
          </CardContent>
          <CardFooter className="flex flex-col items-start">
            <p className="text-sm text-muted-foreground mb-2">Usage:</p>
            <pre className="bg-muted p-2 rounded-md text-xs w-full overflow-auto">
              {`<StepWizard
  title="Create New Employee"
  description="Complete the following steps to create a new employee"
  steps={[
    { label: "Basic Info", completed: true },
    { label: "Contact", completed: false },
    { label: "Role", completed: false },
  ]}
  currentStep={1}
  previousUrl="/employees/create/basic-info"
  nextUrl="/employees/create/role"
>
  <div className="space-y-4">
    {/* Step content */}
  </div>
</StepWizard>`}
            </pre>
            <div className="mt-4 p-4 bg-muted/50 rounded-md w-full">
              <h4 className="text-sm font-semibold mb-2">Server Component Implementation</h4>
              <p className="text-sm text-muted-foreground mb-2">
                This StepWizard component is designed to work with Next.js routing. Each step is a
                separate page, and navigation between steps is handled by links rather than
                client-side state.
              </p>
              <p className="text-sm text-muted-foreground">
                For example, you might have routes like:
              </p>
              <ul className="text-sm text-muted-foreground list-disc list-inside mt-2">
                <li>/employees/create/basic-info</li>
                <li>/employees/create/contact</li>
                <li>/employees/create/role</li>
              </ul>
            </div>
          </CardFooter>
        </Card>
      </section>
    </div>
  );
}
