"use client";

import React, { useState } from "react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  Card<PERSON>oot<PERSON>,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { AvailabilityGrid, TimeSlot } from "@/components/ui/scheduling/availability-grid";
import { ScheduleCalendar, ScheduleEvent } from "@/components/ui/scheduling/schedule-calendar";
import {
  ResourceScheduler,
  Resource,
  ResourceEvent,
} from "@/components/ui/scheduling/resource-scheduler-fixed";
import { showNotification } from "@/components/ui/notification";
import { addDays, addHours, setHours, startOfDay } from "date-fns";

export default function SchedulingPage() {
  // Sample data for availability grid
  const [availabilitySlots, setAvailabilitySlots] = useState<TimeSlot[]>([
    { day: 1, hour: 9, available: true },
    { day: 1, hour: 10, available: true },
    { day: 1, hour: 11, available: true },
    { day: 2, hour: 14, available: true },
    { day: 2, hour: 15, available: true },
    { day: 3, hour: 9, available: true },
    { day: 3, hour: 10, available: true },
    { day: 4, hour: 13, available: true },
    { day: 4, hour: 14, available: true },
    { day: 5, hour: 10, available: true },
    { day: 5, hour: 11, available: true },
  ]);

  // Sample data for schedule calendar
  const today = new Date();
  const [calendarEvents, setCalendarEvents] = useState<ScheduleEvent[]>([
    {
      id: "event1",
      title: "Team Meeting",
      start: setHours(today, 10),
      end: setHours(today, 11),
      type: "meeting",
    },
    {
      id: "event2",
      title: "Client Call",
      start: setHours(addDays(today, 1), 14),
      end: setHours(addDays(today, 1), 15),
      type: "appointment",
    },
    {
      id: "event3",
      title: "Project Review",
      start: setHours(addDays(today, 2), 11),
      end: setHours(addDays(today, 2), 12),
      type: "meeting",
    },
  ]);

  // Sample data for resource scheduler
  const resources: Resource[] = [
    { id: "room1", name: "Conference Room A", color: "bg-blue-500" },
    { id: "room2", name: "Conference Room B", color: "bg-green-500" },
    { id: "room3", name: "Meeting Room 1", color: "bg-yellow-500" },
    { id: "person1", name: "John Doe", type: "person", color: "bg-purple-500" },
    { id: "person2", name: "Jane Smith", type: "person", color: "bg-pink-500" },
  ];

  const [resourceEvents, setResourceEvents] = useState<ResourceEvent[]>([
    {
      id: "res-event1",
      title: "Team Meeting",
      start: setHours(today, 10),
      end: setHours(today, 11),
      resourceId: "room1",
      color: "bg-blue-500",
    },
    {
      id: "res-event2",
      title: "Client Call",
      start: setHours(today, 14),
      end: setHours(today, 15),
      resourceId: "person1",
      color: "bg-purple-500",
    },
    {
      id: "res-event3",
      title: "Project Review",
      start: setHours(addDays(today, 1), 11),
      end: setHours(addDays(today, 1), 12),
      resourceId: "room2",
      color: "bg-green-500",
    },
  ]);

  // Handlers
  const handleTimeSlotToggle = (timeSlot: TimeSlot) => {
    setAvailabilitySlots((prev) => {
      const index = prev.findIndex(
        (slot) => slot.day === timeSlot.day && slot.hour === timeSlot.hour
      );

      if (index !== -1) {
        const newSlots = [...prev];
        newSlots[index] = timeSlot;
        return newSlots;
      } else {
        return [...prev, timeSlot];
      }
    });

    showNotification({
      title: "Availability Updated",
      description: `${timeSlot.available ? "Added" : "Removed"} availability for ${getDayName(timeSlot.day)} at ${timeSlot.hour}:00`,
      type: "success",
    });
  };

  const handleEventClick = (event: ScheduleEvent) => {
    showNotification({
      title: "Event Selected",
      description: `Selected event: ${event.title}`,
      type: "info",
    });
  };

  const handleDateClick = (date: Date) => {
    showNotification({
      title: "Date Selected",
      description: `Selected date: ${date.toLocaleDateString()}`,
      type: "info",
    });
  };

  const handleResourceEventClick = (event: ResourceEvent) => {
    showNotification({
      title: "Resource Event Selected",
      description: `Selected event: ${event.title} (${resources.find((r) => r.id === event.resourceId)?.name})`,
      type: "info",
    });
  };

  const handleTimeSlotClick = (resourceId: string, time: Date) => {
    showNotification({
      title: "Time Slot Selected",
      description: `Selected time slot: ${time.toLocaleTimeString()} for ${resources.find((r) => r.id === resourceId)?.name}`,
      type: "info",
    });
  };

  // Helper functions
  const getDayName = (day: number) => {
    const days = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"];
    return days[day];
  };

  return (
    <div className="space-y-12">
      <h2 className="text-2xl font-semibold mb-4">Scheduling & Availability</h2>

      <section className="space-y-4">
        <h3 className="text-xl font-semibold">Availability Grid</h3>
        <Card>
          <CardHeader>
            <CardTitle>Availability Grid Component</CardTitle>
            <CardDescription>Grid for selecting available time slots</CardDescription>
          </CardHeader>
          <CardContent>
            <AvailabilityGrid
              timeSlots={availabilitySlots}
              onTimeSlotToggle={handleTimeSlotToggle}
              startHour={9}
              endHour={17}
              title="Weekly Availability"
              description="Select your available time slots"
              onSave={() => {
                showNotification({
                  title: "Availability Saved",
                  description: "Your availability has been saved successfully",
                  type: "success",
                });
              }}
              onReset={() => {
                setAvailabilitySlots([]);
                showNotification({
                  title: "Availability Reset",
                  description: "Your availability has been reset",
                  type: "info",
                });
              }}
            />
          </CardContent>
          <CardFooter className="flex flex-col items-start">
            <p className="text-sm text-muted-foreground mb-2">Usage:</p>
            <pre className="bg-muted p-2 rounded-md text-xs w-full overflow-auto">
              {`<AvailabilityGrid
  timeSlots={timeSlots}
  onTimeSlotToggle={handleTimeSlotToggle}
  startHour={9}
  endHour={17}
  title="Weekly Availability"
  description="Select your available time slots"
  onSave={handleSave}
  onReset={handleReset}
/>`}
            </pre>
          </CardFooter>
        </Card>
      </section>

      <section className="space-y-4">
        <h3 className="text-xl font-semibold">Schedule Calendar</h3>
        <Card>
          <CardHeader>
            <CardTitle>Schedule Calendar Component</CardTitle>
            <CardDescription>Calendar for displaying and managing events</CardDescription>
          </CardHeader>
          <CardContent>
            <ScheduleCalendar
              events={calendarEvents}
              onEventClick={handleEventClick}
              onDateClick={handleDateClick}
              title="My Schedule"
              description="View and manage your schedule"
              onAddClick={() => {
                showNotification({
                  title: "Add Event",
                  description: "Opening event creation form",
                  type: "info",
                });
              }}
            />
          </CardContent>
          <CardFooter className="flex flex-col items-start">
            <p className="text-sm text-muted-foreground mb-2">Usage:</p>
            <pre className="bg-muted p-2 rounded-md text-xs w-full overflow-auto">
              {`<ScheduleCalendar
  events={events}
  onEventClick={handleEventClick}
  onDateClick={handleDateClick}
  title="My Schedule"
  description="View and manage your schedule"
  onAddClick={handleAddEvent}
/>`}
            </pre>
          </CardFooter>
        </Card>
      </section>

      <section className="space-y-4">
        <h3 className="text-xl font-semibold">Resource Scheduler</h3>
        <Card>
          <CardHeader>
            <CardTitle>Resource Scheduler Component</CardTitle>
            <CardDescription>Scheduler for managing multiple resources</CardDescription>
          </CardHeader>
          <CardContent>
            <ResourceScheduler
              resources={resources}
              events={resourceEvents}
              onEventClick={handleResourceEventClick}
              onTimeSlotClick={handleTimeSlotClick}
              title="Resource Scheduler"
              description="Schedule rooms and people"
              onAddClick={() => {
                showNotification({
                  title: "Add Resource Event",
                  description: "Opening resource event creation form",
                  type: "info",
                });
              }}
              startHour={9}
              endHour={17}
              interval={60}
            />
          </CardContent>
          <CardFooter className="flex flex-col items-start">
            <p className="text-sm text-muted-foreground mb-2">Usage:</p>
            <pre className="bg-muted p-2 rounded-md text-xs w-full overflow-auto">
              {`<ResourceScheduler
  resources={resources}
  events={events}
  onEventClick={handleEventClick}
  onTimeSlotClick={handleTimeSlotClick}
  title="Resource Scheduler"
  description="Schedule rooms and people"
  onAddClick={handleAddEvent}
  startHour={9}
  endHour={17}
  interval={60}
/>`}
            </pre>
          </CardFooter>
        </Card>
      </section>
    </div>
  );
}
