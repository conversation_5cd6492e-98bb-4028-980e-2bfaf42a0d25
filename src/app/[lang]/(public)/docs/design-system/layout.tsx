import React from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { PageTitle } from "@/components/typography";

interface DesignSystemLayoutProps {
  children: React.ReactNode;
  params: Promise<{
    lang: string;
  }>;
}

export default async function DesignSystemLayout({ children, params }: DesignSystemLayoutProps) {
  const { lang } = await params;

  return (
    <div className="container mx-auto py-8">
      <div className="flex flex-col space-y-6">
        <div>
          <PageTitle description="A showcase of the design system components and patterns">
            Design System
          </PageTitle>
        </div>

        <div className="flex flex-wrap gap-2">
          <Button asChild variant="outline">
            <Link href={`/${lang}/docs/design-system`}>Overview</Link>
          </Button>
          <Button
            asChild
            variant="outline"
            className="bg-primary text-primary-foreground hover:bg-primary/90"
          >
            <Link href={`/${lang}/docs/design-system/ai-guide`}>AI Guide</Link>
          </Button>
          <Button asChild variant="outline">
            <Link href={`/${lang}/docs/design-system/typography`}>Typography</Link>
          </Button>
          <Button asChild variant="outline">
            <Link href={`/${lang}/docs/design-system/components`}>Components</Link>
          </Button>
          <Button asChild variant="outline">
            <Link href={`/${lang}/docs/design-system/layouts`}>Layouts</Link>
          </Button>
          <Button asChild variant="outline">
            <Link href={`/${lang}/docs/design-system/patterns`}>Patterns</Link>
          </Button>
          <Button asChild variant="outline">
            <Link href={`/${lang}/docs/design-system/colors-and-animations`}>
              Colors & Animations
            </Link>
          </Button>
          <Button asChild variant="outline">
            <Link href={`/${lang}/docs/design-system/advanced`}>Advanced</Link>
          </Button>
          <Button asChild variant="outline">
            <Link href={`/${lang}/docs/design-system/additional`}>Additional</Link>
          </Button>
          <Button asChild variant="outline">
            <Link href={`/${lang}/docs/design-system/app-shell`}>App Shell</Link>
          </Button>
          <Button asChild variant="outline">
            <Link href={`/${lang}/docs/design-system/scheduling`}>Scheduling</Link>
          </Button>
          <Button asChild variant="outline">
            <Link href={`/${lang}/docs/design-system/rich-text-editor`}>Rich Text Editor</Link>
          </Button>
        </div>

        <div className="border rounded-lg p-6 bg-card">{children}</div>
      </div>
    </div>
  );
}
