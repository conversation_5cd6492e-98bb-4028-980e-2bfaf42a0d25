import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { H1, H2, H3, Muted } from "@/components/typography";
import { Skeleton } from "@/components/ui/skeleton";

/**
 * Loading state for the RequestStatusBadge example page
 */
export default function RequestStatusBadgeLoading() {
  return (
    <div className="container py-10">
      <div className="max-w-5xl mx-auto">
        <H1>Request Status Badge</H1>
        <Muted className="text-lg mt-2 mb-8">
          A component for displaying request status with appropriate colors and icons
        </Muted>

        <Separator className="my-6" />

        {/* Basic usage */}
        <section className="mb-10">
          <H2 className="mb-4">Basic Usage</H2>
          <Card>
            <CardHeader>
              <CardTitle>Default Status Badges</CardTitle>
              <CardDescription>
                The RequestStatusBadge component displays a badge with the request status, using
                appropriate colors and icons.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-4">
                {[1, 2, 3, 4, 5, 6].map((i) => (
                  <Skeleton key={i} className="h-6 w-24" />
                ))}
              </div>
            </CardContent>
          </Card>
        </section>

        {/* Size variants */}
        <section className="mb-10">
          <H2 className="mb-4">Size Variants</H2>
          <Card>
            <CardHeader>
              <CardTitle>Different Sizes</CardTitle>
              <CardDescription>
                The RequestStatusBadge component supports different sizes: sm, md (default), and lg.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <H3 className="mb-2">Small (sm)</H3>
                  <div className="flex flex-wrap gap-4">
                    {[1, 2, 3, 4, 5, 6].map((i) => (
                      <Skeleton key={i} className="h-5 w-20" />
                    ))}
                  </div>
                </div>

                <div>
                  <H3 className="mb-2">Medium (md) - Default</H3>
                  <div className="flex flex-wrap gap-4">
                    {[1, 2, 3, 4, 5, 6].map((i) => (
                      <Skeleton key={i} className="h-6 w-24" />
                    ))}
                  </div>
                </div>

                <div>
                  <H3 className="mb-2">Large (lg)</H3>
                  <div className="flex flex-wrap gap-4">
                    {[1, 2, 3, 4, 5, 6].map((i) => (
                      <Skeleton key={i} className="h-7 w-28" />
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </section>

        {/* Without icons */}
        <section className="mb-10">
          <H2 className="mb-4">Without Icons</H2>
          <Card>
            <CardHeader>
              <CardTitle>Text-Only Badges</CardTitle>
              <CardDescription>
                The RequestStatusBadge component can be displayed without icons by setting the
                showIcon prop to false.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-4">
                {[1, 2, 3, 4, 5, 6].map((i) => (
                  <Skeleton key={i} className="h-6 w-20" />
                ))}
              </div>
            </CardContent>
          </Card>
        </section>

        {/* Usage example */}
        <section className="mb-10">
          <H2 className="mb-4">Usage Example</H2>
          <Card>
            <CardHeader>
              <CardTitle>In a Request List</CardTitle>
              <CardDescription>
                Example of how the RequestStatusBadge component can be used in a request list.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[1, 2, 3, 4, 5, 6].map((i) => (
                  <div key={i} className="flex items-center justify-between p-4 border rounded-md">
                    <div>
                      <Skeleton className="h-5 w-32 mb-2" />
                      <Skeleton className="h-4 w-40" />
                    </div>
                    <Skeleton className="h-6 w-24" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </section>
      </div>
    </div>
  );
}
