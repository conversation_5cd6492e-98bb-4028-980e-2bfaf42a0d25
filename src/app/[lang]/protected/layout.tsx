import { AppShell, Sidebar, Navbar, <PERSON><PERSON>cNavigationMenu } from "@/components/ui/app-shell";
import { ThemeToggle } from "@/components/theme-toggle";
import { LanguageToggle } from "@/components/language-toggle";
import { Separator } from "@/components/ui/separator";
import { UserMenu } from "./components/user-menu";
import { PERMISSIONS } from "@/lib/authorization/config/permissions";
import { NotificationButton } from "./components/notification-button";
import { NotificationProvider } from "@/lib/notifications/NotificationProvider";
import { getDictionary } from "@/lib/i18n/cache";

// Navigation items with required permissions
const navItems = [
  {
    title: "contacts",
    href: "/contact/management/list",
    icon: "Contact",
    permission: PERMISSIONS.CONTACT.MANAGEMENT.LIST,
  },
  {
    title: "requests",
    href: "/request/list",
    icon: "ClipboardList",
    permission: PERMISSIONS.REQUEST.MANAGEMENT.VIEW,
  },
  {
    title: "employeeManage",
    href: "/employee/management/list",
    icon: "CoffeeIcon",
    permission: PERMISSIONS.EMPLOYEE.MANAGEMENT.LIST,
  },
  {
    title: "employeeAvailability",
    href: "/employee/availability/list",
    icon: "Calendar",
    permission: PERMISSIONS.EMPLOYEE.AVAILABILITY.VIEW,
  },
  {
    title: "organizationAdmin",
    href: "/organization/system-admin/list",
    icon: "Building",
    permission: PERMISSIONS.ORGANIZATION.SYSTEM_ADMIN.LIST,
  },
  {
    title: "userManage",
    href: "/user/management/list",
    icon: "Users",
    permission: PERMISSIONS.USER.MANAGEMENT.LIST,
  },
  {
    title: "organizationProfile",
    href: "/organization/profile",
    icon: "Settings",
    permission: PERMISSIONS.ORGANIZATION.PROFILE.MANAGE,
  },
];

export default async function ProtectedLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ lang: string }>;
}) {
  // Get the language from params for the language toggle
  const { lang } = await params;

  // Use the cached dictionary that automatically determines the best language
  const dictionary = await getDictionary();

  // Use the current URL pathname instead of headers
  const pathname = "/protected";

  // Create the sidebar content with RBAC navigation
  const sidebarContent = (
    <Sidebar title="Si Simple">
      <RbacNavigationMenu
        items={navItems}
        pathname={pathname}
        lang={lang}
        dictionary={dictionary.navigation}
      />
    </Sidebar>
  );

  // Create the navbar content
  const navbarLeft = (
    <div className="lg:hidden">
      <h2 className="text-xl font-bold ml-10">"Si Simple</h2>
    </div>
  );

  const navbarRight = (
    <div className="flex items-center gap-4">
      <NotificationButton title={dictionary.common?.notifications || "Notifications"} />
      <LanguageToggle
        currentLang={lang}
        labels={{
          en: dictionary.common?.english || "English",
          fr: dictionary.common?.french || "Français",
        }}
      />
      <ThemeToggle />
      <Separator orientation="vertical" className="h-8" />
      <UserMenu
        lang={lang}
        dictionary={{
          profile: dictionary.common?.profile,
          account: dictionary.common?.account,
          signOut: dictionary.common?.signOut,
          settings: dictionary.common?.settings,
          notifications: dictionary.common?.notifications,
        }}
      />
    </div>
  );

  return (
    <NotificationProvider>
      <AppShell sidebar={sidebarContent} navbar={<Navbar left={navbarLeft} right={navbarRight} />}>
        <div className="px-32">{children}</div>
      </AppShell>
    </NotificationProvider>
  );
}
