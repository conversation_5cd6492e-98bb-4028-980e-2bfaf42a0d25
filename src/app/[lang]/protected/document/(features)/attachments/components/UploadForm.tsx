"use client";

import React, { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { FileUploader, FileUploadItem } from "@/components/ui/file-uploader";
// REMOVED: uploadAttachment import - Using API route instead
import { useRouter } from "next/navigation";
import { toast } from "sonner";

interface UploadFormProps {
  lang: string;
  entityType?: string;
  entityId?: string;
  returnUrl?: string;
}

export function UploadForm({ lang, entityType = "", entityId = "", returnUrl }: UploadFormProps) {
  const router = useRouter();
  const [files, setFiles] = useState<File[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [formData, setFormData] = useState({
    attached_to_type: entityType,
    attached_to_id: entityId,
    category: "",
    tags: "",
    description: "",
  });

  const handleFilesSelected = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleUpload = async () => {
    if (files.length === 0) {
      toast.error("Please select at least one file");
      return;
    }

    if (!formData.attached_to_type || !formData.attached_to_id) {
      toast.error("Please specify entity type and ID");
      return;
    }

    setIsUploading(true);

    try {
      const uploadFormData = new FormData();

      // Add files
      files.forEach((file) => {
        uploadFormData.append("files", file);
      });

      // Add metadata
      uploadFormData.append("attached_to_type", formData.attached_to_type);
      uploadFormData.append("attached_to_id", formData.attached_to_id);
      uploadFormData.append("category", formData.category);
      uploadFormData.append("tags", formData.tags);
      uploadFormData.append("description", formData.description);

      const result = await uploadAttachment(uploadFormData);

      if (result.success) {
        toast.success("Files uploaded successfully");

        // Redirect back or to attachments page
        const redirectUrl = returnUrl || `/${lang}/protected/document/attachments`;
        router.push(redirectUrl);
      } else {
        toast.error(result.error || "Upload failed");
      }
    } catch (error) {
      console.error("Upload error:", error);
      toast.error("Upload failed due to an unexpected error");
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Main Upload Area */}
      <div className="lg:col-span-2">
        <Card>
          <CardHeader>
            <CardTitle>Select Files</CardTitle>
            <CardDescription>Drag and drop files here or click to browse</CardDescription>
          </CardHeader>
          <CardContent>
            <FileUploader
              onFilesSelected={handleFilesSelected}
              maxFiles={10}
              maxFileSize={50 * 1024 * 1024} // 50MB
              allowedTypes={[
                "application/pdf",
                "application/msword",
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                "image/jpeg",
                "image/png",
                "image/gif",
                "text/plain",
              ]}
              multiple={true}
              disabled={isUploading}
            />
          </CardContent>
        </Card>
      </div>

      {/* Sidebar - Entity Selection & Metadata */}
      <div className="space-y-6">
        {/* Entity Selection */}
        <Card>
          <CardHeader>
            <CardTitle>Attach To</CardTitle>
            <CardDescription>Select the entity to attach documents to</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="entity-type">Entity Type</Label>
              <Select
                value={formData.attached_to_type}
                onValueChange={(value) => handleInputChange("attached_to_type", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select entity type..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="case_file">Case File</SelectItem>
                  <SelectItem value="contact">Contact</SelectItem>
                  <SelectItem value="appointment">Appointment</SelectItem>
                  <SelectItem value="request">Request</SelectItem>
                  <SelectItem value="observation">Observation</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="entity-id">Entity ID</Label>
              <Input
                id="entity-id"
                placeholder="Enter entity ID..."
                value={formData.attached_to_id}
                onChange={(e) => handleInputChange("attached_to_id", e.target.value)}
              />
            </div>
          </CardContent>
        </Card>

        {/* Document Metadata */}
        <Card>
          <CardHeader>
            <CardTitle>Document Details</CardTitle>
            <CardDescription>Add metadata for better organization</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="category">Category</Label>
              <Select
                value={formData.category}
                onValueChange={(value) => handleInputChange("category", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select category..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="legal">Legal Documents</SelectItem>
                  <SelectItem value="medical">Medical Records</SelectItem>
                  <SelectItem value="identification">Identification</SelectItem>
                  <SelectItem value="correspondence">Correspondence</SelectItem>
                  <SelectItem value="assessment">Assessment Reports</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="tags">Tags</Label>
              <Input
                id="tags"
                placeholder="Enter tags separated by commas..."
                value={formData.tags}
                onChange={(e) => handleInputChange("tags", e.target.value)}
              />
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                placeholder="Optional description..."
                rows={3}
                value={formData.description}
                onChange={(e) => handleInputChange("description", e.target.value)}
              />
            </div>
          </CardContent>
        </Card>

        {/* Upload Actions */}
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-3">
              <Button
                className="w-full"
                onClick={handleUpload}
                disabled={isUploading || files.length === 0}
              >
                {isUploading ? "Uploading..." : "Upload Documents"}
              </Button>
              <Button
                variant="outline"
                className="w-full"
                onClick={() => router.back()}
                disabled={isUploading}
              >
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
