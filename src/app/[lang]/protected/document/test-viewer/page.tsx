"use client";

import { DocumentViewer } from "../attachments/[id]/components/DocumentViewer";
import { Card, CardContent } from "@/components/ui/card";
import { PageHeader } from "@/components/ui/page-header";
import { Button } from "@/components/ui/button";
import { FileText, Calendar, User, Hash, Download, Eye } from "lucide-react";

export default function TestViewerPage() {
  const mockDictionary = {
    documents: {
      detail: {
        previewNotAvailable: "Preview Not Available",
        download: "Download",
        openInNewTab: "Open in New Tab",
      },
    },
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Example of PageHeader Component Usage */}
      <PageHeader
        title="Sample Document.pdf"
        metadata={[
          {
            icon: Hash,
            label: "",
            value: "PDF",
          },
          {
            icon: FileText,
            label: "",
            value: "2.4 MB",
          },
          {
            icon: Calendar,
            label: "",
            value: "Dec 15, 2024",
          },
          {
            icon: User,
            label: "",
            value: "<PERSON>",
          },
        ]}
        actions={
          <>
            <span className="hidden sm:block">
              <Button variant="outline">
                <Eye className="mr-1.5 -ml-0.5 h-5 w-5" />
                View Related
              </Button>
            </span>
            <span className="ml-3">
              <Button>
                <Download className="mr-1.5 -ml-0.5 h-5 w-5" />
                Download
              </Button>
            </span>
          </>
        }
      />

      {/* Document Viewer Test */}
      <Card>
        <CardContent className="p-4">
          <div className="border rounded-lg aspect-[4/3] overflow-hidden">
            <DocumentViewer
              documentId="test"
              documentName="Sample PDF"
              documentType="pdf"
              dictionary={mockDictionary}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
