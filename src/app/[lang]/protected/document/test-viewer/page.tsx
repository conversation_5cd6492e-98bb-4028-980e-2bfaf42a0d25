"use client";

import { DocumentViewer } from "../attachments/[id]/components/DocumentViewer";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function TestViewerPage() {
  const mockDictionary = {
    documents: {
      detail: {
        previewNotAvailable: "Preview Not Available",
        download: "Download",
        openInNewTab: "Open in New Tab",
      },
    },
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Document Viewer Test</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="mb-4">
            This page tests the DocumentViewer component with a sample PDF from the web.
          </p>
          
          <div className="border rounded-lg aspect-[4/3] overflow-hidden">
            <DocumentViewer
              documentId="test"
              documentName="Sample PDF"
              documentType="pdf"
              dictionary={mockDictionary}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
