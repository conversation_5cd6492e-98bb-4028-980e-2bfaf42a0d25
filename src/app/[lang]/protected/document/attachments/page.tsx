import { notFound } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Upload, Search, Filter, FileText } from "lucide-react";
import Link from "next/link";
import { i18n } from "@/lib/i18n/services/I18nService";
import { auth } from "@/lib/authentication/services/AuthenticationService";
import { getAttachments } from "../(features)/attachments/actions/attachment-actions";
import { AttachmentManagerWrapper } from "./components/AttachmentManagerWrapper";
import { AuthorizationRequired } from "@/lib/authorization/components/AuthorizationRequired";
import { DOCUMENT_PERMISSIONS } from "@/app/[lang]/protected/document/lib/security";

interface AttachmentsPageProps {
  params: Promise<{
    lang: string;
  }>;
  searchParams: Promise<{
    page?: string;
    limit?: string;
    search?: string;
    entity_type?: string;
    entity_id?: string;
    document_type?: string;
    sort?: string;
    dir?: string;
  }>;
}

/**
 * Document Attachments Management Page
 * Displays a library of all document attachments with filtering and search
 */
export default async function AttachmentsPage({ params, searchParams }: AttachmentsPageProps) {
  // Await the params and searchParams
  const { lang } = await params;
  const searchParamsData = await searchParams;

  // Get the dictionary
  const dictionary = i18n.getDictionary(lang);

  // Get the current user
  const user = await auth.getCurrentUser();
  if (!user) {
    notFound();
  }

  // Parse search parameters
  const page = searchParamsData.page ? parseInt(searchParamsData.page) : 1;
  const limit = searchParamsData.limit ? parseInt(searchParamsData.limit) : 12;
  const search = searchParamsData.search || "";
  const entityType = searchParamsData.entity_type || undefined;
  const entityId = searchParamsData.entity_id || undefined;
  const documentType = searchParamsData.document_type || undefined;
  const sortBy = searchParamsData.sort || "uploaded_at";
  const sortOrder = searchParamsData.dir || "desc";

  // Call server action to get attachments
  let state;
  try {
    state = await getAttachments({
      page,
      limit,
      search,
      entityType,
      entityId,
      documentType,
      sortBy: sortBy as any,
      sortOrder: sortOrder as any,
    });
  } catch (error) {
    console.error("Error loading attachments:", error);
    state = {
      success: false,
      error: "Failed to load attachments",
      data: { items: [], total: 0 },
    };
  }

  // Extract items and total count
  const items = state.data?.items || [];
  const totalItems = state.data?.total || 0;
  const totalPages = Math.ceil(totalItems / limit);

  return (
    <AuthorizationRequired permission={DOCUMENT_PERMISSIONS.ATTACHMENT.VIEW}>
      <div className="space-y-6">
        {/* Header */}
        <Card>
          <CardHeader className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Document Attachments
              </CardTitle>
              <CardDescription>
                Manage and organize document attachments across all entities
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <Button asChild variant="outline">
                <Link href={`/${lang}/protected/document/attachments/upload`}>
                  <Upload className="h-4 w-4 mr-2" />
                  Upload Documents
                </Link>
              </Button>
            </div>
          </CardHeader>
        </Card>

        {/* Filters and Search */}
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-4">
              {/* Search Bar */}
              <div className="flex gap-2">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                  <input
                    type="text"
                    placeholder="Search documents..."
                    className="pl-10 pr-4 py-2 w-full border border-input rounded-md bg-background"
                    defaultValue={search}
                  />
                </div>
                <Button variant="outline">
                  <Filter className="h-4 w-4 mr-2" />
                  Filters
                </Button>
              </div>

              {/* Filter Options */}
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                <select className="px-3 py-2 border border-input rounded-md bg-background">
                  <option value="">All Entity Types</option>
                  <option value="case_file">Case Files</option>
                  <option value="contact">Contacts</option>
                  <option value="appointment">Appointments</option>
                  <option value="request">Requests</option>
                </select>

                <select className="px-3 py-2 border border-input rounded-md bg-background">
                  <option value="">All Document Types</option>
                  <option value="PDF">PDF</option>
                  <option value="DOCX">Word Document</option>
                  <option value="JPG">Image (JPG)</option>
                  <option value="PNG">Image (PNG)</option>
                </select>

                <select className="px-3 py-2 border border-input rounded-md bg-background">
                  <option value="uploaded_at">Sort by Upload Date</option>
                  <option value="document_name">Sort by Name</option>
                  <option value="file_size">Sort by Size</option>
                  <option value="document_type">Sort by Type</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Error Display */}
        {!state.success && (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center py-8">
                <div className="text-red-500 mb-2">⚠️ Error Loading Attachments</div>
                <p className="text-muted-foreground">{state.error}</p>
                <p className="text-sm text-muted-foreground mt-2">
                  Please check the configuration and try again.
                </p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Attachments Grid */}
        {state.success && (
          <AttachmentManagerWrapper
            attachments={items.map((item) => ({
              ...item,
              file_size: item.file_size || 0,
              uploaded_at: item.uploaded_at || new Date().toISOString(),
              metadata: item.metadata as
                | { category?: string; tags?: string[]; description?: string }
                | undefined,
            }))}
            title="Document Attachments"
            description={`${totalItems} documents found`}
          />
        )}
      </div>
    </AuthorizationRequired>
  );
}
