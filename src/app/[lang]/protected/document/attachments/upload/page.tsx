import { notFound } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft, Upload, FileText } from "lucide-react";
import Link from "next/link";
import { i18n } from "@/lib/i18n/services/I18nService";
import { auth } from "@/lib/authentication/services/AuthenticationService";
import { FileUploader } from "@/components/ui/file-uploader";
import { UploadForm } from "../../(features)/attachments/components/UploadForm";
import { AuthorizationRequired } from "@/lib/authorization/components/AuthorizationRequired";
import { DOCUMENT_PERMISSIONS } from "@/app/[lang]/protected/document/lib/security";

interface UploadPageProps {
  params: Promise<{
    lang: string;
  }>;
  searchParams: Promise<{
    entity_type?: string;
    entity_id?: string;
    return_url?: string;
  }>;
}

/**
 * Document Upload Page
 * Provides drag-and-drop file upload interface with entity selection
 */
export default async function UploadPage({ params, searchParams }: UploadPageProps) {
  // Await the params and searchParams
  const { lang } = await params;
  const searchParamsData = await searchParams;

  // Get the dictionary
  const dictionary = i18n.getDictionary(lang);

  // Get the current user
  const user = await auth.getCurrentUser();
  if (!user) {
    notFound();
  }

  // Parse search parameters
  const entityType = searchParamsData.entity_type || "";
  const entityId = searchParamsData.entity_id || "";
  const returnUrl = searchParamsData.return_url || `/${lang}/protected/document/attachments`;

  return (
    <AuthorizationRequired permission={DOCUMENT_PERMISSIONS.ATTACHMENT.CREATE}>
      <div className="space-y-6">
        {/* Header */}
        <Card>
          <CardHeader className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Upload className="h-5 w-5" />
                Upload Documents
              </CardTitle>
              <CardDescription>
                Upload and attach documents to any entity in the system
              </CardDescription>
            </div>
            <Button asChild variant="outline">
              <Link href={returnUrl}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Link>
            </Button>
          </CardHeader>
        </Card>

        {/* Upload Form */}
        <UploadForm lang={lang} entityType={entityType} entityId={entityId} returnUrl={returnUrl} />
      </div>
    </AuthorizationRequired>
  );
}
