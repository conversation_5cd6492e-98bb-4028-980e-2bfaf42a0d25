"use client";

import { useState } from "react";
import { useRouter, useSearchParams, useParams } from "next/navigation";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Search, Filter } from "lucide-react";

interface SearchAndFiltersProps {
  dictionary: any;
  initialSearch?: string;
  initialEntityType?: string;
  initialDocumentType?: string;
  initialSort?: string;
}

export function SearchAndFilters({
  dictionary,
  initialSearch = "",
  initialEntityType = "",
  initialDocumentType = "",
  initialSort = "uploaded_at",
}: SearchAndFiltersProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const params = useParams();
  const lang = params.lang as string;

  const [search, setSearch] = useState(initialSearch);
  const [entityType, setEntityType] = useState(initialEntityType);
  const [documentType, setDocumentType] = useState(initialDocumentType);
  const [sort, setSort] = useState(initialSort);

  const handleSearch = () => {
    const newSearchParams = new URLSearchParams(searchParams);

    if (search) {
      newSearchParams.set("search", search);
    } else {
      newSearchParams.delete("search");
    }

    if (entityType) {
      newSearchParams.set("entity_type", entityType);
    } else {
      newSearchParams.delete("entity_type");
    }

    if (documentType) {
      newSearchParams.set("document_type", documentType);
    } else {
      newSearchParams.delete("document_type");
    }

    if (sort !== "uploaded_at") {
      newSearchParams.set("sort", sort);
    } else {
      newSearchParams.delete("sort");
    }

    // Reset to first page when searching
    newSearchParams.delete("page");

    router.push(`/${lang}/protected/document/attachments?${newSearchParams.toString()}`);
  };

  const handleClearFilters = () => {
    setSearch("");
    setEntityType("");
    setDocumentType("");
    setSort("uploaded_at");
    router.push(`/${lang}/protected/document/attachments`);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };

  return (
    <Card>
      <CardContent className="pt-6">
        <div className="space-y-4">
          {/* Search Bar */}
          <div className="flex gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <input
                type="text"
                placeholder={dictionary.documents?.searchPlaceholder || "Search documents..."}
                className="pl-10 pr-4 py-2 w-full border border-input rounded-md bg-background"
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                onKeyPress={handleKeyPress}
              />
            </div>
            <Button onClick={handleSearch}>
              <Search className="h-4 w-4 mr-2" />
              {dictionary.common?.search || "Search"}
            </Button>
            <Button variant="outline" onClick={handleClearFilters}>
              <Filter className="h-4 w-4 mr-2" />
              {dictionary.common?.clear || "Clear"}
            </Button>
          </div>

          {/* Filter Options */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
            <select
              className="px-3 py-2 border border-input rounded-md bg-background"
              value={entityType}
              onChange={(e) => setEntityType(e.target.value)}
            >
              <option value="">{dictionary.documents?.allEntityTypes || "All Entity Types"}</option>
              <option value="case_file">
                {dictionary.documents?.entityTypes?.caseFile || "Case Files"}
              </option>
              <option value="contact">
                {dictionary.documents?.entityTypes?.contact || "Contacts"}
              </option>
              <option value="appointment">
                {dictionary.documents?.entityTypes?.appointment || "Appointments"}
              </option>
              <option value="request">
                {dictionary.documents?.entityTypes?.request || "Requests"}
              </option>
            </select>

            <select
              className="px-3 py-2 border border-input rounded-md bg-background"
              value={documentType}
              onChange={(e) => setDocumentType(e.target.value)}
            >
              <option value="">
                {dictionary.documents?.allDocumentTypes || "All Document Types"}
              </option>
              <option value="PDF">PDF</option>
              <option value="DOCX">
                {dictionary.documents?.documentTypes?.wordDocument || "Word Document"}
              </option>
              <option value="JPG">
                {dictionary.documents?.documentTypes?.imageJpg || "Image (JPG)"}
              </option>
              <option value="PNG">
                {dictionary.documents?.documentTypes?.imagePng || "Image (PNG)"}
              </option>
              <option value="TXT">
                {dictionary.documents?.documentTypes?.textFile || "Text File"}
              </option>
            </select>

            <select
              className="px-3 py-2 border border-input rounded-md bg-background"
              value={sort}
              onChange={(e) => setSort(e.target.value)}
            >
              <option value="uploaded_at">
                {dictionary.documents?.sortBy?.uploadDate || "Sort by Upload Date"}
              </option>
              <option value="document_name">
                {dictionary.documents?.sortBy?.name || "Sort by Name"}
              </option>
              <option value="file_size">
                {dictionary.documents?.sortBy?.size || "Sort by Size"}
              </option>
              <option value="document_type">
                {dictionary.documents?.sortBy?.type || "Sort by Type"}
              </option>
            </select>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
