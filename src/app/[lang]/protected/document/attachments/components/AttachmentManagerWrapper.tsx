"use client";

import { AttachmentManager, AttachmentItem } from "@/components/ui/attachment-manager/AttachmentManager";
import { useParams } from "next/navigation";

interface AttachmentManagerWrapperProps {
  attachments: AttachmentItem[];
  title?: string;
  description?: string;
}

/**
 * Client-side wrapper for AttachmentManager that handles navigation
 */
export function AttachmentManagerWrapper({
  attachments,
  title,
  description,
}: AttachmentManagerWrapperProps) {
  const params = useParams();
  const lang = params.lang as string;

  const handleView = (attachment: AttachmentItem) => {
    window.location.href = `/${lang}/protected/document/attachments/${attachment.id}`;
  };

  const handleDownload = (attachment: AttachmentItem) => {
    window.location.href = `/api/documents/${attachment.id}/download`;
  };

  const handleUpload = () => {
    window.location.href = `/${lang}/protected/document/attachments/upload`;
  };

  return (
    <AttachmentManager
      attachments={attachments}
      title={title}
      description={description}
      onView={handleView}
      onDownload={handleDownload}
      onUpload={handleUpload}
    />
  );
}
