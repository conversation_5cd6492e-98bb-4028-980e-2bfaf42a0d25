"use client";

import { useState } from "react";
import { useRouter, useParams } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Download, Eye, Trash2 } from "lucide-react";
import Link from "next/link";
import { deleteAttachment } from "../../../(features)/attachments/actions/attachment-actions";

interface AttachmentActionsProps {
  attachmentId: string;
  attachedToType: string;
  attachedToId: string;
  dictionary: any;
  variant?: "default" | "compact";
}

export function AttachmentActions({
  attachmentId,
  attachedToType,
  attachedToId,
  dictionary,
  variant = "default",
}: AttachmentActionsProps) {
  const router = useRouter();
  const params = useParams();
  const lang = params.lang as string;
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async () => {
    const confirmed = window.confirm(
      dictionary.documents?.detail?.deleteConfirmation ||
      "Are you sure you want to delete this document? This action cannot be undone."
    );

    if (!confirmed) return;

    setIsDeleting(true);
    try {
      const result = await deleteAttachment(attachmentId);
      if (result.success) {
        // Redirect back to attachments list
        router.push(`/${lang}/protected/document/attachments`);
      } else {
        alert(result.error || "Failed to delete document");
      }
    } catch (error) {
      console.error("Delete error:", error);
      alert("Failed to delete document");
    } finally {
      setIsDeleting(false);
    }
  };

  const getEntityUrl = () => {
    // Map entity types to their respective URLs
    switch (attachedToType) {
      case "contact":
        return `/${lang}/protected/contact/${attachedToId}`;
      case "case_file":
        return `/${lang}/protected/case/${attachedToId}`;
      case "request":
        return `/${lang}/protected/request/${attachedToId}`;
      case "appointment":
        return `/${lang}/protected/appointment/${attachedToId}`;
      default:
        return null;
    }
  };

  const entityUrl = getEntityUrl();

  if (variant === "compact") {
    return (
      <div className="flex gap-2">
        {entityUrl && (
          <Button variant="outline" size="sm" asChild>
            <Link href={entityUrl}>
              <Eye className="h-4 w-4 mr-1" />
              {dictionary.documents?.detail?.viewEntity || "View Entity"}
            </Link>
          </Button>
        )}
        <Button
          variant="destructive"
          size="sm"
          onClick={handleDelete}
          disabled={isDeleting}
        >
          <Trash2 className="h-4 w-4 mr-1" />
          {isDeleting
            ? (dictionary.documents?.detail?.deleting || "Deleting...")
            : (dictionary.documents?.detail?.delete || "Delete")
          }
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      <Button className="w-full" asChild>
        <Link href={`/api/documents/${attachmentId}/download`}>
          <Download className="h-4 w-4 mr-2" />
          {dictionary.documents?.detail?.download || "Download"}
        </Link>
      </Button>

      {entityUrl && (
        <Button variant="outline" className="w-full" asChild>
          <Link href={entityUrl}>
            <Eye className="h-4 w-4 mr-2" />
            {dictionary.documents?.detail?.viewEntity || "View Entity"}
          </Link>
        </Button>
      )}

      <Button
        variant="destructive"
        className="w-full"
        onClick={handleDelete}
        disabled={isDeleting}
      >
        <Trash2 className="h-4 w-4 mr-2" />
        {isDeleting
          ? (dictionary.documents?.detail?.deleting || "Deleting...")
          : (dictionary.documents?.detail?.delete || "Delete")
        }
      </Button>
    </div>
  );
}
