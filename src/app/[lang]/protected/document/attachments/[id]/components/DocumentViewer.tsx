"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Eye, Download, FileText } from "lucide-react";
import { H3, Muted } from "@/components/typography";
import Link from "next/link";
import <PERSON><PERSON><PERSON><PERSON>, {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@cyntler/react-doc-viewer";
import "@cyntler/react-doc-viewer/dist/index.css";

interface DocumentViewerProps {
  documentId: string;
  documentName: string;
  documentType: string;
  dictionary: any;
}

export function DocumentViewer({
  documentId,
  documentName,
  documentType,
  dictionary,
}: DocumentViewerProps) {
  const [viewerError, setViewerError] = useState(false);

  const downloadUrl = `/api/documents/${documentId}/download`;

  const handleViewerError = () => {
    setViewerError(true);
  };

  if (viewerError) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
          <H3 className="mb-2 text-base">
            {dictionary.documents?.detail?.previewNotAvailable || "Preview Not Available"}
          </H3>
          <Muted className="mb-4 text-xs">Unable to load preview for this file type</Muted>
          <div className="flex gap-2 justify-center">
            <Button asChild>
              <Link href={downloadUrl}>
                <Download className="h-4 w-4 mr-2" />
                {dictionary.documents?.detail?.download || "Download"}
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href={downloadUrl} target="_blank">
                <Eye className="h-4 w-4 mr-2" />
                {dictionary.documents?.detail?.openInNewTab || "Open in New Tab"}
              </Link>
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full w-full">
      <DocViewer
        pluginRenderers={[PDFRenderer, PNGRenderer, TXTRenderer, JPGRenderer]}
        documents={[
          {
            uri: downloadUrl,
            fileType: documentType.toLowerCase(),
          },
        ]}
        config={{
          header: {
            disableHeader: true,
          },
        }}
        style={{
          height: "100%",
          width: "100%",
        }}
      />
    </div>
  );
}
