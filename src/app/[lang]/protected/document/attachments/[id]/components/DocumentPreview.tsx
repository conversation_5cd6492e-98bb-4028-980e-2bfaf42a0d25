"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Eye, Download, FileText, Image, File } from "lucide-react";
import { H3, Muted } from "@/components/typography";
import Link from "next/link";

interface DocumentPreviewProps {
  documentId: string;
  documentName: string;
  documentType: string;
  dictionary: any;
}

export function DocumentPreview({
  documentId,
  documentName,
  documentType,
  dictionary,
}: DocumentPreviewProps) {
  const [previewError, setPreviewError] = useState(false);

  const downloadUrl = `/api/documents/${documentId}/download`;
  
  // Determine if we can preview this file type
  const canPreview = () => {
    const type = documentType.toLowerCase();
    return ['pdf', 'jpg', 'jpeg', 'png', 'gif', 'webp', 'txt', 'csv'].includes(type);
  };

  const getPreviewIcon = () => {
    const type = documentType.toLowerCase();
    if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(type)) {
      return <Image className="h-12 w-12 text-muted-foreground mx-auto mb-3" />;
    }
    if (type === 'pdf') {
      return <File className="h-12 w-12 text-muted-foreground mx-auto mb-3" />;
    }
    return <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-3" />;
  };

  const renderPreview = () => {
    if (previewError || !canPreview()) {
      return (
        <div className="text-center">
          {getPreviewIcon()}
          <H3 className="mb-2 text-base">
            {dictionary.documents?.detail?.previewNotAvailable || "Preview Not Available"}
          </H3>
          <Muted className="mb-4 text-xs">
            {canPreview() 
              ? "Error loading preview"
              : (dictionary.documents?.detail?.previewNotImplemented?.replace('{type}', documentType) || `Preview for ${documentType} files is not yet implemented`)
            }
          </Muted>
          <div className="flex gap-2 justify-center">
            <Button asChild>
              <Link href={downloadUrl}>
                <Download className="h-4 w-4 mr-2" />
                {dictionary.documents?.detail?.download || "Download"}
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href={downloadUrl} target="_blank">
                <Eye className="h-4 w-4 mr-2" />
                {dictionary.documents?.detail?.openInNewTab || "Open in New Tab"}
              </Link>
            </Button>
          </div>
        </div>
      );
    }

    const type = documentType.toLowerCase();

    // PDF Preview
    if (type === 'pdf') {
      return (
        <div className="w-full h-full">
          <iframe
            src={downloadUrl}
            className="w-full h-full border-0 rounded"
            title={documentName}
            onError={() => setPreviewError(true)}
          />
        </div>
      );
    }

    // Image Preview
    if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(type)) {
      return (
        <div className="w-full h-full flex items-center justify-center">
          <img
            src={downloadUrl}
            alt={documentName}
            className="max-w-full max-h-full object-contain rounded"
            onError={() => setPreviewError(true)}
          />
        </div>
      );
    }

    // Text Preview (for small files)
    if (['txt', 'csv'].includes(type)) {
      return (
        <div className="w-full h-full">
          <iframe
            src={downloadUrl}
            className="w-full h-full border-0 rounded bg-white"
            title={documentName}
            onError={() => setPreviewError(true)}
          />
        </div>
      );
    }

    return null;
  };

  return (
    <div className="bg-muted/30 rounded-lg aspect-[4/3] flex items-center justify-center border-2 border-dashed border-muted-foreground/20 overflow-hidden">
      {renderPreview()}
    </div>
  );
}
