import { notFound } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, FileText, Calendar, User } from "lucide-react";
import Link from "next/link";
import { i18n } from "@/lib/i18n/services/I18nService";
import { auth } from "@/lib/authentication/services/AuthenticationService";
import { getAttachmentById } from "../../(features)/attachments/actions/attachment-actions";
import { UserManagementService } from "@/app/[lang]/protected/user/lib/services/UserManagementService";
import { AttachmentActions } from "./components/AttachmentActions";
import { AuthorizationRequired } from "@/lib/authorization/components/AuthorizationRequired";
import { DOCUMENT_PERMISSIONS } from "@/app/[lang]/protected/document/lib/security";

interface AttachmentViewPageProps {
  params: Promise<{
    lang: string;
    id: string;
  }>;
  searchParams: Promise<{
    return_url?: string;
  }>;
}

/**
 * Document Attachment View Page
 * Displays attachment details with preview and download options
 */
export default async function AttachmentViewPage({
  params,
  searchParams,
}: AttachmentViewPageProps) {
  // Await the params and searchParams
  const { lang, id } = await params;
  const searchParamsData = await searchParams;

  // Get the dictionary
  const dictionary = i18n.getDictionary(lang);

  // Get the current user
  const user = await auth.getCurrentUser();
  if (!user) {
    notFound();
  }

  // Parse search parameters
  const returnUrl = searchParamsData.return_url || `/${lang}/protected/document/attachments`;

  // Call server action to get attachment details
  const result = await getAttachmentById(id);
  if (!result.success || !result.data) {
    notFound();
  }

  const attachment = result.data;

  // Get user information for the uploader
  let uploaderName = attachment.uploaded_by;
  try {
    const userProfile = await UserManagementService.getUserProfile(attachment.uploaded_by);
    if (userProfile) {
      uploaderName = `${userProfile.firstName} ${userProfile.lastName}`.trim() || userProfile.email;
    }
  } catch (error) {
    // If we can't get user info, just use the UUID
    console.warn("Could not fetch user profile for uploader:", error);
  }

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(lang, {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <AuthorizationRequired permission={DOCUMENT_PERMISSIONS.ATTACHMENT.VIEW}>
      <div className="space-y-6">
        {/* Header */}
        <Card>
          <CardHeader className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                {attachment.document_name}
              </CardTitle>
              <CardDescription>{dictionary.documents?.detail?.description || "Document attachment details and preview"}</CardDescription>
            </div>
            <div className="flex gap-2">
              <Button asChild variant="outline">
                <Link href={returnUrl}>
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  {dictionary.documents?.detail?.back || "Back"}
                </Link>
              </Button>
            </div>
          </CardHeader>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content - Preview */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>{dictionary.documents?.detail?.documentPreview || "Document Preview"}</CardTitle>
                <CardDescription>
                  {dictionary.documents?.detail?.previewOf?.replace('{name}', attachment.document_name) || `Preview of ${attachment.document_name}`}
                </CardDescription>
              </CardHeader>
              <CardContent>
                {/* TODO: Implement FileViewer component */}
                <div className="border rounded-lg p-12 text-center bg-muted/10">
                  <FileText className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">{dictionary.documents?.detail?.previewNotAvailable || "Preview Not Available"}</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    {dictionary.documents?.detail?.previewNotImplemented?.replace('{type}', attachment.document_type) || `Preview for ${attachment.document_type} files is not yet implemented`}
                  </p>
                  <div className="flex gap-2 justify-center">
                    <Button asChild>
                      <Link href={`/api/documents/${id}/download`}>
                        <Download className="h-4 w-4 mr-2" />
                        {dictionary.documents?.detail?.download || "Download"}
                      </Link>
                    </Button>
                    <Button variant="outline">
                      <Eye className="h-4 w-4 mr-2" />
                      {dictionary.documents?.detail?.openInNewTab || "Open in New Tab"}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar - Details */}
          <div className="space-y-6">
            {/* Document Information */}
            <Card>
              <CardHeader>
                <CardTitle>{dictionary.documents?.detail?.documentInformation || "Document Information"}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">{dictionary.documents?.detail?.type || "Type"}:</span>
                  <Badge variant="secondary">{attachment.document_type}</Badge>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">{dictionary.documents?.detail?.size || "Size"}:</span>
                  <span className="text-sm text-muted-foreground">
                    {formatFileSize(attachment.file_size || 0)}
                  </span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">{dictionary.documents?.detail?.status || "Status"}:</span>
                  <Badge variant={attachment.status === "attached" ? "default" : "secondary"}>
                    {attachment.status}
                  </Badge>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">{dictionary.documents?.detail?.source || "Source"}:</span>
                  <Badge variant="outline">
                    {attachment.attachment_type === "manual"
                      ? (dictionary.documents?.detail?.manualUpload || "Manual Upload")
                      : (dictionary.documents?.detail?.generated || "Generated")
                    }
                  </Badge>
                </div>
              </CardContent>
            </Card>

            {/* Attachment Details */}
            <Card>
              <CardHeader>
                <CardTitle>{dictionary.documents?.detail?.attachedTo || "Attached To"}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">{dictionary.documents?.detail?.entityType || "Entity Type"}:</span>
                  <Badge variant="outline">
                    {attachment.attached_to_type.replace("_", " ").toUpperCase()}
                  </Badge>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">{dictionary.documents?.detail?.entityId || "Entity ID"}:</span>
                  <span className="text-sm text-muted-foreground">{attachment.attached_to_id}</span>
                </div>
              </CardContent>
            </Card>

            {/* Metadata */}
            <Card>
              <CardHeader>
                <CardTitle>{dictionary.documents?.detail?.metadata || "Metadata"}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {(attachment.metadata as any)?.category && (
                  <div>
                    <span className="text-sm font-medium block mb-1">{dictionary.documents?.detail?.category || "Category"}:</span>
                    <Badge variant="secondary">{(attachment.metadata as any).category}</Badge>
                  </div>
                )}

                {(attachment.metadata as any)?.tags &&
                  (attachment.metadata as any).tags.length > 0 && (
                    <div>
                      <span className="text-sm font-medium block mb-2">{dictionary.documents?.detail?.tags || "Tags"}:</span>
                      <div className="flex flex-wrap gap-1">
                        {(attachment.metadata as any).tags.map((tag: string, index: number) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                {(attachment.metadata as any)?.description && (
                  <div>
                    <span className="text-sm font-medium block mb-1">{dictionary.documents?.detail?.description || "Description"}:</span>
                    <p className="text-sm text-muted-foreground">
                      {(attachment.metadata as any).description}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Upload Information */}
            <Card>
              <CardHeader>
                <CardTitle>{dictionary.documents?.detail?.uploadInformation || "Upload Information"}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <div className="text-sm font-medium">{dictionary.documents?.detail?.uploaded || "Uploaded"}</div>
                    <div className="text-xs text-muted-foreground">
                      {formatDate(attachment.uploaded_at || new Date().toISOString())}
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <div className="text-sm font-medium">{dictionary.documents?.detail?.uploadedBy || "Uploaded by"}</div>
                    <div className="text-xs text-muted-foreground">{uploaderName}</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Actions */}
            <Card>
              <CardContent className="pt-6">
                <AttachmentActions
                  attachmentId={id}
                  attachedToType={attachment.attached_to_type}
                  attachedToId={attachment.attached_to_id}
                  dictionary={dictionary}
                />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </AuthorizationRequired>
  );
}
