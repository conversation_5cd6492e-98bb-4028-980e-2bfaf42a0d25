import { notFound } from "next/navigation";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Download, Eye, FileText, Calendar, User, Tag, Hash } from "lucide-react";
import Link from "next/link";
import { i18n } from "@/lib/i18n/services/I18nService";
import { auth } from "@/lib/authentication/services/AuthenticationService";
import { getAttachmentById } from "../../(features)/attachments/actions/attachment-actions";
import { UserManagementService } from "@/app/[lang]/protected/user/lib/services/UserManagementService";
import { AttachmentActions } from "./components/AttachmentActions";
import { AuthorizationRequired } from "@/lib/authorization/components/AuthorizationRequired";
import { DOCUMENT_PERMISSIONS } from "@/app/[lang]/protected/document/lib/security";
import { DetailLayout } from "@/components/layouts";
import { H3, <PERSON><PERSON>, <PERSON> } from "@/components/typography";

interface AttachmentViewPageProps {
  params: Promise<{
    lang: string;
    id: string;
  }>;
  searchParams: Promise<{
    return_url?: string;
  }>;
}

/**
 * Document Attachment View Page
 * Displays attachment details with preview and download options
 */
export default async function AttachmentViewPage({
  params,
  searchParams,
}: AttachmentViewPageProps) {
  // Await the params and searchParams
  const { lang, id } = await params;
  const searchParamsData = await searchParams;

  // Get the dictionary
  const dictionary = i18n.getDictionary(lang);

  // Get the current user
  const user = await auth.getCurrentUser();
  if (!user) {
    notFound();
  }

  // Parse search parameters
  const returnUrl = searchParamsData.return_url || `/${lang}/protected/document/attachments`;

  // Call server action to get attachment details
  const result = await getAttachmentById(id);
  if (!result.success || !result.data) {
    notFound();
  }

  const attachment = result.data;

  // Get user information for the uploader
  let uploaderName = attachment.uploaded_by;
  try {
    const userProfile = await UserManagementService.getUserProfile(attachment.uploaded_by);
    if (userProfile) {
      uploaderName = `${userProfile.firstName} ${userProfile.lastName}`.trim() || userProfile.email;
    }
  } catch (error) {
    // If we can't get user info, just use the UUID
    console.warn("Could not fetch user profile for uploader:", error);
  }

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(lang, {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <AuthorizationRequired permission={DOCUMENT_PERMISSIONS.ATTACHMENT.VIEW}>
      <div className="space-y-6">
        {/* Header with Back Button and Actions */}
        <div className="flex items-center justify-between">
          <Button asChild variant="ghost" size="sm" className="text-muted-foreground">
            <Link href={returnUrl}>
              <ArrowLeft className="h-4 w-4 mr-1" />
              {dictionary.documents?.detail?.back || "Back"}
            </Link>
          </Button>

          <div className="flex gap-2">
            <Button asChild>
              <Link href={`/api/documents/${id}/download`}>
                <Download className="h-4 w-4 mr-2" />
                {dictionary.documents?.detail?.download || "Download"}
              </Link>
            </Button>
            <AttachmentActions
              attachmentId={id}
              attachedToType={attachment.attached_to_type}
              attachedToId={attachment.attached_to_id}
              dictionary={dictionary}
              variant="compact"
            />
          </div>
        </div>

        {/* Document Title and Info */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-start gap-4">
              <FileText className="h-10 w-10 text-muted-foreground mt-1" />
              <div className="flex-1 min-w-0">
                <H3 className="break-words mb-2">{attachment.document_name}</H3>
                <div className="grid grid-cols-2 gap-3 text-sm">
                  <div className="flex items-center gap-2">
                    <Hash className="h-4 w-4 text-muted-foreground" />
                    <Badge variant="secondary" className="text-xs">{attachment.document_type}</Badge>
                    <Small className="text-muted-foreground">{formatFileSize(attachment.file_size || 0)}</Small>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <Small className="text-muted-foreground">{formatDate(attachment.uploaded_at || new Date().toISOString())}</Small>
                  </div>
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-muted-foreground" />
                    <Small className="text-muted-foreground">{uploaderName}</Small>
                  </div>
                  {/* View Related Button */}
                  <div className="flex items-center">
                    <AttachmentActions
                      attachmentId={id}
                      attachedToType={attachment.attached_to_type}
                      attachedToId={attachment.attached_to_id}
                      dictionary={dictionary}
                      variant="inline"
                    />
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          {/* Document Preview */}
          <Card>
            <CardContent className="p-4">
              <div className="bg-muted/30 rounded-lg aspect-[4/3] flex items-center justify-center border-2 border-dashed border-muted-foreground/20">
                <div className="text-center">
                  <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
                  <H3 className="mb-2 text-base">{dictionary.documents?.detail?.previewNotAvailable || "Preview Not Available"}</H3>
                  <Muted className="mb-4 text-xs">
                    {dictionary.documents?.detail?.previewNotImplemented?.replace('{type}', attachment.document_type) || `Preview for ${attachment.document_type} files is not yet implemented`}
                  </Muted>
                  <Button variant="outline" size="sm">
                    <Eye className="h-4 w-4 mr-2" />
                    {dictionary.documents?.detail?.openInNewTab || "Open in New Tab"}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Document Details */}
          <Card>
            <CardContent className="p-4">
              {attachment.metadata && ((attachment.metadata as any)?.category || (attachment.metadata as any)?.tags?.length > 0 || (attachment.metadata as any)?.description) ? (
                <div className="space-y-4">
                  {(attachment.metadata as any)?.category && (
                    <div className="flex items-center gap-2">
                      <Hash className="h-4 w-4 text-muted-foreground" />
                      <Small className="font-medium">{dictionary.documents?.detail?.category || "Category"}:</Small>
                      <Badge variant="outline" className="text-xs">{(attachment.metadata as any).category}</Badge>
                    </div>
                  )}

                  {(attachment.metadata as any)?.tags && (attachment.metadata as any).tags.length > 0 && (
                    <div className="flex items-start gap-2">
                      <Tag className="h-4 w-4 text-muted-foreground mt-0.5" />
                      <div>
                        <Small className="font-medium block mb-1">{dictionary.documents?.detail?.tags || "Tags"}:</Small>
                        <div className="flex flex-wrap gap-1">
                          {(attachment.metadata as any).tags.map((tag: string, index: number) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}

                  {(attachment.metadata as any)?.description && (
                    <div className="flex items-start gap-2">
                      <FileText className="h-4 w-4 text-muted-foreground mt-0.5" />
                      <div>
                        <Small className="font-medium block mb-1">{dictionary.documents?.detail?.description || "Description"}:</Small>
                        <Muted className="text-xs">{(attachment.metadata as any).description}</Muted>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="flex items-center justify-center h-full text-center">
                  <div>
                    <FileText className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                    <Muted className="text-xs">No additional details available</Muted>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </AuthorizationRequired>
  );
}
