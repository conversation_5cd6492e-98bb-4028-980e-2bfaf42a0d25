import { notFound } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Download, Eye, Edit, Trash2, FileText, Calendar, User } from "lucide-react";
import Link from "next/link";
import { i18n } from "@/lib/i18n/services/I18nService";
import { auth } from "@/lib/authentication/services/AuthenticationService";
import { getAttachmentById } from "../../(features)/attachments/actions/attachment-actions";
import { AuthorizationRequired } from "@/lib/authorization/components/AuthorizationRequired";
import { DOCUMENT_PERMISSIONS } from "@/app/[lang]/protected/document/lib/security";

interface AttachmentViewPageProps {
  params: Promise<{
    lang: string;
    id: string;
  }>;
  searchParams: Promise<{
    return_url?: string;
  }>;
}

/**
 * Document Attachment View Page
 * Displays attachment details with preview and download options
 */
export default async function AttachmentViewPage({
  params,
  searchParams,
}: AttachmentViewPageProps) {
  // Await the params and searchParams
  const { lang, id } = await params;
  const searchParamsData = await searchParams;

  // Get the dictionary
  const dictionary = i18n.getDictionary(lang);

  // Get the current user
  const user = await auth.getCurrentUser();
  if (!user) {
    notFound();
  }

  // Parse search parameters
  const returnUrl = searchParamsData.return_url || `/${lang}/protected/document/attachments`;

  // Call server action to get attachment details
  const result = await getAttachmentById(id);
  if (!result.success || !result.data) {
    notFound();
  }

  const attachment = result.data;

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(lang, {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <AuthorizationRequired permission={DOCUMENT_PERMISSIONS.ATTACHMENT.VIEW}>
      <div className="space-y-6">
        {/* Header */}
        <Card>
          <CardHeader className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                {attachment.document_name}
              </CardTitle>
              <CardDescription>Document attachment details and preview</CardDescription>
            </div>
            <div className="flex gap-2">
              <Button asChild variant="outline">
                <Link href={returnUrl}>
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back
                </Link>
              </Button>
            </div>
          </CardHeader>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content - Preview */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Document Preview</CardTitle>
                <CardDescription>Preview of {attachment.document_name}</CardDescription>
              </CardHeader>
              <CardContent>
                {/* TODO: Implement FileViewer component */}
                <div className="border rounded-lg p-12 text-center bg-muted/10">
                  <FileText className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">Preview Not Available</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Preview for {attachment.document_type} files is not yet implemented
                  </p>
                  <div className="flex gap-2 justify-center">
                    <Button asChild>
                      <Link href={`/api/documents/${id}/download`}>
                        <Download className="h-4 w-4 mr-2" />
                        Download
                      </Link>
                    </Button>
                    <Button variant="outline">
                      <Eye className="h-4 w-4 mr-2" />
                      Open in New Tab
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar - Details */}
          <div className="space-y-6">
            {/* Document Information */}
            <Card>
              <CardHeader>
                <CardTitle>Document Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Type:</span>
                  <Badge variant="secondary">{attachment.document_type}</Badge>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Size:</span>
                  <span className="text-sm text-muted-foreground">
                    {formatFileSize(attachment.file_size || 0)}
                  </span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Status:</span>
                  <Badge variant={attachment.status === "attached" ? "default" : "secondary"}>
                    {attachment.status}
                  </Badge>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Source:</span>
                  <Badge variant="outline">
                    {attachment.attachment_type === "manual" ? "Manual Upload" : "Generated"}
                  </Badge>
                </div>
              </CardContent>
            </Card>

            {/* Attachment Details */}
            <Card>
              <CardHeader>
                <CardTitle>Attached To</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Entity Type:</span>
                  <Badge variant="outline">
                    {attachment.attached_to_type.replace("_", " ").toUpperCase()}
                  </Badge>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Entity ID:</span>
                  <span className="text-sm text-muted-foreground">{attachment.attached_to_id}</span>
                </div>

                <Button variant="outline" className="w-full">
                  <Eye className="h-4 w-4 mr-2" />
                  View Entity
                </Button>
              </CardContent>
            </Card>

            {/* Metadata */}
            <Card>
              <CardHeader>
                <CardTitle>Metadata</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {(attachment.metadata as any)?.category && (
                  <div>
                    <span className="text-sm font-medium block mb-1">Category:</span>
                    <Badge variant="secondary">{(attachment.metadata as any).category}</Badge>
                  </div>
                )}

                {(attachment.metadata as any)?.tags &&
                  (attachment.metadata as any).tags.length > 0 && (
                    <div>
                      <span className="text-sm font-medium block mb-2">Tags:</span>
                      <div className="flex flex-wrap gap-1">
                        {(attachment.metadata as any).tags.map((tag: string, index: number) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                {(attachment.metadata as any)?.description && (
                  <div>
                    <span className="text-sm font-medium block mb-1">Description:</span>
                    <p className="text-sm text-muted-foreground">
                      {(attachment.metadata as any).description}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Upload Information */}
            <Card>
              <CardHeader>
                <CardTitle>Upload Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <div className="text-sm font-medium">Uploaded</div>
                    <div className="text-xs text-muted-foreground">
                      {formatDate(attachment.uploaded_at || new Date().toISOString())}
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <div className="text-sm font-medium">Uploaded by</div>
                    <div className="text-xs text-muted-foreground">{attachment.uploaded_by}</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Actions */}
            <Card>
              <CardContent className="pt-6">
                <div className="space-y-3">
                  <Button className="w-full" asChild>
                    <Link href={`/api/documents/${id}/download`}>
                      <Download className="h-4 w-4 mr-2" />
                      Download
                    </Link>
                  </Button>

                  <Button variant="outline" className="w-full">
                    <Edit className="h-4 w-4 mr-2" />
                    Edit Metadata
                  </Button>

                  <Button variant="destructive" className="w-full">
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </AuthorizationRequired>
  );
}
