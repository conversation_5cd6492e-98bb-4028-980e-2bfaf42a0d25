import { <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/scroll-area";
import { Users, Settings, Building, Contact, CoffeeIcon, Calendar } from "lucide-react";
import { PERMISSIONS } from "@/lib/authorization/config/permissions";
import { AuthorizedMenuItem } from "@/lib/authorization/components/AuthorizedMenuItem";
import { SidebarToggle } from "./sidebar-toggle";

// Navigation items with required permissions
const navItems = [
  {
    title: "contacts",
    href: "/contact/management/list",
    icon: Contact,
    permission: PERMISSIONS.CONTACT.MANAGEMENT.LIST,
  },
  {
    title: "employeeManage",
    href: "/employee/management/list",
    icon: CoffeeIcon,
    permission: PERMISSIONS.EMPLOYEE.MANAGEMENT.LIST,
  },
  {
    title: "employeeAvailability",
    href: "/employee/availability/list",
    icon: Calendar,
    permission: PERMISSIONS.EMPLOYEE.AVAILABILITY.VIEW,
  },
  {
    title: "organizationAdmin",
    href: "/organization/system-admin/list",
    icon: Building,
    permission: PERMISSIONS.ORGANIZATION.SYSTEM_ADMIN.LIST,
  },
  {
    title: "userManage",
    href: "/user/management/list",
    icon: Users,
    permission: PERMISSIONS.USER.MANAGEMENT.LIST,
  },
  {
    title: "organizationProfile",
    href: "/organization/profile",
    icon: Settings,
    permission: PERMISSIONS.ORGANIZATION.PROFILE.MANAGE,
  },
];

export type SidebarDictionary = {
  navigation?: Record<string, string>;
};

export async function Sidebar({
  lang,
  dictionary,
  pathname,
}: {
  lang: string;
  dictionary: SidebarDictionary;
  pathname: string;
}) {
  return (
    <>
      {/* Mobile sidebar trigger */}
      <div className="lg:hidden fixed top-4 left-4 z-40">
        <SidebarToggle>
          <SidebarContent lang={lang} dictionary={dictionary} pathname={pathname} />
        </SidebarToggle>
      </div>

      {/* Desktop sidebar */}
      <div className="hidden lg:block w-64 border-r bg-card">
        <SidebarContent lang={lang} dictionary={dictionary} pathname={pathname} />
      </div>
    </>
  );
}

/**
 * Shared sidebar content with permission-based menu items
 */
async function SidebarContent({
  lang,
  dictionary,
  pathname,
}: {
  lang: string;
  dictionary: SidebarDictionary;
  pathname: string;
}) {
  return (
    <div className="flex flex-col h-full">
      <div className="p-6">
        <h2 className="text-xl font-bold">Si Simple</h2>
      </div>

      <ScrollArea className="flex-1">
        <nav className="px-4 py-2 space-y-1">
          {/* Map through nav items and conditionally render based on permissions */}
          {navItems.map((item) => (
            <AuthorizedMenuItem
              key={item.href}
              title={item.title}
              href={item.href}
              icon={item.icon}
              permission={item.permission}
              lang={lang}
              pathname={pathname}
              dictionary={dictionary.navigation}
            />
          ))}
        </nav>
      </ScrollArea>
    </div>
  );
}
