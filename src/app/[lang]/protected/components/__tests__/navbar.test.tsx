import { render, screen } from "@testing-library/react";
import { Navbar } from "../navbar";

// Mock the ThemeToggle and LanguageToggle components
jest.mock("@/components/theme-toggle", () => ({
  ThemeToggle: () => <div data-testid="theme-toggle">Theme Toggle</div>,
}));

jest.mock("@/components/language-toggle", () => ({
  LanguageToggle: ({ currentLang }: { currentLang: string }) => (
    <div data-testid="language-toggle">Language Toggle ({currentLang})</div>
  ),
}));

// Mock the UserMenu component
jest.mock("../user-menu", () => ({
  UserMenu: ({ lang }: { lang: string }) => <div data-testid="user-menu">User Menu ({lang})</div>,
}));

describe("Navbar", () => {
  const mockDictionary = {
    common: {
      profile: "Profile",
      account: "Account",
      signOut: "Sign Out",
    },
  };

  it("renders the navbar with user menu", () => {
    render(<Navbar lang="en" dictionary={mockDictionary} />);

    // Check if theme toggle and language toggle are rendered
    expect(screen.getByTestId("theme-toggle")).toBeInTheDocument();
    expect(screen.getByTestId("language-toggle")).toBeInTheDocument();

    // Check if the user menu is rendered
    expect(screen.getByTestId("user-menu")).toBeInTheDocument();
  });

  it("renders with fallback text when translations are missing", () => {
    const emptyDictionary = {};
    render(<Navbar lang="en" dictionary={emptyDictionary} />);

    // Check if theme toggle and language toggle are still rendered
    expect(screen.getByTestId("theme-toggle")).toBeInTheDocument();
    expect(screen.getByTestId("language-toggle")).toBeInTheDocument();

    // User menu should still be rendered
    expect(screen.getByTestId("user-menu")).toBeInTheDocument();
  });
});
