"use client";

import React, { ReactNode } from "react";
import { ContentLayout } from "@/components/ui/app-shell";
import { cn } from "@/lib/utils";

interface PageContentProps {
  /**
   * The title of the page
   */
  title?: ReactNode;

  /**
   * The description of the page
   */
  description?: ReactNode;

  /**
   * The main content of the page
   */
  children: ReactNode;

  /**
   * Additional actions to display in the header
   */
  actions?: ReactNode;

  /**
   * Additional content to display in the footer
   */
  footer?: ReactNode;

  /**
   * Additional CSS classes to apply to the page content
   */
  className?: string;
}

/**
 * PageContent component that provides a consistent layout for page content
 * with a title, description, actions, and footer.
 */
export function PageContent({
  title,
  description,
  children,
  actions,
  footer,
  className,
}: PageContentProps) {
  return (
    <ContentLayout
      title={title}
      description={description}
      actions={actions}
      footer={footer}
      className={cn("max-w-7xl mx-auto", className)}
    >
      {children}
    </ContentLayout>
  );
}
