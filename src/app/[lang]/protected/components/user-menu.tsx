"use client";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { LogOut, User, Settings, Bell } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";

export type UserMenuDictionary = {
  profile?: string;
  account?: string;
  signOut?: string;
  settings?: string;
  notifications?: string;
};

export function UserMenu({ lang, dictionary }: { lang: string; dictionary: UserMenuDictionary }) {
  const router = useRouter();

  const handleSignOut = async () => {
    // Navigate to the signout page which will handle the signout process
    router.push(`/${lang}/auth/signout`);
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="rounded-full">
          <Avatar className="h-8 w-8">
            <AvatarImage src="/placeholder-avatar.jpg" alt="User" />
            <AvatarFallback>U</AvatarFallback>
          </Avatar>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>{dictionary.profile || "Profile"}</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem asChild>
          <Link href={`/${lang}/protected/user/profile`}>
            <User className="mr-2 h-4 w-4" />
            <span>{dictionary.account || "Account"}</span>
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <Link href={`/${lang}/protected/notifications`}>
            <Bell className="mr-2 h-4 w-4" />
            <span>{dictionary.notifications || "Notifications"}</span>
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <Link href={`/${lang}/protected/user/profile/settings`}>
            <Settings className="mr-2 h-4 w-4" />
            <span>{dictionary.settings || "Settings"}</span>
          </Link>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          onClick={handleSignOut}
          className="text-red-500 hover:text-red-600 cursor-pointer"
        >
          <LogOut className="mr-2 h-4 w-4" />
          <span>{dictionary.signOut || "Sign out"}</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
