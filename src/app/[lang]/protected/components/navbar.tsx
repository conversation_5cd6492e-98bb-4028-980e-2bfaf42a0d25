import { ThemeToggle } from "@/components/theme-toggle";
import { LanguageToggle } from "@/components/language-toggle";
import { Separator } from "@/components/ui/separator";
import { UserMenu } from "./user-menu";

export type NavbarDictionary = {
  common?: {
    profile?: string;
    account?: string;
    signOut?: string;
    settings?: string;
    notifications?: string;
    english?: string;
    french?: string;
  };
};

export function Navbar({ lang, dictionary }: { lang: string; dictionary: NavbarDictionary }) {
  return (
    <header className="sticky top-0 z-30 flex h-16 items-center gap-4 border-b bg-background px-6">
      {/* Mobile logo - visible on mobile only */}
      <div className="lg:hidden flex items-center">
        <h2 className="text-xl font-bold ml-10">RQRSDA</h2>
      </div>

      <div className="flex-1" />

      <div className="flex items-center gap-4">
        <LanguageToggle
          currentLang={lang}
          labels={{
            en: dictionary.common?.english || "English",
            fr: dictionary.common?.french || "Français",
          }}
        />
        <ThemeToggle />
        <Separator orientation="vertical" className="h-8" />

        <UserMenu
          lang={lang}
          dictionary={{
            profile: dictionary.common?.profile,
            account: dictionary.common?.account,
            signOut: dictionary.common?.signOut,
            settings: dictionary.common?.settings,
            notifications: dictionary.common?.notifications,
          }}
        />
      </div>
    </header>
  );
}
