"use client";

import { useState, useEffect } from "react";
import { UserRole } from "@/lib/authorization/types";

// This is just for testing - in a real app, you wouldn't be able to change your role like this
export function RoleSwitcher() {
  const [currentRole, setCurrentRole] = useState<UserRole>("SocialWorker");

  useEffect(() => {
    // Load the current role from localStorage on component mount
    const savedRole = localStorage.getItem("testUserRole");
    if (savedRole) {
      setCurrentRole(savedRole as UserRole);
    }

    // Store the selected role in localStorage
    localStorage.setItem("testUserRole", currentRole);
  }, [currentRole]);

  return (
    <div className="p-4 bg-yellow-100 border border-yellow-400 rounded mb-4">
      <h2 className="font-semibold mb-2">Test Role Switcher</h2>
      <p className="text-sm mb-2">
        This is for testing only. Select a role to simulate different permissions:
      </p>

      <div className="flex space-x-4">
        <label className="flex items-center">
          <input
            type="radio"
            name="role"
            value="Director"
            checked={currentRole === "Director"}
            onChange={() => setCurrentRole("Director")}
            className="mr-2"
          />
          Director (All permissions)
        </label>

        <label className="flex items-center">
          <input
            type="radio"
            name="role"
            value="Coordinator"
            checked={currentRole === "Coordinator"}
            onChange={() => setCurrentRole("Coordinator")}
            className="mr-2"
          />
          Coordinator (Some permissions)
        </label>

        <label className="flex items-center">
          <input
            type="radio"
            name="role"
            value="SocialWorker"
            checked={currentRole === "SocialWorker"}
            onChange={() => setCurrentRole("SocialWorker")}
            className="mr-2"
          />
          Social Worker (Limited permissions)
        </label>
      </div>

      <p className="text-sm mt-2 text-red-600">
        Note: You need to refresh the page after changing roles.
      </p>
    </div>
  );
}
