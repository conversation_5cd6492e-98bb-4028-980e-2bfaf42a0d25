/**
 * Security exports for Template Single CRUD
 *
 * This file exports all security-related configurations from all contacts.
 */

// Import permissions from contacts
import contact1Security from "../../(features)/management/lib/security";

// Export all permissions
const security = {
  // Contact 1 permissions
  contact1: contact1Security,

  // Combine all route permissions
  routePermissions: {
    ...contact1Security.routePermissions,
    // Add more contact route permissions here
  },
};

export default security;
