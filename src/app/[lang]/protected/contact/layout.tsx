import { ReactNode } from "react";
import { PageLayout } from "@/components/layouts/PageLayout";
import { i18n } from "@/lib/i18n/services/I18nService";

interface ContactLayoutProps {
  children: ReactNode;
  params: Promise<{
    lang: string;
  }>;
}

/**
 * Layout for the Contact domain
 * This is the main layout for the contact domain and wraps all pages
 */
export default async function ContactLayout({ children, params }: ContactLayoutProps) {
  // Get translations
  const { lang } = await params;
  const dictionary = i18n.getDictionary(lang);
  const contactDict = dictionary.navigation;

  return (
    <PageLayout
      title={contactDict.contacts}
      description={dictionary.contact?.management?.description}
    >
      {children}
    </PageLayout>
  );
}
