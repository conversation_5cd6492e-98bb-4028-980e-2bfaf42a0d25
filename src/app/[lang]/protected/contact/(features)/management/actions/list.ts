"use server";

import { logger } from "@/lib/logger/services/LoggerService";
import { ContactService } from "../lib/services/ContactService";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { FEATURE1_PERMISSIONS } from "../lib/security/permissions";
import { ActionState, errorActionState, successActionState } from "@/lib/types/responses";
import { ContactItem } from "../lib/types";

interface ListParams {
  page?: number;
  pageSize?: number;
  search?: string;
}

/**
 * List contacts with optional pagination and search
 * Handles both search and pagination in a unified way
 *
 * @param _prevState Previous state
 * @param params Object containing pagination and search parameters
 * @returns List of contacts with pagination info
 */
export const list = requirePermission(FEATURE1_PERMISSIONS.LIST)(async (
  _prevState: ActionState<{ items: ContactItem[]; total: number }>,
  params: ListParams = {}
): Promise<ActionState<{ items: ContactItem[]; total: number }>> => {
  try {
    // Get parameters with defaults
    const page = params.page || 1;
    const pageSize = params.pageSize || 10;
    const searchQuery = params.search || "";

    // Validate pagination parameters
    if (page < 1) {
      return errorActionState("Page number must be at least 1");
    }

    if (pageSize < 1 || pageSize > 100) {
      return errorActionState("Page size must be between 1 and 100");
    }

    // Call the appropriate service method based on whether search is provided
    let result;
    if (searchQuery) {
      // Search with the query and pagination
      result = await ContactService.search(searchQuery, page, pageSize);
    } else {
      // Get paginated list
      result = await ContactService.list(page, pageSize);
    }

    // Handle errors
    if (!result.success) {
      logger.error(`Error fetching contacts: ${result.error}`);
      return errorActionState(`Failed to fetch contacts: ${result.error}`);
    }

    // Return the paginated results
    return successActionState(result.data || { items: [], total: 0 });
  } catch (error) {
    logger.error(`Unexpected error listing contacts: ${error}`);
    return errorActionState(`An unexpected error occurred: ${error}`);
  }
});
