"use server";

import { revalidatePath } from "next/cache";
import { logger } from "@/lib/logger/services/LoggerService";
import { ContactService } from "../lib/services/ContactService";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { FEATURE1_PERMISSIONS } from "../lib/security/permissions";
import { ActionState, errorActionState } from "@/lib/types/responses";
import { ContactItem } from "../lib/types";
import { redirect } from "next/navigation";
import FEATURE_CONFIG from "../lib/config";

/**
 * Remove a contact (soft delete by setting status to inactive)
 * @param prevState Previous state
 * @param formData Form data
 * @returns Result of the remove operation
 */
export const remove = requirePermission(FEATURE1_PERMISSIONS.REMOVE)(async (
  _prevState: ActionState<ContactItem>,
  formData: FormData
): Promise<ActionState<ContactItem>> => {
  // Get form data
  const id = _prevState.data?.id;
  const lang = formData.get("lang") as string;
  try {
    // Validate required fields
    if (!id) {
      return errorActionState("Contact ID is required");
    }

    // Remove the contact
    const result = await ContactService.remove(id);

    if (!result.success) {
      logger.error(`Error removing contact: ${result.error}`);
      return errorActionState(`Failed to remove contact: ${result.message}`);
    }

    // Revalidate the contacts list page
    revalidatePath(`/${lang}${FEATURE_CONFIG.routes.LIST}`);
  } catch (error) {
    logger.error(`Unexpected error removing contact: ${error}`);
    return errorActionState(`An unexpected error occurred: ${error}`);
  }

  redirect(`/${lang}${FEATURE_CONFIG.routes.LIST}`);
});
