"use server";

import { logger } from "@/lib/logger/services/LoggerService";
import { ContactRelationshipService } from "@contact/management/lib/services/ContactRelationshipService";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { FEATURE1_PERMISSIONS } from "@contact/management/lib/security/permissions";
import { ActionState, errorActionState, successActionState } from "@/lib/types/responses";

import { RelationshipWithContact } from "@contact/management/lib/types/relationships";

interface ViewParams {
  id: string;
  contactId: string;
}

/**
 * View a relationship
 * @param _prevState Previous state
 * @param params Parameters for viewing a relationship
 * @returns The relationship
 */
export const view = requirePermission(FEATURE1_PERMISSIONS.VIEW)(async (
  _prevState: ActionState<RelationshipWithContact>,
  params: ViewParams
): Promise<ActionState<RelationshipWithContact>> => {
  try {
    const { id, contactId } = params;

    // Validate required fields
    if (!id) {
      return errorActionState("Relationship ID is required");
    }

    // Get the relationship
    const result = await ContactRelationshipService.view(id);

    if (!result.success) {
      logger.error(`Error retrieving relationship: ${result.error}`);
      return errorActionState(`Failed to retrieve relationship: ${result.error}`);
    }

    if (!result.data) {
      return errorActionState("Relationship data is missing");
    }

    // Determine if this contact is the subject or related contact
    const isSubject = result.data.subject_contact_id === contactId;

    // The service already fetched both contacts, so we just need to pick the right one
    // We need to use 'as any' because the RelationshipWithContact type doesn't include these properties
    const relatedContact = isSubject
      ? (result.data as any).related_contact
      : (result.data as any).subject_contact;

    // Return enhanced relationship
    const enhancedRelationship: RelationshipWithContact = {
      id: result.data.id,
      organization_id: result.data.organization_id,
      subject_contact_id: result.data.subject_contact_id,
      related_contact_id: result.data.related_contact_id,
      relationship: result.data.relationship,
      status: result.data.status,
      created_at: result.data.created_at,
      updated_at: result.data.updated_at,
      relatedContact: relatedContact
        ? {
            id: relatedContact.id,
            name: relatedContact.name,
            status: relatedContact.status,
            email: relatedContact.email,
            phone: relatedContact.phone,
            address: relatedContact.address,
          }
        : undefined,
      // If this contact is the related contact, we need to invert the relationship type
      // for display purposes (e.g., if A is parent of B, then B is child of A)
      inverseRelationship: !isSubject ? result.data.relationship : undefined,
    };

    return successActionState(enhancedRelationship);
  } catch (error) {
    logger.error(`Unexpected error retrieving relationship: ${error}`);
    return errorActionState(`Unexpected error retrieving relationship`);
  }
});
