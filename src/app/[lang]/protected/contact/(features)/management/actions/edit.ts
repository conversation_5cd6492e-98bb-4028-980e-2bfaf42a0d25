"use server";

import { revalidatePath } from "next/cache";
import { logger } from "@/lib/logger/services/LoggerService";
import { ContactService } from "../lib/services/ContactService";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { FEATURE1_PERMISSIONS } from "../lib/security/permissions";
import { ActionState, errorActionState, successActionState } from "@/lib/types/responses";
import { ContactItem } from "../lib/types";
import { auth } from "@/lib/authentication/services/AuthenticationService";
import { redirect } from "next/navigation";
import FEATURE_CONFIG from "../lib/config";

/**
 * Update an existing contact
 * @param prevState Previous state
 * @param formData Form data
 * @returns Result of the update operation
 */
export const edit = requirePermission(FEATURE1_PERMISSIONS.EDIT)(async (
  _prevState: ActionState<ContactItem>,
  formData: FormData
): Promise<ActionState<ContactItem>> => {
  const profile = await auth.getCurrentUserProfile();
  const lang = profile?.language;
  const id = _prevState.data?.id;

  try {
    const name = formData.get("name") as string;
    const email: ContactItem["email"] = {};
    const emailPersonal = formData.get("email_personal") as string;
    const emailWork = formData.get("email_work") as string;
    if (emailPersonal) email.personal = emailPersonal;
    if (emailWork) email.work = emailWork;
    const phone: ContactItem["phone"] = {};
    const phoneMobile = formData.get("phone_mobile") as string;
    const phoneHome = formData.get("phone_home") as string;
    if (phoneMobile) phone.mobile = phoneMobile;
    if (phoneHome) phone.home = phoneHome;
    const address = formData.get("address") as string;

    // Validate required fields
    if (!id) {
      return errorActionState("Contact ID is required");
    }

    if (!name) {
      return errorActionState("Name is required");
    }

    // Update the contact
    const result = await ContactService.edit(id, {
      name,
      email,
      phone,
      address: address || null,
      updated_at: new Date().toISOString(),
    });

    if (!result.success) {
      logger.error(`Error updating contact: ${result.error}`);
      return errorActionState(`Failed to update contact: ${result.error}`);
    }

    // Revalidate the contact pages
    revalidatePath(`/${lang}${FEATURE_CONFIG.routes.LIST}`);
    revalidatePath(`/${lang}${FEATURE_CONFIG.routes.VIEW(id)}`);
  } catch (error) {
    logger.error(`Unexpected error updating contact: ${error}`);
    return errorActionState(`An unexpected error occurred: ${error}`);
  }

  redirect(`/${lang}${FEATURE_CONFIG.routes.LIST}`);

  return successActionState();
});
