"use server";

import { createClient } from "@/lib/supabase/server";
import { logger } from "@/lib/logger/services/LoggerService";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { FEATURE1_PERMISSIONS } from "../lib/security/permissions";
import {
  HistoryItem,
  HistoryAction,
  HistoryChange,
} from "@/components/ui/history-timeline/history-timeline";

/**
 * Fetch history for a specific contact
 * @param contactId Contact ID
 * @returns Array of history items formatted for the HistoryTimeline component
 */
export const fetchContactHistory = requirePermission(FEATURE1_PERMISSIONS.VIEW)(async (
  contactId: string
): Promise<HistoryItem[]> => {
  try {
    const supabase = await createClient();

    // Get contact history
    const { data: historyData, error } = await supabase
      .from("contact_history")
      .select(
        `
        id,
        user_id,
        action,
        changes,
        created_at
      `
      )
      .eq("contact_id", contactId)
      .order("created_at", { ascending: false });

    if (error) {
      logger.error(`Error fetching contact history: ${error.message}`);
      return [];
    }

    // Get user profiles for the user IDs
    const userIds = historyData.map((item) => item.user_id).filter(Boolean);
    let userData: Record<string, { id: string; name: string; avatar_url?: string }> = {};

    if (userIds.length > 0) {
      const { data: profiles } = await supabase
        .from("user_profiles")
        .select("id, first_name, last_name")
        .in("id", userIds);

      if (profiles) {
        userData = profiles.reduce(
          (acc, profile) => {
            acc[profile.id] = {
              id: profile.id,
              name: `${profile.first_name} ${profile.last_name}`.trim(),
            };
            return acc;
          },
          {} as Record<string, { id: string; name: string; avatar_url?: string }>
        );
      }
    }

    // Format the data to match the HistoryTimeline component's expected structure
    return historyData.map((item) => {
      // Map the database action to the HistoryAction type
      const action = mapActionType(item.action);

      // Extract and format changes
      const changes = extractChanges(item.changes as Record<string, unknown>);

      // Create a summary based on the action type
      const summary = createSummary(item.action, changes);

      return {
        id: item.id,
        timestamp: item.created_at || new Date().toISOString(),
        user: {
          id: item.user_id,
          name: userData[item.user_id]?.name || "Unknown User",
          avatarUrl: userData[item.user_id]?.avatar_url,
        },
        action,
        changes,
        summary,
      };
    });
  } catch (error) {
    logger.error(`Unexpected error fetching contact history: ${error}`);
    return [];
  }
});

/**
 * Maps the database action type to the HistoryAction type
 */
function mapActionType(action: string): HistoryAction {
  switch (action) {
    case "INSERT":
      return "create";
    case "UPDATE":
      return "update";
    case "DELETE":
      return "delete";
    default:
      return "update";
  }
}

/**
 * Extracts changes from the database format to the HistoryChange format
 */
function extractChanges(changes: Record<string, unknown>): HistoryChange[] | undefined {
  if (!changes || Object.keys(changes).length === 0) {
    return undefined;
  }

  return Object.entries(changes).map(([field, value]) => {
    if (typeof value === "object" && value !== null) {
      const changeObj = value as { old: unknown; new: unknown };
      return {
        field,
        oldValue: formatValue(changeObj.old),
        newValue: formatValue(changeObj.new),
      };
    }

    return {
      field,
      newValue: formatValue(value),
    };
  });
}

/**
 * Formats a value for display
 */
function formatValue(value: unknown): string | null {
  if (value === null || value === undefined) {
    return null;
  }

  if (typeof value === "object") {
    return JSON.stringify(value);
  }

  return String(value);
}

/**
 * Creates a summary based on the action type and changes
 */
function createSummary(action: string, changes?: HistoryChange[]): string {
  switch (action) {
    case "INSERT":
      return "Contact created";
    case "DELETE":
      return "Contact deleted";
    case "UPDATE":
      if (changes && changes.length > 0) {
        const fieldNames = changes.map((c) => c.field).join(", ");
        return `Updated ${fieldNames}`;
      }
      return "Contact updated";
    default:
      return "Contact modified";
  }
}
