"use server";

import { revalidatePath } from "next/cache";
import { logger } from "@/lib/logger/services/LoggerService";
import { ContactRelationshipService } from "@contact/management/lib/services/ContactRelationshipService";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { FEATURE1_PERMISSIONS } from "@contact/management/lib/security/permissions";
import { ActionState, errorActionState } from "@/lib/types/responses";
import { ContactRelationship } from "@contact/management/lib/types";
import { redirect } from "next/navigation";

/**
 * Remove a relationship (soft delete by setting status to inactive)
 * @param _prevState Previous state
 * @param formData Form data
 * @returns Result of the remove operation
 */
export const remove = requirePermission(FEATURE1_PERMISSIONS.REMOVE)(async (
  _prevState: ActionState<ContactRelationship>,
  formData: FormData
): Promise<ActionState<ContactRelationship>> => {
  // Get form data
  const id = formData.get("id") as string;
  const contactId = formData.get("contactId") as string;
  const lang = formData.get("lang") as string;

  try {
    // Validate required fields
    if (!id) {
      return errorActionState("Relationship ID is required");
    }

    // Remove the relationship
    const result = await ContactRelationshipService.remove(id);

    if (!result.success) {
      logger.error(`Error removing relationship: ${result.error}`);
      return errorActionState(`Failed to remove relationship: ${result.error}`);
    }

    // Revalidate the relationships list page
    revalidatePath(`/${lang}/protected/contact/management/${contactId}/view/relationships`);
  } catch (error) {
    logger.error(`Unexpected error removing relationship: ${error}`);
    return errorActionState(`Unexpected error removing relationship`);
  }
  // Redirect to the relationships list page
  redirect(`/${lang}/protected/contact/management/${contactId}/view/relationships`);
});
