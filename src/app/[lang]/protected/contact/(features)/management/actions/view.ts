"use server";

import { logger } from "@/lib/logger/services/LoggerService";
import { ContactService } from "../lib/services/ContactService";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { FEATURE1_PERMISSIONS } from "../lib/security/permissions";
import { ActionState, errorActionState, successActionState } from "@/lib/types/responses";
import { ContactItem } from "../lib/types";

interface ViewParams {
  id: string;
}

/**
 * Get a contact by ID
 * @param _prevState Previous state
 * @param params Object containing the contact ID
 * @returns The contact
 */
export const view = requirePermission(FEATURE1_PERMISSIONS.VIEW)(async (
  _prevState: ActionState<ContactItem>,
  params: ViewParams
): Promise<ActionState<ContactItem>> => {
  try {
    const { id } = params;

    // Validate required fields
    if (!id) {
      return errorActionState("Contact ID is required");
    }

    // Get the contact
    const result = await ContactService.view(id);

    if (!result.success) {
      logger.error(`Error retrieving contact: ${result.error}`);
      return errorActionState(`Failed to retrieve contact: ${result.error}`);
    }

    // Return the contact
    return successActionState(result.data);
  } catch (error) {
    logger.error(`Unexpected error retrieving contact: ${error}`);
    return errorActionState(`An unexpected error occurred: ${error}`);
  }
});
