"use server";

import { revalidatePath } from "next/cache";
import { logger } from "@/lib/logger/services/LoggerService";
import { ContactRelationshipService } from "@contact/management/lib/services/ContactRelationshipService";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { FEATURE1_PERMISSIONS } from "@contact/management/lib/security/permissions";
import { redirect } from "next/navigation";
import { ActionState, errorActionState } from "@/lib/types/responses";
import { ContactRelationship } from "@contact/management/lib/types";
import { auth } from "@/lib/authentication/services/AuthenticationService";

/**
 * Create a new relationship
 * @param _prevState Previous state
 * @param formData Form data
 * @returns Result of the create operation
 */
export const create = requirePermission(FEATURE1_PERMISSIONS.CREATE)(async (
  _prevState: ActionState<ContactRelationship>,
  formData: FormData
): Promise<ActionState<ContactRelationship>> => {
  const lang = formData.get("lang") as string;
  const contactId = formData.get("contactId") as string;
  try {
    // Get form data

    const relatedContactId = formData.get("relatedContactId") as string;
    const relationship = formData.get("relationship") as string;

    // Get organization ID from auth

    const organizationId = await auth.getCurrentUserOrganizationId();

    // Debug form data
    logger.verbose("Form data received:", {
      contactId,
      relatedContactId,
      relationship,
      organizationId,
    });

    // Validate required fields
    if (!contactId) {
      logger.verbose("Missing contactId");
      return errorActionState("Contact ID is required");
    }

    if (!relatedContactId) {
      logger.verbose("Missing relatedContactId");
      return errorActionState("Related contact is required");
    }

    if (!relationship) {
      logger.verbose("Missing relationship");
      return errorActionState("Relationship type is required");
    }

    if (!organizationId) {
      logger.verbose("Missing organizationId");
      return errorActionState("Organization ID is required");
    }

    // Create the relationship
    const result = await ContactRelationshipService.create({
      subject_contact_id: contactId,
      related_contact_id: relatedContactId,
      relationship,
      organization_id: organizationId,
      status: "active",
    });

    if (!result.success) {
      logger.error(`Error creating relationship: ${result.error}`);
      return errorActionState(`Failed to create relationship: ${result.error}`);
    }

    // Revalidate the relationships list page
    revalidatePath(`/${lang}/protected/contact/management/${contactId}/relationships/list`);
  } catch (error) {
    logger.error(`Unexpected error creating relationship: ${error}`);
    return errorActionState(`Unexpected error creating relationship`);
  }
  // Redirect to the relationships list page
  redirect(`/${lang}/protected/contact/management/${contactId}/view/relationships`);
});
