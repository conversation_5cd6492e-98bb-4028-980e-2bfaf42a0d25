"use server";

import { logger } from "@/lib/logger/services/LoggerService";
import { ContactRelationshipService } from "@contact/management/lib/services/ContactRelationshipService";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { FEATURE1_PERMISSIONS } from "@contact/management/lib/security/permissions";
import { ActionState, errorActionState, successActionState } from "@/lib/types/responses";
import { RelationshipWithContact } from "@contact/management/lib/types/relationships";

interface ListParams {
  contactId: string;
  page?: number;
  pageSize?: number;
}

/**
 * List relationships for a contact
 * @param _prevState Previous state
 * @param params Parameters for listing relationships
 * @returns List of relationships
 */
export const list = requirePermission(FEATURE1_PERMISSIONS.VIEW)(async (
  _prevState: ActionState<{ items: RelationshipWithContact[]; total: number }>,
  params: ListParams
): Promise<ActionState<{ items: RelationshipWithContact[]; total: number }>> => {
  try {
    const { contactId, page = 1, pageSize = 10 } = params;

    // Validate required fields
    if (!contactId) {
      return errorActionState("Contact ID is required");
    }

    // Get the relationships with related contact information (already joined in the service)
    const result = await ContactRelationshipService.listForContact(contactId, page, pageSize);

    if (!result.success) {
      logger.error(`Error retrieving relationships: ${result.error}`);
      return errorActionState(`Failed to retrieve relationships: ${result.error}`);
    }

    // Return the result directly since it already contains the enhanced items
    return successActionState({
      items: result.data?.items || [],
      total: result.data?.total || 0,
    });
  } catch (error) {
    logger.error(`Unexpected error listing relationships: ${error}`);
    return errorActionState(`Unexpected error listing relationships`);
  }
});
