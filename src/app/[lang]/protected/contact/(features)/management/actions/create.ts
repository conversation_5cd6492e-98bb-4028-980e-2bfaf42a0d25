"use server";

import { revalidatePath } from "next/cache";
import { logger } from "@/lib/logger/services/LoggerService";
import { ContactService } from "../lib/services/ContactService";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { FEATURE1_PERMISSIONS } from "../lib/security/permissions";
import { redirect } from "next/navigation";
import { ActionState, errorActionState } from "@/lib/types/responses";
import { ContactItem } from "../lib/types";
import { auth } from "@/lib/authentication/services/AuthenticationService";
import FEATURE_CONFIG from "../lib/config";

/**
 * Create a new contact
 * @param prevState Previous state
 * @param formData Form data
 * @returns Result of the create operation
 */
export const create = requirePermission(FEATURE1_PERMISSIONS.CREATE)(async (
  _prevState: ActionState<ContactItem>,
  formData: FormData
): Promise<ActionState<ContactItem>> => {
  let newContact: ContactItem;
  const profile = await auth.getCurrentUserProfile();
  const organizationId = profile?.organizationId;
  const lang = profile?.language;

  try {
    // Get form data
    const name = formData.get("name") as string;
    const email: ContactItem["email"] = {
      personal: formData.get("email_personal") as string,
      work: formData.get("email_work") as string,
    };
    const phone: ContactItem["phone"] = {
      mobile: formData.get("phone_mobile") as string,
      home: formData.get("phone_home") as string,
    };
    const address = formData.get("address") as string;

    // Validate required fields
    if (!name) {
      return errorActionState("Name is required");
    }

    if (!organizationId) {
      return errorActionState("Organization ID is required");
    }

    // Create the contact
    const result = await ContactService.create({
      name,
      status: "active",
      email,
      phone,
      address: address || null,
      organization_id: organizationId,
    });

    if (!result.success) {
      logger.error(`Error creating contact: ${result.error}`);
      return errorActionState(`An error occurred while creating the contact: ${result.message}`);
    }

    newContact = result.data as ContactItem;

    // Revalidate the contacts list page
    revalidatePath(`/${lang}${FEATURE_CONFIG.routes.LIST}`);
  } catch (error) {
    logger.error(`Unexpected error creating contact: ${error}`);
    return errorActionState(`An unexpected error occurred: ${error}`);
  }

  redirect(`/${lang}${FEATURE_CONFIG.routes.VIEW(newContact.id)}`);
});
