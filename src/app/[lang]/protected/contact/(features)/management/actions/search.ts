"use server";

import { ContactService } from "@contact/management/lib/services/ContactService";
import { ContactSearchItem, SearchState } from "@contact/management/lib/types";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import FEATURE_CONFIG from "@contact/management/lib/config/feature";
import { logger } from "@/lib/logger/services/LoggerService";

/**
 * Search for contacts by name
 * This action is used by the autocomplete component to search for contacts as the user types
 */
export const searchContacts = requirePermission(FEATURE_CONFIG.permissions.VIEW)(async (
  _prevState: SearchState,
  searchTerm: string
): Promise<SearchState> => {
  "use server";

  // Return empty results if search term is too short
  if (!searchTerm || searchTerm.trim().length < 2) {
    return {
      items: [],
      loading: false,
      query: searchTerm,
    };
  }

  try {
    const response = await ContactService.search(searchTerm);

    if (!response.success) {
      logger.error(`Error searching contacts: ${response.error}`);
      return {
        items: [],
        loading: false,
        query: searchTerm,
      };
    }

    return {
      items: response.data?.items || [],
      loading: false,
      query: searchTerm,
    };
  } catch (error) {
    logger.error(`Error searching contacts: ${error}`);
    return {
      items: [],
      loading: false,
      query: searchTerm,
    };
  }
});

/**
 * Search for contacts and return them in the format expected by the autocomplete component
 */
export async function searchContactItems(query: string): Promise<ContactSearchItem[]> {
  "use server";

  logger.verbose(`searchContactItems called with query: ${query}`);

  // Return empty results if search term is too short
  if (!query || query.trim().length < 2) {
    logger.verbose("Query too short, returning empty results");
    return [];
  }

  try {
    const response = await ContactService.search(query);
    logger.verbose("Search response received");

    if (!response.success) {
      logger.error(`Error searching contacts: ${response.error}`);
      return [];
    }

    // Map contacts to the format expected by the autocomplete component
    const results = (response.data?.items || []).map((contact) => ({
      value: contact.id,
      label: contact.name,
    }));

    logger.verbose(`Found ${results.length} matching contacts`);
    return results;
  } catch (error) {
    logger.error(`Error searching contacts: ${error}`);
    return [];
  }
}
