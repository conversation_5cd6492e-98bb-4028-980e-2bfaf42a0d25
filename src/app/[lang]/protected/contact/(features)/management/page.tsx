import { redirect } from "next/navigation";
import FEATURE_CONFIG from "./lib/config";

interface ContactPageProps {
  params: Promise<{
    lang: string;
  }>;
}

/**
 * Default page for Contact 1 in Template Single CRUD
 * Redirects to the list page
 */
export default async function ContactPage({ params }: ContactPageProps) {
  // Await the params
  const resolvedParams = await params;

  // Redirect to the list page
  redirect(`/${resolvedParams.lang}${FEATURE_CONFIG.routes.LIST}`);
}
