import { List } from "../../components/List";
import { list } from "../../actions/list";
import { i18n } from "@/lib/i18n/services/I18nService";
import { DOMAIN_ID } from "../../../../lib/config/domain";
import { FEATURE_ID } from "../../lib/config/feature";

interface ListPageProps {
  params: Promise<{
    lang: string;
  }>;
  searchParams?: Promise<{
    page?: string;
    pageSize?: string;
    search?: string;
  }>;
}

/**
 * List page for Contact 1 items
 * Fetches data using the server action and passes it to the List component
 */
export default async function ListPage({ params, searchParams }: ListPageProps) {
  // Await the params
  const resolvedParams = await params;
  const resolvedSearchParams = (await searchParams) || {};

  // Parse search parameters
  const page = resolvedSearchParams.page ? parseInt(resolvedSearchParams.page, 10) : 1;
  const pageSize = resolvedSearchParams.pageSize ? parseInt(resolvedSearchParams.pageSize, 10) : 10;
  const search = resolvedSearchParams.search || "";

  // Create initial state
  const initialState = {
    success: true,
    error: "",
    data: { items: [], total: 0 },
  };

  // Call the server action to get the data with all parameters
  const state = await list(initialState, { page, pageSize, search });

  // Extract items and total count
  const items = state.data?.items || [];
  const totalItems = state.data?.total || 0;

  // Get the dictionary
  const dictionary = i18n.getDomainFeatureDictionary(resolvedParams.lang, DOMAIN_ID, FEATURE_ID);

  // Render the List component with the data
  return (
    <List
      lang={resolvedParams.lang}
      initialItems={items}
      totalItems={totalItems}
      currentPage={page}
      search={search}
      dictionary={dictionary}
    />
  );
}
