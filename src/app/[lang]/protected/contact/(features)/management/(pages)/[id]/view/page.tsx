import { redirect } from "next/navigation";

interface ViewPageProps {
  params: Promise<{
    lang: string;
    id: string;
  }>;
}

/**
 * View page for a specific Contact item
 * Redirects to the history page which is now the default tab
 */
export default async function ViewPage({ params }: ViewPageProps) {
  const { lang, id } = await params;

  // Redirect to the history page
  redirect(`/${lang}/protected/contact/management/${id}/view/history`);
}
