import { Skeleton } from "@/components/ui/skeleton";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ead<PERSON> } from "@/components/ui/card";

/**
 * Loading state for the Remove page
 */
export default function RemoveLoading() {
  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="text-center">
        <Skeleton className="h-8 w-48 mx-auto mb-2" />
        <Skeleton className="h-4 w-64 mx-auto" />
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Item details skeleton */}
        <div className="border rounded-md p-4">
          <Skeleton className="h-6 w-48 mb-2" />
          <Skeleton className="h-4 w-full mb-2" />
          <Skeleton className="h-4 w-full mb-2" />
          <div className="mt-2">
            <Skeleton className="h-6 w-16 rounded-full" />
          </div>
        </div>

        {/* Warning message skeleton */}
        <Skeleton className="h-16 w-full rounded-md" />
      </CardContent>

      <CardFooter className="flex justify-between">
        <Skeleton className="h-10 w-20" />
        <Skeleton className="h-10 w-24" />
      </CardFooter>
    </Card>
  );
}
