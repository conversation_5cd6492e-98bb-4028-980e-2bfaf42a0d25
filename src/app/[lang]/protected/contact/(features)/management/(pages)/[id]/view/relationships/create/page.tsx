import { CreateForm } from "@/app/[lang]/protected/contact/(features)/management/components/relationships";
import { i18n } from "@/lib/i18n/services/I18nService";
import { DOMAIN_ID } from "@contact/lib/config/domain";
import { FEATURE_ID } from "@contact/management/lib/config/feature";
import { RelationshipModal } from "../../../../../components";
import { list } from "@contact/management/actions/list";
import { ActionState } from "@/lib/types/responses";
import { ContactItem } from "@contact/management/lib/types";

interface CreatePageProps {
  params: Promise<{
    lang: string;
    id: string;
  }>;
}

/**
 * Create relationship page
 * Rendered as a modal over the relationships list
 */
export default async function CreatePage({ params }: CreatePageProps) {
  const { lang, id } = await params;

  // Get the dictionary
  const dictionary = i18n.getDomainFeatureDictionary(lang, DOMAIN_ID, FEATURE_ID);
  const relationshipDict = dictionary.relationships || {};

  // Fetch contacts for the dropdown
  const initialState: ActionState<{ items: ContactItem[]; total: number }> = {
    success: true,
    error: "",
    data: { items: [], total: 0 },
  };
  const contactsState = await list(initialState, {});
  const contacts = contactsState.data?.items || [];

  return (
    <RelationshipModal>
      <CreateForm lang={lang} contactId={id} contacts={contacts} dictionary={relationshipDict} />
    </RelationshipModal>
  );
}
