import { fetchContactHistory } from "../../../../actions/history";
import { HistoryTimeline } from "@/components/ui/history-timeline/history-timeline";
import { i18n } from "@/lib/i18n/services/I18nService";
import { DOMAIN_ID } from "@contact/lib/config/domain";
import { FEATURE_ID } from "../../../../lib/config/feature";
import { TabsContent } from "@/components/ui/tabs";

interface HistoryPageProps {
  params: Promise<{
    lang: string;
    id: string;
  }>;
}

/**
 * History page for a contact
 * Displays the contact's history timeline
 */
export default async function HistoryPage({ params }: HistoryPageProps) {
  const { lang, id } = await params;

  // Fetch contact history (already in the format expected by HistoryTimeline)
  const historyItems = await fetchContactHistory(id);

  // Get the dictionary
  const dictionary = i18n.getDomainFeatureDictionary(lang, DOMAIN_ID, FEATURE_ID);

  return (
    <TabsContent value="history" className="mt-4">
      <div className="h-full">
        <HistoryTimeline
          items={historyItems}
          title={dictionary.history.title}
          showTitle={true}
          className="h-full"
        />
      </div>
    </TabsContent>
  );
}
