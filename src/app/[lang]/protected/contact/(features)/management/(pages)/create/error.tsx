"use client";

import { ErrorDisplay } from "@/components/ui/error-display";

/**
 * Error component for the Create page
 */
export default function CreateError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  return (
    <ErrorDisplay
      title="Error Creating Item"
      message="There was a problem loading the create form."
      error={error}
      reset={reset}
    />
  );
}
