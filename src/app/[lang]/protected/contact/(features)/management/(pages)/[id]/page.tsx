import { redirect } from "next/navigation";

interface PageProps {
  params: Promise<{
    lang: string;
    id: string;
  }>;
}

/**
 * Main page for a contact
 * Redirects to the view/history page
 */
export default async function Page({ params }: PageProps) {
  const { lang, id } = await params;

  // Redirect to the view/history page
  redirect(`/${lang}/protected/contact/management/${id}/view/history`);
}
