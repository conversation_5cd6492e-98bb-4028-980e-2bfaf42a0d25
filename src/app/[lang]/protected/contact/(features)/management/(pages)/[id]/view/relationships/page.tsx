import { list } from "../../../../actions/relationships/list";
import { List } from "../../../../components/relationships";
import { ActionState } from "@/lib/types/responses";
import { RelationshipWithContact } from "@contact/management/lib/types/relationships";
import { i18n } from "@/lib/i18n/services/I18nService";
import { DOMAIN_ID } from "@contact/lib/config/domain";
import { FEATURE_ID } from "@contact/management/lib/config/feature";
import { TabsContent } from "@/components/ui/tabs";

interface RelationshipsPageProps {
  params: Promise<{
    lang: string;
    id: string;
  }>;
}

/**
 * Relationships page for a contact
 * Displays the contact's relationships
 */
export default async function RelationshipsPage({ params }: RelationshipsPageProps) {
  const { lang, id } = await params;

  // Create initial state
  const initialState: ActionState<{ items: RelationshipWithContact[]; total: number }> = {
    success: true,
    error: "",
    data: { items: [], total: 0 },
  };

  // Fetch the relationships using the server action
  const state = await list(initialState, { contactId: id });

  // Get the dictionary
  const dictionary = i18n.getDomainFeatureDictionary(lang, DOMAIN_ID, FEATURE_ID);
  const relationshipDict = dictionary.relationships || {};

  // Render the List component with the relationships
  return (
    <TabsContent value="relationships" className="mt-4">
      <List
        lang={lang}
        contactId={id}
        items={state.data?.items || []}
        totalItems={state.data?.total || 0}
        dictionary={relationshipDict}
      />
    </TabsContent>
  );
}
