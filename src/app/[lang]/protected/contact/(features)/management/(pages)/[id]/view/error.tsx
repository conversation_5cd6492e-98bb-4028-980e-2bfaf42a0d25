"use client";

import { ErrorDisplay } from "@/components/ui/error-display";

/**
 * Error component for the View page
 */
export default function ViewError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  return (
    <ErrorDisplay
      title="Error Viewing Item"
      message="There was a problem loading the item details."
      error={error}
      reset={reset}
    />
  );
}
