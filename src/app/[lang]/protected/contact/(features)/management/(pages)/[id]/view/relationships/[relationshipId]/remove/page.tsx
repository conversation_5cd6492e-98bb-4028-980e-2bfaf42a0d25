import { RemoveForm } from "@/app/[lang]/protected/contact/(features)/management/components/relationships";
import { view } from "@/app/[lang]/protected/contact/(features)/management/actions/relationships/view";
import { notFound } from "next/navigation";
import { ActionState } from "@/lib/types/responses";
import { RelationshipWithContact } from "@contact/management/lib/types/relationships";
import { i18n } from "@/lib/i18n/services/I18nService";
import { DOMAIN_ID } from "@contact/lib/config/domain";
import { FEATURE_ID } from "@contact/management/lib/config/feature";
import { RelationshipModal } from "../../../../../../components";

interface RemovePageProps {
  params: Promise<{
    lang: string;
    id: string;
    relationshipId: string;
  }>;
}

/**
 * Remove relationship page
 * Rendered as a modal over the relationships list
 */
export default async function RemovePage({ params }: RemovePageProps) {
  const { lang, id, relationshipId } = await params;

  // Create initial state
  const initialState: ActionState<RelationshipWithContact> = {
    success: true,
    error: "",
    data: null,
  };

  // Fetch the relationship using the server action
  const state = await view(initialState, { id: relationshipId, contactId: id });

  // If the relationship is not found, show the not-found page
  if (!state.success || !state.data) {
    notFound();
  }

  // Get the dictionary
  const dictionary = i18n.getDomainFeatureDictionary(lang, DOMAIN_ID, FEATURE_ID);
  const relationshipDict = dictionary.relationships || {};

  return (
    <RelationshipModal>
      <RemoveForm
        lang={lang}
        contactId={id}
        relationship={state.data}
        dictionary={relationshipDict}
      />
    </RelationshipModal>
  );
}
