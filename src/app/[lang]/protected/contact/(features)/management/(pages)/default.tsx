import { redirect } from "next/navigation";
import FEATURE_CONFIG from "../lib/config";

interface ContactDefaultPageProps {
  params: Promise<{
    lang: string;
  }>;
}

/**
 * Default page for Contact 1 pages in Template Single CRUD
 * Redirects to the list page
 */
export default async function ContactDefaultPage({ params }: ContactDefaultPageProps) {
  // Await the params
  const resolvedParams = await params;

  // Redirect to the list page
  redirect(`/${resolvedParams.lang}${FEATURE_CONFIG.routes.LIST}`);
}
