"use client";

import { ErrorDisplay } from "@/components/ui/error-display";
import { RelationshipModal } from "../../../../../components";

/**
 * Error component for create relationship page
 */
export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  return (
    <RelationshipModal title="Error">
      <ErrorDisplay error={error} reset={reset} />
    </RelationshipModal>
  );
}
