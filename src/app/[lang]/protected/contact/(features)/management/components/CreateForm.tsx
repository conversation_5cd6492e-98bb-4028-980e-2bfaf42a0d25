"use client";

import { useActionState } from "react";
import { create } from "../actions";
import { ActionState } from "@/lib/types/responses";
import { ContactItem, FeatureDictionary } from "../lib/types";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { FormLayout } from "@/components/layouts";
import Link from "next/link";
import { Separator } from "@/components/ui/separator";

interface CreateFormProps {
  dictionary: FeatureDictionary;
  lang?: string;
}

/**
 * Form for creating a new contact
 * Uses the standardized FormLayout component for consistent UI
 */
export function CreateForm({ dictionary }: CreateFormProps) {
  // Use React's useActionState to manage the form state with the server action
  const initialState: ActionState<ContactItem> = {
    success: true,
    error: "",
    data: null,
  };

  const [state, formAction, pending] = useActionState(create, initialState);

  // Form actions (buttons) with better spacing
  const actions = (
    <div className="flex justify-between w-full pt-4">
      <Button variant="outline" asChild>
        <Link href="..">{dictionary.actions?.cancel || "Cancel"}</Link>
      </Button>
      <Button
        type="submit"
        disabled={pending}
        onClick={() => {
          try {
            // Update hidden JSON fields before submission
            const emailPersonal = (
              document.getElementById("email_personal") as HTMLInputElement
            )?.value?.trim();
            const emailWork = (
              document.getElementById("email_work") as HTMLInputElement
            )?.value?.trim();
            const phoneMobile = (
              document.getElementById("phone_mobile") as HTMLInputElement
            )?.value?.trim();
            const phoneHome = (
              document.getElementById("phone_home") as HTMLInputElement
            )?.value?.trim();

            const emailField = document.getElementById("email") as HTMLInputElement;
            const phoneField = document.getElementById("phone") as HTMLInputElement;

            // Only include non-empty values in the JSON objects
            const emailJson: Record<string, string> = {};
            if (emailPersonal) emailJson.personal = emailPersonal;
            if (emailWork) emailJson.work = emailWork;

            const phoneJson: Record<string, string> = {};
            if (phoneMobile) phoneJson.mobile = phoneMobile;
            if (phoneHome) phoneJson.home = phoneHome;

            // Set the hidden field values
            emailField.value = Object.keys(emailJson).length > 0 ? JSON.stringify(emailJson) : "";
            phoneField.value = Object.keys(phoneJson).length > 0 ? JSON.stringify(phoneJson) : "";
          } catch (error) {
            console.error("Error preparing form data:", error);
          }
        }}
      >
        {pending ? dictionary.actions?.creating : dictionary.actions?.create}
      </Button>
    </div>
  );

  return (
    <FormLayout
      title={dictionary.createTitle}
      description={dictionary.createDescription}
      formAction={formAction}
      error={!state.success ? state.error : undefined}
      actions={actions}
      className="max-w-2xl mx-auto"
    >
      {/* Hidden fields for JSON data */}
      <input type="hidden" id="email" name="email" />
      <input type="hidden" id="phone" name="phone" />

      {/* Basic Information Section */}
      <div className="space-y-4">
        <div>
          <h3 className="text-base font-medium mb-2">Basic Information</h3>
          <Separator className="mb-4" />
        </div>

        {/* Name field */}
        <div className="space-y-2">
          <Label htmlFor="name">{dictionary.fields?.name}</Label>
          <Input
            id="name"
            name="name"
            placeholder={dictionary.fields?.placeholders?.name}
            required
            aria-invalid={!state.success ? "true" : "false"}
          />
        </div>

        {/* Address field */}
        <div className="space-y-2">
          <Label htmlFor="address">{dictionary.fields?.address}</Label>
          <Textarea
            id="address"
            name="address"
            placeholder={dictionary.fields?.placeholders?.address}
            rows={3}
          />
        </div>
      </div>

      {/* Contact Information Section */}
      <div className="space-y-4 mt-8">
        <div>
          <h3 className="text-base font-medium mb-2">Contact Information</h3>
          <Separator className="mb-4" />
        </div>

        {/* Email fields */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="email_personal">{dictionary.fields?.emailPersonal}</Label>
            <Input
              id="email_personal"
              name="email_personal"
              type="email"
              placeholder={dictionary.fields?.placeholders?.emailPersonal}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="email_work">{dictionary.fields?.emailWork}</Label>
            <Input
              id="email_work"
              name="email_work"
              type="email"
              placeholder={dictionary.fields?.placeholders?.emailWork}
            />
          </div>
        </div>

        {/* Phone fields */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="phone_mobile">{dictionary.fields?.phoneMobile}</Label>
            <Input
              id="phone_mobile"
              name="phone_mobile"
              placeholder={dictionary.fields?.placeholders?.phoneMobile}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="phone_home">{dictionary.fields?.phoneHome}</Label>
            <Input
              id="phone_home"
              name="phone_home"
              placeholder={dictionary.fields?.placeholders?.phoneHome}
            />
          </div>
        </div>
      </div>
    </FormLayout>
  );
}
