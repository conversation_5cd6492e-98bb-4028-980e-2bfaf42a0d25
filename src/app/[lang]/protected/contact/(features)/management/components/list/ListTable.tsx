"use client";

import { Table, TableBody, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { ContactItem, FeatureDictionary } from "../../lib/types";
import { ListItem } from "./ListItem";

interface ListTableProps {
  items: ContactItem[];
  lang: string;
  dictionary: FeatureDictionary;
}

/**
 * Table component for displaying contacts
 */
export function ListTable({ items, lang, dictionary }: ListTableProps) {
  if (items.length === 0) {
    return (
      <div className="text-center py-10">
        <p className="text-muted-foreground">{dictionary.noItems || "No contacts found"}</p>
      </div>
    );
  }

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>{dictionary.fields.name}</TableHead>
          <TableHead>{dictionary.fields.email}</TableHead>
          <TableHead>{dictionary.fields.phone}</TableHead>
          <TableHead>{dictionary.fields.status}</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {items.map((item) => (
          <ListItem key={item.id} item={item} lang={lang} dictionary={dictionary} />
        ))}
      </TableBody>
    </Table>
  );
}
