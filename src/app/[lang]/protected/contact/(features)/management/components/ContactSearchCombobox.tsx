"use client";

import * as React from "react";
import { Check, Loader2 } from "lucide-react";
import { useDebounce } from "use-debounce";

import { cn } from "@/lib/utils";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { searchContactItems } from "../actions/search";
import { ContactSearchItem } from "../lib/types";
import { Label } from "@/components/ui/label";
import { logger } from "@/lib/logger/services/LoggerService";

interface ContactSearchComboboxProps {
  placeholder?: string;
  emptyMessage?: string;
  onSelect?: (value: string) => void;
  excludeContactId?: string;
  label?: string;
  required?: boolean;
  name?: string;
  value?: string;
  onChange?: (value: string) => void;
  minCharactersMessage?: string;
  errorMessage?: string;
}

/**
 * Simplified contact search combobox
 */
export function ContactSearchCombobox({
  placeholder = "Search contacts...",
  emptyMessage = "No contacts found.",
  onSelect,
  excludeContactId,
  label,
  required = false,
  name = "relatedContactId",
  minCharactersMessage = "Type at least 2 characters to search",
  errorMessage = "Please select a contact",
}: ContactSearchComboboxProps) {
  const [open, setOpen] = React.useState(false);
  const [query, setQuery] = React.useState("");
  const [debouncedQuery] = useDebounce(query, 300);
  const [items, setItems] = React.useState<ContactSearchItem[]>([]);
  const [selectedItem, setSelectedItem] = React.useState<ContactSearchItem | null>(null);
  const [isLoading, setIsLoading] = React.useState(false);
  const [formValue, setFormValue] = React.useState("");

  // Fetch contacts when query changes
  React.useEffect(() => {
    const fetchItems = async () => {
      if (debouncedQuery.length < 2) {
        setItems([]);
        return;
      }

      setIsLoading(true);
      try {
        const results = await searchContactItems(debouncedQuery);

        // Filter out the excluded contact
        const filteredResults = excludeContactId
          ? results.filter((contact) => contact.value !== excludeContactId)
          : results;

        setItems(filteredResults);
      } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger.error(`Failed to fetch contacts: ${errorMessage}`);
        setItems([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchItems();
  }, [debouncedQuery, excludeContactId]);

  // Handle item selection
  const handleSelect = (item: ContactSearchItem) => {
    setSelectedItem(item);
    setQuery(item.label);
    setFormValue(item.value);
    setOpen(false);

    if (onSelect) {
      onSelect(item.value);
    }
  };

  return (
    <div className="space-y-2">
      {label && <Label htmlFor={name}>{label}</Label>}

      <div className="relative">
        <Command shouldFilter={false} className="rounded-lg border shadow-md">
          <CommandInput
            placeholder={placeholder}
            value={query}
            onValueChange={(value) => {
              setQuery(value);
              if (!value) {
                setSelectedItem(null);
                setFormValue("");
              }
            }}
            onFocus={() => setOpen(true)}
            className="h-10"
          />
          {open && (
            <CommandList className="absolute top-full left-0 right-0 z-50 mt-1 rounded-lg border bg-popover shadow-md">
              {isLoading ? (
                <div className="flex items-center justify-center py-6">
                  <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
                </div>
              ) : debouncedQuery.length < 2 ? (
                <div className="py-6 text-center text-sm text-muted-foreground">
                  {minCharactersMessage}
                </div>
              ) : items.length === 0 ? (
                <CommandEmpty>{emptyMessage}</CommandEmpty>
              ) : (
                <CommandGroup>
                  {items.map((item) => (
                    <CommandItem
                      key={item.value}
                      value={item.value}
                      onSelect={() => handleSelect(item)}
                    >
                      <Check
                        className={cn(
                          "mr-2 h-4 w-4",
                          selectedItem?.value === item.value ? "opacity-100" : "opacity-0"
                        )}
                      />
                      {item.label}
                    </CommandItem>
                  ))}
                </CommandGroup>
              )}
            </CommandList>
          )}
        </Command>
      </div>

      {/* Regular input for form submission */}
      <input
        type="text"
        name={name}
        value={formValue}
        onChange={(e) => setFormValue(e.target.value)}
        required={required}
        id={name}
        className="hidden"
      />

      {/* Error message only when no contact is selected */}
      {required && !selectedItem?.value && (
        <div className="text-sm mt-1 text-red-500">{errorMessage}</div>
      )}
    </div>
  );
}
