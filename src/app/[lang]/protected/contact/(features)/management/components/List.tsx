"use client";

import { ContactItem, FeatureDictionary } from "../lib/types";
import { ListLayout } from "@/components/layouts";
import { CreateButton, ListTable } from "./list";
import { Pagination, SearchBar } from "@/components";

interface ListProps {
  lang: string;
  initialItems: ContactItem[];
  totalItems: number;
  currentPage: number;
  search?: string;
  dictionary: FeatureDictionary;
}

/**
 * Component for displaying a list of contacts
 * Uses the standardized ListLayout component for consistent UI
 */
export function List({
  lang,
  initialItems,
  totalItems,
  currentPage,
  search,
  dictionary,
}: ListProps) {
  // Header with search and create button
  const header = (
    <div className="flex justify-between items-center">
      <SearchBar
        lang={lang}
        placeholder={dictionary.fields?.searchPlaceholder}
        buttonText={dictionary.actions?.search}
      />
      <CreateButton lang={lang} dictionary={dictionary} />
    </div>
  );

  // Footer with pagination
  const footer = (
    <Pagination
      totalItems={totalItems}
      pageSize={10}
      currentPage={currentPage}
      lang={lang}
      search={search}
      itemName="contacts"
    />
  );

  return (
    <ListLayout header={header} footer={footer}>
      <ListTable items={initialItems} lang={lang} dictionary={dictionary} />
    </ListLayout>
  );
}
