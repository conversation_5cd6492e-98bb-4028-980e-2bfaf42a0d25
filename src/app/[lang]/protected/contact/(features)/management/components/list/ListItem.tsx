"use client";

import { TableCell, TableRow } from "@/components/ui/table";
import { ContactItem, FeatureDictionary } from "../../lib/types";
import FEATURE_CONFIG from "../../lib/config";
import { useRouter } from "next/navigation";

interface ListItemProps {
  item: ContactItem;
  lang: string;
  dictionary: FeatureDictionary;
}

/**
 * Component for rendering a single contact in the list table
 */
export function ListItem({ item, lang, dictionary }: ListItemProps) {
  const router = useRouter();

  // Format email for display
  const formatEmail = () => {
    if (!item.email) return "";
    const email = typeof item.email === "string" ? JSON.parse(item.email) : item.email;
    return email.personal || email.work || "";
  };

  // Format phone for display
  const formatPhone = () => {
    if (!item.phone) return "";
    const phone = typeof item.phone === "string" ? JSON.parse(item.phone) : item.phone;
    return phone.mobile || phone.home || "";
  };

  // Handle row click to navigate to view page
  const handleRowClick = () => {
    router.push(`/${lang}${FEATURE_CONFIG.routes.VIEW(item.id)}`);
  };

  return (
    <TableRow
      key={item.id}
      onClick={handleRowClick}
      className="cursor-pointer hover:bg-primary/5 transition-colors"
    >
      <TableCell className="font-medium">{item.name}</TableCell>
      <TableCell className="text-sm">{formatEmail()}</TableCell>
      <TableCell className="text-sm">{formatPhone()}</TableCell>
      <TableCell>
        <span
          className={`px-2 py-1 rounded-full text-xs ${
            item.status === "active"
              ? "bg-success/20 text-success"
              : item.status === "inactive"
                ? "bg-muted text-muted-foreground"
                : "bg-warning/20 text-warning"
          }`}
        >
          {item.status === "active"
            ? dictionary.status.active
            : item.status === "inactive"
              ? dictionary.status.inactive
              : dictionary.status.archived}
        </span>
      </TableCell>
    </TableRow>
  );
}
