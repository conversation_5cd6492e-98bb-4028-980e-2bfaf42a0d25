"use client";

import { useActionState } from "react";
import { remove } from "../../actions/relationships";
import { ActionState } from "@/lib/types/responses";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import Link from "next/link";
import {
  RelationshipWithContact,
  getRelationshipCategory,
  RelationshipCategory,
} from "@contact/management/lib/types/relationships";
import { Badge } from "@/components/ui/badge";
import { FormLayout } from "@/components/layouts";

interface RemoveFormProps {
  lang: string;
  contactId: string;
  relationship: RelationshipWithContact;
  dictionary: {
    removeTitle?: string;
    removeDescription?: string;
    backToContact?: string;
    fields?: {
      relatedContact?: string;
      relationship?: string;
    };
    categories?: {
      family?: string;
      professional?: string;
      other?: string;
    };
    types?: Record<string, string>;
    actions?: {
      remove?: string;
      removing?: string;
      cancel?: string;
    };
  };
}

/**
 * Form for confirming removal of a relationship
 */
export function RemoveForm({ lang, contactId, relationship, dictionary }: RemoveFormProps) {
  // Use React's useActionState to manage the form state with the server action
  const initialState: ActionState<RelationshipWithContact> = {
    success: true,
    error: "",
    data: relationship,
  };

  const [state, formAction, pending] = useActionState(remove, initialState);

  // Determine which relationship type to display
  const displayRelationship = relationship.inverseRelationship || relationship.relationship;
  const category = getRelationshipCategory(displayRelationship);

  // Get category label
  const getCategoryLabel = (category: RelationshipCategory) => {
    switch (category) {
      case RelationshipCategory.FAMILY:
        return dictionary.categories?.family || "Family";
      case RelationshipCategory.PROFESSIONAL:
        return dictionary.categories?.professional || "Professional";
      case RelationshipCategory.OTHER:
        return dictionary.categories?.other || "Other";
      default:
        return category;
    }
  };

  // Get category color
  const getCategoryColor = (category: RelationshipCategory) => {
    switch (category) {
      case RelationshipCategory.FAMILY:
        return "bg-blue-100 text-blue-800";
      case RelationshipCategory.PROFESSIONAL:
        return "bg-purple-100 text-purple-800";
      case RelationshipCategory.OTHER:
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Get relationship type label
  const getRelationshipLabel = (type: string) => {
    return dictionary.types?.[type] || type;
  };

  // Form actions (buttons) with better spacing
  const actions = (
    <div className="flex justify-between w-full pt-4">
      <Button variant="outline" asChild>
        <Link href={`/${lang}/protected/contact/management/${contactId}/view/relationships`}>
          {dictionary.actions?.cancel || "Cancel"}
        </Link>
      </Button>
      <Button type="submit" variant="destructive" disabled={pending}>
        {pending
          ? dictionary.actions?.removing || "Removing..."
          : dictionary.actions?.remove || "Remove"}
      </Button>
    </div>
  );

  return (
    <FormLayout
      title={<span className="text-2xl font-bold">Remove Relationship</span>}
      description={
        dictionary.removeDescription || "Are you sure you want to remove this relationship?"
      }
      formAction={formAction}
      error={!state.success ? state.error : undefined}
      actions={actions}
      className="max-w-md mx-auto text-center"
    >
      {/* Hidden fields */}
      <input type="hidden" name="id" value={relationship.id} />
      <input type="hidden" name="contactId" value={contactId} />
      <input type="hidden" name="lang" value={lang} />

      {/* Relationship details */}
      <div className="border rounded-md p-4 mb-4 text-left">
        <div className="mb-2">
          <span className="font-medium">
            {dictionary.fields?.relatedContact || "Related Contact"}:
          </span>{" "}
          {relationship.relatedContact?.name || "Unknown Contact"}
        </div>
        <div>
          <span className="font-medium">{dictionary.fields?.relationship || "Relationship"}:</span>{" "}
          <Badge className={`${getCategoryColor(category)} mr-2`}>
            {getCategoryLabel(category)}
          </Badge>
          {getRelationshipLabel(displayRelationship)}
        </div>
      </div>

      <Alert variant="destructive" className="bg-destructive/10 border-destructive/20">
        <AlertDescription className="text-destructive">
          This action cannot be undone. The relationship will be marked as inactive.
        </AlertDescription>
      </Alert>
    </FormLayout>
  );
}
