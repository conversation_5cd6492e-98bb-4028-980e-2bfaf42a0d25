"use client";

import * as React from "react";
import { Check, ChevronsUpDown, Loader2 } from "lucide-react";
import { useDebounce } from "use-debounce";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { searchContactItems } from "../actions";
import { ContactSearchItem } from "@contact/management/lib/types";
import { Label } from "@/components/ui/label";

interface ContactAutocompleteProps {
  placeholder?: string;
  emptyMessage?: string;
  onSelect?: (value: string) => void;
  excludeContactId?: string;
  label?: string;
  required?: boolean;
  name?: string;
  value?: string;

  onChange?: (contactId: string) => void;
}

/**
 * Autocomplete component for searching and selecting contacts
 * Features:
 * - Server-side search with debounce
 * - Dropdown with search results
 * - Keyboard navigation
 * - Optimized for large contact lists (thousands of records)
 */
export function ContactAutocomplete({
  placeholder = "Search contacts...",
  emptyMessage = "No contacts found.",
  onSelect,
  excludeContactId,
  label,
  required = false,
  name = "relatedContactId",
  value,
  onChange,
}: ContactAutocompleteProps) {
  const [open, setOpen] = React.useState(false);
  const [query, setQuery] = React.useState("");
  const [debouncedQuery] = useDebounce(query, 300);
  const [items, setItems] = React.useState<ContactSearchItem[]>([]);
  const [selectedItem, setSelectedItem] = React.useState<ContactSearchItem | null>(null);
  const [isLoading, setIsLoading] = React.useState(false);

  // Find the selected contact when value changes
  React.useEffect(() => {
    if (value) {
      // Try to find in search results
      const contactFromSearch = items.find((c) => c.value === value);
      if (contactFromSearch) {
        setSelectedItem(contactFromSearch);
      }
    } else {
      setSelectedItem(null);
    }
  }, [value, items]);

  React.useEffect(() => {
    const fetchItems = async () => {
      if (debouncedQuery.length < 2) {
        setItems([]);
        return;
      }

      setIsLoading(true);
      try {
        const results = await searchContactItems(debouncedQuery);

        // Filter out the excluded contact
        const filteredResults = excludeContactId
          ? results.filter((contact) => contact.value !== excludeContactId)
          : results;

        setItems(filteredResults);
      } catch (error) {
        console.error("Failed to fetch contacts:", error);
        setItems([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchItems();
  }, [debouncedQuery, excludeContactId]);

  const handleSelect = (currentValue: string) => {
    const selected = items.find((item) => item.value === currentValue);
    setSelectedItem(selected || null);
    setOpen(false);

    if (onSelect) {
      onSelect(currentValue);
    }

    if (onChange) {
      onChange(currentValue);
    }
  };

  return (
    <div className="space-y-2">
      {label && <Label htmlFor={name}>{label}</Label>}

      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between"
          >
            {selectedItem ? selectedItem.label : placeholder}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[300px] p-0">
          <Command shouldFilter={false}>
            <CommandInput placeholder={placeholder} value={query} onValueChange={setQuery} />
            <CommandList>
              {isLoading ? (
                <div className="flex items-center justify-center py-6">
                  <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
                </div>
              ) : debouncedQuery.length < 2 ? (
                <div className="py-6 text-center text-sm text-muted-foreground">
                  Type at least 2 characters to search
                </div>
              ) : items.length === 0 ? (
                <CommandEmpty>{emptyMessage}</CommandEmpty>
              ) : (
                <CommandGroup>
                  {items.map((item) => (
                    <CommandItem key={item.value} value={item.value} onSelect={handleSelect}>
                      <Check
                        className={cn(
                          "mr-2 h-4 w-4",
                          selectedItem?.value === item.value ? "opacity-100" : "opacity-0"
                        )}
                      />
                      {item.label}
                    </CommandItem>
                  ))}
                </CommandGroup>
              )}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>

      {/* Hidden input to store the value for form submission */}
      <input
        type="hidden"
        name={name}
        value={selectedItem?.value || ""}
        required={required}
        id={name}
      />
    </div>
  );
}
