"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import Link from "next/link";
import { PlusCircle, Trash2 } from "lucide-react";
import { RelationshipWithContact } from "@contact/management/lib/types/relationships";
import { ContactDrawer } from "@/components/ui/contact-drawer";

interface ListProps {
  lang: string;
  contactId: string;
  items: RelationshipWithContact[];
  totalItems: number;
  dictionary: {
    title?: string;
    description?: string;
    noRelationships?: string;
    unknownContact?: string;
    fields?: {
      relatedContact?: string;
      relationship?: string;
    };
    categories?: {
      family?: string;
      professional?: string;
      other?: string;
    };
    types?: Record<string, string>;
    actions?: {
      create?: string;
      edit?: string;
      remove?: string;
    };
    status?: {
      active?: string;
      inactive?: string;
      archived?: string;
    };
  };
}

/**
 * Component for displaying a list of relationships
 */
export function List({ lang, contactId, items, dictionary }: ListProps) {
  // State for drawer
  const [selectedContact, setSelectedContact] = useState<any>(null);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  // Open drawer with contact info
  const openContactDrawer = (contact: any) => {
    console.log("Opening drawer with contact:", JSON.stringify(contact, null, 2));

    // Create a simplified contact object with only the needed properties
    const simplifiedContact = {
      id: contact.id,
      name: contact.name,
      email: contact.email || null,
      phone: contact.phone || null,
      address: contact.address || null,
      status: contact.status || "active",
    };

    console.log("Simplified contact:", simplifiedContact);

    // Set the simplified contact
    setSelectedContact(simplifiedContact);

    // Use setTimeout to ensure state is updated before drawer opens
    setTimeout(() => {
      setIsDrawerOpen(true);
    }, 0);
  };

  // Close drawer
  const closeDrawer = () => {
    setIsDrawerOpen(false);
  };

  // Get relationship type label
  const getRelationshipLabel = (type: string) => {
    return dictionary.types?.[type] || type;
  };

  return (
    <>
      <Card className="w-full">
        <CardHeader className="pb-2">
          <div className="flex justify-between items-center">
            <div>
              <CardTitle className="text-lg">Contacts</CardTitle>
            </div>
            <Button asChild size="sm">
              <Link
                href={`/${lang}/protected/contact/management/${contactId}/view/relationships/create`}
              >
                <PlusCircle className="mr-2 h-4 w-4" />
                {dictionary.actions?.create || "Create"}
              </Link>
            </Button>
          </div>
        </CardHeader>

        <CardContent>
          {items.length === 0 ? (
            <div className="text-center py-4 text-muted-foreground">
              {dictionary.noRelationships || "No relationships found"}
            </div>
          ) : (
            <div className="space-y-2">
              {items.map((item) => {
                // Determine which relationship type to display
                const displayRelationship = item.inverseRelationship || item.relationship;

                return (
                  <div
                    key={item.id}
                    className="flex items-center justify-between p-2 border rounded-md"
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center text-primary text-sm font-semibold">
                        {(item.relatedContact?.name || "?").charAt(0)}
                      </div>
                      <div>
                        <button
                          type="button"
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            if (item.relatedContact && item.relatedContact.id) {
                              openContactDrawer(item.relatedContact);
                            }
                          }}
                          className="font-medium hover:underline text-left"
                        >
                          {item.relatedContact?.name || "Unknown Contact"}
                        </button>
                        <div className="text-sm text-muted-foreground">
                          {getRelationshipLabel(displayRelationship)}
                        </div>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      asChild
                      className="h-8 w-8"
                      title={dictionary.actions?.remove || "Remove"}
                    >
                      <Link
                        href={`/${lang}/protected/contact/management/${contactId}/view/relationships/${item.id}/remove`}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Contact drawer */}
      <ContactDrawer
        isOpen={isDrawerOpen}
        onClose={closeDrawer}
        contact={selectedContact}
        lang={lang}
      />
    </>
  );
}
