"use client";

import { useActionState } from "react";
import { edit } from "../actions/edit";
import { ActionState } from "@/lib/types/responses";
import { ContactItem, EmailAddress, FeatureDictionary, PhoneNumber } from "../lib/types";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { FormLayout } from "@/components/layouts";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import Link from "next/link";

interface EditFormProps {
  item: ContactItem;
  dictionary: FeatureDictionary;
  lang?: string;
}

/**
 * Form for editing an existing contact
 */
export function EditForm({ item, dictionary }: EditFormProps) {
  // Use React's useActionState to manage the form state with the server action
  const initialState: ActionState<ContactItem> = {
    success: false,
    error: "",
    data: item,
  };

  const [state, formAction, pending] = useActionState(edit, initialState);

  // Form actions (buttons) with better spacing
  const actions = (
    <div className="flex justify-between w-full pt-4">
      <Button variant="outline" asChild>
        <Link href="..">{dictionary.actions?.cancel || "Cancel"}</Link>
      </Button>
      <Button type="submit" disabled={pending}>
        {pending ? dictionary.actions?.saving : dictionary.actions?.save}
      </Button>
    </div>
  );

  // Success message
  let errorMessage;
  if (!state.success && state.error) {
    errorMessage = state.error;
  }

  return (
    <FormLayout
      title={dictionary.editTitle}
      description={dictionary.editDescription}
      formAction={formAction}
      error={errorMessage}
      actions={actions}
      className="max-w-2xl mx-auto"
    >
      {/* Display success message if there is one */}
      {state.success && state.data && state.data.id === item.id && (
        <Alert className="bg-success/20 text-success mb-4">
          <AlertDescription>{dictionary.itemUpdated}</AlertDescription>
        </Alert>
      )}

      {/* Basic Information Section */}
      <div className="space-y-4">
        <div>
          <h3 className="text-base font-medium mb-2">Basic Information</h3>
          <Separator className="mb-4" />
        </div>

        {/* Name field */}
        <div className="space-y-2">
          <Label htmlFor="name">{dictionary.fields.name}</Label>
          <Input
            id="name"
            name="name"
            defaultValue={item.name}
            placeholder={dictionary.fields?.placeholders?.name || "Enter name"}
            required
          />
        </div>

        {/* Address field */}
        <div className="space-y-2">
          <Label htmlFor="address">{dictionary.fields?.address}</Label>
          <Textarea
            id="address"
            name="address"
            defaultValue={item.address || ""}
            placeholder={dictionary.fields?.placeholders?.address}
            rows={3}
          />
        </div>
      </div>

      {/* Contact Information Section */}
      <div className="space-y-4 mt-8">
        <div>
          <h3 className="text-base font-medium mb-2">Contact Information</h3>
          <Separator className="mb-4" />
        </div>

        {/* Email fields */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="email_personal">{dictionary.fields?.emailPersonal}</Label>
            <Input
              id="email_personal"
              name="email_personal"
              type="email"
              defaultValue={(item.email as EmailAddress)?.personal || ""}
              placeholder={dictionary.fields?.placeholders?.emailPersonal}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="email_work">{dictionary.fields?.emailWork}</Label>
            <Input
              id="email_work"
              name="email_work"
              type="email"
              defaultValue={(item.email as EmailAddress)?.work || ""}
              placeholder={dictionary.fields?.placeholders?.emailWork}
            />
          </div>
        </div>

        {/* Phone fields */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="phone_mobile">{dictionary.fields?.phoneMobile}</Label>
            <Input
              id="phone_mobile"
              name="phone_mobile"
              defaultValue={(item.phone as PhoneNumber)?.mobile || ""}
              placeholder={dictionary.fields?.placeholders?.phoneMobile}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="phone_home">{dictionary.fields?.phoneHome}</Label>
            <Input
              id="phone_home"
              name="phone_home"
              defaultValue={(item.phone as PhoneNumber)?.home || ""}
              placeholder={dictionary.fields?.placeholders?.phoneHome}
            />
          </div>
        </div>
      </div>
    </FormLayout>
  );
}
