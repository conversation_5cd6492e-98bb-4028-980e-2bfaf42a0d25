"use client";

import { useActionState } from "react";
import { remove } from "../actions";
import { ActionState } from "@/lib/types/responses";
import { ContactItem, FeatureDictionary } from "../lib/types";
import { Button } from "@/components/ui/button";
import { FormLayout } from "@/components/layouts";
import { Alert, AlertDescription } from "@/components/ui/alert";
import Link from "next/link";

import FEATURE_CONFIG from "../lib/config";

interface RemoveFormProps {
  lang: string;
  item: ContactItem;
  dictionary: FeatureDictionary;
}

/**
 * Form for confirming deletion of a contact
 */
export function RemoveForm({ lang, item, dictionary }: RemoveFormProps) {
  // Use React's useFormState to manage the form state with the server action
  const initialState: ActionState<ContactItem> = {
    success: true,
    error: "",
    data: item,
  };

  const [state, formAction, _pending] = useActionState(remove, initialState);

  // Format email and phone for display
  const emailDisplay = () => {
    if (!item.email) return "No email provided";
    const email = typeof item.email === "string" ? JSON.parse(item.email) : item.email;
    const parts = [];
    if (email.personal) parts.push(`Personal: ${email.personal}`);
    if (email.work) parts.push(`Work: ${email.work}`);
    return parts.length > 0 ? parts.join(", ") : "No email provided";
  };

  const phoneDisplay = () => {
    if (!item.phone) return "No phone provided";
    const phone = typeof item.phone === "string" ? JSON.parse(item.phone) : item.phone;
    const parts = [];
    if (phone.mobile) parts.push(`Mobile: ${phone.mobile}`);
    if (phone.home) parts.push(`Home: ${phone.home}`);
    return parts.length > 0 ? parts.join(", ") : "No phone provided";
  };

  // Form actions (buttons) with better spacing
  const actions = (
    <div className="flex justify-between w-full pt-4">
      <Button variant="outline" asChild>
        <Link href={`/${lang}${FEATURE_CONFIG.routes.LIST}`}>{dictionary.backToList}</Link>
      </Button>
      <Button type="submit" variant="destructive" disabled={_pending}>
        {_pending ? dictionary.actions?.removing : dictionary.removeConfirm}
      </Button>
    </div>
  );

  return (
    <FormLayout
      title={<span className="text-2xl font-bold">Remove</span>}
      description="Are you sure you want to delete this contact?"
      formAction={formAction}
      error={!state.success ? state.error : undefined}
      actions={actions}
      className="max-w-md mx-auto text-center"
    >
      {/* Hidden fields for ID and language */}
      <input type="hidden" name="id" value={item.id} />
      <input type="hidden" name="lang" value={lang} />

      {/* Item details */}
      <div className="border rounded-md p-4 mb-4">
        <h3 className="font-medium">{item.name}</h3>
        <p className="text-sm text-muted-foreground mt-1">
          <strong>Email:</strong> {emailDisplay()}
        </p>
        <p className="text-sm text-muted-foreground mt-1">
          <strong>Phone:</strong> {phoneDisplay()}
        </p>
      </div>

      <Alert variant="destructive" className="bg-destructive/10 border-destructive/20">
        <AlertDescription className="text-destructive">
          This action cannot be undone. The contact will be marked as inactive.
        </AlertDescription>
      </Alert>
    </FormLayout>
  );
}
