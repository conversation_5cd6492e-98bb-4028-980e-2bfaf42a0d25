"use client";
import { ContactItem, FeatureDictionary } from "../lib/types";
import { Button } from "@/components/ui/button";
import { ContentLayout } from "@/components/layouts";
import Link from "next/link";
import FEATURE_CONFIG from "../lib/config";

interface ViewProps {
  lang: string;
  item: ContactItem;
  dictionary: FeatureDictionary;
  hideTitle?: boolean;
}

/**
 * Component for viewing a contact with edit and remove functionality
 * Uses the standardized ContentLayout component for consistent UI
 */
export function View({ lang, item, dictionary, hideTitle = false }: ViewProps) {
  // Format email and phone for display
  const formatEmail = () => {
    if (!item.email) return "No email provided";
    const email = typeof item.email === "string" ? JSON.parse(item.email) : item.email;
    const parts = [];
    if (email.personal) parts.push(`Personal: ${email.personal}`);
    if (email.work) parts.push(`Work: ${email.work}`);
    return parts.length > 0 ? parts.join(", ") : "No email provided";
  };

  const formatPhone = () => {
    if (!item.phone) return "No phone provided";
    const phone = typeof item.phone === "string" ? JSON.parse(item.phone) : item.phone;
    const parts = [];
    if (phone.mobile) parts.push(`Mobile: ${phone.mobile}`);
    if (phone.home) parts.push(`Home: ${phone.home}`);
    return parts.length > 0 ? parts.join(", ") : "No phone provided";
  };

  // Footer with action buttons
  const footer = (
    <div className="flex justify-between w-full">
      <Button variant="outline" size="sm" asChild>
        <Link href={`/${lang}${FEATURE_CONFIG.routes.LIST}`}>{dictionary.backToList}</Link>
      </Button>

      <div className="flex gap-2">
        <Button variant="outline" size="sm" asChild>
          <Link href={`/${lang}${FEATURE_CONFIG.routes.EDIT(item.id)}`}>
            {dictionary.actions.edit}
          </Link>
        </Button>

        <Button variant="destructive" size="sm" asChild>
          <Link href={`/${lang}${FEATURE_CONFIG.routes.REMOVE(item.id)}`}>
            {dictionary.actions.remove}
          </Link>
        </Button>
      </div>
    </div>
  );

  // We've removed the status badge as it's not valuable for users

  return (
    <ContentLayout footer={footer} className="w-full">
      <div className="space-y-6">
        {/* Contact header with avatar and name */}
        <div className="flex items-center">
          <div className="flex items-center gap-4">
            <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center text-primary text-2xl font-semibold">
              {item.name.charAt(0)}
            </div>
            <div>
              <h2 className="text-2xl font-bold">{item.name}</h2>
              <p className="text-muted-foreground">{dictionary.title}</p>
            </div>
          </div>
        </div>

        {/* Contact details in a more compact layout */}
        <div className="grid grid-cols-2 gap-3 mt-6 pt-6 border-t">
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">{dictionary.fields.email}</h3>
            <p className="text-sm mt-1">{formatEmail()}</p>
          </div>

          <div>
            <h3 className="text-sm font-medium text-muted-foreground">{dictionary.fields.phone}</h3>
            <p className="text-sm mt-1">{formatPhone()}</p>
          </div>
        </div>

        <div>
          <h3 className="text-sm font-medium text-muted-foreground">{dictionary.fields.address}</h3>
          <p className="text-sm mt-1">{item.address || "No address provided"}</p>
        </div>

        <div className="grid grid-cols-2 gap-3 text-sm">
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">Created At</h3>
            <p className="mt-1">{new Date(item.created_at || "").toLocaleString()}</p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">Updated At</h3>
            <p className="mt-1">{new Date(item.updated_at || "").toLocaleString()}</p>
          </div>
        </div>
      </div>
    </ContentLayout>
  );
}
