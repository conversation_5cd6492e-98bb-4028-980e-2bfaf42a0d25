"use client";

import { useActionState } from "react";
import { create } from "../../actions/relationships";
import { ActionState } from "@/lib/types/responses";
import { But<PERSON> } from "@/components/ui/button";
import { logger } from "@/lib/logger/services/LoggerService";
import Link from "next/link";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import {
  RelationshipType,
  RelationshipCategory,
  getRelationshipCategory,
} from "@contact/management/lib/types/relationships";
import { ContactItem, ContactRelationship } from "@contact/management/lib/types";
import { ContactSearchCombobox } from "@contact/management/components";
import { FormLayout } from "@/components/layouts";

interface CreateFormProps {
  lang: string;
  contactId: string;
  contacts: ContactItem[];
  dictionary: {
    createTitle?: string;
    createDescription?: string;
    backToContact?: string;
    fields?: {
      relatedContact?: string;
      relationship?: string;
      selectContact?: string;
      selectRelationship?: string;
      noContactsFound?: string;
      minCharactersMessage?: string;
      pleaseSelectContact?: string;
    };
    categories?: {
      family?: string;
      professional?: string;
      other?: string;
    };
    types?: Record<string, string>;
    actions?: {
      create?: string;
      creating?: string;
      cancel?: string;
    };
  };
}

/**
 * Form for creating a new relationship
 */
export function CreateForm({ lang, contactId, dictionary }: CreateFormProps) {
  // Use React's useActionState to manage the form state with the server action
  const initialState: ActionState<ContactRelationship> = {
    success: true,
    error: "",
    data: null,
  };

  const [state, formAction, pending] = useActionState(create, initialState);

  // Group relationship types by category
  const familyRelationships = Object.values(RelationshipType).filter(
    (type) => getRelationshipCategory(type) === RelationshipCategory.FAMILY
  );

  const professionalRelationships = Object.values(RelationshipType).filter(
    (type) => getRelationshipCategory(type) === RelationshipCategory.PROFESSIONAL
  );

  const otherRelationships = Object.values(RelationshipType).filter(
    (type) => getRelationshipCategory(type) === RelationshipCategory.OTHER
  );

  // Form actions (buttons) with better spacing
  const actions = (
    <div className="flex justify-between w-full">
      <Button variant="outline" asChild>
        <Link href={`/${lang}/protected/contact/management/${contactId}/view/relationships`}>
          {dictionary.actions?.cancel || "Cancel"}
        </Link>
      </Button>
      <Button type="submit" disabled={pending}>
        {pending
          ? dictionary.actions?.creating || "Creating..."
          : dictionary.actions?.create || "Create"}
      </Button>
    </div>
  );

  return (
    <FormLayout
      title={
        <span className="text-2xl font-bold">
          {dictionary.createTitle || "Create Relationship"}
        </span>
      }
      description={dictionary.createDescription || "Add a new relationship to this contact"}
      formAction={formAction}
      error={!state.success ? state.error : undefined}
      actions={actions}
      className="max-w-md mx-auto"
    >
      {/* Hidden fields */}
      <input type="hidden" name="contactId" value={contactId} />
      <input type="hidden" name="lang" value={lang} />

      <div className="space-y-6">
        {/* Related Contact field with autocomplete */}
        <ContactSearchCombobox
          label={dictionary.fields?.relatedContact || "Related Contact"}
          placeholder={dictionary.fields?.selectContact || "Search for a contact..."}
          emptyMessage={dictionary.fields?.noContactsFound || "No contacts found"}
          minCharactersMessage={
            dictionary.fields?.minCharactersMessage || "Type at least 2 characters to search"
          }
          errorMessage={dictionary.fields?.pleaseSelectContact || "Please select a contact"}
          onSelect={(value) => {
            // This ensures the value is properly set when a contact is selected
            logger.verbose(`Selected contact: ${value}`);
          }}
          excludeContactId={contactId}
          required
          name="relatedContactId"
        />

        {/* Relationship Type field */}
        <div className="space-y-2">
          <Label htmlFor="relationship">
            {dictionary.fields?.relationship || "Relationship Type"}
          </Label>
          <Select name="relationship" required>
            <SelectTrigger>
              <SelectValue
                placeholder={dictionary.fields?.selectRelationship || "Select relationship type"}
              />
            </SelectTrigger>
            <SelectContent>
              {/* Family relationships */}
              <SelectGroup>
                <SelectLabel>{dictionary.categories?.family || "Family"}</SelectLabel>
                {familyRelationships.map((type) => (
                  <SelectItem key={type} value={type}>
                    {dictionary.types?.[type] || type}
                  </SelectItem>
                ))}
              </SelectGroup>

              {/* Professional relationships */}
              <SelectGroup>
                <SelectLabel>{dictionary.categories?.professional || "Professional"}</SelectLabel>
                {professionalRelationships.map((type) => (
                  <SelectItem key={type} value={type}>
                    {dictionary.types?.[type] || type}
                  </SelectItem>
                ))}
              </SelectGroup>

              {/* Other relationships */}
              <SelectGroup>
                <SelectLabel>{dictionary.categories?.other || "Other"}</SelectLabel>
                {otherRelationships.map((type) => (
                  <SelectItem key={type} value={type}>
                    {dictionary.types?.[type] || type}
                  </SelectItem>
                ))}
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>
      </div>
    </FormLayout>
  );
}
