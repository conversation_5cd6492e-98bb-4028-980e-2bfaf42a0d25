import { ContactRelationship } from ".";

/**
 * Common relationship types
 */
export enum RelationshipType {
  // Family relationships
  PARENT = "parent",
  CHILD = "child",
  SIBLING = "sibling",
  SPOUSE = "spouse",
  GRANDPARENT = "grandparent",
  GRANDCHILD = "grandchild",
  AUNT_UNCLE = "aunt_uncle",
  NIECE_NEPHEW = "niece_nephew",
  COUSIN = "cousin",
  STEP_PARENT = "step_parent",
  STEP_CHILD = "step_child",
  GUARDIAN = "guardian",
  WARD = "ward",

  // Professional relationships
  LAWYER = "lawyer",
  CLIENT = "client",
  SOCIAL_WORKER = "social_worker",
  CASE_MANAGER = "case_manager",
  DOCTOR = "doctor",
  PATIENT = "patient",
  THERAPIST = "therapist",

  // Other relationships
  FRIEND = "friend",
  NEIGHBOR = "neighbor",
  COLLEAGUE = "colleague",
  OTHER = "other",
}

/**
 * Relationship categories
 */
export enum RelationshipCategory {
  FAMILY = "family",
  PROFESSIONAL = "professional",
  OTHER = "other",
}

/**
 * Get the category for a relationship type
 * @param type The relationship type
 * @returns The category
 */
export function getRelationshipCategory(type: string): RelationshipCategory {
  const familyRelationships = [
    RelationshipType.PARENT,
    RelationshipType.CHILD,
    RelationshipType.SIBLING,
    RelationshipType.SPOUSE,
    RelationshipType.GRANDPARENT,
    RelationshipType.GRANDCHILD,
    RelationshipType.AUNT_UNCLE,
    RelationshipType.NIECE_NEPHEW,
    RelationshipType.COUSIN,
    RelationshipType.STEP_PARENT,
    RelationshipType.STEP_CHILD,
    RelationshipType.GUARDIAN,
    RelationshipType.WARD,
  ];

  const professionalRelationships = [
    RelationshipType.LAWYER,
    RelationshipType.CLIENT,
    RelationshipType.SOCIAL_WORKER,
    RelationshipType.CASE_MANAGER,
    RelationshipType.DOCTOR,
    RelationshipType.PATIENT,
    RelationshipType.THERAPIST,
  ];

  if (familyRelationships.includes(type as RelationshipType)) {
    return RelationshipCategory.FAMILY;
  } else if (professionalRelationships.includes(type as RelationshipType)) {
    return RelationshipCategory.PROFESSIONAL;
  } else {
    return RelationshipCategory.OTHER;
  }
}

/**
 * Get the inverse relationship type
 * @param type The relationship type
 * @returns The inverse relationship type
 */
export function getInverseRelationship(type: string): RelationshipType {
  switch (type) {
    case RelationshipType.PARENT:
      return RelationshipType.CHILD;
    case RelationshipType.CHILD:
      return RelationshipType.PARENT;
    case RelationshipType.SIBLING:
      return RelationshipType.SIBLING;
    case RelationshipType.SPOUSE:
      return RelationshipType.SPOUSE;
    case RelationshipType.GRANDPARENT:
      return RelationshipType.GRANDCHILD;
    case RelationshipType.GRANDCHILD:
      return RelationshipType.GRANDPARENT;
    case RelationshipType.AUNT_UNCLE:
      return RelationshipType.NIECE_NEPHEW;
    case RelationshipType.NIECE_NEPHEW:
      return RelationshipType.AUNT_UNCLE;
    case RelationshipType.COUSIN:
      return RelationshipType.COUSIN;
    case RelationshipType.STEP_PARENT:
      return RelationshipType.STEP_CHILD;
    case RelationshipType.STEP_CHILD:
      return RelationshipType.STEP_PARENT;
    case RelationshipType.GUARDIAN:
      return RelationshipType.WARD;
    case RelationshipType.WARD:
      return RelationshipType.GUARDIAN;
    case RelationshipType.LAWYER:
      return RelationshipType.CLIENT;
    case RelationshipType.CLIENT:
      return RelationshipType.LAWYER;
    case RelationshipType.SOCIAL_WORKER:
      return RelationshipType.CLIENT;
    case RelationshipType.CASE_MANAGER:
      return RelationshipType.CLIENT;
    case RelationshipType.DOCTOR:
      return RelationshipType.PATIENT;
    case RelationshipType.PATIENT:
      return RelationshipType.DOCTOR;
    case RelationshipType.THERAPIST:
      return RelationshipType.PATIENT;
    case RelationshipType.FRIEND:
      return RelationshipType.FRIEND;
    case RelationshipType.NEIGHBOR:
      return RelationshipType.NEIGHBOR;
    case RelationshipType.COLLEAGUE:
      return RelationshipType.COLLEAGUE;
    default:
      return RelationshipType.OTHER;
  }
}

/**
 * Extended relationship information with related contact details
 */
export interface RelationshipWithContact extends ContactRelationship {
  relatedContact?: {
    id: string;
    name: string;
    status: string;
    email: any; // Using any to accommodate various formats from the database
    phone: any; // Using any to accommodate various formats from the database
    address: string | null;
  };
  inverseRelationship?: string;
}
