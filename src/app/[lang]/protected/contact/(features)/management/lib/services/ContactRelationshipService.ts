import { createClient } from "@/lib/supabase/server";
import { logger } from "@/lib/logger/services/LoggerService";
import {
  ContactRelationship,
  ContactRelationshipInsert,
  ContactRelationshipUpdate,
} from "../types";
import { ServiceResponse, successResponse, errorResponse } from "@/lib/types/responses";

/**
 * Service for managing contact relationships
 * This is a singleton class that provides methods for CRUD operations on contact relationships
 */
export class ContactRelationshipService {
  /**
   * Private constructor to prevent direct instantiation
   * Use the static methods instead
   */
  private constructor() {}

  /**
   * List all relationships for a specific contact with pagination
   * Includes related contact information using a join
   * @param contactId The ID of the contact
   * @param page Page number (1-based)
   * @param pageSize Number of items per page
   * @returns Service response with paginated array of relationships with contact info and count
   */
  static async listForContact(
    contactId: string,
    page: number = 1,
    pageSize: number = 10
  ): Promise<
    ServiceResponse<{
      items: import("../types/relationships").RelationshipWithContact[];
      total: number;
    }>
  > {
    try {
      const supabase = await createClient();
      const offset = (page - 1) * pageSize;

      // Get count of all relationships for this contact
      const { count, error: countError } = await supabase
        .from("contact_relationships")
        .select("*", { count: "exact", head: true })
        .or(`subject_contact_id.eq.${contactId},related_contact_id.eq.${contactId}`)
        .eq("status", "active");

      if (countError) {
        logger.error(`Error counting contact relationships: ${countError.message}`);
        return errorResponse(
          countError,
          `Failed to count contact relationships: ${countError.message}`
        );
      }

      // Get paginated relationships with joined contact data
      const { data, error } = await supabase
        .from("contact_relationships")
        .select(
          `
          *,
          subject_contact:contacts!contact_relationships_subject_contact_id_fkey(id, name, status, email, phone, address),
          related_contact:contacts!contact_relationships_related_contact_id_fkey(id, name, status, email, phone, address)
        `
        )
        .or(`subject_contact_id.eq.${contactId},related_contact_id.eq.${contactId}`)
        .eq("status", "active")
        .order("created_at", { ascending: false })
        .range(offset, offset + pageSize - 1);

      if (error) {
        logger.error(`Error listing contact relationships: ${error.message}`);
        return errorResponse(error, `Failed to list contact relationships: ${error.message}`);
      }

      // Transform the data to match the RelationshipWithContact interface
      const transformedData = data.map((item) => {
        // Determine if this contact is the subject or related contact
        const isSubject = item.subject_contact_id === contactId;

        // Get the related contact based on whether this contact is the subject or related
        const relatedContact = isSubject ? item.related_contact : item.subject_contact;

        // Create the RelationshipWithContact object
        return {
          id: item.id,
          organization_id: item.organization_id,
          subject_contact_id: item.subject_contact_id,
          related_contact_id: item.related_contact_id,
          relationship: item.relationship,
          status: item.status,
          created_at: item.created_at,
          updated_at: item.updated_at,
          relatedContact: relatedContact
            ? {
                id: relatedContact.id,
                name: relatedContact.name,
                status: relatedContact.status,
                email: relatedContact.email,
                phone: relatedContact.phone,
                address: relatedContact.address,
              }
            : undefined,
          // If this contact is the related contact, we need to invert the relationship type
          inverseRelationship: !isSubject ? item.relationship : undefined,
        };
      });

      return successResponse(
        { items: transformedData, total: count || 0 },
        "Successfully retrieved contact relationships"
      );
    } catch (error) {
      logger.error(`Unexpected error listing contact relationships: ${error}`);
      return errorResponse(error, `Unexpected error listing contact relationships`);
    }
  }

  /**
   * View a specific relationship by ID
   * Includes related contact information using a join
   * @param id The ID of the relationship
   * @returns Service response with the relationship and related contact info
   */
  static async view(
    id: string
  ): Promise<ServiceResponse<import("../types/relationships").RelationshipWithContact>> {
    try {
      const supabase = await createClient();

      const { data, error } = await supabase
        .from("contact_relationships")
        .select(
          `
          *,
          subject_contact:contacts!contact_relationships_subject_contact_id_fkey(id, name, status, email, phone, address),
          related_contact:contacts!contact_relationships_related_contact_id_fkey(id, name, status, email, phone, address)
        `
        )
        .eq("id", id)
        .single();

      if (error) {
        if (error.code === "PGRST116") {
          // Record not found
          return errorResponse(error, `Contact relationship with ID ${id} not found`);
        }

        logger.error(`Error reading contact relationship: ${error.message}`);
        return errorResponse(error, `Failed to read contact relationship: ${error.message}`);
      }

      // Transform the data to match the RelationshipWithContact interface
      const relationship = data;

      // For view, we need to determine which contact is the "related" one based on the context
      // This will be handled in the action where we have the contactId parameter

      return successResponse(
        {
          id: relationship.id,
          organization_id: relationship.organization_id,
          subject_contact_id: relationship.subject_contact_id,
          related_contact_id: relationship.related_contact_id,
          relationship: relationship.relationship,
          status: relationship.status,
          created_at: relationship.created_at,
          updated_at: relationship.updated_at,
          // We'll include both contacts and let the action determine which one is "related"
          subject_contact: relationship.subject_contact,
          related_contact: relationship.related_contact,
        } as any, // Using any here to bypass type checking as we'll handle it in the action
        "Successfully retrieved contact relationship"
      );
    } catch (error) {
      logger.error(`Unexpected error reading contact relationship: ${error}`);
      return errorResponse(error, `Unexpected error reading contact relationship`);
    }
  }

  /**
   * Create a new relationship
   * @param item The relationship to create
   * @returns Service response with the created relationship
   */
  static async create(
    item: ContactRelationshipInsert
  ): Promise<ServiceResponse<ContactRelationship>> {
    try {
      const supabase = await createClient();

      // Validate that subject and related contacts are different
      if (item.subject_contact_id === item.related_contact_id) {
        return errorResponse(
          new Error("Subject and related contacts must be different"),
          "Subject and related contacts must be different"
        );
      }

      const { data, error } = await supabase
        .from("contact_relationships")
        .insert(item)
        .select()
        .single();

      if (error) {
        logger.error(`Error creating contact relationship: ${error.message}`);
        return errorResponse(error, `Failed to create contact relationship: ${error.message}`);
      }

      return successResponse(
        data as ContactRelationship,
        "Successfully created contact relationship"
      );
    } catch (error) {
      logger.error(`Unexpected error creating contact relationship: ${error}`);
      return errorResponse(error, `Unexpected error creating contact relationship`);
    }
  }

  /**
   * Update an existing relationship
   * @param id The ID of the relationship to update
   * @param item The updated relationship data
   * @returns Service response with the updated relationship
   */
  static async edit(
    id: string,
    item: ContactRelationshipUpdate
  ): Promise<ServiceResponse<ContactRelationship>> {
    try {
      const supabase = await createClient();

      // Add updated_at timestamp if not provided
      const updateData = {
        ...item,
        updated_at: item.updated_at || new Date().toISOString(),
      };

      const { data, error } = await supabase
        .from("contact_relationships")
        .update(updateData)
        .eq("id", id)
        .select()
        .single();

      if (error) {
        logger.error(`Error updating contact relationship: ${error.message}`);
        return errorResponse(error, `Failed to update contact relationship: ${error.message}`);
      }

      return successResponse(
        data as ContactRelationship,
        "Successfully updated contact relationship"
      );
    } catch (error) {
      logger.error(`Unexpected error updating contact relationship: ${error}`);
      return errorResponse(error, `Unexpected error updating contact relationship`);
    }
  }

  /**
   * Remove a relationship (soft delete by setting status to inactive)
   * @param id The ID of the relationship to remove
   * @returns Service response
   */
  static async remove(id: string): Promise<ServiceResponse<null>> {
    try {
      const supabase = await createClient();

      const { error } = await supabase
        .from("contact_relationships")
        .update({ status: "inactive", updated_at: new Date().toISOString() })
        .eq("id", id);

      if (error) {
        logger.error(`Error removing contact relationship: ${error.message}`);
        return errorResponse(error, `Failed to remove contact relationship: ${error.message}`);
      }

      return successResponse(null, "Successfully removed contact relationship");
    } catch (error) {
      logger.error(`Unexpected error removing contact relationship: ${error}`);
      return errorResponse(error, `Unexpected error removing contact relationship`);
    }
  }
}
