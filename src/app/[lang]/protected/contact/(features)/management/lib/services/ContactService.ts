import { createClient } from "@/lib/supabase/server";
import { logger } from "@/lib/logger/services/LoggerService";
import { ContactItem, ContactItemInsert, ContactItemUpdate } from "../types";
import { ServiceResponse, successResponse, errorResponse } from "@/lib/types/responses";

/**
 * Service for managing contacts
 * This is a singleton class that provides methods for CRUD operations on contacts
 */
export class ContactService {
  /**
   * Private constructor to prevent direct instantiation
   * Use the static methods instead
   */
  private constructor() {}

  /**
   * List contacts with pagination
   * @param page Page number (1-based)
   * @param pageSize Number of items per page
   * @returns Service response with paginated array of contacts and count
   */
  static async list(
    page: number = 1,
    pageSize: number = 10
  ): Promise<ServiceResponse<{ items: ContactItem[]; total: number }>> {
    const supabase = await createClient();

    // Calculate offset
    const offset = (page - 1) * pageSize;

    try {
      // Get count of all items
      const { count, error: countError } = await supabase
        .from("contacts")
        .select("*", { count: "exact", head: true });

      if (countError) {
        logger.error(`Error counting contacts: ${countError.message}`);
        return errorResponse(countError, `Failed to count contacts: ${countError.message}`);
      }

      // Get paginated items
      const { data, error } = await supabase
        .from("contacts")
        .select("*")
        .order("created_at", { ascending: true })
        .range(offset, offset + pageSize - 1);

      if (error) {
        logger.error(`Error listing contacts: ${error.message}`);
        return errorResponse(error, `Failed to list contacts: ${error.message}`);
      }

      return successResponse(
        {
          items: data as ContactItem[],
          total: count || 0,
        },
        `Successfully retrieved ${data.length} contacts (page ${page} of ${Math.ceil((count || 0) / pageSize)})`
      );
    } catch (error) {
      logger.error(`Unexpected error listing contacts: ${error}`);
      return errorResponse(error, `Unexpected error listing contacts`);
    }
  }

  /**
   * Read a contact by ID
   * @param id The ID of the contact
   * @returns Service response with the contact
   */
  static async view(id: string): Promise<ServiceResponse<ContactItem>> {
    try {
      const supabase = await createClient();

      const { data, error } = await supabase.from("contacts").select("*").eq("id", id).single();

      if (error) {
        if (error.code === "PGRST116") {
          // Record not found
          return errorResponse(error, `Contact with ID ${id} not found`);
        }

        logger.error(`Error reading contact: ${error.message}`);
        return errorResponse(error, `Failed to read contact: ${error.message}`);
      }

      return successResponse(data as ContactItem, "Successfully retrieved contact");
    } catch (error) {
      logger.error(`Unexpected error reading contact: ${error}`);
      return errorResponse(error, `Unexpected error reading contact`);
    }
  }

  /**
   * Search contacts by name with pagination
   * @param query Search query
   * @param page Page number (1-based)
   * @param pageSize Number of items per page
   * @returns Service response with paginated array of matching contacts and count
   */
  static async search(
    query: string,
    page: number = 1,
    pageSize: number = 10
  ): Promise<ServiceResponse<{ items: ContactItem[]; total: number }>> {
    if (!query || query.trim() === "") {
      return this.list(page, pageSize);
    }

    const supabase = await createClient();
    const searchTerm = `%${query.toLowerCase()}%`;

    // Calculate offset
    const offset = (page - 1) * pageSize;

    try {
      // Get count of matching items
      const { count, error: countError } = await supabase
        .from("contacts")
        .select("*", { count: "exact", head: true })
        .ilike("name", searchTerm);

      if (countError) {
        logger.error(`Error counting search results: ${countError.message}`);
        return errorResponse(countError, `Failed to count search results: ${countError.message}`);
      }

      // Get paginated search results
      const { data, error } = await supabase
        .from("contacts")
        .select("*")
        .ilike("name", searchTerm)
        .order("created_at", { ascending: false })
        .range(offset, offset + pageSize - 1);

      if (error) {
        logger.error(`Error searching contacts: ${error.message}`);
        return errorResponse(error, `Failed to search contacts: ${error.message}`);
      }

      return successResponse(
        {
          items: data as ContactItem[],
          total: count || 0,
        },
        `Found ${data.length} contacts matching "${query}" (page ${page} of ${Math.ceil((count || 0) / pageSize)})`
      );
    } catch (error) {
      logger.error(`Unexpected error searching contacts: ${error}`);
      return errorResponse(error, `Unexpected error searching contacts`);
    }
  }

  /**
   * Create a new contact
   * @param item The contact to create
   * @returns Service response with the created contact
   */
  static async create(item: ContactItemInsert): Promise<ServiceResponse<ContactItem>> {
    try {
      const supabase = await createClient();

      const { data, error } = await supabase.from("contacts").insert(item).select().single();

      if (error) {
        logger.error(`Error creating contact: ${error.message}`);
        return errorResponse(error, `Failed to create contact: ${error.message}`);
      }

      return successResponse(data as ContactItem, "Successfully created contact");
    } catch (error) {
      logger.error(`Unexpected error creating contact: ${error}`);
      return errorResponse(error, `Unexpected error creating contact`);
    }
  }

  /**
   * Update an existing contact
   * @param id The ID of the contact to update
   * @param item The updated contact data
   * @returns Service response with the updated contact
   */
  static async edit(id: string, item: ContactItemUpdate): Promise<ServiceResponse<ContactItem>> {
    try {
      const supabase = await createClient();

      // Add updated_at timestamp if not provided
      const updateData = {
        ...item,
        updated_at: item.updated_at || new Date().toISOString(),
      };

      const { data, error } = await supabase
        .from("contacts")
        .update(updateData)
        .eq("id", id)
        .select()
        .single();

      if (error) {
        logger.error(`Error updating contact: ${error.message}`);
        return errorResponse(error, `Failed to update contact: ${error.message}`);
      }

      return successResponse(data as ContactItem, "Successfully updated contact");
    } catch (error) {
      logger.error(`Unexpected error updating contact: ${error}`);
      return errorResponse(error, `Unexpected error updating contact`);
    }
  }

  /**
   * Remove a contact (soft delete by setting status to inactive)
   * @param id The ID of the contact to remove
   * @returns Service response
   */
  static async remove(id: string): Promise<ServiceResponse<null>> {
    try {
      const supabase = await createClient();

      const { error } = await supabase
        .from("contacts")
        .update({ status: "inactive", updated_at: new Date().toISOString() })
        .eq("id", id);

      if (error) {
        logger.error(`Error removing contact: ${error.message}`);
        return errorResponse(error, `Failed to remove contact: ${error.message}`);
      }

      return successResponse(null, "Successfully removed contact");
    } catch (error) {
      logger.error(`Unexpected error removing contact: ${error}`);
      return errorResponse(error, `Unexpected error removing contact`);
    }
  }
}
