/**
 * Contact Configuration
 *
 * This file defines the contact-level configuration including:
 * - Contact name
 * - Contact paths
 * - Contact permissions
 * - Route permissions
 *
 * This configuration is used throughout the contact to ensure consistency
 * and make it easier to update paths, names, and permissions in one place.
 */

import DOMAIN_CONFIG from "../../../../lib/config/domain";

// Contact identifier (used in permissions, routes, etc.)
export const FEATURE_ID = "management";

// Contact display name (used in UI)
export const FEATURE_NAME = "Contact";

// Contact description (used in UI)
export const FEATURE_DESCRIPTION = "Contact management";

// Base path for all routes in this contact
export const FEATURE_BASE_PATH = `${DOMAIN_CONFIG.basePath}/${FEATURE_ID}`;

// Contact permissions domain
export const FEATURE_PERMISSION_DOMAIN = `${DOMAIN_CONFIG.id}:${FEATURE_ID}`;

// Contact permissions
export const FEATURE_PERMISSIONS = {
  // CRUD operations
  LIST: `${FEATURE_PERMISSION_DOMAIN}:list`,
  CREATE: `${FEATURE_PERMISSION_DOMAIN}:create`,
  VIEW: `${FEATURE_PERMISSION_DOMAIN}:view`,
  EDIT: `${FEATURE_PERMISSION_DOMAIN}:edit`,
  REMOVE: `${FEATURE_PERMISSION_DOMAIN}:remove`,
  // Wildcard permission for all Contact operations
  ALL: `${FEATURE_PERMISSION_DOMAIN}:*`,
};

// Contact routes
export const FEATURE_ROUTES = {
  // Main contact page
  INDEX: `/${FEATURE_BASE_PATH}`,

  // List page
  LIST: `/${FEATURE_BASE_PATH}/list`,

  // Create page
  CREATE: `/${FEATURE_BASE_PATH}/create`,

  // View page (function to generate dynamic route)
  VIEW: (id: string) => `/${FEATURE_BASE_PATH}/${id}/view`,

  // Edit page (function to generate dynamic route)
  EDIT: (id: string) => `/${FEATURE_BASE_PATH}/${id}/edit`,

  // Remove page (function to generate dynamic route)
  REMOVE: (id: string) => `/${FEATURE_BASE_PATH}/${id}/remove`,
};

// Route permissions mapping for Feature
export const FEATURE_ROUTE_PERMISSIONS = {
  // List page
  [`/[lang]${FEATURE_ROUTES.LIST}`]: FEATURE_PERMISSIONS.LIST,

  // Create page
  [`/[lang]${FEATURE_ROUTES.CREATE}`]: FEATURE_PERMISSIONS.CREATE,

  // View page
  [`/[lang]${FEATURE_BASE_PATH}/[id]/view`]: FEATURE_PERMISSIONS.VIEW,

  // Edit page
  [`/[lang]${FEATURE_BASE_PATH}/[id]/edit`]: FEATURE_PERMISSIONS.EDIT,

  // Remove page
  [`/[lang]${FEATURE_BASE_PATH}/[id]/remove`]: FEATURE_PERMISSIONS.REMOVE,
};

// Contact configuration object
const FEATURE_CONFIG = {
  id: FEATURE_ID,
  name: FEATURE_NAME,
  description: FEATURE_DESCRIPTION,
  basePath: FEATURE_BASE_PATH,
  permissionDomain: FEATURE_PERMISSION_DOMAIN,
  permissions: FEATURE_PERMISSIONS,
  routes: FEATURE_ROUTES,
  routePermissions: FEATURE_ROUTE_PERMISSIONS,
};

export default FEATURE_CONFIG;
