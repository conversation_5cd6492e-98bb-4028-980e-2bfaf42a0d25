import { Dictionary } from "@/lib/i18n/services/I18nService";
import { DOMAIN_ID } from "../../../../lib/config/domain";
import { FEATURE_ID } from "../config/feature";
import { Database } from "@/lib/types/database.types";

/**
 * Contact status
 */
export type ContactStatus = "active" | "inactive" | "archived";

/**
 * Email address structure
 */
export interface EmailAddress {
  personal?: string;
  work?: string;
  other?: string;
}

/**
 * Phone number structure
 */
export interface PhoneNumber {
  mobile?: string;
  home?: string;
  work?: string;
  other?: string;
}

/**
 * Contact types from database schema
 */
export type ContactItem = Database["public"]["Tables"]["contacts"]["Row"];
export type ContactItemInsert = Database["public"]["Tables"]["contacts"]["Insert"];
export type ContactItemUpdate = Database["public"]["Tables"]["contacts"]["Update"];

/**
 * Contact relationship types from database schema
 */
export type ContactRelationship = Database["public"]["Tables"]["contact_relationships"]["Row"];
export type ContactRelationshipInsert =
  Database["public"]["Tables"]["contact_relationships"]["Insert"];
export type ContactRelationshipUpdate =
  Database["public"]["Tables"]["contact_relationships"]["Update"];

/**
 * Contact search item type for autocomplete
 */
export interface ContactSearchItem {
  value: string;
  label: string;
}

/**
 * Search state type for contact search
 */
export interface SearchState {
  items: ContactItem[];
  loading: boolean;
  query: string;
}

/**
 * Initial search state
 */
export function initialSearchState(): SearchState {
  return {
    items: [],
    loading: false,
    query: "",
  };
}

/**
 * Contact history types from database schema
 */
export type ContactHistory = Database["public"]["Tables"]["contact_history"]["Row"];

export type FeatureDictionary = Dictionary extends {
  [K in typeof DOMAIN_ID]: { [L in typeof FEATURE_ID]: infer U };
}
  ? U
  : never;
