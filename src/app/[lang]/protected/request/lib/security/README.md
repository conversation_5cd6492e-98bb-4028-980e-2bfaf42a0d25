# Request Domain Permissions

This document outlines the permissions for the Request domain in the RQRSDA2025 application.

## Permission Structure

Permissions in the Request domain follow the standard format: `request:<action>`.

## Available Permissions

### request:view

- **Purpose**: Allows users to view request listings and individual request details.
- **Scope**: Provides read-only access to request data, including:
  - Viewing the list of requests
  - Viewing detailed information about a specific request
  - Viewing request history and status
- **Assigned Roles**:
  - Director: Full access to all requests within their organization
  - Coordinator: Access to view all requests within their organization
  - SocialWorker: Access to view requests within their organization

### request:create

- **Purpose**: Allows users to create new requests.
- **Scope**: Provides ability to:
  - Create new service requests
  - Submit initial request information
- **Assigned Roles**:
  - Director: Full access to create requests within their organization
  - Coordinator: Access to create requests within their organization
  - SocialWorker: No access to create requests

### request:update

- **Purpose**: Allows users to update existing requests.
- **Scope**: Provides ability to:
  - Edit request details
  - Update request status
- **Assigned Roles**:
  - Director: Full access to update requests within their organization
  - Coordinator: Access to update requests within their organization
  - SocialWorker: No access to update requests

### request:delete

- **Purpose**: Allows users to delete requests.
- **Scope**: Provides ability to:
  - Mark requests as deleted
  - Remove requests from the system
- **Assigned Roles**:
  - Director: Full access to delete requests within their organization
  - Coordinator: No access to delete requests
  - SocialWorker: No access to delete requests

### request:workflow

- **Purpose**: Allows users to manage request workflow transitions.
- **Scope**: Provides ability to:
  - Change request status (approve, reject, waitlist, complete)
  - Add workflow-related notes
  - Manage request lifecycle
- **Assigned Roles**:
  - Director: Full access to manage request workflows within their organization
  - Coordinator: Access to manage request workflows within their organization
  - SocialWorker: No access to manage request workflows

### request:admin

- **Purpose**: Provides administrative access to the request system.
- **Scope**: Provides ability to:
  - Manage request configurations
  - Override standard workflows
  - Access administrative functions
- **Assigned Roles**:
  - Director: Full access to request administration within their organization
  - Coordinator: No access to request administration
  - SocialWorker: No access to request administration

## Implementation Details

- All permissions are enforced through the authorization middleware
- Route-based permissions are defined in `src/lib/authorization/config/routePermissions.ts`
- Role-based permissions are defined in `src/lib/authorization/services/ConfigurationService.ts`
- Server actions are protected using the `requirePermission` decorator
