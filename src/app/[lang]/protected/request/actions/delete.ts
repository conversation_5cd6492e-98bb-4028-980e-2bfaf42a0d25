"use server";

import { revalidatePath } from "next/cache";
import { redirect } from "next/navigation";
import { RequestService } from "../lib/services/RequestService";
import { ActionState } from "@/lib/types/responses";
import { logger } from "@/lib/logger/services/LoggerService";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { DOMAIN_PERMISSIONS } from "../lib/config/domain";

/**
 * Delete a request
 * @param id The ID of the request to delete
 * @returns ActionState with success or error
 */
export const deleteRequest = requirePermission(DOMAIN_PERMISSIONS.DELETE)(async (
  id: string
): Promise<ActionState<null>> => {
  try {
    // Validate required fields
    if (!id) {
      return {
        success: false,
        error: "Request ID is required",
        data: null,
      };
    }

    // Delete the request
    const response = await RequestService.delete(id);

    if (!response.success) {
      return {
        success: false,
        error: response.message || "Failed to delete request",
        data: null,
      };
    }

    // Revalidate the requests list page
    revalidatePath("/[lang]/protected/request");

    return {
      success: true,
      error: "",
      data: null,
    };
  } catch (error) {
    logger.error(`Unexpected error deleting request: ${error}`);
    return {
      success: false,
      error: `Unexpected error deleting request: ${error}`,
      data: null,
    };
  }
});

/**
 * Delete a request and redirect to the requests list page
 * @param id The ID of the request to delete
 * @param lang The language code for the redirect URL
 */
export const deleteRequestAndRedirect = requirePermission(DOMAIN_PERMISSIONS.DELETE)(async (
  id: string,
  lang: string = "en"
): Promise<ActionState<null>> => {
  const result = await deleteRequest(id);

  if (result.success) {
    // Redirect to the requests list page
    redirect(`/${lang}/protected/request`);
  }

  return result;
});
