"use server";

import { RequestService } from "../lib/services/RequestService";
import { RequestWithRelations } from "../lib/types";
import { ActionState } from "@/lib/types/responses";
import { logger } from "@/lib/logger/services/LoggerService";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { DOMAIN_PERMISSIONS } from "../lib/config/domain";

/**
 * Get a request by ID with optional related data
 * @param id The ID of the request
 * @param includeMetadata Whether to include metadata
 * @param includeHistory Whether to include history
 * @param includeContacts Whether to include contacts
 * @returns ActionState with the request and related data
 */
export const getRequest = requirePermission(DOMAIN_PERMISSIONS.VIEW)(async (
  id: string,
  includeMetadata: boolean = false,
  includeHistory: boolean = false,
  includeContacts: boolean = false
): Promise<ActionState<RequestWithRelations | null>> => {
  try {
    // Call the service to get the request
    const response = await RequestService.view(
      id,
      includeMetadata,
      includeHistory,
      includeContacts
    );

    if (!response.success || !response.data) {
      return {
        success: false,
        error: response.message || "Failed to retrieve request",
        data: null,
      };
    }

    return {
      success: true,
      error: "",
      data: response.data,
    };
  } catch (error) {
    logger.error(`Unexpected error retrieving request: ${error}`);
    return {
      success: false,
      error: `Unexpected error retrieving request: ${error}`,
      data: null,
    };
  }
});

/**
 * Get a request with all related data (metadata, history, contacts)
 * @param id The ID of the request
 * @returns ActionState with the request and all related data
 */
export const getRequestWithAllRelations = requirePermission(DOMAIN_PERMISSIONS.VIEW)(async (
  id: string
): Promise<ActionState<RequestWithRelations | null>> => {
  return getRequest(id, true, true, true);
});

/**
 * Get a request with its history
 * @param id The ID of the request
 * @returns ActionState with the request and its history
 */
export const getRequestWithHistory = requirePermission(DOMAIN_PERMISSIONS.VIEW)(async (
  id: string
): Promise<ActionState<RequestWithRelations | null>> => {
  return getRequest(id, false, true, false);
});

/**
 * Get a request with its metadata
 * @param id The ID of the request
 * @returns ActionState with the request and its metadata
 */
export const getRequestWithMetadata = requirePermission(DOMAIN_PERMISSIONS.VIEW)(async (
  id: string
): Promise<ActionState<RequestWithRelations | null>> => {
  return getRequest(id, true, false, false);
});

/**
 * Get a request with its contacts
 * @param id The ID of the request
 * @returns ActionState with the request and its contacts
 */
export async function getRequestWithContacts(
  id: string
): Promise<ActionState<RequestWithRelations | null>> {
  return getRequest(id, false, false, true);
}
