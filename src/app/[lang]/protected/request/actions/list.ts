"use server";

import { logger } from "@/lib/logger/services/LoggerService";
import { RequestService } from "../lib/services/RequestService";
import { ActionState } from "@/lib/types/responses";
import { Request, RequestListParams } from "../lib/types";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { DOMAIN_PERMISSIONS } from "../lib/config/domain";

/**
 * List requests with optional pagination, filtering, and sorting
 * @param params Parameters for filtering, pagination, and sorting
 * @returns ActionState with the list of requests and total count
 */
export const listRequests = requirePermission(DOMAIN_PERMISSIONS.VIEW)(async (
  params: RequestListParams = {}
): Promise<ActionState<{ items: Request[]; total: number }>> => {
  try {
    // Set default values for pagination if not provided
    const listParams: RequestListParams = {
      page: params.page || 1,
      limit: params.limit || 10,
      sortBy: params.sortBy || "created_at",
      sortOrder: params.sortOrder || "desc",
      ...params,
    };

    // Validate pagination parameters
    if (listParams.page && listParams.page < 1) {
      return {
        success: false,
        error: "Page number must be at least 1",
        data: { items: [], total: 0 },
      };
    }

    if (listParams.limit && (listParams.limit < 1 || listParams.limit > 100)) {
      return {
        success: false,
        error: "Page size must be between 1 and 100",
        data: { items: [], total: 0 },
      };
    }

    // Call the service to get the requests
    const response = await RequestService.list(listParams);

    if (!response.success || !response.data) {
      return {
        success: false,
        error: response.message || "Failed to retrieve requests",
        data: { items: [], total: 0 },
      };
    }

    return {
      success: true,
      error: "",
      data: response.data,
    };
  } catch (error) {
    logger.error(`Unexpected error listing requests: ${error}`);
    return {
      success: false,
      error: `Unexpected error listing requests: ${error}`,
      data: { items: [], total: 0 },
    };
  }
});

/**
 * Search requests by title or description
 * @param searchQuery The search query
 * @param page The page number (1-based)
 * @param limit The number of items per page
 * @returns ActionState with the list of matching requests and total count
 */
export const searchRequests = requirePermission(DOMAIN_PERMISSIONS.VIEW)(async (
  searchQuery: string,
  page: number = 1,
  limit: number = 10
): Promise<ActionState<{ items: Request[]; total: number }>> => {
  return listRequests({
    page,
    limit,
    search: searchQuery,
    sortBy: "created_at",
    sortOrder: "desc",
  });
});

/**
 * Filter requests by status
 * @param status The status or array of statuses to filter by
 * @param page The page number (1-based)
 * @param limit The number of items per page
 * @returns ActionState with the list of matching requests and total count
 */
export const filterRequestsByStatus = requirePermission(DOMAIN_PERMISSIONS.VIEW)(async (
  status: string | string[],
  page: number = 1,
  limit: number = 10
): Promise<ActionState<{ items: Request[]; total: number }>> => {
  return listRequests({
    page,
    limit,
    status: status as any, // Type cast to match RequestStatus
    sortBy: "created_at",
    sortOrder: "desc",
  });
});
