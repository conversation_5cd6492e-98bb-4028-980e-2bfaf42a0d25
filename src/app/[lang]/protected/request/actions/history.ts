"use server";

import { logger } from "@/lib/logger/services/LoggerService";
import { RequestHistoryService } from "../lib/services/RequestHistoryService";
import { ActionState } from "@/lib/types/responses";
import { RequestHistory } from "../lib/types";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { DOMAIN_PERMISSIONS } from "../lib/config/domain";

/**
 * Interface for history retrieval parameters
 */
interface HistoryParams {
  requestId: string;
  page?: number;
  limit?: number;
  action?: string | string[];
  fromDate?: string;
  toDate?: string;
  userId?: string;
  includeUserDetails?: boolean;
  sortOrder?: "asc" | "desc";
}

/**
 * Get history entries for a request with advanced filtering and pagination
 * @param params Parameters for filtering and pagination
 * @returns ActionState with the history entries and formatted data for display
 */
export const getRequestHistory = requirePermission(DOMAIN_PERMISSIONS.VIEW)(async (
  params: HistoryParams
): Promise<ActionState<{ items: RequestHistory[]; total: number; formattedItems: any[] }>> => {
  try {
    // Validate required fields
    if (!params.requestId) {
      return {
        success: false,
        error: "Request ID is required",
        data: { items: [], total: 0, formattedItems: [] },
      };
    }

    // Set default values for pagination if not provided
    const historyParams = {
      ...params,
      page: params.page || 1,
      limit: params.limit || 20,
      includeUserDetails: params.includeUserDetails !== false, // Default to true
      sortOrder: params.sortOrder || "desc",
    };

    // Validate pagination parameters
    if (historyParams.page && historyParams.page < 1) {
      return {
        success: false,
        error: "Page number must be at least 1",
        data: { items: [], total: 0, formattedItems: [] },
      };
    }

    if (historyParams.limit && (historyParams.limit < 1 || historyParams.limit > 100)) {
      return {
        success: false,
        error: "Page size must be between 1 and 100",
        data: { items: [], total: 0, formattedItems: [] },
      };
    }

    // Call the service to get the request history
    const response = await RequestHistoryService.getHistoryByRequestId(historyParams);

    if (!response.success || !response.data) {
      return {
        success: false,
        error: response.message || "Failed to retrieve request history",
        data: { items: [], total: 0, formattedItems: [] },
      };
    }

    return {
      success: true,
      error: "",
      data: response.data,
    };
  } catch (error) {
    logger.error(`Unexpected error retrieving request history: ${error}`);
    return {
      success: false,
      error: `Unexpected error retrieving request history: ${error}`,
      data: { items: [], total: 0, formattedItems: [] },
    };
  }
});

/**
 * Get a specific history entry
 * @param historyId The ID of the history entry
 * @returns ActionState with the history entry
 */
export const getHistoryEntry = requirePermission(DOMAIN_PERMISSIONS.VIEW)(async (
  historyId: string
): Promise<ActionState<RequestHistory | null>> => {
  try {
    // Validate required fields
    if (!historyId) {
      return {
        success: false,
        error: "History entry ID is required",
        data: null,
      };
    }

    // Call the service to get the history entry
    const response = await RequestHistoryService.getHistoryEntry(historyId);

    if (!response.success || !response.data) {
      return {
        success: false,
        error: response.message || "Failed to retrieve history entry",
        data: null,
      };
    }

    return {
      success: true,
      error: "",
      data: response.data,
    };
  } catch (error) {
    logger.error(`Unexpected error retrieving history entry: ${error}`);
    return {
      success: false,
      error: `Unexpected error retrieving history entry: ${error}`,
      data: null,
    };
  }
});

/**
 * Get history entries with user details
 * @param requestId The ID of the request
 * @param page The page number (1-based)
 * @param limit The number of items per page
 * @returns ActionState with the history entries including user details
 */
export async function getHistoryWithUserDetails(
  requestId: string,
  page: number = 1,
  limit: number = 20
): Promise<ActionState<{ items: any[]; total: number }>> {
  try {
    // Validate required fields
    if (!requestId) {
      return {
        success: false,
        error: "Request ID is required",
        data: { items: [], total: 0 },
      };
    }

    // Calculate offset from page and limit
    const offset = (page - 1) * limit;

    // Call the service to get the history entries with user details
    const response = await RequestHistoryService.getHistoryWithUserDetails(
      requestId,
      limit,
      offset
    );

    if (!response.success || !response.data) {
      return {
        success: false,
        error: response.message || "Failed to retrieve history entries with user details",
        data: { items: [], total: 0 },
      };
    }

    return {
      success: true,
      error: "",
      data: response.data,
    };
  } catch (error) {
    logger.error(`Unexpected error retrieving history entries with user details: ${error}`);
    return {
      success: false,
      error: `Unexpected error retrieving history entries with user details: ${error}`,
      data: { items: [], total: 0 },
    };
  }
}

/**
 * Compare changes between two request versions
 * @param requestId The ID of the request
 * @param historyId1 The ID of the first history entry
 * @param historyId2 The ID of the second history entry
 * @returns ActionState with formatted differences between the two versions
 */
export async function compareRequestChanges(
  requestId: string,
  historyId1: string,
  historyId2: string
): Promise<
  ActionState<{
    differences: Record<string, { old: any; new: any }>;
    formattedDifferences: any[];
    metadata: {
      olderVersion: {
        id: string;
        timestamp: string;
        user: any;
      };
      newerVersion: {
        id: string;
        timestamp: string;
        user: any;
      };
    };
  }>
> {
  try {
    // Validate required fields
    if (!requestId) {
      return {
        success: false,
        error: "Request ID is required",
        data: {
          differences: {},
          formattedDifferences: [],
          metadata: {
            olderVersion: { id: "", timestamp: "", user: null },
            newerVersion: { id: "", timestamp: "", user: null },
          },
        },
      };
    }

    if (!historyId1 || !historyId2) {
      return {
        success: false,
        error: "Both history entry IDs are required",
        data: {
          differences: {},
          formattedDifferences: [],
          metadata: {
            olderVersion: { id: "", timestamp: "", user: null },
            newerVersion: { id: "", timestamp: "", user: null },
          },
        },
      };
    }

    // Call the service to compare changes
    const response = await RequestHistoryService.compareChanges(requestId, historyId1, historyId2);

    if (!response.success || !response.data) {
      return {
        success: false,
        error: response.message || "Failed to compare changes",
        data: {
          differences: {},
          formattedDifferences: [],
          metadata: {
            olderVersion: { id: "", timestamp: "", user: null },
            newerVersion: { id: "", timestamp: "", user: null },
          },
        },
      };
    }

    return {
      success: true,
      error: "",
      data: response.data,
    };
  } catch (error) {
    logger.error(`Unexpected error comparing changes: ${error}`);
    return {
      success: false,
      error: `Unexpected error comparing changes: ${error}`,
      data: {
        differences: {},
        formattedDifferences: [],
        metadata: {
          olderVersion: { id: "", timestamp: "", user: null },
          newerVersion: { id: "", timestamp: "", user: null },
        },
      },
    };
  }
}

/**
 * Get a detailed comparison between two request versions
 * @param requestId The ID of the request
 * @param versionA The ID of the first version (history entry)
 * @param versionB The ID of the second version (history entry)
 * @returns ActionState with detailed comparison results
 */
export async function getDetailedComparison(
  requestId: string,
  versionA: string,
  versionB: string
): Promise<
  ActionState<{
    comparison: any;
    metadata: any;
    summary: {
      totalChanges: number;
      addedFields: string[];
      removedFields: string[];
      modifiedFields: string[];
    };
  }>
> {
  try {
    // Validate required fields
    if (!requestId) {
      return {
        success: false,
        error: "Request ID is required",
        data: {
          comparison: null,
          metadata: null,
          summary: {
            totalChanges: 0,
            addedFields: [],
            removedFields: [],
            modifiedFields: [],
          },
        },
      };
    }

    if (!versionA || !versionB) {
      return {
        success: false,
        error: "Both version IDs are required",
        data: {
          comparison: null,
          metadata: null,
          summary: {
            totalChanges: 0,
            addedFields: [],
            removedFields: [],
            modifiedFields: [],
          },
        },
      };
    }

    // Call the service to get detailed comparison
    const response = await RequestHistoryService.getDetailedComparison(
      requestId,
      versionA,
      versionB
    );

    if (!response.success || !response.data) {
      return {
        success: false,
        error: response.message || "Failed to get detailed comparison",
        data: {
          comparison: null,
          metadata: null,
          summary: {
            totalChanges: 0,
            addedFields: [],
            removedFields: [],
            modifiedFields: [],
          },
        },
      };
    }

    return {
      success: true,
      error: "",
      data: response.data,
    };
  } catch (error) {
    logger.error(`Unexpected error getting detailed comparison: ${error}`);
    return {
      success: false,
      error: `Unexpected error getting detailed comparison: ${error}`,
      data: {
        comparison: null,
        metadata: null,
        summary: {
          totalChanges: 0,
          addedFields: [],
          removedFields: [],
          modifiedFields: [],
        },
      },
    };
  }
}
