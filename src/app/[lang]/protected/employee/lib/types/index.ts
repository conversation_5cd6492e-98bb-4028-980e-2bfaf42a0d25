/**
 * Employee interface
 * Represents a single employee
 */
export interface Employee {
  id: string;
  organization_id: string;
  user_account_id?: string;
  first_name: string;
  last_name: string;
  profile_image?: string;
  date_of_birth?: string;
  gender?: string;
  address?: EmployeeAddress;
  employee_id?: string;
  hire_date?: string;
  termination_date?: string;
  employment_status: "active" | "inactive" | "terminated";
  job_title?: string;
  department?: string;
  supervisor_id?: string;
  specializations?: string[];
  certifications?: EmployeeCertification[];
  education?: EmployeeEducation[];
  emails?: EmployeeEmail[];
  phones?: EmployeePhone[];
  created_at: string;
  updated_at: string;
}

/**
 * Employee Address interface
 */
export interface EmployeeAddress {
  street: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
}

/**
 * Employee Certification interface
 */
export interface EmployeeCertification {
  name: string;
  issuer: string;
  date_obtained: string;
  expiration_date?: string;
}

/**
 * Employee Education interface
 */
export interface EmployeeEducation {
  institution: string;
  degree: string;
  field_of_study: string;
  start_date: string;
  end_date?: string;
}

/**
 * Employee Email interface
 */
export interface EmployeeEmail {
  type: "work" | "personal" | "other";
  email: string;
  primary: boolean;
}

/**
 * Employee Phone interface
 */
export interface EmployeePhone {
  type: "work" | "mobile" | "home" | "other";
  number: string;
  primary: boolean;
}

/**
 * Employee Insert interface
 * Used when creating a new employee
 */
export interface EmployeeInsert {
  organization_id: string;
  user_account_id?: string;
  first_name: string;
  last_name: string;
  profile_image?: string;
  date_of_birth?: string;
  gender?: string;
  address?: EmployeeAddress;
  employee_id?: string;
  hire_date?: string;
  termination_date?: string;
  employment_status: "active" | "inactive" | "terminated";
  job_title?: string;
  department?: string;
  supervisor_id?: string;
  specializations?: string[];
  certifications?: EmployeeCertification[];
  education?: EmployeeEducation[];
  emails?: EmployeeEmail[];
  phones?: EmployeePhone[];
}

/**
 * Employee Update interface
 * Used when updating an existing employee
 */
export interface EmployeeUpdate {
  user_account_id?: string;
  first_name?: string;
  last_name?: string;
  profile_image?: string;
  date_of_birth?: string;
  gender?: string;
  address?: EmployeeAddress;
  employee_id?: string;
  hire_date?: string;
  termination_date?: string;
  employment_status?: "active" | "inactive" | "terminated";
  job_title?: string;
  department?: string;
  supervisor_id?: string;
  specializations?: string[];
  certifications?: EmployeeCertification[];
  education?: EmployeeEducation[];
  emails?: EmployeeEmail[];
  phones?: EmployeePhone[];
  updated_at: string;
}
