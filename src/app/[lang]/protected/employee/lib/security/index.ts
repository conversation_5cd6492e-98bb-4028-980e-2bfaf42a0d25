/**
 * Security exports for Employee Domain
 *
 * This file exports all security-related configurations from all features.
 */

// Import permissions from features
import managementSecurity from "../../(features)/management/lib/security";

// Export all permissions
const security = {
  // Management permissions
  management: managementSecurity,

  // Combine all route permissions
  routePermissions: {
    ...managementSecurity.routePermissions,
    // Add more feature route permissions here
  },
};

export default security;
