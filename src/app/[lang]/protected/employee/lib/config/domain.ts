/**
 * Domain Configuration
 *
 * This file defines the domain-level configuration including:
 * - Domain name
 * - Base path
 * - Features list
 * - Domain-level permissions
 *
 * This configuration is used throughout the domain to ensure consistency
 * and make it easier to update paths, names, and permissions in one place.
 */

// Domain identifier (used in permissions, routes, etc.)
export const DOMAIN_ID = "employee";

// Domain display name (used in UI)
export const DOMAIN_NAME = "Employee";

// Base path for all routes in this domain
export const DOMAIN_BASE_PATH = "/protected/employee";

// Domain description (used in UI)
export const DOMAIN_DESCRIPTION = "Employee management and availability";

// Domain permissions
export const DOMAIN_PERMISSIONS = {
  VIEW: `${DOMAIN_ID}:view`,
  ADMIN: `${DOMAIN_ID}:admin`,
};

// Domain configuration object
const DOMAIN_CONFIG = {
  id: DOMAIN_ID,
  name: DOMAIN_NAME,
  basePath: DOMAIN_BASE_PATH,
  description: DOMAIN_DESCRIPTION,
  permissions: DOMAIN_PERMISSIONS,
};

export default DOMAIN_CONFIG;
