import { createClient } from "@/lib/supabase/server";
import { ServiceResponse, errorResponse, successResponse } from "@/lib/types/responses";
import { ProfileService } from "@/app/[lang]/protected/organization/(features)/profile/lib/services/ProfileService";
import { logger } from "@/lib/logger/services/LoggerService";

/**
 * Service for employee management operations
 */
export class EmployeeService {
  /**
   * Get an employee by ID
   * @param id The employee ID
   * @returns Service response with the employee data
   */
  static async getEmployeeById(id: string): Promise<ServiceResponse> {
    try {
      const organization = await ProfileService.getCurrentOrganization();
      if (!organization) {
        return errorResponse(null, "Organization not found");
      }
      const organizationId = organization.id;

      const supabase = await createClient();
      const { data, error } = await supabase
        .from("employees")
        .select("*")
        .eq("id", id)
        .eq("organization_id", organizationId)
        .single();

      if (error) {
        logger.error(`Error fetching employee: ${error.message}`);
        return errorResponse(error, "Failed to fetch employee");
      }

      return successResponse(data);
    } catch (error) {
      logger.error(`Error in getEmployeeById: ${error}`);
      return errorResponse(error, "An unexpected error occurred");
    }
  }

  /**
   * List all employees
   * @returns Service response with the list of employees
   */
  static async listEmployees(): Promise<ServiceResponse> {
    try {
      const organization = await ProfileService.getCurrentOrganization();
      if (!organization) {
        return errorResponse(null, "Organization not found");
      }
      const organizationId = organization.id;

      const supabase = await createClient();
      const { data, error } = await supabase
        .from("employees")
        .select("*")
        .eq("organization_id", organizationId)
        .order("last_name", { ascending: true });

      if (error) {
        logger.error(`Error listing employees: ${error.message}`);
        return errorResponse(error, "Failed to list employees");
      }

      return successResponse(data || []);
    } catch (error) {
      logger.error(`Error in listEmployees: ${error}`);
      return errorResponse(error, "An unexpected error occurred");
    }
  }
}
