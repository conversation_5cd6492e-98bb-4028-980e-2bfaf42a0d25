"use server";

import { revalidatePath } from "next/cache";
import { logger } from "@/lib/logger/services/LoggerService";
import { ManagementService } from "../lib/services/ManagementService";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { MANAGEMENT_PERMISSIONS } from "../lib/security/permissions";
import { redirect } from "next/navigation";
import { ActionState, errorActionState, successActionState } from "@/lib/types/responses";
import { Employee } from "../lib/types";
import { auth } from "@/lib/authentication/services/AuthenticationService";
import FEATURE_CONFIG from "../lib/config";

/**
 * Create a new employee
 * @param prevState Previous state
 * @param formData Form data
 * @returns Result of the create operation
 */
export const create = requirePermission(MANAGEMENT_PERMISSIONS.CREATE)(async (
  _prevState: ActionState<Employee>,
  formData: FormData
): Promise<ActionState<Employee>> => {
  // Variables to store data needed for redirect
  let redirectUrl: string | null = null;
  let lang: string | undefined;

  try {
    // Get form data
    const firstName = formData.get("firstName") as string;
    const lastName = formData.get("lastName") as string;
    const employmentStatus = formData.get("employmentStatus") as
      | "active"
      | "inactive"
      | "terminated";
    const jobTitle = formData.get("jobTitle") as string;
    const department = formData.get("department") as string;
    const profile = await auth.getCurrentUserProfile();
    const organizationId = profile?.organizationId;
    lang = profile?.language;

    // Validate required fields
    if (!firstName) {
      return errorActionState("First name is required");
    }

    if (!lastName) {
      return errorActionState("Last name is required");
    }

    if (!organizationId) {
      return errorActionState("Organization ID is required");
    }

    // Handle "unspecified" values
    const processedDepartment = department === "unspecified" ? null : department;

    // Get additional form fields
    const gender = formData.get("gender") as string;
    const processedGender = gender === "unspecified" ? null : gender;

    // Create the employee
    const result = await ManagementService.create({
      first_name: firstName,
      last_name: lastName,
      employment_status: employmentStatus || "active",
      job_title: jobTitle || undefined,
      department: processedDepartment || undefined,
      gender: processedGender || undefined,
      organization_id: organizationId,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    });

    if (!result.success) {
      logger.error(`Error creating employee: ${result.error}`);
      return errorActionState(`An error occurred while creating the employee: ${result.message}`);
    }

    // Revalidate the employees list page
    revalidatePath(`/${lang}${FEATURE_CONFIG.routes.LIST}`);

    // Set the redirect URL to be used after the try-catch block
    redirectUrl = `/${lang}${FEATURE_CONFIG.routes.VIEW(result.data?.id as string)}`;

    // Return success state (this will only be used if redirect doesn't happen)
    return successActionState(result.data as Employee);
  } catch (error) {
    logger.error(`Unexpected error creating employee: ${error}`);
    return errorActionState(`An unexpected error occurred: ${error}`);
  } finally {
    // Perform redirect outside of try-catch if URL is set
    if (redirectUrl) {
      redirect(redirectUrl);
    }
  }
});
