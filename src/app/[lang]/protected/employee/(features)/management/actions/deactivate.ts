"use server";

import { revalidatePath } from "next/cache";
import { logger } from "@/lib/logger/services/LoggerService";
import { ManagementService } from "../lib/services/ManagementService";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { MANAGEMENT_PERMISSIONS } from "../lib/security/permissions";
import { ActionState, errorActionState, successActionState } from "@/lib/types/responses";
import { Employee } from "../lib/types";
import FEATURE_CONFIG from "../lib/config";

/**
 * Deactivate an employee
 * @param prevState Previous state
 * @param formData Form data
 * @returns Result of the deactivate operation
 */
export const deactivate = requirePermission(MANAGEMENT_PERMISSIONS.DEACTIVATE)(async (
  _prevState: ActionState<Employee>,
  formData: FormData
): Promise<ActionState<Employee>> => {
  try {
    // Get form data
    const id = _prevState.data?.id;
    const lang = formData.get("lang") as string;

    // Validate required fields
    if (!id) {
      return errorActionState("Employee ID is required");
    }

    // Deactivate the employee
    const result = await ManagementService.deactivate(id);

    if (!result.success) {
      logger.error(`Error deactivating employee: ${result.error}`);
      return errorActionState(`Failed to deactivate employee: ${result.message}`);
    }

    // Revalidate the employee pages
    revalidatePath(`/${lang}${FEATURE_CONFIG.routes.LIST}`);
    revalidatePath(`/${lang}${FEATURE_CONFIG.routes.VIEW(id)}`);

    // Return success with the deactivated employee
    return successActionState(result.data);
  } catch (error) {
    logger.error(`Unexpected error deactivating employee: ${error}`);
    return errorActionState(`An unexpected error occurred: ${error}`);
  }
});
