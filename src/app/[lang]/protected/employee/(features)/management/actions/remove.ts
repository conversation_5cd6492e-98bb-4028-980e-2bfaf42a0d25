"use server";

import { revalidatePath } from "next/cache";
import { logger } from "@/lib/logger/services/LoggerService";
import { ManagementService } from "../lib/services/ManagementService";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { MANAGEMENT_PERMISSIONS } from "../lib/security/permissions";
import { ActionState, errorActionState, successActionState } from "@/lib/types/responses";
import { Employee } from "../lib/types";
import { redirect } from "next/navigation";
import FEATURE_CONFIG from "../lib/config";

/**
 * Remove an employee (soft delete by setting employment_status to terminated)
 * @param prevState Previous state
 * @param formData Form data
 * @returns Result of the remove operation
 */
export const remove = requirePermission(MANAGEMENT_PERMISSIONS.REMOVE)(async (
  _prevState: ActionState<Employee>,
  formData: FormData
): Promise<ActionState<Employee>> => {
  // Variables to store data needed for redirect
  let redirectUrl: string | null = null;
  let lang: string | undefined;
  let result;
  try {
    // Get form data
    const id = _prevState.data?.id;
    lang = formData.get("lang") as string;

    // Validate required fields
    if (!id) {
      return errorActionState("Employee ID is required");
    }

    // Remove the employee
    result = await ManagementService.remove(id);

    if (!result.success) {
      logger.error(`Error removing employee: ${result.error}`);
      return errorActionState(`Failed to remove employee: ${result.message}`);
    }

    // Revalidate the employees list page
    revalidatePath(`/${lang}${FEATURE_CONFIG.routes.LIST}`);

    // Set the redirect URL to be used after the try-catch block
    redirectUrl = `/${lang}${FEATURE_CONFIG.routes.LIST}`;
  } catch (error) {
    logger.error(`Unexpected error removing employee: ${error}`);
    return errorActionState(`An unexpected error occurred: ${error}`);
  } finally {
    // Perform redirect outside of try-catch if URL is set
    if (redirectUrl) {
      redirect(redirectUrl);
    }

    // Return success state (this will only be used if redirect doesn't happen)
    return successActionState(result?.data as unknown as Employee);
  }
});
