"use server";

import { revalidatePath } from "next/cache";
import { logger } from "@/lib/logger/services/LoggerService";
import { ManagementService } from "../lib/services/ManagementService";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { MANAGEMENT_PERMISSIONS } from "../lib/security/permissions";
import { ActionState, errorActionState, successActionState } from "@/lib/types/responses";
import { Employee } from "../lib/types";
import FEATURE_CONFIG from "../lib/config";

/**
 * Link an employee to a user account
 * @param prevState Previous state
 * @param formData Form data
 * @returns Result of the link operation
 */
export const linkUserAccount = requirePermission(MANAGEMENT_PERMISSIONS.EDIT)(async (
  _prevState: ActionState<Employee>,
  formData: FormData
): Promise<ActionState<Employee>> => {
  try {
    // Get form data
    const id = _prevState.data?.id;
    const userAccountId = formData.get("userAccountId") as string;
    const lang = formData.get("lang") as string;

    // Validate required fields
    if (!id) {
      return errorActionState("Employee ID is required");
    }

    if (!userAccountId) {
      return errorActionState("User Account ID is required");
    }

    // Link the employee to the user account
    const result = await ManagementService.linkUserAccount(id, userAccountId);

    if (!result.success) {
      logger.error(`Error linking user account: ${result.error}`);
      return errorActionState(`Failed to link user account: ${result.message}`);
    }

    // Revalidate the employee pages
    revalidatePath(`/${lang}${FEATURE_CONFIG.routes.LIST}`);
    revalidatePath(`/${lang}${FEATURE_CONFIG.routes.VIEW(id)}`);

    // Return success with the updated employee
    return successActionState(result.data);
  } catch (error) {
    logger.error(`Unexpected error linking user account: ${error}`);
    return errorActionState(`An unexpected error occurred: ${error}`);
  }
});
