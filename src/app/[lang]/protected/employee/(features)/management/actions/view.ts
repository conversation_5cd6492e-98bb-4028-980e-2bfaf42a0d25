"use server";

import { logger } from "@/lib/logger/services/LoggerService";
import { ManagementService } from "../lib/services/ManagementService";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { MANAGEMENT_PERMISSIONS } from "../lib/security/permissions";
import { ActionState, errorActionState, successActionState } from "@/lib/types/responses";
import { Employee } from "../lib/types";

interface ViewParams {
  id: string;
}

/**
 * Get an employee by ID
 * @param _prevState Previous state
 * @param params Object containing the employee ID
 * @returns The employee
 */
export const view = requirePermission(MANAGEMENT_PERMISSIONS.VIEW)(async (
  _prevState: ActionState<Employee>,
  params: ViewParams
): Promise<ActionState<Employee>> => {
  try {
    const { id } = params;

    // Validate required fields
    if (!id) {
      return errorActionState("Employee ID is required");
    }

    // Get the employee
    const result = await ManagementService.view(id);

    if (!result.success) {
      logger.error(`Error retrieving employee: ${result.error}`);
      return errorActionState(`Failed to retrieve employee: ${result.error}`);
    }

    // Return the employee
    return successActionState(result.data);
  } catch (error) {
    logger.error(`Unexpected error retrieving employee: ${error}`);
    return errorActionState(`An unexpected error occurred: ${error}`);
  }
});
