import { redirect } from "next/navigation";

interface ViewPageProps {
  params: Promise<{
    lang: string;
    id: string;
  }>;
}

/**
 * Default view page that redirects to the contact tab
 */
export default async function ViewPage({ params }: ViewPageProps) {
  const { lang, id } = await params;

  // Redirect to the contact tab
  redirect(`/${lang}/protected/employee/management/${id}/view/contact`);
}
