import { notFound } from "next/navigation";
import { ActionState } from "@/lib/types/responses";
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card";
import Link from "next/link";
import { i18n } from "@/lib/i18n/services/I18nService";

// Import from the management feature
import { view } from "@/app/[lang]/protected/employee/(features)/management/actions/view";
import { Employee } from "@/app/[lang]/protected/employee/(features)/management/lib/types";
import { getDomainFeatureDictionary } from "@/app/[lang]/protected/employee/(features)/management/lib/utils/dictionary";
import { EmployeeViewHeader } from "@/app/[lang]/protected/employee/(features)/management/components/view/EmployeeViewHeader";
import { EmployeeViewTabs } from "@/app/[lang]/protected/employee/(features)/management/components/view/EmployeeViewTabs";

interface ViewLayoutProps {
  children: React.ReactNode;
  params: Promise<{
    lang: string;
    id: string;
  }>;
}

/**
 * Layout for the employee view pages
 * Provides the common structure and navigation for all view pages
 */
export default async function ViewLayout({ children, params }: ViewLayoutProps) {
  const { lang, id } = await params;

  // Create initial state
  const initialState: ActionState<Employee> = {
    success: true,
    error: "",
    data: null,
  };

  // Fetch the item using the server action
  const state = await view(initialState, { id });

  // If the item is not found, show the not-found page
  if (!state.success || !state.data) {
    notFound();
  }

  // Get the dictionary
  const baseDictionary = await i18n.getDictionary(lang);
  const dictionary = getDomainFeatureDictionary(baseDictionary);

  // Render the layout with the employee data
  return (
    <Card className="w-full max-w-4xl mx-auto">
      {/* Employee header with name, job title, and action buttons */}
      <EmployeeViewHeader employee={state.data} dictionary={dictionary} lang={lang} />

      <CardContent>
        {/* Tab navigation */}
        <EmployeeViewTabs employee={state.data} dictionary={dictionary} lang={lang} id={id} />

        {/* Tab content (rendered by child pages) */}
        {children}
      </CardContent>

      <CardFooter className="flex justify-between">
        <Link
          href={`/${lang}/protected/employee/management/list`}
          className="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-input hover:bg-accent hover:text-accent-foreground h-10 py-2 px-4"
        >
          {dictionary.backToList || "Back to List"}
        </Link>
      </CardFooter>
    </Card>
  );
}
