import { redirect } from "next/navigation";
import FEATURE_CONFIG from "../lib/config";

interface Feature1DefaultPageProps {
  params: Promise<{
    lang: string;
  }>;
}

/**
 * Default page for Feature 1 pages in Template Single CRUD
 * Redirects to the list page
 */
export default async function Feature1DefaultPage({ params }: Feature1DefaultPageProps) {
  // Await the params
  const resolvedParams = await params;

  // Redirect to the list page
  redirect(`/${resolvedParams.lang}${FEATURE_CONFIG.routes.LIST}`);
}
