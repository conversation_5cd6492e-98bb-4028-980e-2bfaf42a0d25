"use client";

import { ErrorDisplay } from "@/components/ui/error-display";

/**
 * Error component for the employee view pages
 */
export default function ViewError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  return (
    <ErrorDisplay
      title="Error Loading Employee"
      message="There was a problem loading the employee information."
      error={error}
      reset={reset}
    />
  );
}
