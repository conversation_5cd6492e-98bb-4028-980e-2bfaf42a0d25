import { CreateForm } from "../../components/CreateForm";
import { i18n } from "@/lib/i18n/services/I18nService";
import { getDomainFeatureDictionary } from "../../lib/utils/dictionary";

interface CreatePageProps {
  params: Promise<{
    lang: string;
  }>;
}

/**
 * Create page for Feature 1 items
 * Uses the CreateForm component to create a new Feature 1 item
 */
export default async function CreatePage({ params }: CreatePageProps) {
  // Await the params
  const { lang } = await params;

  // Get the dictionary
  const baseDictionary = await i18n.getDictionary(lang);
  const dictionary = getDomainFeatureDictionary(baseDictionary);

  return <CreateForm dictionary={dictionary} />;
}
