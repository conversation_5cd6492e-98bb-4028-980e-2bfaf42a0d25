import { Skeleton } from "@/components/ui/skeleton";

/**
 * Loading state for the employment details page
 */
export default function EmploymentLoading() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {Array.from({ length: 8 }).map((_, index) => (
        <div key={index}>
          <Skeleton className="h-4 w-24 mb-2" />
          <Skeleton className="h-6 w-32" />
        </div>
      ))}
    </div>
  );
}
