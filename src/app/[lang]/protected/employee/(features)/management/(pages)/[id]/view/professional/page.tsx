import { notFound } from "next/navigation";
import { ActionState } from "@/lib/types/responses";
import { i18n } from "@/lib/i18n/services/I18nService";

// Import from the management feature
import { view } from "@/app/[lang]/protected/employee/(features)/management/actions/view";
import { Employee } from "@/app/[lang]/protected/employee/(features)/management/lib/types";
import { getDomainFeatureDictionary } from "@/app/[lang]/protected/employee/(features)/management/lib/utils/dictionary";
import { ProfessionalInfoDisplay } from "@/app/[lang]/protected/employee/(features)/management/components/view/ProfessionalInfoDisplay";

interface ProfessionalPageProps {
  params: Promise<{
    lang: string;
    id: string;
  }>;
}

/**
 * Professional information page for employee view
 */
export default async function ProfessionalPage({ params }: ProfessionalPageProps) {
  const { lang, id } = await params;

  // Create initial state
  const initialState: ActionState<Employee> = {
    success: true,
    error: "",
    data: null,
  };

  // Fetch the item using the server action
  const state = await view(initialState, { id });

  // If the item is not found, show the not-found page
  if (!state.success || !state.data) {
    notFound();
  }

  // Get the dictionary
  const baseDictionary = await i18n.getDictionary(lang);
  const dictionary = getDomainFeatureDictionary(baseDictionary);

  // Render the professional information display component
  return <ProfessionalInfoDisplay employee={state.data} dictionary={dictionary} />;
}
