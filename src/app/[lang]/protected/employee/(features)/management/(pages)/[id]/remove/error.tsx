"use client";

import { ErrorDisplay } from "@/components/ui/error-display";

/**
 * Error component for the Remove page
 */
export default function RemoveError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  return (
    <ErrorDisplay
      title="Error Removing Item"
      message="There was a problem loading the remove confirmation page."
      error={error}
      reset={reset}
    />
  );
}
