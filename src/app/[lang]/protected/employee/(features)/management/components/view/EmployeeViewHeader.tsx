"use client";

import { Employee, FeatureDictionary } from "../../lib/types";
import { <PERSON><PERSON><PERSON><PERSON>, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Edit, Trash, UserCheck, UserX } from "lucide-react";
import Link from "next/link";
import FEATURE_CONFIG from "../../lib/config";
import { useRouter } from "next/navigation";
import { useTransition } from "react";
import { activate } from "../../actions/activate";
import { deactivate } from "../../actions/deactivate";

interface EmployeeViewHeaderProps {
  employee: Employee;
  dictionary: FeatureDictionary;
  lang: string;
}

/**
 * Header component for the employee view
 * Displays employee name, job title, status, and action buttons
 */
export function EmployeeViewHeader({ employee, dictionary, lang }: EmployeeViewHeaderProps) {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();

  // Handle employee activation
  const handleActivate = () => {
    startTransition(async () => {
      const formData = new FormData();
      formData.append("lang", lang || "en");
      await activate({ success: true, error: "", data: employee }, formData);
      router.refresh();
    });
  };

  // Handle employee deactivation
  const handleDeactivate = () => {
    startTransition(async () => {
      const formData = new FormData();
      formData.append("lang", lang || "en");
      await deactivate({ success: true, error: "", data: employee }, formData);
      router.refresh();
    });
  };

  // Get status badge color
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return (
          <Badge className="bg-green-500">{dictionary.employmentStatus?.active || "Active"}</Badge>
        );
      case "inactive":
        return (
          <Badge variant="outline">{dictionary.employmentStatus?.inactive || "Inactive"}</Badge>
        );
      case "terminated":
        return (
          <Badge variant="destructive">
            {dictionary.employmentStatus?.terminated || "Terminated"}
          </Badge>
        );
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  // Get initials for avatar fallback
  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  return (
    <CardHeader>
      <div className="flex flex-col sm:flex-row justify-between items-start gap-4">
        <div className="flex items-center gap-4 w-full sm:w-auto">
          <Avatar className="size-16 border shrink-0">
            {employee.profile_image ? (
              <AvatarImage
                src={employee.profile_image}
                alt={`${employee.first_name} ${employee.last_name}`}
              />
            ) : (
              <AvatarFallback className="bg-primary/10 text-primary text-lg">
                {getInitials(employee.first_name, employee.last_name)}
              </AvatarFallback>
            )}
          </Avatar>
          <div className="min-w-0">
            <CardTitle className="text-2xl truncate">
              {employee.first_name} {employee.last_name}
            </CardTitle>
            <CardDescription className="mt-1 truncate">
              {employee.job_title || dictionary.common?.noJobTitle || "No job title"}{" "}
              {employee.department ? `• ${employee.department}` : ""}
            </CardDescription>
          </div>
        </div>
        <div className="flex flex-wrap gap-2">
          {/* Status Badge */}
          {getStatusBadge(employee.employment_status)}

          {/* Status Management Buttons */}
          {employee.employment_status === "active" ? (
            <Dialog>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm" className="gap-1">
                  <UserX className="h-4 w-4" />
                  {dictionary.actions?.deactivate || "Deactivate"}
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>
                    {dictionary.confirmDeactivate || "Confirm Deactivation"}
                  </DialogTitle>
                  <DialogDescription>
                    {dictionary.deactivateDescription ||
                      "Are you sure you want to deactivate this employee? They will no longer have access to the system."}
                  </DialogDescription>
                </DialogHeader>
                <DialogFooter>
                  <Button
                    variant="outline"
                    onClick={() =>
                      document.querySelector<HTMLButtonElement>("[data-dialog-close]")?.click()
                    }
                  >
                    {dictionary.common?.cancel || "Cancel"}
                  </Button>
                  <Button variant="default" onClick={handleDeactivate} disabled={isPending}>
                    {isPending
                      ? dictionary.deactivating || "Deactivating..."
                      : dictionary.actions?.deactivate || "Deactivate"}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          ) : (
            <Button
              variant="outline"
              size="sm"
              className="gap-1"
              onClick={handleActivate}
              disabled={isPending}
            >
              <UserCheck className="h-4 w-4" />
              {isPending
                ? dictionary.activating || "Activating..."
                : dictionary.actions?.activate || "Activate"}
            </Button>
          )}

          {/* Edit Button */}
          <Button variant="outline" size="sm" className="gap-1" asChild>
            <Link href={`/${lang}${FEATURE_CONFIG.routes.EDIT(employee.id)}`}>
              <Edit className="h-4 w-4" />
              {dictionary.actions?.edit || "Edit"}
            </Link>
          </Button>

          {/* Remove Button */}
          <Button variant="destructive" size="sm" className="gap-1" asChild>
            <Link href={`/${lang}${FEATURE_CONFIG.routes.REMOVE(employee.id)}`}>
              <Trash className="h-4 w-4" />
              {dictionary.actions?.remove || "Remove"}
            </Link>
          </Button>
        </div>
      </div>
    </CardHeader>
  );
}
