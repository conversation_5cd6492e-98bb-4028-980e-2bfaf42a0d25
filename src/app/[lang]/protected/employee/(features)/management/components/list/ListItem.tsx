"use client";

import { But<PERSON> } from "@/components/ui/button";
import { TableCell, TableRow } from "@/components/ui/table";
import { Employee, FeatureDictionary } from "../../lib/types";
import Link from "next/link";
import FEATURE_CONFIG from "../../lib/config";

interface ListItemProps {
  item: Employee;
  lang: string;
  dictionary: FeatureDictionary;
}

/**
 * Component for rendering a single Feature 1 item in the list table
 */
export function ListItem({ item, lang, dictionary }: ListItemProps) {
  return (
    <TableRow key={item.id}>
      <TableCell className="font-medium">
        {item.first_name} {item.last_name}
      </TableCell>
      <TableCell>
        <span
          className={`px-2 py-1 rounded-full text-xs ${
            item.employment_status === "active"
              ? "bg-green-100 text-green-800"
              : item.employment_status === "inactive"
                ? "bg-gray-100 text-gray-800"
                : "bg-red-100 text-red-800"
          }`}
        >
          {item.employment_status === "active"
            ? dictionary.employmentStatus?.active || "Active"
            : item.employment_status === "inactive"
              ? dictionary.employmentStatus?.inactive || "Inactive"
              : dictionary.employmentStatus?.terminated || "Terminated"}
        </span>
      </TableCell>
      <TableCell className="max-w-xs truncate">{item.job_title || "-"}</TableCell>
      <TableCell className="text-right">
        <div className="flex justify-end gap-2">
          <Button variant="outline" size="sm" asChild>
            <Link href={`/${lang}${FEATURE_CONFIG.routes.VIEW(item.id)}`}>
              {dictionary.actions?.view || "View"}
            </Link>
          </Button>
          <Button variant="outline" size="sm" asChild>
            <Link href={`/${lang}${FEATURE_CONFIG.routes.EDIT(item.id)}`}>
              {dictionary.actions?.edit || "Edit"}
            </Link>
          </Button>
        </div>
      </TableCell>
    </TableRow>
  );
}
