"use client";

import { Employee, FeatureDictionary } from "../lib/types";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

import { CreateButton, SearchBar, ListTable, Pagination } from "./list";

interface ListProps {
  lang: string;
  initialItems: Employee[];
  totalItems: number;
  currentPage: number;
  search?: string;
  dictionary: FeatureDictionary;
}

/**
 * Component for displaying a list of Feature 1 items
 * Composed of smaller, focused components for better separation of concerns
 */
export function List({
  lang,
  initialItems,
  totalItems,
  currentPage,
  search,
  dictionary,
}: ListProps) {
  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>{dictionary.title}</CardTitle>
            <CardDescription>{dictionary.description}</CardDescription>
          </div>
          <CreateButton lang={lang} dictionary={dictionary} />
        </div>
      </CardHeader>

      <CardContent>
        <SearchBar lang={lang} dictionary={dictionary} />
        <ListTable items={initialItems} lang={lang} dictionary={dictionary} />
      </CardContent>

      <CardFooter>
        <Pagination
          totalItems={totalItems}
          pageSize={10}
          currentPage={currentPage}
          lang={lang}
          search={search}
          dictionary={dictionary}
        />
      </CardFooter>
    </Card>
  );
}
