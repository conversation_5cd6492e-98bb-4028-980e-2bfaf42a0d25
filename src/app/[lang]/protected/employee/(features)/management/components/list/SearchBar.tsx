"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useRouter, useSearchParams } from "next/navigation";
import { FeatureDictionary } from "../../lib/types";
import { Search, X } from "lucide-react";

interface SearchBarProps {
  lang?: string; // Make lang optional since we're not using it
  dictionary: FeatureDictionary;
}

/**
 * Enhanced search bar component with multiple filtering options
 */
export function SearchBar({ dictionary }: SearchBarProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [searchTerm, setSearchTerm] = useState(searchParams.get("search") || "");
  const [status, setStatus] = useState(searchParams.get("status") || "");
  const [department, setDepartment] = useState(searchParams.get("department") || "");

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const params = new URLSearchParams(searchParams.toString());

    // Update or remove search parameters
    if (searchTerm) {
      params.set("search", searchTerm);
    } else {
      params.delete("search");
    }

    if (status) {
      params.set("status", status);
    } else {
      params.delete("status");
    }

    if (department) {
      params.set("department", department);
    } else {
      params.delete("department");
    }

    // Reset to page 1 when filtering
    params.set("page", "1");

    router.push(`?${params.toString()}`);
  };

  // Clear all filters
  const clearFilters = () => {
    setSearchTerm("");
    setStatus("");
    setDepartment("");

    const params = new URLSearchParams();
    params.set("page", "1");
    router.push(`?${params.toString()}`);
  };

  // Check if any filters are active
  const hasActiveFilters = searchTerm || status || department;

  return (
    <form onSubmit={handleSubmit} className="mb-6">
      <div className="flex flex-col gap-4 sm:flex-row">
        <div className="flex-1">
          <Input
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder={dictionary.common?.searchPlaceholder || "Search employees..."}
            className="w-full"
          />
        </div>

        <Button type="submit" className="gap-2">
          <Search className="h-4 w-4" />
          {dictionary.common?.search || "Search"}
        </Button>

        {hasActiveFilters && (
          <Button type="button" variant="outline" onClick={clearFilters} className="gap-2">
            <X className="h-4 w-4" />
            {dictionary.common?.clearFilters || "Clear"}
          </Button>
        )}
      </div>
    </form>
  );
}
