"use client";

import { Employee, FeatureDictionary } from "../../lib/types";
import { usePathname } from "next/navigation";
import Link from "next/link";
import { cn } from "@/lib/utils";

interface EmployeeViewTabsProps {
  employee: Employee;
  dictionary: FeatureDictionary;
  lang: string;
  id: string;
}

/**
 * Tab navigation component for the employee view
 * Displays tabs as links to different pages
 */
export function EmployeeViewTabs({ dictionary, lang, id }: EmployeeViewTabsProps) {
  const pathname = usePathname();

  // Define the tabs
  const tabs = [
    {
      id: "contact",
      label: dictionary.tabs?.contact || "Contact Information",
      href: `/${lang}/protected/employee/management/${id}/view/contact`,
    },
    {
      id: "employment",
      label: dictionary.tabs?.employment || "Employment Details",
      href: `/${lang}/protected/employee/management/${id}/view/employment`,
    },
    {
      id: "professional",
      label: dictionary.tabs?.professional || "Professional Information",
      href: `/${lang}/protected/employee/management/${id}/view/professional`,
    },
  ];

  return (
    <div className="border-b mb-6">
      <div className="flex space-x-6 overflow-x-auto">
        {tabs.map((tab) => (
          <Link
            key={tab.id}
            href={tab.href}
            className={cn(
              "py-3 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors",
              pathname.includes(`/view/${tab.id}`)
                ? "border-primary text-primary"
                : "border-transparent text-muted-foreground hover:text-foreground hover:border-muted"
            )}
          >
            {tab.label}
          </Link>
        ))}
      </div>
    </div>
  );
}
