"use client";

import { useActionState } from "react";
import { remove } from "../actions";
import { ActionState } from "@/lib/types/responses";
import { Employee, FeatureDictionary } from "../lib/types";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import Link from "next/link";

import FEATURE_CONFIG from "../lib/config";

interface RemoveFormProps {
  lang: string;
  item: Employee;
  dictionary: FeatureDictionary;
}

/**
 * Form for confirming deletion of a Feature 1 item
 */
export function RemoveForm({ lang, item, dictionary }: RemoveFormProps) {
  // Use React's useFormState to manage the form state with the server action
  const initialState: ActionState<Employee> = {
    success: true,
    error: "",
    data: item,
  };

  const [state, formAction, _pending] = useActionState(remove, initialState);
  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="text-center">
        <CardTitle>{dictionary.removeTitle || "Remove Employee"}</CardTitle>
        <CardDescription>
          {dictionary.removeDescription || "Are you sure you want to remove this employee?"}
        </CardDescription>
      </CardHeader>

      <form action={formAction}>
        <CardContent className="space-y-4">
          {/* Hidden fields for ID and language */}
          <input type="hidden" name="id" value={item.id} />
          <input type="hidden" name="lang" value={lang} />

          {/* Display error message if there is one */}
          {!state.success && state.error && (
            <Alert variant="destructive">
              <AlertDescription>{state.error}</AlertDescription>
            </Alert>
          )}

          {/* Employee details */}
          <div className="border rounded-md p-4">
            <h3 className="font-medium">
              {item.first_name} {item.last_name}
            </h3>
            <p className="text-sm text-muted-foreground mt-1">
              {item.job_title || "No job title provided"}
            </p>
            <div className="mt-2">
              <span
                className={`px-2 py-1 rounded-full text-xs ${
                  item.employment_status === "active"
                    ? "bg-green-100 text-green-800"
                    : item.employment_status === "inactive"
                      ? "bg-gray-100 text-gray-800"
                      : "bg-red-100 text-red-800"
                }`}
              >
                {item.employment_status === "active"
                  ? dictionary.employmentStatus?.active || "Active"
                  : item.employment_status === "inactive"
                    ? dictionary.employmentStatus?.inactive || "Inactive"
                    : dictionary.employmentStatus?.terminated || "Terminated"}
              </span>
            </div>
          </div>

          <Alert variant="destructive" className="bg-red-50 border-red-200">
            <AlertDescription className="text-red-800">
              {dictionary.removeWarning ||
                "This action cannot be undone. The employee will be marked as terminated."}
            </AlertDescription>
          </Alert>
        </CardContent>

        <CardFooter className="flex justify-between">
          <Button variant="outline" asChild>
            <Link href={`/${lang}${FEATURE_CONFIG.routes.LIST}`}>
              {dictionary.backToList || "Back to List"}
            </Link>
          </Button>
          <Button type="submit" variant="destructive" disabled={_pending}>
            {_pending ? "Deleting..." : dictionary.removeConfirm || "Confirm Delete"}
          </Button>
        </CardFooter>
      </form>
    </Card>
  );
}
