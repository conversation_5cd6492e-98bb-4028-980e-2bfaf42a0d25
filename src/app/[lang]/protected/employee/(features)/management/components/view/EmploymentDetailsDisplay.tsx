import { Employee, FeatureDictionary } from "../../lib/types";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { LinkIcon } from "lucide-react";
import Link from "next/link";
import FEATURE_CONFIG from "../../lib/config";

interface EmploymentDetailsDisplayProps {
  employee: Employee;
  dictionary: FeatureDictionary;
  lang: string;
}

/**
 * Component to display employee employment details
 */
export function EmploymentDetailsDisplay({
  employee,
  dictionary,
  lang,
}: EmploymentDetailsDisplayProps) {
  // Format date for display
  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return dictionary.common?.notSpecified || "Not specified";
    return new Date(dateString).toLocaleDateString();
  };

  // Get status badge color
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return (
          <Badge className="bg-green-500">{dictionary.employmentStatus?.active || "Active"}</Badge>
        );
      case "inactive":
        return (
          <Badge variant="outline">{dictionary.employmentStatus?.inactive || "Inactive"}</Badge>
        );
      case "terminated":
        return (
          <Badge variant="destructive">
            {dictionary.employmentStatus?.terminated || "Terminated"}
          </Badge>
        );
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div>
        <h3 className="text-sm font-medium text-muted-foreground mb-2">
          {dictionary.fields?.employeeId || "Employee ID"}
        </h3>
        <p>{employee.employee_id || dictionary.common?.notAssigned || "Not assigned"}</p>
      </div>

      <div>
        <h3 className="text-sm font-medium text-muted-foreground mb-2">
          {dictionary.fields?.employmentStatus || "Employment Status"}
        </h3>
        <p>{getStatusBadge(employee.employment_status)}</p>
      </div>

      <div>
        <h3 className="text-sm font-medium text-muted-foreground mb-2">
          {dictionary.fields?.hireDate || "Hire Date"}
        </h3>
        <p>{formatDate(employee.hire_date)}</p>
      </div>

      <div>
        <h3 className="text-sm font-medium text-muted-foreground mb-2">
          {dictionary.fields?.terminationDate || "Termination Date"}
        </h3>
        <p>{formatDate(employee.termination_date)}</p>
      </div>

      <div>
        <h3 className="text-sm font-medium text-muted-foreground mb-2">
          {dictionary.fields?.jobTitle || "Job Title"}
        </h3>
        <p>{employee.job_title || dictionary.common?.notSpecified || "Not specified"}</p>
      </div>

      <div>
        <h3 className="text-sm font-medium text-muted-foreground mb-2">
          {dictionary.fields?.department || "Department"}
        </h3>
        <p>{employee.department || dictionary.common?.notSpecified || "Not specified"}</p>
      </div>

      <div>
        <h3 className="text-sm font-medium text-muted-foreground mb-2">
          {dictionary.fields?.supervisor || "Supervisor"}
        </h3>
        <p>{employee.supervisor_id || dictionary.common?.noneAssigned || "None assigned"}</p>
      </div>

      <div>
        <h3 className="text-sm font-medium text-muted-foreground mb-2">
          {dictionary.fields?.userAccount || "User Account"}
        </h3>
        <div className="flex items-center gap-2">
          <p>{employee.user_account_id || dictionary.common?.notLinked || "Not linked"}</p>
          {!employee.user_account_id && (
            <Button variant="outline" size="sm" asChild>
              <Link href={`/${lang}${FEATURE_CONFIG.routes.VIEW(employee.id)}/link-account`}>
                <LinkIcon className="h-4 w-4 mr-1" />
                {dictionary.actions?.linkUserAccount || "Link Account"}
              </Link>
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
