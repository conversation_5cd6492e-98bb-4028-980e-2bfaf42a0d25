import { Employee, FeatureDictionary } from "../../lib/types";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";

interface ContactInfoDisplayProps {
  employee: Employee;
  dictionary: FeatureDictionary;
}

/**
 * Component to display employee contact and personal information
 */
export function ContactInfoDisplay({ employee, dictionary }: ContactInfoDisplayProps) {
  // Format date for display
  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return dictionary.common?.notSpecified || "Not specified";
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div className="space-y-6">
      {/* Personal Information Section */}
      <div>
        <h3 className="text-sm font-medium text-muted-foreground mb-2">
          {dictionary.personalInformation || "Personal Information"}
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p className="text-sm text-muted-foreground">
              {dictionary.fields?.dateOfBirth || "Date of Birth"}
            </p>
            <p>{formatDate(employee.date_of_birth)}</p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">{dictionary.fields?.gender || "Gender"}</p>
            <p>{employee.gender || dictionary.common?.notSpecified || "Not specified"}</p>
          </div>
          <div className="col-span-1 md:col-span-2">
            <p className="text-sm text-muted-foreground">
              {dictionary.fields?.address || "Address"}
            </p>
            <p>
              {typeof employee.address === "string"
                ? employee.address
                : dictionary.common?.noAddressProvided || "No address provided"}
            </p>
          </div>
        </div>
      </div>

      <Separator />

      {/* Email Addresses Section */}
      <div>
        <h3 className="text-sm font-medium text-muted-foreground mb-2">
          {dictionary.fields?.emails || "Email Addresses"}
        </h3>
        {employee.emails && employee.emails.length > 0 ? (
          <div className="space-y-2">
            {employee.emails.map((emailItem, index) => {
              // Cast to Record<string, unknown> to handle the Json type
              const email = emailItem as Record<string, unknown>;
              return (
                <div key={index} className="flex items-center gap-2">
                  <p className="font-medium">
                    {email && typeof email.email === "string"
                      ? email.email
                      : dictionary.common?.noEmail || "No email"}
                  </p>
                  <Badge variant="outline" className="text-xs">
                    {email && typeof email.type === "string"
                      ? email.type
                      : dictionary.common?.unknown || "Unknown"}
                  </Badge>
                  {email && email.primary === true && (
                    <Badge className="bg-blue-500 text-xs">
                      {dictionary.common?.primary || "Primary"}
                    </Badge>
                  )}
                </div>
              );
            })}
          </div>
        ) : (
          <p>{dictionary.common?.noEmailsListed || "No email addresses listed"}</p>
        )}
      </div>

      <Separator />

      {/* Phone Numbers Section */}
      <div>
        <h3 className="text-sm font-medium text-muted-foreground mb-2">
          {dictionary.fields?.phones || "Phone Numbers"}
        </h3>
        {employee.phones && employee.phones.length > 0 ? (
          <div className="space-y-2">
            {employee.phones.map((phoneItem, index) => {
              // Cast to Record<string, unknown> to handle the Json type
              const phone = phoneItem as Record<string, unknown>;
              return (
                <div key={index} className="flex items-center gap-2">
                  <p className="font-medium">
                    {phone && typeof phone.number === "string"
                      ? phone.number
                      : dictionary.common?.noNumber || "No number"}
                  </p>
                  <Badge variant="outline" className="text-xs">
                    {phone && typeof phone.type === "string"
                      ? phone.type
                      : dictionary.common?.unknown || "Unknown"}
                  </Badge>
                  {phone && phone.primary === true && (
                    <Badge className="bg-blue-500 text-xs">
                      {dictionary.common?.primary || "Primary"}
                    </Badge>
                  )}
                </div>
              );
            })}
          </div>
        ) : (
          <p>{dictionary.common?.noPhoneNumbers || "No phone numbers listed"}</p>
        )}
      </div>

      <Separator />

      {/* System Information Section */}
      <div>
        <h3 className="text-sm font-medium text-muted-foreground mb-2">
          {dictionary.common?.systemInformation || "System Information"}
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p className="text-sm text-muted-foreground">
              {dictionary.common?.created || "Created"}
            </p>
            <p>
              {employee.created_at
                ? new Date(employee.created_at).toLocaleString()
                : dictionary.common?.notAvailable || "N/A"}
            </p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">
              {dictionary.common?.lastUpdated || "Last Updated"}
            </p>
            <p>
              {employee.updated_at
                ? new Date(employee.updated_at).toLocaleString()
                : dictionary.common?.notAvailable || "N/A"}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
