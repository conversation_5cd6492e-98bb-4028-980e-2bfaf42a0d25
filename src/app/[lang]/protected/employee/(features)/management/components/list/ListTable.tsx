"use client";

import { Table, TableBody, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Employee, FeatureDictionary } from "../../lib/types";
import { ListItem } from "./ListItem";
import { ArrowUpDown, ArrowUp, ArrowDown } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useSearchParams, useRouter } from "next/navigation";

interface ListTableProps {
  items: Employee[];
  lang: string;
  dictionary: FeatureDictionary;
}

type SortField = "name" | "status" | "jobTitle";
type SortDirection = "asc" | "desc";

/**
 * Table component for displaying employees with sorting functionality
 */
export function ListTable({ items, lang, dictionary }: ListTableProps) {
  const searchParams = useSearchParams();
  const router = useRouter();

  const sortField = (searchParams.get("sort") || "name") as SortField;
  const sortDirection = (searchParams.get("dir") || "asc") as SortDirection;

  if (items.length === 0) {
    return (
      <div className="text-center py-10">
        <p className="text-muted-foreground">
          {dictionary.noItems || "No employees found. Create your first employee to get started."}
        </p>
      </div>
    );
  }

  const handleSort = (field: SortField) => {
    const params = new URLSearchParams(searchParams.toString());

    if (sortField === field) {
      // Toggle direction if same field
      params.set("dir", sortDirection === "asc" ? "desc" : "asc");
    } else {
      // Set new field and default to ascending
      params.set("sort", field);
      params.set("dir", "asc");
    }

    router.push(`?${params.toString()}`);
  };

  // Sort the items based on URL parameters
  const sortedItems = [...items].sort((a, b) => {
    const direction = sortDirection === "asc" ? 1 : -1;

    switch (sortField) {
      case "name":
        const nameA = `${a.last_name} ${a.first_name}`.toLowerCase();
        const nameB = `${b.last_name} ${b.first_name}`.toLowerCase();
        return nameA.localeCompare(nameB) * direction;

      case "status":
        const statusOrder = { active: 0, inactive: 1, terminated: 2 };
        const statusA = statusOrder[a.employment_status as keyof typeof statusOrder] || 0;
        const statusB = statusOrder[b.employment_status as keyof typeof statusOrder] || 0;
        return (statusA - statusB) * direction;

      case "jobTitle":
        const jobTitleA = (a.job_title || "").toLowerCase();
        const jobTitleB = (b.job_title || "").toLowerCase();
        return jobTitleA.localeCompare(jobTitleB) * direction;

      default:
        return 0;
    }
  });

  const getSortIcon = (field: SortField) => {
    if (sortField === field) {
      return sortDirection === "asc" ? (
        <ArrowUp className="ml-2 h-4 w-4" />
      ) : (
        <ArrowDown className="ml-2 h-4 w-4" />
      );
    }
    return <ArrowUpDown className="ml-2 h-4 w-4 opacity-30" />;
  };

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>
            <Button
              variant="ghost"
              onClick={() => handleSort("name")}
              className="p-0 h-auto font-medium"
            >
              {dictionary.fields?.fullName || "Full Name"}
              {getSortIcon("name")}
            </Button>
          </TableHead>
          <TableHead>
            <Button
              variant="ghost"
              onClick={() => handleSort("status")}
              className="p-0 h-auto font-medium"
            >
              {dictionary.fields?.employmentStatus || "Employment Status"}
              {getSortIcon("status")}
            </Button>
          </TableHead>
          <TableHead>
            <Button
              variant="ghost"
              onClick={() => handleSort("jobTitle")}
              className="p-0 h-auto font-medium"
            >
              {dictionary.fields?.jobTitle || "Job Title"}
              {getSortIcon("jobTitle")}
            </Button>
          </TableHead>
          <TableHead className="text-right">{dictionary.common?.actions || "Actions"}</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {sortedItems.map((item) => (
          <ListItem key={item.id} item={item} lang={lang} dictionary={dictionary} />
        ))}
      </TableBody>
    </Table>
  );
}
