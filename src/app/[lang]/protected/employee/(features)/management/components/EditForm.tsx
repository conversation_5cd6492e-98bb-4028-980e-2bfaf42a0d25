"use client";

import { useActionState } from "react";
import { edit } from "../actions/edit";
import { ActionState } from "@/lib/types/responses";
import { Employee, FeatureDictionary } from "../lib/types";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { DatePicker } from "@/components/ui/date-picker";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { <PERSON><PERSON>, AlertDescription } from "@/components/ui/alert";
import Link from "next/link";
import FEATURE_CONFIG from "../lib/config";

interface EditFormProps {
  item: Employee;
  dictionary: FeatureDictionary;
  lang?: string;
}

/**
 * Form for editing an existing employee with tabbed interface
 */
export function EditForm({ item, dictionary, lang }: EditFormProps) {
  // Use React's useActionState to manage the form state with the server action
  const initialState: ActionState<Employee> = {
    success: false,
    error: "",
    data: item,
  };

  const [state, formAction, pending] = useActionState(edit, initialState);

  // Format date for defaultValue
  const formatDateForInput = (dateString?: string | null) => {
    if (!dateString) return undefined;
    return dateString;
  };

  // Format arrays/objects for textarea inputs
  const formatJsonForTextarea = (data: unknown) => {
    if (!data) return "";
    return JSON.stringify(data, null, 2);
  };

  // Get initials for avatar fallback
  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return (
          <Badge className="bg-green-500">{dictionary.employmentStatus?.active || "Active"}</Badge>
        );
      case "inactive":
        return (
          <Badge variant="outline">{dictionary.employmentStatus?.inactive || "Inactive"}</Badge>
        );
      case "terminated":
        return (
          <Badge variant="destructive">
            {dictionary.employmentStatus?.terminated || "Terminated"}
          </Badge>
        );
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <div className="flex flex-col sm:flex-row justify-between items-start gap-4">
          <div className="flex items-center gap-4 w-full sm:w-auto">
            <Avatar className="size-16 border shrink-0">
              {item.profile_image ? (
                <AvatarImage
                  src={item.profile_image}
                  alt={`${item.first_name} ${item.last_name}`}
                />
              ) : (
                <AvatarFallback className="bg-primary/10 text-primary text-lg">
                  {getInitials(item.first_name, item.last_name)}
                </AvatarFallback>
              )}
            </Avatar>
            <div className="min-w-0">
              <CardTitle className="text-2xl truncate">
                {item.first_name} {item.last_name}
              </CardTitle>
              <CardDescription className="mt-1 truncate">
                {item.job_title || "No job title"} {item.department ? `• ${item.department}` : ""}
              </CardDescription>
            </div>
          </div>
          <div className="flex flex-wrap gap-2">
            {/* Status Badge */}
            {getStatusBadge(item.employment_status)}
          </div>
        </div>
      </CardHeader>

      <form action={formAction}>
        <CardContent>
          {/* Hidden ID field */}
          <input type="hidden" name="id" value={item.id} />

          {/* Display success message if there is one */}
          {state.success && state.data && state.data.id === item.id && (
            <Alert className="bg-green-50 border-green-200 mb-6">
              <AlertDescription className="text-green-800">
                {dictionary.itemUpdated || "Employee updated successfully"}
              </AlertDescription>
            </Alert>
          )}

          {/* Display error message if there is one */}
          {!state.success && state.error && (
            <Alert variant="destructive" className="mb-6">
              <AlertDescription>{state.error}</AlertDescription>
            </Alert>
          )}

          <Tabs defaultValue="contact" className="w-full">
            <div className="border-b mb-6">
              <div className="flex space-x-6 overflow-x-auto">
                <TabsList>
                  <TabsTrigger value="contact">
                    {dictionary.tabs?.contact || "Contact Information"}
                  </TabsTrigger>
                  <TabsTrigger value="employment">
                    {dictionary.tabs?.employment || "Employment Details"}
                  </TabsTrigger>
                  <TabsTrigger value="professional">
                    {dictionary.tabs?.professional || "Professional Information"}
                  </TabsTrigger>
                </TabsList>
              </div>
            </div>

            {/* Contact Information Tab */}
            <TabsContent value="contact" className="space-y-6">
              {/* Personal Information Section */}
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-2">
                  {dictionary.personalInformation || "Personal Information"}
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* First Name field */}
                  <div className="space-y-2">
                    <Label htmlFor="firstName">{dictionary?.fields?.firstName}</Label>
                    <Input
                      id="firstName"
                      name="firstName"
                      defaultValue={item.first_name}
                      placeholder={dictionary.placeholders?.enterFirstName || "Enter first name"}
                      required
                      aria-invalid={!state.success ? "true" : "false"}
                    />
                  </div>

                  {/* Last Name field */}
                  <div className="space-y-2">
                    <Label htmlFor="lastName">{dictionary?.fields?.lastName}</Label>
                    <Input
                      id="lastName"
                      name="lastName"
                      defaultValue={item.last_name}
                      placeholder={dictionary.placeholders?.enterLastName || "Enter last name"}
                      required
                      aria-invalid={!state.success ? "true" : "false"}
                    />
                  </div>

                  {/* Date of Birth field */}
                  <div className="space-y-2">
                    <Label htmlFor="dateOfBirth">
                      {dictionary.fields?.dateOfBirth || "Date of Birth"}
                    </Label>
                    <DatePicker
                      name="dateOfBirth"
                      defaultValue={formatDateForInput(item.date_of_birth)}
                    />
                  </div>

                  {/* Gender field */}
                  <div className="space-y-2">
                    <Label htmlFor="gender">{dictionary.fields?.gender || "Gender"}</Label>
                    <Select name="gender" defaultValue={item.gender || "unspecified"}>
                      <SelectTrigger id="gender">
                        <SelectValue
                          placeholder={dictionary.placeholders?.selectGender || "Select gender"}
                        />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="unspecified">
                          {dictionary.gender?.notSpecified || "Not specified"}
                        </SelectItem>
                        <SelectItem value="male">{dictionary.gender?.male || "Male"}</SelectItem>
                        <SelectItem value="female">
                          {dictionary.gender?.female || "Female"}
                        </SelectItem>
                        <SelectItem value="other">{dictionary.gender?.other || "Other"}</SelectItem>
                        <SelectItem value="prefer_not_to_say">
                          {dictionary.gender?.preferNotToSay || "Prefer not to say"}
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Address field */}
                  <div className="col-span-1 md:col-span-2 space-y-2">
                    <Label htmlFor="address">{dictionary.fields?.address || "Address"}</Label>
                    <Textarea
                      id="address"
                      name="address"
                      defaultValue={typeof item.address === "string" ? item.address : ""}
                      placeholder={
                        dictionary.placeholders?.enterFullAddress || "Enter full address"
                      }
                      rows={3}
                    />
                  </div>
                </div>
              </div>

              <Separator />

              {/* Profile Image field */}
              <div className="space-y-2">
                <h3 className="text-sm font-medium text-muted-foreground mb-2">
                  {dictionary.fields?.profileImage || "Profile Image"}
                </h3>
                <Input
                  id="profileImage"
                  name="profileImage"
                  defaultValue={item.profile_image || ""}
                  placeholder={
                    dictionary.placeholders?.enterProfileImageUrl || "Enter profile image URL"
                  }
                />
              </div>

              <Separator />

              {/* Emails field */}
              <div className="space-y-2">
                <h3 className="text-sm font-medium text-muted-foreground mb-2">
                  {dictionary.fields?.emails || "Email Addresses"}
                </h3>
                <Textarea
                  id="emails"
                  name="emails"
                  defaultValue={formatJsonForTextarea(item.emails)}
                  placeholder={
                    dictionary.placeholders?.enterEmailAddresses ||
                    "Enter email addresses (JSON format)"
                  }
                  rows={3}
                />
              </div>

              <Separator />

              {/* Phones field */}
              <div className="space-y-2">
                <h3 className="text-sm font-medium text-muted-foreground mb-2">
                  {dictionary.fields?.phones || "Phone Numbers"}
                </h3>
                <Textarea
                  id="phones"
                  name="phones"
                  defaultValue={formatJsonForTextarea(item.phones)}
                  placeholder={
                    dictionary.placeholders?.enterPhoneNumbers ||
                    "Enter phone numbers (JSON format)"
                  }
                  rows={3}
                />
              </div>
            </TabsContent>

            {/* Employment Details Tab */}
            <TabsContent value="employment" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Employee ID field */}
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-2">
                    {dictionary.fields?.employeeId || "Employee ID"}
                  </h3>
                  <Input
                    id="employeeId"
                    name="employeeId"
                    defaultValue={item.employee_id || ""}
                    placeholder={dictionary.placeholders?.enterEmployeeId || "Enter employee ID"}
                  />
                </div>

                {/* Employment Status field */}
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-2">
                    {dictionary.fields?.employmentStatus || "Employment Status"}
                  </h3>
                  <Select name="employmentStatus" defaultValue={item.employment_status}>
                    <SelectTrigger id="employmentStatus">
                      <SelectValue
                        placeholder={dictionary.placeholders?.selectStatus || "Select status"}
                      />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="active">
                        {dictionary.employmentStatus?.active || "Active"}
                      </SelectItem>
                      <SelectItem value="inactive">
                        {dictionary.employmentStatus?.inactive || "Inactive"}
                      </SelectItem>
                      <SelectItem value="terminated">
                        {dictionary.employmentStatus?.terminated || "Terminated"}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Hire Date field */}
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-2">
                    {dictionary.fields?.hireDate || "Hire Date"}
                  </h3>
                  <DatePicker name="hireDate" defaultValue={formatDateForInput(item.hire_date)} />
                </div>

                {/* Termination Date field */}
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-2">
                    {dictionary.fields?.terminationDate || "Termination Date"}
                  </h3>
                  <DatePicker
                    name="terminationDate"
                    defaultValue={formatDateForInput(item.termination_date)}
                  />
                </div>

                {/* Job Title field */}
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-2">
                    {dictionary.fields?.jobTitle || "Job Title"}
                  </h3>
                  <Input
                    id="jobTitle"
                    name="jobTitle"
                    defaultValue={item.job_title || ""}
                    placeholder={dictionary.placeholders?.enterJobTitle || "Enter job title"}
                  />
                </div>

                {/* Department field */}
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-2">
                    {dictionary.fields?.department || "Department"}
                  </h3>
                  <Select name="department" defaultValue={item.department || "unspecified"}>
                    <SelectTrigger id="department">
                      <SelectValue
                        placeholder={
                          dictionary.placeholders?.selectDepartment || "Select department"
                        }
                      />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="unspecified">
                        {dictionary.department?.notSpecified || "Not specified"}
                      </SelectItem>
                      <SelectItem value="hr">
                        {dictionary.department?.hr || "Human Resources"}
                      </SelectItem>
                      <SelectItem value="it">
                        {dictionary.department?.it || "Information Technology"}
                      </SelectItem>
                      <SelectItem value="finance">
                        {dictionary.department?.finance || "Finance"}
                      </SelectItem>
                      <SelectItem value="operations">
                        {dictionary.department?.operations || "Operations"}
                      </SelectItem>
                      <SelectItem value="sales">
                        {dictionary.department?.sales || "Sales"}
                      </SelectItem>
                      <SelectItem value="marketing">
                        {dictionary.department?.marketing || "Marketing"}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Supervisor field */}
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-2">
                    {dictionary.fields?.supervisor || "Supervisor"}
                  </h3>
                  <Input
                    id="supervisorId"
                    name="supervisorId"
                    defaultValue={item.supervisor_id || ""}
                    placeholder={
                      dictionary.placeholders?.enterSupervisorId || "Enter supervisor ID"
                    }
                  />
                </div>

                {/* User Account field */}
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-2">
                    {dictionary.fields?.userAccount || "User Account"}
                  </h3>
                  <Input
                    id="userAccountId"
                    name="userAccountId"
                    defaultValue={item.user_account_id || ""}
                    placeholder={
                      dictionary.placeholders?.enterUserAccountId ||
                      "Enter user account ID (optional)"
                    }
                  />
                </div>
              </div>
            </TabsContent>

            {/* Professional Information Tab */}
            <TabsContent value="professional" className="space-y-6">
              {/* Specializations field */}
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-2">
                  {dictionary.fields?.specializations || "Specializations"}
                </h3>
                <Textarea
                  id="specializations"
                  name="specializations"
                  defaultValue={item.specializations ? item.specializations.join(", ") : ""}
                  placeholder={
                    dictionary.placeholders?.enterSpecializations ||
                    "Enter specializations (comma separated)"
                  }
                  rows={3}
                />
              </div>

              <Separator />

              {/* Certifications field */}
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-2">
                  {dictionary.fields?.certifications || "Certifications"}
                </h3>
                <Textarea
                  id="certifications"
                  name="certifications"
                  defaultValue={formatJsonForTextarea(item.certifications)}
                  placeholder={
                    dictionary.placeholders?.enterCertifications ||
                    "Enter certifications (JSON format)"
                  }
                  rows={3}
                />
              </div>

              <Separator />

              {/* Education field */}
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-2">
                  {dictionary.fields?.education || "Education"}
                </h3>
                <Textarea
                  id="education"
                  name="education"
                  defaultValue={formatJsonForTextarea(item.education)}
                  placeholder={
                    dictionary.placeholders?.enterEducation ||
                    "Enter education history (JSON format)"
                  }
                  rows={3}
                />
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>

        <CardFooter className="flex justify-between">
          <Button variant="outline" asChild>
            <Link href={lang ? `/${lang}${FEATURE_CONFIG.routes.VIEW(item.id)}` : ".."}>
              {dictionary.common?.cancel || "Cancel"}
            </Link>
          </Button>
          <Button type="submit" disabled={pending}>
            {pending ? dictionary.common?.saving || "Saving..." : dictionary.common?.save || "Save"}
          </Button>
        </CardFooter>
      </form>
    </Card>
  );
}
