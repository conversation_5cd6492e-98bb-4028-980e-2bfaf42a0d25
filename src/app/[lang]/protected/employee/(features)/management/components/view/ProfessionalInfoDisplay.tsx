import { Employee, FeatureDictionary } from "../../lib/types";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";

interface ProfessionalInfoDisplayProps {
  employee: Employee;
  dictionary: FeatureDictionary;
}

/**
 * Component to display employee professional information
 */
export function ProfessionalInfoDisplay({ employee, dictionary }: ProfessionalInfoDisplayProps) {
  // Format date for display
  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return dictionary.common?.notSpecified || "Not specified";
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-sm font-medium text-muted-foreground mb-2">
          {dictionary.fields?.specializations || "Specializations"}
        </h3>
        {employee.specializations && employee.specializations.length > 0 ? (
          <div className="flex flex-wrap gap-2">
            {employee.specializations.map((spec, index) => (
              <Badge key={index} variant="secondary">
                {typeof spec === "string" ? spec : dictionary.common?.unknown || "Unknown"}
              </Badge>
            ))}
          </div>
        ) : (
          <p>{dictionary.common?.noSpecializationsListed || "No specializations listed"}</p>
        )}
      </div>

      <Separator />

      <div>
        <h3 className="text-sm font-medium text-muted-foreground mb-2">
          {dictionary.fields?.certifications || "Certifications"}
        </h3>
        {employee.certifications && employee.certifications.length > 0 ? (
          <div className="space-y-4">
            {employee.certifications.map((certItem, index) => {
              // Cast to Record<string, unknown> to handle the Json type
              const cert = certItem as Record<string, unknown>;
              return (
                <div key={index} className="border rounded-md p-3">
                  <h4 className="font-medium">
                    {cert && typeof cert.name === "string"
                      ? cert.name
                      : dictionary.common?.unnamed || "Unnamed"}
                  </h4>
                  <p className="text-sm text-muted-foreground">
                    {dictionary.common?.issuer || "Issuer"}:{" "}
                    {cert && typeof cert.issuer === "string"
                      ? cert.issuer
                      : dictionary.common?.unknown || "Unknown"}
                  </p>
                  <div className="flex gap-4 mt-1 text-sm">
                    <p>
                      {dictionary.common?.obtained || "Obtained"}:{" "}
                      {formatDate(
                        cert && typeof cert.date_obtained === "string"
                          ? (cert.date_obtained as string)
                          : null
                      )}
                    </p>
                    {cert && typeof cert.expiration_date === "string" && (
                      <p>
                        {dictionary.common?.expires || "Expires"}:{" "}
                        {formatDate(cert.expiration_date as string)}
                      </p>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          <p>{dictionary.common?.noCertificationsListed || "No certifications listed"}</p>
        )}
      </div>

      <Separator />

      <div>
        <h3 className="text-sm font-medium text-muted-foreground mb-2">
          {dictionary.fields?.education || "Education"}
        </h3>
        {employee.education && employee.education.length > 0 ? (
          <div className="space-y-4">
            {employee.education.map((eduItem, index) => {
              // Cast to Record<string, unknown> to handle the Json type
              const edu = eduItem as Record<string, unknown>;
              return (
                <div key={index} className="border rounded-md p-3">
                  <h4 className="font-medium">
                    {edu && typeof edu.institution === "string"
                      ? edu.institution
                      : dictionary.common?.unknownInstitution || "Unknown Institution"}
                  </h4>
                  <p>
                    {edu && typeof edu.degree === "string"
                      ? edu.degree
                      : dictionary.common?.unknownDegree || "Unknown degree"}{" "}
                    in{" "}
                    {edu && typeof edu.field_of_study === "string"
                      ? edu.field_of_study
                      : dictionary.common?.unknownField || "Unknown field"}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {formatDate(
                      edu && typeof edu.start_date === "string" ? (edu.start_date as string) : null
                    )}{" "}
                    -{" "}
                    {edu && typeof edu.end_date === "string"
                      ? formatDate(edu.end_date as string)
                      : dictionary.common?.present || "Present"}
                  </p>
                </div>
              );
            })}
          </div>
        ) : (
          <p>{dictionary.common?.noEducationHistory || "No education history listed"}</p>
        )}
      </div>
    </div>
  );
}
