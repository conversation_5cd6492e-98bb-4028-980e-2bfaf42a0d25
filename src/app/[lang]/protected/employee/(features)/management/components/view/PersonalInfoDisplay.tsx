import { Employee, FeatureDictionary } from "../../lib/types";
import Image from "next/image";

interface PersonalInfoDisplayProps {
  employee: Employee;
  dictionary: FeatureDictionary;
}

/**
 * Component to display employee personal information
 */
export function PersonalInfoDisplay({ employee, dictionary }: PersonalInfoDisplayProps) {
  // Format date for display
  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return dictionary.common?.notSpecified || "Not specified";
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div>
        <h3 className="text-sm font-medium text-muted-foreground mb-2">
          {dictionary.fields?.fullName || "Full Name"}
        </h3>
        <p className="text-lg font-medium">
          {employee.first_name} {employee.last_name}
        </p>
      </div>

      <div>
        <h3 className="text-sm font-medium text-muted-foreground mb-2">
          {dictionary.fields?.dateOfBirth || "Date of Birth"}
        </h3>
        <p>{formatDate(employee.date_of_birth)}</p>
      </div>

      <div>
        <h3 className="text-sm font-medium text-muted-foreground mb-2">
          {dictionary.fields?.gender || "Gender"}
        </h3>
        <p>{employee.gender || dictionary.common?.notSpecified || "Not specified"}</p>
      </div>

      <div>
        <h3 className="text-sm font-medium text-muted-foreground mb-2">
          {dictionary.fields?.profileImage || "Profile Image"}
        </h3>
        {employee.profile_image ? (
          <div className="h-20 w-20 rounded-full overflow-hidden">
            <Image
              src={employee.profile_image}
              alt={`${employee.first_name} ${employee.last_name}`}
              className="h-full w-full object-cover"
              width={80}
              height={80}
            />
          </div>
        ) : (
          <p>{dictionary.common?.noProfileImage || "No profile image"}</p>
        )}
      </div>

      <div className="col-span-2">
        <h3 className="text-sm font-medium text-muted-foreground mb-2">
          {dictionary.fields?.address || "Address"}
        </h3>
        <p>
          {typeof employee.address === "string"
            ? employee.address
            : dictionary.common?.noAddressProvided || "No address provided"}
        </p>
      </div>
    </div>
  );
}
