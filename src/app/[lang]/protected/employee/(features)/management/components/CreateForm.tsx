"use client";

import { useActionState } from "react";
import { create } from "../actions";
import { ActionState } from "@/lib/types/responses";
import { Employee, FeatureDictionary } from "../lib/types";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { DatePicker } from "@/components/ui/date-picker";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Al<PERSON>, AlertDescription } from "@/components/ui/alert";
import { User } from "lucide-react";
import Link from "next/link";
import FEATURE_CONFIG from "../lib/config";

interface CreateFormProps {
  dictionary: FeatureDictionary;
  lang?: string;
}

/**
 * Form for creating a new employee with tabbed interface
 */
export function CreateForm({ dictionary, lang }: CreateFormProps) {
  // Use React's useActionState to manage the form state with the server action
  const initialState: ActionState<Employee> = {
    success: true,
    error: "",
    data: null,
  };

  const [state, formAction, pending] = useActionState(create, initialState);

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return (
          <Badge className="bg-green-500">{dictionary.employmentStatus?.active || "Active"}</Badge>
        );
      case "inactive":
        return (
          <Badge variant="outline">{dictionary.employmentStatus?.inactive || "Inactive"}</Badge>
        );
      case "terminated":
        return (
          <Badge variant="destructive">
            {dictionary.employmentStatus?.terminated || "Terminated"}
          </Badge>
        );
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <div className="flex flex-col sm:flex-row justify-between items-start gap-4">
          <div className="flex items-center gap-4 w-full sm:w-auto">
            <Avatar className="size-16 border shrink-0">
              <AvatarFallback className="bg-primary/10 text-primary text-lg">
                <User className="h-8 w-8" />
              </AvatarFallback>
            </Avatar>
            <div className="min-w-0">
              <CardTitle className="text-2xl truncate">
                {dictionary.createTitle || "New Employee"}
              </CardTitle>
              <CardDescription className="mt-1 truncate">
                {dictionary.createDescription || "Create a new employee record"}
              </CardDescription>
            </div>
          </div>
          <div className="flex flex-wrap gap-2">
            {/* Status Badge - Default to active for new employees */}
            {getStatusBadge("active")}
          </div>
        </div>
      </CardHeader>

      <form action={formAction}>
        <CardContent>
          {/* Display error message if there is one */}
          {!state.success && state.error && (
            <Alert variant="destructive" className="mb-6">
              <AlertDescription>{state.error}</AlertDescription>
            </Alert>
          )}

          <Tabs defaultValue="contact" className="w-full">
            <div className="border-b mb-6">
              <div className="flex space-x-6 overflow-x-auto">
                <TabsList>
                  <TabsTrigger value="contact">
                    {dictionary.tabs?.contact || "Contact Information"}
                  </TabsTrigger>
                  <TabsTrigger value="employment">
                    {dictionary.tabs?.employment || "Employment Details"}
                  </TabsTrigger>
                  <TabsTrigger value="professional">
                    {dictionary.tabs?.professional || "Professional Information"}
                  </TabsTrigger>
                </TabsList>
              </div>
            </div>

            {/* Contact Information Tab */}
            <TabsContent value="contact" className="space-y-6">
              {/* Personal Information Section */}
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-2">
                  {dictionary.personalInformation || "Personal Information"}
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* First Name field */}
                  <div className="space-y-2">
                    <Label htmlFor="firstName">{dictionary.fields?.firstName}</Label>
                    <Input
                      id="firstName"
                      name="firstName"
                      placeholder={dictionary.placeholders?.enterFirstName || "Enter first name"}
                      required
                      aria-invalid={!state.success ? "true" : "false"}
                    />
                  </div>

                  {/* Last Name field */}
                  <div className="space-y-2">
                    <Label htmlFor="lastName">{dictionary.fields?.lastName}</Label>
                    <Input
                      id="lastName"
                      name="lastName"
                      placeholder={dictionary.placeholders?.enterLastName || "Enter last name"}
                      required
                      aria-invalid={!state.success ? "true" : "false"}
                    />
                  </div>

                  {/* Date of Birth field */}
                  <div className="space-y-2">
                    <Label htmlFor="dateOfBirth">
                      {dictionary.fields?.dateOfBirth || "Date of Birth"}
                    </Label>
                    <DatePicker name="dateOfBirth" />
                  </div>

                  {/* Gender field */}
                  <div className="space-y-2">
                    <Label htmlFor="gender">{dictionary.fields?.gender || "Gender"}</Label>
                    <Select name="gender" defaultValue="unspecified">
                      <SelectTrigger id="gender">
                        <SelectValue
                          placeholder={dictionary.placeholders?.selectGender || "Select gender"}
                        />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="unspecified">
                          {dictionary.gender?.notSpecified || "Not specified"}
                        </SelectItem>
                        <SelectItem value="male">{dictionary.gender?.male || "Male"}</SelectItem>
                        <SelectItem value="female">
                          {dictionary.gender?.female || "Female"}
                        </SelectItem>
                        <SelectItem value="other">{dictionary.gender?.other || "Other"}</SelectItem>
                        <SelectItem value="prefer_not_to_say">
                          {dictionary.gender?.preferNotToSay || "Prefer not to say"}
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Address field */}
                  <div className="col-span-1 md:col-span-2 space-y-2">
                    <Label htmlFor="address">{dictionary.fields?.address || "Address"}</Label>
                    <Textarea
                      id="address"
                      name="address"
                      placeholder={
                        dictionary.placeholders?.enterFullAddress || "Enter full address"
                      }
                      rows={3}
                    />
                  </div>
                </div>
              </div>

              <Separator />

              {/* Profile Image field */}
              <div className="space-y-2">
                <h3 className="text-sm font-medium text-muted-foreground mb-2">
                  {dictionary.fields?.profileImage || "Profile Image"}
                </h3>
                <Input
                  id="profileImage"
                  name="profileImage"
                  placeholder={
                    dictionary.placeholders?.enterProfileImageUrl || "Enter profile image URL"
                  }
                />
              </div>

              <Separator />

              {/* Emails field */}
              <div className="space-y-2">
                <h3 className="text-sm font-medium text-muted-foreground mb-2">
                  {dictionary.fields?.emails || "Email Addresses"}
                </h3>
                <Textarea
                  id="emails"
                  name="emails"
                  placeholder={
                    dictionary.placeholders?.enterEmailAddresses ||
                    "Enter email addresses (JSON format)"
                  }
                  rows={3}
                />
              </div>

              <Separator />

              {/* Phones field */}
              <div className="space-y-2">
                <h3 className="text-sm font-medium text-muted-foreground mb-2">
                  {dictionary.fields?.phones || "Phone Numbers"}
                </h3>
                <Textarea
                  id="phones"
                  name="phones"
                  placeholder={
                    dictionary.placeholders?.enterPhoneNumbers ||
                    "Enter phone numbers (JSON format)"
                  }
                  rows={3}
                />
              </div>
            </TabsContent>

            {/* Employment Details Tab */}
            <TabsContent value="employment" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Employee ID field */}
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-2">
                    {dictionary.fields?.employeeId || "Employee ID"}
                  </h3>
                  <Input
                    id="employeeId"
                    name="employeeId"
                    placeholder={dictionary.placeholders?.enterEmployeeId || "Enter employee ID"}
                  />
                </div>

                {/* Employment Status field */}
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-2">
                    {dictionary.fields?.employmentStatus || "Employment Status"}
                  </h3>
                  <Select name="employmentStatus" defaultValue="active">
                    <SelectTrigger id="employmentStatus">
                      <SelectValue
                        placeholder={dictionary.placeholders?.selectStatus || "Select status"}
                      />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="active">
                        {dictionary.employmentStatus?.active || "Active"}
                      </SelectItem>
                      <SelectItem value="inactive">
                        {dictionary.employmentStatus?.inactive || "Inactive"}
                      </SelectItem>
                      <SelectItem value="terminated">
                        {dictionary.employmentStatus?.terminated || "Terminated"}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Hire Date field */}
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-2">
                    {dictionary.fields?.hireDate || "Hire Date"}
                  </h3>
                  <DatePicker name="hireDate" />
                </div>

                {/* Termination Date field */}
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-2">
                    {dictionary.fields?.terminationDate || "Termination Date"}
                  </h3>
                  <DatePicker name="terminationDate" />
                </div>

                {/* Job Title field */}
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-2">
                    {dictionary.fields?.jobTitle || "Job Title"}
                  </h3>
                  <Input
                    id="jobTitle"
                    name="jobTitle"
                    placeholder={dictionary.placeholders?.enterJobTitle || "Enter job title"}
                  />
                </div>

                {/* Department field */}
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-2">
                    {dictionary.fields?.department || "Department"}
                  </h3>
                  <Select name="department" defaultValue="unspecified">
                    <SelectTrigger id="department">
                      <SelectValue
                        placeholder={
                          dictionary.placeholders?.selectDepartment || "Select department"
                        }
                      />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="unspecified">
                        {dictionary.department?.notSpecified || "Not specified"}
                      </SelectItem>
                      <SelectItem value="hr">
                        {dictionary.department?.hr || "Human Resources"}
                      </SelectItem>
                      <SelectItem value="it">
                        {dictionary.department?.it || "Information Technology"}
                      </SelectItem>
                      <SelectItem value="finance">
                        {dictionary.department?.finance || "Finance"}
                      </SelectItem>
                      <SelectItem value="operations">
                        {dictionary.department?.operations || "Operations"}
                      </SelectItem>
                      <SelectItem value="sales">
                        {dictionary.department?.sales || "Sales"}
                      </SelectItem>
                      <SelectItem value="marketing">
                        {dictionary.department?.marketing || "Marketing"}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Supervisor field */}
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-2">
                    {dictionary.fields?.supervisor || "Supervisor"}
                  </h3>
                  <Input
                    id="supervisorId"
                    name="supervisorId"
                    placeholder={
                      dictionary.placeholders?.enterSupervisorId || "Enter supervisor ID"
                    }
                  />
                </div>

                {/* User Account field */}
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-2">
                    {dictionary.fields?.userAccount || "User Account"}
                  </h3>
                  <Input
                    id="userAccountId"
                    name="userAccountId"
                    placeholder={
                      dictionary.placeholders?.enterUserAccountId ||
                      "Enter user account ID (optional)"
                    }
                  />
                </div>
              </div>
            </TabsContent>

            {/* Professional Information Tab */}
            <TabsContent value="professional" className="space-y-6">
              {/* Specializations field */}
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-2">
                  {dictionary.fields?.specializations || "Specializations"}
                </h3>
                <Textarea
                  id="specializations"
                  name="specializations"
                  placeholder={
                    dictionary.placeholders?.enterSpecializations ||
                    "Enter specializations (comma separated)"
                  }
                  rows={3}
                />
              </div>

              <Separator />

              {/* Certifications field */}
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-2">
                  {dictionary.fields?.certifications || "Certifications"}
                </h3>
                <Textarea
                  id="certifications"
                  name="certifications"
                  placeholder={
                    dictionary.placeholders?.enterCertifications ||
                    "Enter certifications (JSON format)"
                  }
                  rows={3}
                />
              </div>

              <Separator />

              {/* Education field */}
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-2">
                  {dictionary.fields?.education || "Education"}
                </h3>
                <Textarea
                  id="education"
                  name="education"
                  placeholder={
                    dictionary.placeholders?.enterEducation ||
                    "Enter education history (JSON format)"
                  }
                  rows={3}
                />
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>

        <CardFooter className="flex justify-between">
          <Button variant="outline" asChild>
            <Link href={lang ? `/${lang}${FEATURE_CONFIG.routes.LIST}` : ".."}>
              {dictionary.common?.cancel || "Cancel"}
            </Link>
          </Button>
          <Button type="submit" disabled={pending}>
            {pending
              ? dictionary.common?.saving || "Creating..."
              : dictionary.common?.create || "Create"}
          </Button>
        </CardFooter>
      </form>
    </Card>
  );
}
