"use client";

import {
  Pagination as ShadcnPagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { FeatureDictionary } from "../../lib/types";
import FEATURE_CONFIG from "../../lib/config";

interface PaginationProps {
  totalItems: number;
  pageSize: number;
  currentPage: number;
  lang: string;
  search?: string;
  dictionary: FeatureDictionary;
}

/**
 * Simple pagination component that creates links with search params
 */
export function Pagination({
  totalItems,
  pageSize,
  currentPage,
  lang,
  search,
  dictionary: _dictionary,
}: PaginationProps) {
  // Calculate total pages
  const totalPages = Math.max(1, Math.ceil(totalItems / pageSize));

  // Create URL for a specific page
  const createPageUrl = (page: number) => {
    const params = new URLSearchParams();
    params.set("page", page.toString());
    if (search) params.set("search", search);
    return `/${lang}${FEATURE_CONFIG.routes.LIST}?${params.toString()}`;
  };

  // Don't show pagination if there's only one page
  if (totalPages <= 1) {
    return (
      <div className="flex justify-between w-full">
        <div className="text-sm text-muted-foreground">
          Showing {totalItems} of {totalItems} items
        </div>
      </div>
    );
  }

  return (
    <div className="flex justify-between w-full items-center">
      <div className="text-sm text-muted-foreground">
        Showing {Math.min(pageSize, totalItems - (currentPage - 1) * pageSize)} of {totalItems}{" "}
        items
      </div>

      <ShadcnPagination>
        <PaginationContent>
          {/* Previous button */}
          <PaginationItem>
            {currentPage > 1 ? (
              <PaginationPrevious href={createPageUrl(currentPage - 1)} />
            ) : (
              <PaginationPrevious href="#" className="pointer-events-none opacity-50" />
            )}
          </PaginationItem>

          {/* First page */}
          <PaginationItem>
            <PaginationLink href={createPageUrl(1)} isActive={currentPage === 1}>
              1
            </PaginationLink>
          </PaginationItem>

          {/* Last page if more than 1 page */}
          {totalPages > 1 && (
            <PaginationItem>
              <PaginationLink
                href={createPageUrl(totalPages)}
                isActive={currentPage === totalPages}
              >
                {totalPages}
              </PaginationLink>
            </PaginationItem>
          )}

          {/* Next button */}
          <PaginationItem>
            {currentPage < totalPages ? (
              <PaginationNext href={createPageUrl(currentPage + 1)} />
            ) : (
              <PaginationNext href="#" className="pointer-events-none opacity-50" />
            )}
          </PaginationItem>
        </PaginationContent>
      </ShadcnPagination>
    </div>
  );
}
