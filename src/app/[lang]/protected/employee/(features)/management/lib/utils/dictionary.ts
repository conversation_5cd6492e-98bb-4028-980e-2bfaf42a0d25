import { Dictionary } from "@/lib/i18n/services/I18nService";
import { DOMAIN_ID } from "../../../../lib/config/domain";
import { FEATURE_ID } from "../config/feature";
import { FeatureDictionary } from "../types";

/**
 * Get the domain feature dictionary from the main dictionary
 * @param dictionary The main dictionary
 * @returns The domain feature dictionary
 */
export function getDomainFeatureDictionary(dictionary: Dictionary): FeatureDictionary {
  // Get the domain feature dictionary
  const employeeManagement = dictionary[DOMAIN_ID][FEATURE_ID];

  // Create a new FeatureDictionary with all properties
  return {
    // Common dictionary properties
    placeholders: dictionary.common.placeholders,
    gender: dictionary.common.gender,
    department: dictionary.common.department,
    common: dictionary.common,

    // Employee management properties
    title: employeeManagement.title,
    description: employeeManagement.description,
    createTitle: employeeManagement.createTitle,
    createDescription: employeeManagement.createDescription,
    editTitle: employeeManagement.editTitle,
    editDescription: employeeManagement.editDescription,
    viewTitle: employeeManagement.viewTitle,
    removeTitle: employeeManagement.removeTitle,
    removeDescription: employeeManagement.removeDescription,
    removeConfirm: employeeManagement.removeConfirm,
    removeCancel: employeeManagement.removeCancel,
    removeWarning: employeeManagement.removeWarning,
    backToList: employeeManagement.backToList,
    itemCreated: employeeManagement.itemCreated,
    itemUpdated: employeeManagement.itemUpdated,
    itemRemoved: employeeManagement.itemRemoved,
    noItems: employeeManagement.noItems,
    personalInformation: employeeManagement.personalInformation,

    // Tabs
    tabs: employeeManagement.tabs,

    // Fields
    fields: employeeManagement.fields,

    // Status
    status: employeeManagement.status,

    // Employment Status
    employmentStatus: employeeManagement.employmentStatus,

    // Actions
    actions: employeeManagement.actions,

    // Dialog texts
    confirmDeactivate: employeeManagement.confirmDeactivate,
    deactivateDescription: employeeManagement.deactivateDescription,
    activating: employeeManagement.activating,
    deactivating: employeeManagement.deactivating,
  };
}
