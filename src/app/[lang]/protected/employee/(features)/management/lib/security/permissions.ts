/**
 * Employee Management Permissions
 *
 * This file defines all permissions related to Employee Management.
 * These permissions can be imported and composed in the central authorization system.
 *
 * This file now uses the centralized configuration from the feature config.
 */

import FEATURE_CONFIG from "../config/feature";

// Re-export the permissions from the feature config
export const MANAGEMENT_DOMAIN = FEATURE_CONFIG.permissionDomain;
export const MANAGEMENT_PERMISSIONS = FEATURE_CONFIG.permissions;
export const MANAGEMENT_ROUTE_PERMISSIONS = FEATURE_CONFIG.routePermissions;

// Export the security configuration
const MANAGEMENT_SECURITY = {
  domain: MANAGEMENT_DOMAIN,
  permissions: <PERSON><PERSON><PERSON>MENT_PERMISSIONS,
  routePermissions: MANAGEMENT_ROUTE_PERMISSIONS,
};

export default MANAGEMENT_SECURITY;
