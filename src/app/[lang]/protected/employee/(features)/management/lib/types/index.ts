import { Dictionary } from "@/lib/i18n/services/I18nService";
import { Database } from "@/lib/types/database.types";

/**
 * Employee interface
 * Represents a single employee
 */
export type Employee = Database["public"]["Tables"]["employees"]["Row"];

/**
 * Employee Insert interface
 * Used when creating a new employee
 */
export type EmployeeInsert = Database["public"]["Tables"]["employees"]["Insert"];

/**
 * Employee Update interface
 * Used when updating an existing employee
 */
export type EmployeeUpdate = Database["public"]["Tables"]["employees"]["Update"];

/**
 * Feature Dictionary interface
 * Extends the base dictionary with additional properties
 */
export interface FeatureDictionary {
  // Common dictionary properties
  placeholders?: Dictionary["common"]["placeholders"];
  gender?: Dictionary["common"]["gender"];
  department?: Dictionary["common"]["department"];
  common?: Dictionary["common"];

  // Employee management properties
  title?: string;
  description?: string;
  createTitle?: string;
  createDescription?: string;
  editTitle?: string;
  editDescription?: string;
  viewTitle?: string;
  removeTitle?: string;
  removeDescription?: string;
  removeConfirm?: string;
  removeCancel?: string;
  removeWarning?: string;
  backToList?: string;
  itemCreated?: string;
  itemUpdated?: string;
  itemRemoved?: string;
  noItems?: string;
  personalInformation?: string;

  // Tabs
  tabs?: {
    contact?: string;
    employment?: string;
    professional?: string;
    personal?: string;
  };

  // Fields
  fields?: {
    name?: string;
    description?: string;
    status?: string;
    firstName?: string;
    lastName?: string;
    fullName?: string;
    profileImage?: string;
    dateOfBirth?: string;
    gender?: string;
    address?: string;
    employeeId?: string;
    hireDate?: string;
    terminationDate?: string;
    employmentStatus?: string;
    jobTitle?: string;
    department?: string;
    supervisor?: string;
    specializations?: string;
    certifications?: string;
    education?: string;
    emails?: string;
    phones?: string;
    userAccount?: string;
    createdAt?: string;
    updatedAt?: string;
  };

  // Status
  status?: {
    active?: string;
    inactive?: string;
    draft?: string;
  };

  // Employment Status
  employmentStatus?: {
    active?: string;
    inactive?: string;
    terminated?: string;
  };

  // Actions
  actions?: {
    create?: string;
    edit?: string;
    view?: string;
    remove?: string;
    activate?: string;
    deactivate?: string;
    linkUserAccount?: string;
  };

  // Dialog texts
  confirmDeactivate?: string;
  deactivateDescription?: string;
  activating?: string;
  deactivating?: string;
}
