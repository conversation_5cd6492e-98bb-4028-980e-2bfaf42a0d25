import { createClient } from "@/lib/supabase/server";
import { logger } from "@/lib/logger/services/LoggerService";
import { Employee, EmployeeInsert, EmployeeUpdate } from "../types";
import { ServiceResponse, successResponse, errorResponse } from "@/lib/types/responses";

/**
 * Service for managing employees
 * This is a singleton class that provides methods for CRUD operations on employees
 */
export class ManagementService {
  /**
   * Private constructor to prevent direct instantiation
   * Use the static methods instead
   */
  private constructor() {}

  /**
   * Maps database result to Employee type
   * @param data Database result
   * @returns Employee object
   */
  private static mapToEmployee(data: Record<string, unknown>): Employee {
    return {
      id: data.id,
      organization_id: data.organization_id || "",
      first_name: data.first_name || "",
      last_name: data.last_name || "",
      employment_status:
        (data.employment_status as "active" | "inactive" | "terminated") || "inactive",
      job_title: data.job_title || "",
      department: data.department || "",
      user_account_id: data.user_account_id,
      profile_image: data.profile_image,
      date_of_birth: data.date_of_birth,
      gender: data.gender,
      address: data.address,
      employee_id: data.employee_id,
      hire_date: data.hire_date,
      termination_date: data.termination_date,
      supervisor_id: data.supervisor_id,
      specializations: data.specializations,
      certifications: data.certifications,
      education: data.education,
      emails: data.emails,
      phones: data.phones,
      created_at: data.created_at || new Date().toISOString(),
      updated_at: data.updated_at || new Date().toISOString(),
    } as Employee;
  }

  /**
   * List employees with pagination
   * @param page Page number (1-based)
   * @param pageSize Number of items per page
   * @returns Service response with paginated array of employees and count
   */
  static async list(
    page: number = 1,
    pageSize: number = 10
  ): Promise<ServiceResponse<{ items: Employee[]; total: number }>> {
    const supabase = await createClient();

    // Calculate offset
    const offset = (page - 1) * pageSize;

    try {
      // Get count of all employees
      const { count, error: countError } = await supabase
        .from("employees")
        .select("*", { count: "exact", head: true });

      if (countError) {
        logger.error(`Error counting employees: ${countError.message}`);
        return errorResponse(countError, `Failed to count employees: ${countError.message}`);
      }

      // Get paginated employees
      const { data, error } = await supabase
        .from("employees")
        .select("*")
        .order("last_name", { ascending: true })
        .range(offset, offset + pageSize - 1);

      if (error) {
        logger.error(`Error listing employees: ${error.message}`);
        return errorResponse(error, `Failed to list employees: ${error.message}`);
      }

      // Map database results to Employee type
      const employees = data.map((item) => this.mapToEmployee(item));

      return successResponse(
        {
          items: employees,
          total: count || 0,
        },
        `Successfully retrieved ${data.length} employees (page ${page} of ${Math.ceil((count || 0) / pageSize)})`
      );
    } catch (error) {
      logger.error(`Unexpected error listing employees: ${error}`);
      return errorResponse(error, `Unexpected error listing employees`);
    }
  }

  /**
   * Read an employee by ID
   * @param id The ID of the employee
   * @returns Service response with the employee
   */
  static async view(id: string): Promise<ServiceResponse<Employee>> {
    try {
      const supabase = await createClient();

      const { data, error } = await supabase.from("employees").select("*").eq("id", id).single();

      if (error) {
        if (error.code === "PGRST116") {
          // Record not found
          return errorResponse(error, `Employee with ID ${id} not found`);
        }

        logger.error(`Error reading employee: ${error.message}`);
        return errorResponse(error, `Failed to read employee: ${error.message}`);
      }

      // Map database result to Employee type
      const employee = this.mapToEmployee(data as Record<string, unknown>);

      return successResponse(employee, "Successfully retrieved employee");
    } catch (error) {
      logger.error(`Unexpected error reading employee: ${error}`);
      return errorResponse(error, `Unexpected error reading employee`);
    }
  }

  /**
   * Search employees by name, department, or job title with pagination
   * @param query Search query
   * @param page Page number (1-based)
   * @param pageSize Number of items per page
   * @returns Service response with paginated array of matching employees and count
   */
  static async search(
    query: string,
    page: number = 1,
    pageSize: number = 10
  ): Promise<ServiceResponse<{ items: Employee[]; total: number }>> {
    if (!query || query.trim() === "") {
      return this.list(page, pageSize);
    }

    const supabase = await createClient();
    const searchTerm = `%${query.toLowerCase()}%`;

    // Calculate offset
    const offset = (page - 1) * pageSize;

    try {
      // Get count of matching employees
      const { count, error: countError } = await supabase
        .from("employees")
        .select("*", { count: "exact", head: true })
        .or(
          `first_name.ilike.${searchTerm},last_name.ilike.${searchTerm},department.ilike.${searchTerm},job_title.ilike.${searchTerm}`
        );

      if (countError) {
        logger.error(`Error counting search results: ${countError.message}`);
        return errorResponse(countError, `Failed to count search results: ${countError.message}`);
      }

      // Get paginated search results
      const { data, error } = await supabase
        .from("employees")
        .select("*")
        .or(
          `first_name.ilike.${searchTerm},last_name.ilike.${searchTerm},department.ilike.${searchTerm},job_title.ilike.${searchTerm}`
        )
        .order("last_name", { ascending: true })
        .range(offset, offset + pageSize - 1);

      if (error) {
        logger.error(`Error searching employees: ${error.message}`);
        return errorResponse(error, `Failed to search employees: ${error.message}`);
      }

      // Map database results to Employee type
      const employees = data.map((item) => this.mapToEmployee(item));

      return successResponse(
        {
          items: employees,
          total: count || 0,
        },
        `Found ${data.length} employees matching "${query}" (page ${page} of ${Math.ceil((count || 0) / pageSize)})`
      );
    } catch (error) {
      logger.error(`Unexpected error searching employees: ${error}`);
      return errorResponse(error, `Unexpected error searching employees`);
    }
  }

  /**
   * Create a new employee
   * @param employee The employee to create
   * @returns Service response with the created employee
   */
  static async create(employeeData: EmployeeInsert): Promise<ServiceResponse<Employee>> {
    try {
      const supabase = await createClient();

      const { data, error } = await supabase
        .from("employees")
        .insert(employeeData)
        .select()
        .single();

      if (error) {
        logger.error(`Error creating employee: ${error.message}`);
        return errorResponse(error, `Failed to create employee: ${error.message}`);
      }

      // Map database result to Employee type
      const employee = this.mapToEmployee(data);

      return successResponse(employee, "Successfully created employee");
    } catch (error) {
      logger.error(`Unexpected error creating employee: ${error}`);
      return errorResponse(error, `Unexpected error creating employee`);
    }
  }

  /**
   * Update an existing employee
   * @param id The ID of the employee to update
   * @param employee The updated employee data
   * @returns Service response with the updated employee
   */
  static async edit(id: string, employeeData: EmployeeUpdate): Promise<ServiceResponse<Employee>> {
    try {
      const supabase = await createClient();

      const { data, error } = await supabase
        .from("employees")
        .update(employeeData)
        .eq("id", id)
        .select()
        .single();

      if (error) {
        logger.error(`Error updating employee: ${error.message}`);
        return errorResponse(error, `Failed to update employee: ${error.message}`);
      }

      // Map database result to Employee type
      const employee = this.mapToEmployee(data);

      return successResponse(employee, "Successfully updated employee");
    } catch (error) {
      logger.error(`Unexpected error updating employee: ${error}`);
      return errorResponse(error, `Unexpected error updating employee`);
    }
  }

  /**
   * Remove an employee (soft delete by setting employment_status to terminated)
   * @param id The ID of the employee to remove
   * @returns Service response
   */
  static async remove(id: string): Promise<ServiceResponse<null>> {
    try {
      const supabase = await createClient();

      const { error } = await supabase
        .from("employees")
        .update({ employment_status: "terminated", updated_at: new Date().toISOString() })
        .eq("id", id);

      if (error) {
        logger.error(`Error removing employee: ${error.message}`);
        return errorResponse(error, `Failed to remove employee: ${error.message}`);
      }

      return successResponse(null, "Successfully removed employee");
    } catch (error) {
      logger.error(`Unexpected error removing employee: ${error}`);
      return errorResponse(error, `Unexpected error removing employee`);
    }
  }

  /**
   * Activate an employee
   * @param id The ID of the employee to activate
   * @returns Service response with the activated employee
   */
  static async activate(id: string): Promise<ServiceResponse<Employee>> {
    try {
      const supabase = await createClient();

      const { data, error } = await supabase
        .from("employees")
        .update({ employment_status: "active", updated_at: new Date().toISOString() })
        .eq("id", id)
        .select()
        .single();

      if (error) {
        logger.error(`Error activating employee: ${error.message}`);
        return errorResponse(error, `Failed to activate employee: ${error.message}`);
      }

      // Map database result to Employee type
      const employee = this.mapToEmployee(data);

      return successResponse(employee, "Successfully activated employee");
    } catch (error) {
      logger.error(`Unexpected error activating employee: ${error}`);
      return errorResponse(error, `Unexpected error activating employee`);
    }
  }

  /**
   * Deactivate an employee
   * @param id The ID of the employee to deactivate
   * @returns Service response with the deactivated employee
   */
  static async deactivate(id: string): Promise<ServiceResponse<Employee>> {
    try {
      const supabase = await createClient();

      const { data, error } = await supabase
        .from("employees")
        .update({ employment_status: "inactive", updated_at: new Date().toISOString() })
        .eq("id", id)
        .select()
        .single();

      if (error) {
        logger.error(`Error deactivating employee: ${error.message}`);
        return errorResponse(error, `Failed to deactivate employee: ${error.message}`);
      }

      // Map database result to Employee type
      const employee = this.mapToEmployee(data);

      return successResponse(employee, "Successfully deactivated employee");
    } catch (error) {
      logger.error(`Unexpected error deactivating employee: ${error}`);
      return errorResponse(error, `Unexpected error deactivating employee`);
    }
  }

  /**
   * Link an employee to a user account
   * @param id The ID of the employee
   * @param userAccountId The ID of the user account
   * @returns Service response with the updated employee
   */
  static async linkUserAccount(
    id: string,
    userAccountId: string
  ): Promise<ServiceResponse<Employee>> {
    try {
      const supabase = await createClient();

      const { data, error } = await supabase
        .from("employees")
        .update({ user_account_id: userAccountId, updated_at: new Date().toISOString() })
        .eq("id", id)
        .select()
        .single();

      if (error) {
        logger.error(`Error linking user account: ${error.message}`);
        return errorResponse(error, `Failed to link user account: ${error.message}`);
      }

      // Map database result to Employee type
      const employee = this.mapToEmployee(data);

      return successResponse(employee, "Successfully linked user account");
    } catch (error) {
      logger.error(`Unexpected error linking user account: ${error}`);
      return errorResponse(error, `Unexpected error linking user account`);
    }
  }
}
