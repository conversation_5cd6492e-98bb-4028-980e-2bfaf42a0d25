import React, { ReactNode } from "react";
import { getDictionary } from "@/lib/i18n/services/I18nService";

type AvailabilityLayoutProps = {
  children: React.ReactNode,
  params: Promise<{ lang: string }>,
}

export default async function AvailabilityLayout({
  children,
  params,
}: AvailabilityLayoutProps) {
  const { lang } = await params;
  const dictionary = await getDictionary(lang);
  const featureDictionary = dictionary.employee.availability;

  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">{featureDictionary.title}</h1>
        <p className="text-muted-foreground">{featureDictionary.description}</p>
      </div>
      {children}
    </div>
  );
}
