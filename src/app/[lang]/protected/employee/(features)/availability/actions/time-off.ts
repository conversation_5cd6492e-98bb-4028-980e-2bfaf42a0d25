"use server";

import { revalidatePath } from "next/cache";
import { redirect } from "next/navigation";
import { AvailabilityService } from "../lib/services";
import { EmployeeTimeOffInsert, EmployeeTimeOffUpdate } from "../lib/types";
import { auth } from "@/lib/authentication/services/AuthenticationService";
import { logger } from "@/lib/logger/services/LoggerService";
import { ServiceResponse, errorResponse, successResponse } from "@/lib/types/responses";
import { ProfileService } from "@/app/[lang]/protected/organization/(features)/profile/lib/services/ProfileService";

/**
 * Get all time-off requests for an employee
 * @param employeeId The employee ID to get time-off requests for
 * @returns Action response with the time-off requests
 */
export async function getTimeOffByEmployee(employeeId: string): Promise<ServiceResponse> {
  try {
    const currentUser = await auth.getCurrentUser();
    if (!currentUser) {
      return errorResponse(null, "Unauthorized");
    }

    const result = await AvailabilityService.getTimeOffByEmployee(employeeId);

    if (!result.success) {
      return errorResponse(result.error, result.message);
    }

    return successResponse(result.data);
  } catch (error) {
    logger.error(`Error in getTimeOffByEmployee action: ${error}`);
    return errorResponse(error, "Failed to get time-off requests");
  }
}

/**
 * Get a specific time-off request by ID
 * @param id The time-off request ID
 * @returns Action response with the time-off request
 */
export async function getTimeOffById(id: string): Promise<ServiceResponse> {
  try {
    const currentUser = await auth.getCurrentUser();
    if (!currentUser) {
      return errorResponse(null, "Unauthorized");
    }

    const result = await AvailabilityService.getTimeOffById(id);

    if (!result.success) {
      return errorResponse(result.error, result.message);
    }

    return successResponse(result.data);
  } catch (error) {
    logger.error(`Error in getTimeOffById action: ${error}`);
    return errorResponse(error, "Failed to get time-off request");
  }
}

/**
 * Create a new time-off request
 * @param formData The form data containing the time-off request details
 * @returns Action response with the created time-off request
 */
export async function createTimeOff(formData: FormData): Promise<ServiceResponse> {
  try {
    const currentUser = await auth.getCurrentUser();
    if (!currentUser) {
      return errorResponse(null, "Unauthorized");
    }

    const organization = await ProfileService.getCurrentOrganization();
    if (!organization) {
      return errorResponse(null, "Organization not found");
    }
    const organizationId = organization.id;

    const employeeId = formData.get("employee_id") as string;
    const startDate = formData.get("start_date") as string;
    const endDate = formData.get("end_date") as string;
    const startTime = (formData.get("start_time") as string) || undefined;
    const endTime = (formData.get("end_time") as string) || undefined;
    const type = formData.get("type") as "vacation" | "sick_leave" | "personal_leave" | "other";
    const description = (formData.get("description") as string) || undefined;

    const timeOff: EmployeeTimeOffInsert = {
      employee_id: employeeId,
      organization_id: organizationId,
      start_date: startDate,
      end_date: endDate,
      start_time: startTime,
      end_time: endTime,
      type,
      description,
      created_by: currentUser.id,
    };

    const result = await AvailabilityService.createTimeOff(timeOff);

    if (!result.success) {
      return errorResponse(result.error, result.message);
    }

    revalidatePath(`/[lang]/protected/employee/(features)/availability/[id]/view`);
    return successResponse(result.data);
  } catch (error) {
    logger.error(`Error in createTimeOff action: ${error}`);
    return errorResponse(error, "Failed to create time-off request");
  }
}

/**
 * Update an existing time-off request
 * @param id The time-off request ID
 * @param formData The form data containing the updated time-off request details
 * @returns Action response with the updated time-off request
 */
export async function updateTimeOff(id: string, formData: FormData): Promise<ServiceResponse> {
  try {
    const currentUser = await auth.getCurrentUser();
    if (!currentUser) {
      return errorResponse(null, "Unauthorized");
    }

    const startDate = formData.get("start_date") as string;
    const endDate = formData.get("end_date") as string;
    const startTime = (formData.get("start_time") as string) || undefined;
    const endTime = (formData.get("end_time") as string) || undefined;
    const type = formData.get("type") as "vacation" | "sick_leave" | "personal_leave" | "other";
    const description = (formData.get("description") as string) || undefined;

    const timeOff: EmployeeTimeOffUpdate = {
      start_date: startDate,
      end_date: endDate,
      start_time: startTime,
      end_time: endTime,
      type,
      description,
      updated_by: currentUser.id,
    };

    const result = await AvailabilityService.updateTimeOff(id, timeOff);

    if (!result.success) {
      return errorResponse(result.error, result.message);
    }

    revalidatePath(`/[lang]/protected/employee/(features)/availability/[id]/view`);
    return successResponse(result.data);
  } catch (error) {
    logger.error(`Error in updateTimeOff action: ${error}`);
    return errorResponse(error, "Failed to update time-off request");
  }
}

/**
 * Delete a time-off request
 * @param id The time-off request ID
 * @returns Action response with success status
 */
export async function deleteTimeOff(id: string): Promise<ServiceResponse> {
  try {
    const currentUser = await auth.getCurrentUser();
    if (!currentUser) {
      return errorResponse(null, "Unauthorized");
    }

    const result = await AvailabilityService.deleteTimeOff(id);

    if (!result.success) {
      return errorResponse(result.error, result.message);
    }

    revalidatePath(`/[lang]/protected/employee/(features)/availability/[id]/view`);
    return successResponse(null);
  } catch (error) {
    logger.error(`Error in deleteTimeOff action: ${error}`);
    return errorResponse(error, "Failed to delete time-off request");
  }
}

/**
 * Approve a time-off request
 * @param id The time-off request ID
 * @returns Action response with the approved time-off request
 */
export async function approveTimeOff(id: string): Promise<ServiceResponse> {
  try {
    const currentUser = await auth.getCurrentUser();
    if (!currentUser) {
      return errorResponse(null, "Unauthorized");
    }

    const timeOff: EmployeeTimeOffUpdate = {
      status: "approved",
      approved_by: currentUser.id,
      approved_at: new Date().toISOString(),
      updated_by: currentUser.id,
    };

    const result = await AvailabilityService.updateTimeOff(id, timeOff);

    if (!result.success) {
      return errorResponse(result.error, result.message);
    }

    revalidatePath(`/[lang]/protected/employee/(features)/availability/[id]/view`);
    return successResponse(result.data);
  } catch (error) {
    logger.error(`Error in approveTimeOff action: ${error}`);
    return errorResponse(error, "Failed to approve time-off request");
  }
}

/**
 * Reject a time-off request
 * @param id The time-off request ID
 * @returns Action response with the rejected time-off request
 */
export async function rejectTimeOff(id: string): Promise<ServiceResponse> {
  try {
    const currentUser = await auth.getCurrentUser();
    if (!currentUser) {
      return errorResponse(null, "Unauthorized");
    }

    const timeOff: EmployeeTimeOffUpdate = {
      status: "rejected",
      updated_by: currentUser.id,
    };

    const result = await AvailabilityService.updateTimeOff(id, timeOff);

    if (!result.success) {
      return errorResponse(result.error, result.message);
    }

    revalidatePath(`/[lang]/protected/employee/(features)/availability/[id]/view`);
    return successResponse(result.data);
  } catch (error) {
    logger.error(`Error in rejectTimeOff action: ${error}`);
    return errorResponse(error, "Failed to reject time-off request");
  }
}

/**
 * Cancel a time-off request
 * @param id The time-off request ID
 * @returns Action response with the cancelled time-off request
 */
export async function cancelTimeOff(id: string): Promise<ServiceResponse> {
  try {
    const currentUser = await auth.getCurrentUser();
    if (!currentUser) {
      return errorResponse(null, "Unauthorized");
    }

    const timeOff: EmployeeTimeOffUpdate = {
      status: "cancelled",
      updated_by: currentUser.id,
    };

    const result = await AvailabilityService.updateTimeOff(id, timeOff);

    if (!result.success) {
      return errorResponse(result.error, result.message);
    }

    revalidatePath(`/[lang]/protected/employee/(features)/availability/[id]/view`);
    return successResponse(result.data);
  } catch (error) {
    logger.error(`Error in cancelTimeOff action: ${error}`);
    return errorResponse(error, "Failed to cancel time-off request");
  }
}
