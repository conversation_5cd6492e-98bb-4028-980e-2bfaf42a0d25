import Link from "next/link";
import { notFound } from "next/navigation";
import { getDictionary } from "@/lib/i18n/services/I18nService";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { EmployeeService } from "@/app/[lang]/protected/employee/lib/services/EmployeeService";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { CalendarIcon, ClockIcon, ExclamationTriangleIcon } from "@radix-ui/react-icons";
import { AvailabilityEditForm } from "../../../components/AvailabilityEditForm";
import { TimeOffForm } from "../../../components/TimeOffForm";
import { ExceptionForm } from "../../../components/ExceptionForm";
import { getAvailabilityByEmployee } from "../../../actions/availability";
import { getTimeOffById } from "../../../actions/time-off";
import {
  getAvailabilityExceptionById,
} from "../../../actions/exceptions";
import { EmployeeTimeOff, EmployeeAvailabilityException } from "../../../lib/types";

// Using any type to bypass type checking issues
type EditPageProps = {
  params: Promise<{ lang:string, id:string }>;
  searchParams: Promise<{ availabilityId: string, timeOffId:string, exceptionId:string, tab:string }>,
};

export default async function EditPage({
  params,
  searchParams
}: EditPageProps) {
  const { lang, id } = await params;
  const { availabilityId, timeOffId, exceptionId, tab } = await searchParams; 
  const dictionary = await getDictionary(lang);
  const featureDictionary = dictionary.employee.availability;

  // Get employee details
  const employeeResponse = await EmployeeService.getEmployeeById(id);
  if (!employeeResponse.success) {
    notFound();
  }
  const employee = employeeResponse.data as {
    id: string;
    first_name: string;
    last_name: string;
    job_title: string;
    profile_image?: string;
  };

  // Get availability data
  const availabilityResponse = await getAvailabilityByEmployee(id);
  const availability = availabilityResponse.success ? availabilityResponse.data : [];

  // Cast availability to the correct type
  const typedAvailability = availability as any[];

  // Get time-off data
  let timeOffData: EmployeeTimeOff | undefined;
  if (timeOffId) {
    const timeOffResponse = await getTimeOffById(timeOffId);
    timeOffData = timeOffResponse.success ? (timeOffResponse.data as EmployeeTimeOff) : undefined;
  }

  // Get exceptions data
  let exceptionData: EmployeeAvailabilityException | undefined;
  if (exceptionId) {
    const exceptionResponse = await getAvailabilityExceptionById(exceptionId);
    exceptionData = exceptionResponse.success
      ? (exceptionResponse.data as EmployeeAvailabilityException)
      : undefined;
  }

  // Determine which tab to show based on the search params
  let activeTab = tab;
  if (availabilityId) activeTab = "regularHours";
  if (timeOffId) activeTab = "timeOff";
  if (exceptionId) activeTab = "exceptions";

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-4">
          <Avatar className="h-16 w-16">
            <AvatarImage
              src={employee.profile_image || ""}
              alt={`${employee.first_name} ${employee.last_name}`}
            />
            <AvatarFallback className="text-lg">
              {employee.first_name?.[0]}
              {employee.last_name?.[0]}
            </AvatarFallback>
          </Avatar>
          <div>
            <h2 className="text-2xl font-bold">
              {employee.first_name} {employee.last_name}
            </h2>
            <p className="text-muted-foreground">{employee.job_title}</p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button asChild variant="outline">
            <Link href={`/${lang}/protected/employee/availability/${id}/view`}>
              {featureDictionary.cancelButton}
            </Link>
          </Button>
        </div>
      </div>

      <Tabs defaultValue={activeTab} className="mt-6">
        <TabsList className="mb-4">
          <TabsTrigger value="regularHours">
            <ClockIcon className="mr-2 h-4 w-4" />
            {featureDictionary.tabs.regularHours}
          </TabsTrigger>
          <TabsTrigger value="timeOff">
            <CalendarIcon className="mr-2 h-4 w-4" />
            {featureDictionary.tabs.timeOff}
          </TabsTrigger>
          <TabsTrigger value="exceptions">
            <ExclamationTriangleIcon className="mr-2 h-4 w-4" />
            {featureDictionary.tabs.exceptions}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="regularHours">
          <Card>
            <CardHeader>
              <CardTitle>
                {availabilityId ? featureDictionary.editTitle : featureDictionary.createTitle}
              </CardTitle>
              <CardDescription>
                {availabilityId
                  ? featureDictionary.editDescription
                  : featureDictionary.createDescription}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <AvailabilityEditForm
                employeeId={id}
                availabilityId={availabilityId}
                initialData={typedAvailability}
                lang={lang}
                dictionary={dictionary}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="timeOff">
          <Card>
            <CardHeader>
              <CardTitle>
                {timeOffId
                  ? dictionary.employee.availability.timeOff.editTitle
                  : dictionary.employee.availability.timeOff.createTitle}
              </CardTitle>
              <CardDescription>
                {timeOffId
                  ? dictionary.employee.availability.timeOff.editDescription
                  : dictionary.employee.availability.timeOff.createDescription}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <TimeOffForm
                employeeId={id}
                timeOffId={timeOffId}
                initialData={timeOffData}
                lang={lang}
                dictionary={dictionary}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="exceptions">
          <Card>
            <CardHeader>
              <CardTitle>
                {exceptionId
                  ? dictionary.employee.availability.exceptions.editTitle
                  : dictionary.employee.availability.exceptions.createTitle}
              </CardTitle>
              <CardDescription>
                {exceptionId
                  ? dictionary.employee.availability.exceptions.editDescription
                  : dictionary.employee.availability.exceptions.createDescription}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ExceptionForm
                employeeId={id}
                exceptionId={exceptionId}
                initialData={exceptionData}
                lang={lang}
                dictionary={dictionary}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
