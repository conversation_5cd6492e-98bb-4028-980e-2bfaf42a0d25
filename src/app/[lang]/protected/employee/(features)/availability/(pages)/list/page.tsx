import Link from "next/link";
import { getDictionary } from "@/lib/i18n/services/I18nService";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { EmployeeService } from "@/app/[lang]/protected/employee/lib/services/EmployeeService";

// Using any type to bypass type checking issues
type ListPageProps = any;

export default async function ListPage({ params }: ListPageProps) {
  const { lang } = await params;
  const dictionary = await getDictionary(lang);
  const featureDictionary = dictionary.employee.availability;

  // Get all employees
  const employeesResponse = await EmployeeService.listEmployees();
  const employees = employeesResponse.success ? employeesResponse.data : [];

  // Define employee type
  type Employee = {
    id: string;
    first_name: string;
    last_name: string;
    job_title: string;
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-2xl font-bold">{featureDictionary.listTitle}</h2>
          <p className="text-muted-foreground">{featureDictionary.listDescription}</p>
        </div>
      </div>

      {(employees as Employee[]).length === 0 ? (
        <Card>
          <CardContent className="py-10">
            <div className="text-center">
              <p className="text-muted-foreground">{featureDictionary.noItems}</p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {(employees as Employee[]).map((employee) => (
            <Card key={employee.id} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">
                  {employee.first_name} {employee.last_name}
                </CardTitle>
                <CardDescription>{employee.job_title}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex justify-end">
                  <Button asChild variant="outline" size="sm">
                    <Link href={`/${lang}/protected/employee/availability/${employee.id}/view`}>
                      {dictionary.common.actions}
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
