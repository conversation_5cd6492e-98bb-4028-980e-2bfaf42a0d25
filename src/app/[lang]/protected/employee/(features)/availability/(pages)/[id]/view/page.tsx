import Link from "next/link";
import { notFound } from "next/navigation";
import { getDictionary } from "@/lib/i18n/services/I18nService";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { EmployeeService } from "@/app/[lang]/protected/employee/lib/services/EmployeeService";
import {
  getAvailabilityByEmployee,
  getTimeOffByEmployee,
  getAvailabilityExceptionsByEmployee,
} from "../../../actions";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { CalendarIcon, ClockIcon, ExclamationTriangleIcon, PlusIcon } from "@radix-ui/react-icons";
import { TimeOffList } from "../../../components/TimeOffList";
import { ExceptionsList } from "../../../components/ExceptionsList";
import { EmployeeTimeOff, EmployeeAvailabilityException } from "../../../lib/types";

// Using any type to bypass type checking issues
type ViewPageProps = any;

export default async function ViewPage({ params }: ViewPageProps) {
  const { lang, id } = await params;
  const dictionary = await getDictionary(lang);
  const featureDictionary = dictionary.employee.availability;

  // Get employee details
  const employeeResponse = await EmployeeService.getEmployeeById(id);
  if (!employeeResponse.success) {
    notFound();
  }
  const employee = employeeResponse.data as {
    id: string;
    first_name: string;
    last_name: string;
    job_title: string;
    profile_image?: string;
  };

  // Get availability data
  const availabilityResponse = await getAvailabilityByEmployee(id);
  const availability = availabilityResponse.success ? availabilityResponse.data : [];

  // Cast availability to the correct type
  const typedAvailability = availability as AvailabilityItem[];

  // Define availability item type
  type AvailabilityItem = {
    id: string;
    day_of_week: number;
    start_time: string;
    end_time: string;
  };

  // Get time-off data
  const timeOffResponse = await getTimeOffByEmployee(id);
  const timeOff = timeOffResponse.success ? timeOffResponse.data : [];

  // Cast timeOff to the correct type
  const typedTimeOff = timeOff as EmployeeTimeOff[];

  // Get exceptions data
  const exceptionsResponse = await getAvailabilityExceptionsByEmployee(id);
  const exceptions = exceptionsResponse.success ? exceptionsResponse.data : [];

  // Cast exceptions to the correct type
  const typedExceptions = exceptions as EmployeeAvailabilityException[];

  // Helper function to get day name
  const getDayName = (dayOfWeek: number) => {
    const days = [
      featureDictionary.daysOfWeek.sunday,
      featureDictionary.daysOfWeek.monday,
      featureDictionary.daysOfWeek.tuesday,
      featureDictionary.daysOfWeek.wednesday,
      featureDictionary.daysOfWeek.thursday,
      featureDictionary.daysOfWeek.friday,
      featureDictionary.daysOfWeek.saturday,
    ];
    return days[dayOfWeek];
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-4">
          <Avatar className="h-16 w-16">
            <AvatarImage
              src={employee.profile_image || ""}
              alt={`${employee.first_name} ${employee.last_name}`}
            />
            <AvatarFallback className="text-lg">
              {employee.first_name?.[0]}
              {employee.last_name?.[0]}
            </AvatarFallback>
          </Avatar>
          <div>
            <h2 className="text-2xl font-bold">
              {employee.first_name} {employee.last_name}
            </h2>
            <p className="text-muted-foreground">{employee.job_title}</p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button asChild variant="outline">
            <Link href={`/${lang}/protected/employee/availability/list`}>
              {featureDictionary.backToList}
            </Link>
          </Button>
        </div>
      </div>

      <Tabs defaultValue="regularHours" className="mt-6">
        <TabsList className="mb-4">
          <TabsTrigger value="regularHours">
            <ClockIcon className="mr-2 h-4 w-4" />
            {featureDictionary.tabs.regularHours}
          </TabsTrigger>
          <TabsTrigger value="timeOff">
            <CalendarIcon className="mr-2 h-4 w-4" />
            {featureDictionary.tabs.timeOff}
          </TabsTrigger>
          <TabsTrigger value="exceptions">
            <ExclamationTriangleIcon className="mr-2 h-4 w-4" />
            {featureDictionary.tabs.exceptions}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="regularHours">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>{featureDictionary.tabs.regularHours}</CardTitle>
                <CardDescription>{featureDictionary.viewDescription}</CardDescription>
              </div>
              <Button asChild size="sm">
                <Link href={`/${lang}/protected/employee/availability/${employee.id}/edit`}>
                  <PlusIcon className="mr-2 h-4 w-4" />
                  {featureDictionary.createButton || "Add"}
                </Link>
              </Button>
            </CardHeader>
            <CardContent>
              {typedAvailability.length === 0 ? (
                <div className="text-center py-4">
                  <p className="text-muted-foreground">{featureDictionary.noItems}</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {typedAvailability.map((item: AvailabilityItem) => (
                    <div
                      key={item.id}
                      className="flex justify-between items-center p-4 border rounded-lg"
                    >
                      <div>
                        <div className="font-medium">{getDayName(item.day_of_week)}</div>
                        <div className="text-sm text-muted-foreground">
                          {item.start_time} - {item.end_time}
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button asChild size="sm" variant="outline">
                          <Link
                            href={`/${lang}/protected/employee/availability/${employee.id}/edit?availabilityId=${item.id}`}
                          >
                            {featureDictionary.editButton}
                          </Link>
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="timeOff">
          <Card>
            <CardHeader>
              <CardTitle>{featureDictionary.tabs.timeOff}</CardTitle>
              <CardDescription>{featureDictionary.viewDescription}</CardDescription>
            </CardHeader>
            <CardContent>
              <TimeOffList
                employeeId={id}
                timeOffList={typedTimeOff}
                lang={lang}
                dictionary={dictionary}
                isManager={true}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="exceptions">
          <Card>
            <CardHeader>
              <CardTitle>{featureDictionary.tabs.exceptions}</CardTitle>
              <CardDescription>{featureDictionary.viewDescription}</CardDescription>
            </CardHeader>
            <CardContent>
              <ExceptionsList
                employeeId={id}
                exceptionsList={typedExceptions}
                lang={lang}
                dictionary={dictionary}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
