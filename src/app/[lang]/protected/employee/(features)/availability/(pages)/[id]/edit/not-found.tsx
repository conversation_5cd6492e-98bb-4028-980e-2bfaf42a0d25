import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

export default function NotFound() {
  return (
    <div className="flex items-center justify-center min-h-[60vh]">
      <Card className="max-w-md w-full">
        <CardHeader>
          <CardTitle className="text-2xl">Employee Not Found</CardTitle>
          <CardDescription>
            The employee you are trying to edit does not exist or you don't have permission to edit
            their availability.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            Please check the employee ID and try again, or go back to the list to select a different
            employee.
          </p>
        </CardContent>
        <CardFooter>
          <Button asChild>
            <Link href="../list">Back to List</Link>
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
