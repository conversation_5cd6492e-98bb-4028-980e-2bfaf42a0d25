import { createClient } from "@/lib/supabase/server";
import { logger } from "@/lib/logger/services/LoggerService";
import {
  EmployeeAvailability,
  EmployeeAvailabilityInsert,
  EmployeeAvailabilityUpdate,
  EmployeeTimeOff,
  EmployeeTimeOffInsert,
  EmployeeTimeOffUpdate,
  EmployeeAvailabilityException,
  EmployeeAvailabilityExceptionInsert,
  EmployeeAvailabilityExceptionUpdate,
} from "../types";
import { ServiceResponse, successResponse, errorResponse } from "@/lib/types/responses";

/**
 * Service for managing employee availability
 * This is a singleton class that provides methods for CRUD operations on employee availability
 */
export class AvailabilityService {
  /**
   * Private constructor to prevent direct instantiation
   * Use the static methods instead
   */
  private constructor() {}

  /**
   * Get all availability records for an employee
   * @param employeeId The employee ID to get availability for
   * @returns Service response with the availability records
   */
  static async getAvailabilityByEmployee(
    employeeId: string
  ): Promise<ServiceResponse<EmployeeAvailability[]>> {
    try {
      const client = await createClient();

      const { data, error } = await client
        .from("employee_availability")
        .select("*")
        .eq("employee_id", employeeId)
        .order("day_of_week", { ascending: true })
        .order("start_time", { ascending: true });

      if (error) {
        logger.error(`Error getting availability for employee ${employeeId}: ${error.message}`);
        return errorResponse(error, `Failed to get availability: ${error.message}`);
      }

      return successResponse(data);
    } catch (error) {
      logger.error(`Unexpected error getting availability: ${error}`);
      return errorResponse(error, `Unexpected error getting availability`);
    }
  }

  /**
   * Get a specific availability record by ID
   * @param id The availability record ID
   * @returns Service response with the availability record
   */
  static async getAvailabilityById(id: string): Promise<ServiceResponse<EmployeeAvailability>> {
    try {
      const client = await createClient();

      const { data, error } = await client
        .from("employee_availability")
        .select("*")
        .eq("id", id)
        .single();

      if (error) {
        logger.error(`Error getting availability ${id}: ${error.message}`);
        return errorResponse(error, `Failed to get availability: ${error.message}`);
      }

      return successResponse(data);
    } catch (error) {
      logger.error(`Unexpected error getting availability: ${error}`);
      return errorResponse(error, `Unexpected error getting availability`);
    }
  }

  /**
   * Create a new availability record
   * @param availability The availability record to create
   * @returns Service response with the created availability record
   */
  static async createAvailability(
    availability: EmployeeAvailabilityInsert
  ): Promise<ServiceResponse<EmployeeAvailability>> {
    try {
      const client = await createClient();

      const { data, error } = await client
        .from("employee_availability")
        .insert(availability)
        .select()
        .single();

      if (error) {
        logger.error(`Error creating availability: ${error.message}`);
        return errorResponse(error, `Failed to create availability: ${error.message}`);
      }

      return successResponse(data);
    } catch (error) {
      logger.error(`Unexpected error creating availability: ${error}`);
      return errorResponse(error, `Unexpected error creating availability`);
    }
  }

  /**
   * Update an existing availability record
   * @param id The availability record ID
   * @param availability The updated availability record
   * @returns Service response with the updated availability record
   */
  static async updateAvailability(
    id: string,
    availability: EmployeeAvailabilityUpdate
  ): Promise<ServiceResponse<EmployeeAvailability>> {
    try {
      const client = await createClient();

      const { data, error } = await client
        .from("employee_availability")
        .update(availability)
        .eq("id", id)
        .select()
        .single();

      if (error) {
        logger.error(`Error updating availability ${id}: ${error.message}`);
        return errorResponse(error, `Failed to update availability: ${error.message}`);
      }

      return successResponse(data);
    } catch (error) {
      logger.error(`Unexpected error updating availability: ${error}`);
      return errorResponse(error, `Unexpected error updating availability`);
    }
  }

  /**
   * Delete an availability record
   * @param id The availability record ID
   * @returns Service response with success status
   */
  static async deleteAvailability(id: string): Promise<ServiceResponse<null>> {
    try {
      const client = await createClient();

      const { error } = await client.from("employee_availability").delete().eq("id", id);

      if (error) {
        logger.error(`Error deleting availability ${id}: ${error.message}`);
        return errorResponse(error, `Failed to delete availability: ${error.message}`);
      }

      return successResponse(null);
    } catch (error) {
      logger.error(`Unexpected error deleting availability: ${error}`);
      return errorResponse(error, `Unexpected error deleting availability`);
    }
  }

  /**
   * Get all time-off requests for an employee
   * @param employeeId The employee ID to get time-off requests for
   * @returns Service response with the time-off requests
   */
  static async getTimeOffByEmployee(
    employeeId: string
  ): Promise<ServiceResponse<EmployeeTimeOff[]>> {
    try {
      const client = await createClient();

      const { data, error } = await client
        .from("employee_time_off")
        .select("*")
        .eq("employee_id", employeeId)
        .order("start_date", { ascending: true });

      if (error) {
        logger.error(`Error getting time-off for employee ${employeeId}: ${error.message}`);
        return errorResponse(error, `Failed to get time-off: ${error.message}`);
      }

      return successResponse(data);
    } catch (error) {
      logger.error(`Unexpected error getting time-off: ${error}`);
      return errorResponse(error, `Unexpected error getting time-off`);
    }
  }

  /**
   * Get a specific time-off request by ID
   * @param id The time-off request ID
   * @returns Service response with the time-off request
   */
  static async getTimeOffById(id: string): Promise<ServiceResponse<EmployeeTimeOff>> {
    try {
      const client = await createClient();

      const { data, error } = await client
        .from("employee_time_off")
        .select("*")
        .eq("id", id)
        .single();

      if (error) {
        logger.error(`Error getting time-off ${id}: ${error.message}`);
        return errorResponse(error, `Failed to get time-off: ${error.message}`);
      }

      return successResponse(data);
    } catch (error) {
      logger.error(`Unexpected error getting time-off: ${error}`);
      return errorResponse(error, `Unexpected error getting time-off`);
    }
  }

  /**
   * Create a new time-off request
   * @param timeOff The time-off request to create
   * @returns Service response with the created time-off request
   */
  static async createTimeOff(
    timeOff: EmployeeTimeOffInsert
  ): Promise<ServiceResponse<EmployeeTimeOff>> {
    try {
      const client = await createClient();

      const { data, error } = await client
        .from("employee_time_off")
        .insert(timeOff)
        .select()
        .single();

      if (error) {
        logger.error(`Error creating time-off: ${error.message}`);
        return errorResponse(error, `Failed to create time-off: ${error.message}`);
      }

      return successResponse(data);
    } catch (error) {
      logger.error(`Unexpected error creating time-off: ${error}`);
      return errorResponse(error, `Unexpected error creating time-off`);
    }
  }

  /**
   * Update an existing time-off request
   * @param id The time-off request ID
   * @param timeOff The updated time-off request
   * @returns Service response with the updated time-off request
   */
  static async updateTimeOff(
    id: string,
    timeOff: EmployeeTimeOffUpdate
  ): Promise<ServiceResponse<EmployeeTimeOff>> {
    try {
      const client = await createClient();

      const { data, error } = await client
        .from("employee_time_off")
        .update(timeOff)
        .eq("id", id)
        .select()
        .single();

      if (error) {
        logger.error(`Error updating time-off ${id}: ${error.message}`);
        return errorResponse(error, `Failed to update time-off: ${error.message}`);
      }

      return successResponse(data);
    } catch (error) {
      logger.error(`Unexpected error updating time-off: ${error}`);
      return errorResponse(error, `Unexpected error updating time-off`);
    }
  }

  /**
   * Delete a time-off request
   * @param id The time-off request ID
   * @returns Service response with success status
   */
  static async deleteTimeOff(id: string): Promise<ServiceResponse<null>> {
    try {
      const client = await createClient();

      const { error } = await client.from("employee_time_off").delete().eq("id", id);

      if (error) {
        logger.error(`Error deleting time-off ${id}: ${error.message}`);
        return errorResponse(error, `Failed to delete time-off: ${error.message}`);
      }

      return successResponse(null);
    } catch (error) {
      logger.error(`Unexpected error deleting time-off: ${error}`);
      return errorResponse(error, `Unexpected error deleting time-off`);
    }
  }

  /**
   * Get all availability exceptions for an employee
   * @param employeeId The employee ID to get availability exceptions for
   * @returns Service response with the availability exceptions
   */
  static async getAvailabilityExceptionsByEmployee(
    employeeId: string
  ): Promise<ServiceResponse<EmployeeAvailabilityException[]>> {
    try {
      const client = await createClient();

      const { data, error } = await client
        .from("employee_availability_exceptions")
        .select("*")
        .eq("employee_id", employeeId)
        .order("exception_date", { ascending: true });

      if (error) {
        logger.error(
          `Error getting availability exceptions for employee ${employeeId}: ${error.message}`
        );
        return errorResponse(error, `Failed to get availability exceptions: ${error.message}`);
      }

      return successResponse(data);
    } catch (error) {
      logger.error(`Unexpected error getting availability exceptions: ${error}`);
      return errorResponse(error, `Unexpected error getting availability exceptions`);
    }
  }

  /**
   * Get a specific availability exception by ID
   * @param id The availability exception ID
   * @returns Service response with the availability exception
   */
  static async getAvailabilityExceptionById(
    id: string
  ): Promise<ServiceResponse<EmployeeAvailabilityException>> {
    try {
      const client = await createClient();

      const { data, error } = await client
        .from("employee_availability_exceptions")
        .select("*")
        .eq("id", id)
        .single();

      if (error) {
        logger.error(`Error getting availability exception ${id}: ${error.message}`);
        return errorResponse(error, `Failed to get availability exception: ${error.message}`);
      }

      return successResponse(data);
    } catch (error) {
      logger.error(`Unexpected error getting availability exception: ${error}`);
      return errorResponse(error, `Unexpected error getting availability exception`);
    }
  }

  /**
   * Create a new availability exception
   * @param exception The availability exception to create
   * @returns Service response with the created availability exception
   */
  static async createAvailabilityException(
    exception: EmployeeAvailabilityExceptionInsert
  ): Promise<ServiceResponse<EmployeeAvailabilityException>> {
    try {
      const client = await createClient();

      const { data, error } = await client
        .from("employee_availability_exceptions")
        .insert(exception)
        .select()
        .single();

      if (error) {
        logger.error(`Error creating availability exception: ${error.message}`);
        return errorResponse(error, `Failed to create availability exception: ${error.message}`);
      }

      return successResponse(data);
    } catch (error) {
      logger.error(`Unexpected error creating availability exception: ${error}`);
      return errorResponse(error, `Unexpected error creating availability exception`);
    }
  }

  /**
   * Update an existing availability exception
   * @param id The availability exception ID
   * @param exception The updated availability exception
   * @returns Service response with the updated availability exception
   */
  static async updateAvailabilityException(
    id: string,
    exception: EmployeeAvailabilityExceptionUpdate
  ): Promise<ServiceResponse<EmployeeAvailabilityException>> {
    try {
      const client = await createClient();

      const { data, error } = await client
        .from("employee_availability_exceptions")
        .update(exception)
        .eq("id", id)
        .select()
        .single();

      if (error) {
        logger.error(`Error updating availability exception ${id}: ${error.message}`);
        return errorResponse(error, `Failed to update availability exception: ${error.message}`);
      }

      return successResponse(data);
    } catch (error) {
      logger.error(`Unexpected error updating availability exception: ${error}`);
      return errorResponse(error, `Unexpected error updating availability exception`);
    }
  }

  /**
   * Delete an availability exception
   * @param id The availability exception ID
   * @returns Service response with success status
   */
  static async deleteAvailabilityException(id: string): Promise<ServiceResponse<null>> {
    try {
      const client = await createClient();

      const { error } = await client.from("employee_availability_exceptions").delete().eq("id", id);

      if (error) {
        logger.error(`Error deleting availability exception ${id}: ${error.message}`);
        return errorResponse(error, `Failed to delete availability exception: ${error.message}`);
      }

      return successResponse(null);
    } catch (error) {
      logger.error(`Unexpected error deleting availability exception: ${error}`);
      return errorResponse(error, `Unexpected error deleting availability exception`);
    }
  }
}
