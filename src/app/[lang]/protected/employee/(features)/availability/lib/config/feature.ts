import DOMAIN_CONFIG from "../../../../lib/config/domain";

// Feature identifier (used in permissions, routes, etc.)
export const FEATURE_ID = "availability";

// Feature display name (used in UI)
export const FEATURE_NAME = "Availability";

// Base path for all routes in this feature
export const FEATURE_BASE_PATH = `${DOMAIN_CONFIG.basePath}/(features)/${FEATURE_ID}`;

// Feature description (used in UI)
export const FEATURE_DESCRIPTION = "Employee availability management";

// Feature configuration object
const FEATURE_CONFIG = {
  id: FEATURE_ID,
  name: FEATURE_NAME,
  basePath: FEATURE_BASE_PATH,
  description: FEATURE_DESCRIPTION,
  domain: DOMAIN_CONFIG,
};

export default FEATURE_CONFIG;
