import { Database } from "@/lib/types/database.types";

/**
 * Employee Availability interface
 * Represents a single availability slot for an employee
 */
export type EmployeeAvailability = Database["public"]["Tables"]["employee_availability"]["Row"];

/**
 * Employee Availability Insert interface
 * Used when creating a new availability slot
 */
export type EmployeeAvailabilityInsert =
  Database["public"]["Tables"]["employee_availability"]["Insert"];

/**
 * Employee Availability Update interface
 * Used when updating an existing availability slot
 */
export type EmployeeAvailabilityUpdate =
  Database["public"]["Tables"]["employee_availability"]["Update"];

/**
 * Employee Time Off interface
 * Represents a time-off request for an employee
 */
export type EmployeeTimeOff = Database["public"]["Tables"]["employee_time_off"]["Row"];

/**
 * Employee Time Off Insert interface
 * Used when creating a new time-off request
 */
export type EmployeeTimeOffInsert = Database["public"]["Tables"]["employee_time_off"]["Insert"];

/**
 * Employee Time Off Update interface
 * Used when updating an existing time-off request
 */
export type EmployeeTimeOffUpdate = Database["public"]["Tables"]["employee_time_off"]["Update"];

/**
 * Employee Availability Exception interface
 * Represents a one-time exception to an employee's regular availability
 */
export type EmployeeAvailabilityException =
  Database["public"]["Tables"]["employee_availability_exceptions"]["Row"];

/**
 * Employee Availability Exception Insert interface
 * Used when creating a new availability exception
 */
export type EmployeeAvailabilityExceptionInsert =
  Database["public"]["Tables"]["employee_availability_exceptions"]["Insert"];

/**
 * Employee Availability Exception Update interface
 * Used when updating an existing availability exception
 */
export type EmployeeAvailabilityExceptionUpdate =
  Database["public"]["Tables"]["employee_availability_exceptions"]["Update"];

/**
 * Time Slot interface for the AvailabilityGrid component
 */
export interface TimeSlot {
  day: number;
  hour: number;
  available: boolean;
}
