import DOM<PERSON>IN_CONFIG from "../../../../lib/config/domain";
import { FEATURE_ID } from "../config/feature";

// Permission constants for the Availability feature
export const AVAILABILITY_PERMISSIONS = {
  VIEW: `${DOMAIN_CONFIG.id}:${FEATURE_ID}:view`,
  CREATE: `${DOMAIN_CONFIG.id}:${FEATURE_ID}:create`,
  EDIT: `${DOMAIN_CONFIG.id}:${FEATURE_ID}:edit`,
  DELETE: `${DOMAIN_CONFIG.id}:${FEATURE_ID}:delete`,
  MANAGE_OWN: `${DOMAIN_CONFIG.id}:${FEATURE_ID}:manage-own`,
  APPROVE: `${DOMAIN_CONFIG.id}:${FEATURE_ID}:approve`,
};

// Route permissions mapping
export const ROUTE_PERMISSIONS = {
  // List page
  "/list": [AVAILABILITY_PERMISSIONS.VIEW],

  // View page
  "/[id]/view": [AVAILABILITY_PERMISSIONS.VIEW],

  // Edit page
  "/[id]/edit": [AVAILABILITY_PERMISSIONS.EDIT],

  // Create page
  "/create": [AVAILABILITY_PERMISSIONS.CREATE],
};

export default {
  permissions: AVAILABILITY_PERMISSIONS,
  routePermissions: ROUTE_PERMISSIONS,
};
