"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import { createTimeOff, updateTimeOff } from "../actions/time-off";
import { EmployeeTimeOff } from "../lib/types";

interface TimeOffFormProps {
  employeeId: string;
  timeOffId?: string;
  initialData?: EmployeeTimeOff;
  lang: string;
  dictionary: any;
}

export function TimeOffForm({
  employeeId,
  timeOffId,
  initialData,
  lang,
  dictionary,
}: TimeOffFormProps) {
  const router = useRouter();
  const featureDictionary = dictionary.employee.availability;
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Form state
  const [startDate, setStartDate] = useState(
    initialData?.start_date ? new Date(initialData.start_date).toISOString().split("T")[0] : ""
  );
  const [endDate, setEndDate] = useState(
    initialData?.end_date ? new Date(initialData.end_date).toISOString().split("T")[0] : ""
  );
  const [startTime, setStartTime] = useState(initialData?.start_time || "");
  const [endTime, setEndTime] = useState(initialData?.end_time || "");
  const [type, setType] = useState<"vacation" | "sick_leave" | "personal_leave" | "other">(
    (initialData?.type as "vacation" | "sick_leave" | "personal_leave" | "other") || "vacation"
  );
  const [description, setDescription] = useState(initialData?.description || "");

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!startDate || !endDate) {
      toast.error(featureDictionary.requiredFieldsError);
      return;
    }

    setIsSubmitting(true);

    try {
      const formData = new FormData();
      formData.append("employee_id", employeeId);
      formData.append("start_date", startDate);
      formData.append("end_date", endDate);

      if (startTime) formData.append("start_time", startTime);
      if (endTime) formData.append("end_time", endTime);

      formData.append("type", type);
      if (description) formData.append("description", description);

      if (timeOffId) {
        // Update existing time-off request
        await updateTimeOff(timeOffId, formData);
        toast.success(featureDictionary.timeOff.updateSuccess);
      } else {
        // Create new time-off request
        await createTimeOff(formData);
        toast.success(featureDictionary.timeOff.createSuccess);
      }

      // Redirect to view page
      router.push(`/${lang}/protected/employee/availability/${employeeId}/view?tab=timeOff`);
      router.refresh();
    } catch (error) {
      console.error("Error submitting time-off form:", error);
      toast.error(featureDictionary.errorSubmitting);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <Card>
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="start_date">{featureDictionary.timeOff.startDate} *</Label>
              <Input
                id="start_date"
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="end_date">{featureDictionary.timeOff.endDate} *</Label>
              <Input
                id="end_date"
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="start_time">{featureDictionary.timeOff.startTime}</Label>
              <Input
                id="start_time"
                type="time"
                value={startTime}
                onChange={(e) => setStartTime(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="end_time">{featureDictionary.timeOff.endTime}</Label>
              <Input
                id="end_time"
                type="time"
                value={endTime}
                onChange={(e) => setEndTime(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="type">{featureDictionary.timeOff.type} *</Label>
              <Select value={type} onValueChange={(value) => setType(value as any)}>
                <SelectTrigger id="type">
                  <SelectValue placeholder={featureDictionary.timeOff.selectType} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="vacation">
                    {featureDictionary.timeOff.types.vacation}
                  </SelectItem>
                  <SelectItem value="sick_leave">
                    {featureDictionary.timeOff.types.sickLeave}
                  </SelectItem>
                  <SelectItem value="personal_leave">
                    {featureDictionary.timeOff.types.personalLeave}
                  </SelectItem>
                  <SelectItem value="other">{featureDictionary.timeOff.types.other}</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2 md:col-span-2">
              <Label htmlFor="description">{featureDictionary.timeOff.description}</Label>
              <Textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                rows={3}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="flex justify-end gap-2">
        <Button
          type="button"
          variant="outline"
          onClick={() =>
            router.push(`/${lang}/protected/employee/availability/${employeeId}/view?tab=timeOff`)
          }
          disabled={isSubmitting}
        >
          {featureDictionary.cancelButton}
        </Button>
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? featureDictionary.submitting : featureDictionary.saveButton}
        </Button>
      </div>
    </form>
  );
}
