"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  CalendarIcon,
  ClockIcon,
  PlusIcon,
  Pencil1Icon,
  TrashIcon,
  CheckIcon,
  Cross2Icon,
} from "@radix-ui/react-icons";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { toast } from "sonner";
import { EmployeeTimeOff } from "../lib/types";
import { approveTimeOff, cancelTimeOff, deleteTimeOff, rejectTimeOff } from "../actions/time-off";

interface TimeOffListProps {
  employeeId: string;
  timeOffList: EmployeeTimeOff[];
  lang: string;
  dictionary: any;
  isManager?: boolean;
}

export function TimeOffList({
  employeeId,
  timeOffList,
  lang,
  dictionary,
  isManager = false,
}: TimeOffListProps) {
  const router = useRouter();
  const featureDictionary = dictionary.employee.availability;
  const [isDeleting, setIsDeleting] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat(lang === "fr" ? "fr-CA" : "en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    }).format(date);
  };

  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case "approved":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100";
      case "rejected":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100";
      case "cancelled":
        return "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-100";
      default:
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100";
    }
  };

  // Get type label
  const getTypeLabel = (type: string) => {
    switch (type) {
      case "vacation":
        return featureDictionary.timeOff.types.vacation;
      case "sick_leave":
        return featureDictionary.timeOff.types.sickLeave;
      case "personal_leave":
        return featureDictionary.timeOff.types.personalLeave;
      case "other":
        return featureDictionary.timeOff.types.other;
      default:
        return type;
    }
  };

  // Handle delete time-off request
  const handleDelete = async (id: string) => {
    setIsDeleting(true);
    try {
      await deleteTimeOff(id);
      toast.success(featureDictionary.timeOff.deleteSuccess);
      router.refresh();
    } catch (error) {
      console.error("Error deleting time-off request:", error);
      toast.error(featureDictionary.errorDeleting);
    } finally {
      setIsDeleting(false);
    }
  };

  // Handle approve time-off request
  const handleApprove = async (id: string) => {
    setIsProcessing(true);
    try {
      await approveTimeOff(id);
      toast.success(featureDictionary.timeOff.approveSuccess);
      router.refresh();
    } catch (error) {
      console.error("Error approving time-off request:", error);
      toast.error(featureDictionary.errorProcessing);
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle reject time-off request
  const handleReject = async (id: string) => {
    setIsProcessing(true);
    try {
      await rejectTimeOff(id);
      toast.success(featureDictionary.timeOff.rejectSuccess);
      router.refresh();
    } catch (error) {
      console.error("Error rejecting time-off request:", error);
      toast.error(featureDictionary.errorProcessing);
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle cancel time-off request
  const handleCancel = async (id: string) => {
    setIsProcessing(true);
    try {
      await cancelTimeOff(id);
      toast.success(featureDictionary.timeOff.cancelSuccess);
      router.refresh();
    } catch (error) {
      console.error("Error cancelling time-off request:", error);
      toast.error(featureDictionary.errorProcessing);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">{featureDictionary.timeOff.listTitle}</h3>
        <Button
          size="sm"
          onClick={() =>
            router.push(`/${lang}/protected/employee/availability/${employeeId}/edit?tab=timeOff`)
          }
        >
          <PlusIcon className="mr-2 h-4 w-4" />
          {featureDictionary.timeOff.addButton}
        </Button>
      </div>

      {timeOffList.length === 0 ? (
        <p className="text-center py-8 text-muted-foreground">
          {featureDictionary.timeOff.noRecords}
        </p>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {timeOffList.map((timeOff) => (
            <Card key={timeOff.id} className="overflow-hidden">
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <CardTitle className="text-base font-medium">
                    {getTypeLabel(timeOff.type)}
                  </CardTitle>
                  <Badge className={getStatusColor(timeOff.status || "pending")}>
                    {featureDictionary.timeOff.status[timeOff.status || "pending"]}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="pb-2">
                <div className="space-y-2">
                  <div className="flex items-center text-sm">
                    <CalendarIcon className="mr-2 h-4 w-4 text-muted-foreground" />
                    <span>
                      {formatDate(timeOff.start_date)} - {formatDate(timeOff.end_date)}
                    </span>
                  </div>

                  {(timeOff.start_time || timeOff.end_time) && (
                    <div className="flex items-center text-sm">
                      <ClockIcon className="mr-2 h-4 w-4 text-muted-foreground" />
                      <span>
                        {timeOff.start_time || "00:00"} - {timeOff.end_time || "23:59"}
                      </span>
                    </div>
                  )}

                  {timeOff.description && <p className="text-sm mt-2">{timeOff.description}</p>}
                </div>
              </CardContent>
              <CardFooter className="pt-2 flex justify-end gap-2">
                {timeOff.status === "pending" && (
                  <>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        router.push(
                          `/${lang}/protected/employee/availability/${employeeId}/edit?timeOffId=${timeOff.id}&tab=timeOff`
                        )
                      }
                    >
                      <Pencil1Icon className="h-4 w-4" />
                      <span className="sr-only">{featureDictionary.editButton}</span>
                    </Button>

                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button variant="outline" size="sm" className="text-red-500">
                          <TrashIcon className="h-4 w-4" />
                          <span className="sr-only">{featureDictionary.deleteButton}</span>
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>
                            {featureDictionary.timeOff.deleteConfirmTitle}
                          </AlertDialogTitle>
                          <AlertDialogDescription>
                            {featureDictionary.timeOff.deleteConfirmMessage}
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>{featureDictionary.cancelButton}</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => handleDelete(timeOff.id)}
                            className="bg-red-500 hover:bg-red-600"
                          >
                            {featureDictionary.deleteButton}
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>

                    {isManager && (
                      <>
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-green-500"
                          onClick={() => handleApprove(timeOff.id)}
                          disabled={isProcessing}
                        >
                          <CheckIcon className="h-4 w-4" />
                          <span className="sr-only">{featureDictionary.timeOff.approveButton}</span>
                        </Button>

                        <Button
                          variant="outline"
                          size="sm"
                          className="text-red-500"
                          onClick={() => handleReject(timeOff.id)}
                          disabled={isProcessing}
                        >
                          <Cross2Icon className="h-4 w-4" />
                          <span className="sr-only">{featureDictionary.timeOff.rejectButton}</span>
                        </Button>
                      </>
                    )}
                  </>
                )}

                {(timeOff.status === "approved" || timeOff.status === "rejected") && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleCancel(timeOff.id)}
                    disabled={isProcessing}
                  >
                    {featureDictionary.timeOff.cancelButton}
                  </Button>
                )}
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
