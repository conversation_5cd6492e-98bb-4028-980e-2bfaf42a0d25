"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, Card<PERSON>ontent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { CalendarIcon, ClockIcon, PlusIcon, Pencil1Icon, TrashIcon } from "@radix-ui/react-icons";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { toast } from "sonner";
import { EmployeeAvailabilityException } from "../lib/types";
import { deleteAvailabilityException } from "../actions/exceptions";

interface ExceptionsListProps {
  employeeId: string;
  exceptionsList: EmployeeAvailabilityException[];
  lang: string;
  dictionary: any;
}

export function ExceptionsList({
  employeeId,
  exceptionsList,
  lang,
  dictionary,
}: ExceptionsListProps) {
  const router = useRouter();
  const featureDictionary = dictionary.employee.availability;
  const [isDeleting, setIsDeleting] = useState(false);

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat(lang === "fr" ? "fr-CA" : "en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    }).format(date);
  };

  // Handle delete exception
  const handleDelete = async (id: string) => {
    setIsDeleting(true);
    try {
      await deleteAvailabilityException(id);
      toast.success(featureDictionary.exceptions.deleteSuccess);
      router.refresh();
    } catch (error) {
      console.error("Error deleting exception:", error);
      toast.error(featureDictionary.errorDeleting);
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">{featureDictionary.exceptions.listTitle}</h3>
        <Button
          size="sm"
          onClick={() =>
            router.push(
              `/${lang}/protected/employee/availability/${employeeId}/edit?tab=exceptions`
            )
          }
        >
          <PlusIcon className="mr-2 h-4 w-4" />
          {featureDictionary.exceptions.addButton}
        </Button>
      </div>

      {exceptionsList.length === 0 ? (
        <p className="text-center py-8 text-muted-foreground">
          {featureDictionary.exceptions.noRecords}
        </p>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {exceptionsList.map((exception) => (
            <Card key={exception.id} className="overflow-hidden">
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <CardTitle className="text-base font-medium">
                    {formatDate(exception.exception_date)}
                  </CardTitle>
                  <Badge
                    className={
                      exception.is_available
                        ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100"
                        : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100"
                    }
                  >
                    {exception.is_available
                      ? featureDictionary.exceptions.availableLabel
                      : featureDictionary.exceptions.unavailableLabel}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="pb-2">
                <div className="space-y-2">
                  <div className="flex items-center text-sm">
                    <ClockIcon className="mr-2 h-4 w-4 text-muted-foreground" />
                    <span>
                      {exception.start_time} - {exception.end_time}
                    </span>
                  </div>

                  {exception.reason && <p className="text-sm mt-2">{exception.reason}</p>}
                </div>
              </CardContent>
              <CardFooter className="pt-2 flex justify-end gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    router.push(
                      `/${lang}/protected/employee/availability/${employeeId}/edit?exceptionId=${exception.id}&tab=exceptions`
                    )
                  }
                >
                  <Pencil1Icon className="h-4 w-4" />
                  <span className="sr-only">{featureDictionary.editButton}</span>
                </Button>

                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button variant="outline" size="sm" className="text-red-500">
                      <TrashIcon className="h-4 w-4" />
                      <span className="sr-only">{featureDictionary.deleteButton}</span>
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>
                        {featureDictionary.exceptions.deleteConfirmTitle}
                      </AlertDialogTitle>
                      <AlertDialogDescription>
                        {featureDictionary.exceptions.deleteConfirmMessage}
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>{featureDictionary.cancelButton}</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={() => handleDelete(exception.id)}
                        className="bg-red-500 hover:bg-red-600"
                      >
                        {featureDictionary.deleteButton}
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
