"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { toast } from "sonner";
import { createAvailabilityException, updateAvailabilityException } from "../actions/exceptions";
import { EmployeeAvailabilityException } from "../lib/types";

interface ExceptionFormProps {
  employeeId: string;
  exceptionId?: string;
  initialData?: EmployeeAvailabilityException;
  lang: string;
  dictionary: any;
}

export function ExceptionForm({
  employeeId,
  exceptionId,
  initialData,
  lang,
  dictionary,
}: ExceptionFormProps) {
  const router = useRouter();
  const featureDictionary = dictionary.employee.availability;
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Form state
  const [exceptionDate, setExceptionDate] = useState(
    initialData?.exception_date
      ? new Date(initialData.exception_date).toISOString().split("T")[0]
      : ""
  );
  const [startTime, setStartTime] = useState(initialData?.start_time || "09:00");
  const [endTime, setEndTime] = useState(initialData?.end_time || "17:00");
  const [isAvailable, setIsAvailable] = useState(initialData?.is_available ?? false);
  const [reason, setReason] = useState(initialData?.reason || "");

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!exceptionDate || !startTime || !endTime) {
      toast.error(featureDictionary.requiredFieldsError);
      return;
    }

    setIsSubmitting(true);

    try {
      const formData = new FormData();
      formData.append("employee_id", employeeId);
      formData.append("exception_date", exceptionDate);
      formData.append("start_time", startTime);
      formData.append("end_time", endTime);
      formData.append("is_available", isAvailable.toString());
      if (reason) formData.append("reason", reason);

      if (exceptionId) {
        // Update existing exception
        await updateAvailabilityException(exceptionId, formData);
        toast.success(featureDictionary.exceptions.updateSuccess);
      } else {
        // Create new exception
        await createAvailabilityException(formData);
        toast.success(featureDictionary.exceptions.createSuccess);
      }

      // Redirect to view page
      router.push(`/${lang}/protected/employee/availability/${employeeId}/view?tab=exceptions`);
      router.refresh();
    } catch (error) {
      console.error("Error submitting exception form:", error);
      toast.error(featureDictionary.errorSubmitting);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <Card>
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="exception_date">{featureDictionary.exceptions.date} *</Label>
              <Input
                id="exception_date"
                type="date"
                value={exceptionDate}
                onChange={(e) => setExceptionDate(e.target.value)}
                required
              />
            </div>

            <div className="flex items-center space-x-2 h-full pt-8">
              <Switch id="is_available" checked={isAvailable} onCheckedChange={setIsAvailable} />
              <Label htmlFor="is_available">
                {isAvailable
                  ? featureDictionary.exceptions.availableLabel
                  : featureDictionary.exceptions.unavailableLabel}
              </Label>
            </div>

            <div className="space-y-2">
              <Label htmlFor="start_time">{featureDictionary.startTime} *</Label>
              <Input
                id="start_time"
                type="time"
                value={startTime}
                onChange={(e) => setStartTime(e.target.value)}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="end_time">{featureDictionary.endTime} *</Label>
              <Input
                id="end_time"
                type="time"
                value={endTime}
                onChange={(e) => setEndTime(e.target.value)}
                required
              />
            </div>

            <div className="space-y-2 md:col-span-2">
              <Label htmlFor="reason">{featureDictionary.exceptions.reason}</Label>
              <Textarea
                id="reason"
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                rows={3}
                placeholder={featureDictionary.exceptions.reasonPlaceholder}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="flex justify-end gap-2">
        <Button
          type="button"
          variant="outline"
          onClick={() =>
            router.push(
              `/${lang}/protected/employee/availability/${employeeId}/view?tab=exceptions`
            )
          }
          disabled={isSubmitting}
        >
          {featureDictionary.cancelButton}
        </Button>
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? featureDictionary.submitting : featureDictionary.saveButton}
        </Button>
      </div>
    </form>
  );
}
