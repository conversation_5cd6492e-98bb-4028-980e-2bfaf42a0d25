import { ReactNode } from "react";
import { i18n } from "@/lib/i18n/services/I18nService";

interface TemplateLayoutProps {
  children: ReactNode;
  params: Promise<{
    lang: string;
  }>;
}

/**
 * Layout for the Template Single CRUD feature
 * This is the main layout for the feature and wraps all pages
 */
export default async function TemplateLayout({ children, params }: TemplateLayoutProps) {
  // Get the language from params
  const { lang } = await params;

  // Get the dictionary for translations
  const dictionary = i18n.getDictionary(lang);

  return (
    <div className="container mx-auto py-6">
      <h1 className="text-3xl font-bold mb-6">
        {dictionary.employee?.management?.title || "Employee Management"}
      </h1>
      {children}
    </div>
  );
}
