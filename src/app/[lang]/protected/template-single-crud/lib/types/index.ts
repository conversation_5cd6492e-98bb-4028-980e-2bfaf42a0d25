/**
 * Template Item interface
 * Represents a single item in the template CRUD feature
 */
export interface TemplateItem {
  id: string;
  name: string;
  description: string | null;
  status: "active" | "inactive" | "draft";
  created_at: string;
  updated_at: string;
  organization_id: string;
}

/**
 * Template Item Insert interface
 * Used when creating a new template item
 */
export interface TemplateItemInsert {
  name: string;
  description: string | null;
  status: "active" | "inactive" | "draft";
  organization_id: string;
}

/**
 * Template Item Update interface
 * Used when updating an existing template item
 */
export interface TemplateItemUpdate {
  name?: string;
  description?: string | null;
  status?: "active" | "inactive" | "draft";
  updated_at: string;
}
