/**
 * Security exports for Template Single CRUD
 *
 * This file exports all security-related configurations from all features.
 */

// Import permissions from features
import feature1Security from "../../(features)/feature-1/lib/security";

// Export all permissions
const security = {
  // Feature 1 permissions
  feature1: feature1Security,

  // Combine all route permissions
  routePermissions: {
    ...feature1Security.routePermissions,
    // Add more feature route permissions here
  },
};

export default security;
