import { ReactNode } from "react";
import { PageLayout } from "@/components/layouts/PageLayout";

interface TemplateLayoutProps {
  children: ReactNode;
}

/**
 * Layout for the Template Single CRUD feature
 * This is the main layout for the feature and wraps all pages
 * Uses the PageLayout component from the design system
 */
export default function TemplateLayout({ children }: TemplateLayoutProps) {
  return (
    <PageLayout
      title="Template Single CRUD"
      description="Example of a single CRUD feature using the design system"
    >
      {children}
    </PageLayout>
  );
}
