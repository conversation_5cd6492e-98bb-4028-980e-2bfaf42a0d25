import { ReactNode } from "react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { i18n } from "@/lib/i18n/services/I18nService";
import FEATURE_CONFIG from "../lib/config";
import { ContentLayout } from "@/components/layouts";
import { H3 } from "@/components/typography";

interface Feature1PagesLayoutProps {
  children: ReactNode;
  params: Promise<{
    lang: string;
  }>;
}

/**
 * Layout for the Feature 1 pages in Template Single CRUD
 * This layout includes navigation for the CRUD operations
 * Uses the ContentLayout component from the design system
 */
export default async function Feature1PagesLayout({ children, params }: Feature1PagesLayoutProps) {
  // Await the params
  const resolvedParams = await params;

  // Get translations
  const dictionary = i18n.getDictionary(resolvedParams.lang);

  // Create the actions for the header
  const actions = (
    <div className="flex gap-2">
      <Link href={`/${resolvedParams.lang}${FEATURE_CONFIG.routes.LIST}`}>
        <Button variant="outline">List</Button>
      </Link>
      <Link href={`/${resolvedParams.lang}${FEATURE_CONFIG.routes.CREATE}`}>
        <Button>
          {dictionary["template-single-crud"]?.["feature-1"]?.actions?.create || "Create Item"}
        </Button>
      </Link>
    </div>
  );

  return (
    <ContentLayout
      title={<H3>Feature 1 Items</H3>}
      description="Manage your feature items"
      actions={actions}
    >
      {children}
    </ContentLayout>
  );
}
