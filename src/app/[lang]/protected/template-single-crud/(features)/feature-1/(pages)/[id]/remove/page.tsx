import { RemoveForm } from "../../../components/RemoveForm";
import { view } from "../../../actions/view";
import { notFound } from "next/navigation";
import { ActionState } from "@/lib/types/responses";
import { Feature1Item } from "../../../lib/types";
import { i18n } from "@/lib/i18n/services/I18nService";
import { DOMAIN_ID } from "@/app/[lang]/protected/template-single-crud/lib/config/domain";
import { FEATURE_ID } from "../../../lib/config/feature";

interface RemovePageProps {
  params: Promise<{
    lang: string;
    id: string;
  }>;
}

/**
 * Remove page for a specific Feature 1 item
 * Uses the view server action to fetch the item and the RemoveForm component to confirm deletion
 */
export default async function RemovePage({ params }: RemovePageProps) {
  const { lang, id } = await params;

  // Create initial state
  const initialState: ActionState<Feature1Item> = {
    success: true,
    error: "",
    data: null,
  };

  // Fetch the item using the server action
  const state = await view(initialState, { id });

  // If the item is not found, show the not-found page
  if (!state.success || !state.data) {
    notFound();
  }

  // Get the dictionary
  const dictionary = i18n.getDomainFeatureDictionary(lang, DOMAIN_ID, FEATURE_ID);

  // Render the RemoveForm component with the item
  return <RemoveForm lang={lang} item={state.data} dictionary={dictionary} />;
}
