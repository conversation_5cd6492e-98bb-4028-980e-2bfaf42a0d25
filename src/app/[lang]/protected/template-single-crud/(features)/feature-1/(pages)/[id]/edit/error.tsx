"use client";

import { ErrorDisplay } from "@/components/ui/error-display";

/**
 * Error component for the Edit page
 */
export default function EditError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  return (
    <ErrorDisplay
      title="Error Editing Item"
      message="There was a problem loading the edit form."
      error={error}
      reset={reset}
    />
  );
}
