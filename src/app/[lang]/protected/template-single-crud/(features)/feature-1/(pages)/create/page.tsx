import { DOMAIN_ID } from "../../../../lib/config/domain";
import { CreateForm } from "../../components/CreateForm";
import { i18n } from "@/lib/i18n/services/I18nService";
import { FEATURE_ID } from "../../lib/config/feature";

interface CreatePageProps {
  params: Promise<{
    lang: string;
  }>;
}

/**
 * Create page for Feature 1 items
 * Uses the CreateForm component to create a new Feature 1 item
 */
export default async function CreatePage({ params }: CreatePageProps) {
  // Await the params
  const { lang } = await params;

  // Get the dictionary
  const dictionary = i18n.getDomainFeatureDictionary(lang, DOMAIN_ID, FEATURE_ID);

  return <CreateForm dictionary={dictionary} />;
}
