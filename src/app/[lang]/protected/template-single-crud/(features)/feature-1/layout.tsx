import { ReactNode } from "react";

interface Feature1LayoutProps {
  children: ReactNode;
}

/**
 * Layout for the Feature 1 in Template Single CRUD
 * This is the main layout for the feature and wraps all pages
 */
export default function Feature1Layout({ children }: Feature1LayoutProps) {
  return (
    <div className="container mx-auto py-6">
      <h1 className="text-2xl font-bold mb-6">Feature 1</h1>
      {children}
    </div>
  );
}
