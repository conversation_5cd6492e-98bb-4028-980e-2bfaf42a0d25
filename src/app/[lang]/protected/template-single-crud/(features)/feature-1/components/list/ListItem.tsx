"use client";

import { Button } from "@/components/ui/button";
import { TableCell, TableRow } from "@/components/ui/table";
import { Feature1Item, FeatureDictionary } from "../../lib/types";
import Link from "next/link";
import FEATURE_CONFIG from "../../lib/config";

interface ListItemProps {
  item: Feature1Item;
  lang: string;
  dictionary: FeatureDictionary;
}

/**
 * Component for rendering a single Feature 1 item in the list table
 */
export function ListItem({ item, lang, dictionary }: ListItemProps) {
  return (
    <TableRow key={item.id}>
      <TableCell className="font-medium">{item.name}</TableCell>
      <TableCell>
        <span
          className={`px-2 py-1 rounded-full text-xs ${
            item.status === "active"
              ? "bg-green-100 text-green-800"
              : item.status === "inactive"
                ? "bg-gray-100 text-gray-800"
                : "bg-yellow-100 text-yellow-800"
          }`}
        >
          {item.status === "active"
            ? dictionary.status.active
            : item.status === "inactive"
              ? dictionary.status.inactive
              : dictionary.status.draft}
        </span>
      </TableCell>
      <TableCell className="max-w-xs truncate">{item.description || "-"}</TableCell>
      <TableCell className="text-right">
        <div className="flex justify-end gap-2">
          <Button variant="outline" size="sm" asChild>
            <Link href={`/${lang}${FEATURE_CONFIG.routes.VIEW(item.id)}`}>
              {dictionary.actions.view}
            </Link>
          </Button>
          <Button variant="outline" size="sm" asChild>
            <Link href={`/${lang}${FEATURE_CONFIG.routes.EDIT(item.id)}`}>
              {dictionary.actions.edit}
            </Link>
          </Button>
        </div>
      </TableCell>
    </TableRow>
  );
}
