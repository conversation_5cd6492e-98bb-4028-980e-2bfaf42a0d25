"use client";

import { Table, TableBody, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Feature1Item, FeatureDictionary } from "../../lib/types";
import { ListItem } from "./ListItem";

interface ListTableProps {
  items: Feature1Item[];
  lang: string;
  dictionary: FeatureDictionary;
}

/**
 * Table component for displaying Feature 1 items
 */
export function ListTable({ items, lang, dictionary }: ListTableProps) {
  if (items.length === 0) {
    return (
      <div className="text-center py-10">
        <p className="text-muted-foreground">{dictionary.noItems}</p>
      </div>
    );
  }

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>{dictionary.fields.name}</TableHead>
          <TableHead>{dictionary.fields.status}</TableHead>
          <TableHead>{dictionary.fields.description}</TableHead>
          <TableHead className="text-right">Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {items.map((item) => (
          <ListItem key={item.id} item={item} lang={lang} dictionary={dictionary} />
        ))}
      </TableBody>
    </Table>
  );
}
