"use client";

import { useActionState } from "react";
import { edit } from "../actions/edit";
import { ActionState } from "@/lib/types/responses";
import { Feature1Item, FeatureDictionary } from "../lib/types";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import Link from "next/link";
import { FormLayout } from "@/components/layouts";
import FEATURE_CONFIG from "../lib/config";

interface EditFormProps {
  item: Feature1Item;
  dictionary: FeatureDictionary;
  lang?: string;
}

/**
 * Form for editing an existing Feature 1 item
 * Uses the FormLayout component from the design system
 */
export function EditForm({ item, dictionary, lang }: EditFormProps) {
  // Use React's useActionState to manage the form state with the server action
  const initialState: ActionState<Feature1Item> = {
    success: true,
    error: "",
    data: item,
  };

  const [state, formAction, pending] = useActionState(edit, initialState);

  // Create form actions
  const actions = (
    <>
      <Link
        href={lang ? `/${lang}${FEATURE_CONFIG.routes.LIST}` : ".."}
        className="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-input hover:bg-accent hover:text-accent-foreground h-10 py-2 px-4"
      >
        Cancel
      </Link>
      <Button type="submit" disabled={pending}>
        {pending ? "Saving..." : "Save"}
      </Button>
    </>
  );

  return (
    <FormLayout
      title={dictionary.editTitle}
      description={dictionary.editDescription}
      error={!state.success && state.error ? state.error : undefined}
      formAction={formAction}
      actions={actions}
      className="max-w-2xl mx-auto"
    >
      <div className="space-y-4">
        {/* Display success message if there is one */}
        {state.success && state.data && state.data.id === item.id && (
          <Alert className="bg-green-50 border-green-200">
            <AlertDescription className="text-green-800">{dictionary.itemUpdated}</AlertDescription>
          </Alert>
        )}

        {/* Name field */}
        <div className="space-y-2">
          <Label htmlFor="name">{dictionary.fields.name}</Label>
          <Input
            id="name"
            name="name"
            defaultValue={item.name}
            placeholder="Enter name"
            required
            aria-invalid={!state.success ? "true" : "false"}
          />
        </div>

        {/* Description field */}
        <div className="space-y-2">
          <Label htmlFor="description">{dictionary.fields.description}</Label>
          <Textarea
            id="description"
            name="description"
            defaultValue={item.description || ""}
            placeholder="Enter description"
            rows={4}
          />
        </div>

        {/* Status field */}
        <div className="space-y-2">
          <Label htmlFor="status">{dictionary.fields.status}</Label>
          <Select name="status" defaultValue={item.status}>
            <SelectTrigger id="status">
              <SelectValue placeholder="Select status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="active">{dictionary.status.active}</SelectItem>
              <SelectItem value="inactive">{dictionary.status.inactive}</SelectItem>
              <SelectItem value="draft">{dictionary.status.draft}</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </FormLayout>
  );
}
