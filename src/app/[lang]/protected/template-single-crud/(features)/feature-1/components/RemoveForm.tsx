"use client";

import { useActionState } from "react";
import { remove } from "../actions";
import { ActionState } from "@/lib/types/responses";
import { Feature1Item, FeatureDictionary } from "../lib/types";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import Link from "next/link";
import { FormLayout } from "@/components/layouts";
import { Badge } from "@/components/ui/badge";
import { H4, Muted } from "@/components/typography";

import FEATURE_CONFIG from "../lib/config";

interface RemoveFormProps {
  lang: string;
  item: Feature1Item;
  dictionary: FeatureDictionary;
}

/**
 * Form for confirming deletion of a Feature 1 item
 * Uses the FormLayout component from the design system
 */
export function RemoveForm({ lang, item, dictionary }: RemoveFormProps) {
  // Use React's useFormState to manage the form state with the server action
  const initialState: ActionState<Feature1Item> = {
    success: true,
    error: "",
    data: item,
  };

  const [state, formAction, pending] = useActionState(remove, initialState);

  // Create form actions
  const actions = (
    <>
      <Button variant="outline" asChild>
        <Link href={`/${lang}${FEATURE_CONFIG.routes.LIST}`}>{dictionary.backToList}</Link>
      </Button>
      <Button type="submit" variant="destructive" disabled={pending}>
        {pending ? "Deleting..." : dictionary.removeConfirm}
      </Button>
    </>
  );

  return (
    <FormLayout
      title={dictionary.removeTitle}
      description={dictionary.removeDescription}
      error={!state.success && state.error ? state.error : undefined}
      formAction={formAction}
      actions={actions}
      className="max-w-md mx-auto text-center"
    >
      <div className="space-y-4">
        {/* Hidden fields for ID and language */}
        <input type="hidden" name="id" value={item.id} />
        <input type="hidden" name="lang" value={lang} />

        {/* Item details */}
        <div className="border rounded-md p-4 text-left">
          <H4>{item.name}</H4>
          <Muted className="mt-1">{item.description || "No description provided"}</Muted>
          <div className="mt-2">
            <Badge
              variant={
                item.status === "active"
                  ? "default"
                  : item.status === "inactive"
                    ? "secondary"
                    : "outline"
              }
            >
              {item.status === "active"
                ? dictionary.status.active
                : item.status === "inactive"
                  ? dictionary.status.inactive
                  : dictionary.status.draft}
            </Badge>
          </div>
        </div>

        <Alert variant="destructive" className="bg-red-50 border-red-200">
          <AlertDescription className="text-red-800">
            This action cannot be undone. The item will be permanently deleted.
          </AlertDescription>
        </Alert>
      </div>
    </FormLayout>
  );
}
