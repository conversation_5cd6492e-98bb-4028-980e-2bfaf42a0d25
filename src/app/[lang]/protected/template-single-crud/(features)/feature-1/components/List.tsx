"use client";

import { Feature1Item, FeatureDictionary } from "../lib/types";
import { ListLayout } from "@/components/layouts";
import { CreateButton, SearchBar, ListTable, Pagination } from "./list";
import { SectionTitle } from "@/components/typography";

interface ListProps {
  lang: string;
  initialItems: Feature1Item[];
  totalItems: number;
  currentPage: number;
  search?: string;
  dictionary: FeatureDictionary;
}

/**
 * Component for displaying a list of Feature 1 items
 * Composed of smaller, focused components for better separation of concerns
 * Uses the ListLayout component from the design system
 */
export function List({
  lang,
  initialItems,
  totalItems,
  currentPage,
  search,
  dictionary,
}: ListProps) {
  // Create the header with title and create button
  const header = (
    <div className="flex justify-between items-center">
      <SectionTitle description={dictionary.description}>{dictionary.title}</SectionTitle>
      <CreateButton lang={lang} dictionary={dictionary} />
    </div>
  );

  // Create the footer with pagination
  const footer = (
    <Pagination
      totalItems={totalItems}
      pageSize={10}
      currentPage={currentPage}
      lang={lang}
      search={search}
      dictionary={dictionary}
    />
  );

  return (
    <div className="space-y-4">
      <SearchBar lang={lang} dictionary={dictionary} />

      <ListLayout header={header} footer={footer}>
        <ListTable items={initialItems} lang={lang} dictionary={dictionary} />
      </ListLayout>
    </div>
  );
}
