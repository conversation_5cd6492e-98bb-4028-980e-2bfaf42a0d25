"use client";

import { useActionState } from "react";
import { create } from "../actions";
import { ActionState } from "@/lib/types/responses";
import { Feature1Item, FeatureDictionary } from "../lib/types";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import Link from "next/link";
import { FormLayout } from "@/components/layouts";

import FEATURE_CONFIG from "../lib/config";

interface CreateFormProps {
  dictionary: FeatureDictionary;
  lang?: string;
}

/**
 * Form for creating a new Feature 1 item
 * Uses the FormLayout component from the design system
 */
export function CreateForm({ dictionary, lang }: CreateFormProps) {
  // Use React's useFormState to manage the form state with the server action
  const initialState: ActionState<Feature1Item> = {
    success: true,
    error: "",
    data: null,
  };

  const [state, formAction, pending] = useActionState(create, initialState);

  // Create form actions
  const actions = (
    <>
      <Link
        href={lang ? `/${lang}${FEATURE_CONFIG.routes.LIST}` : ".."}
        className="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-input hover:bg-accent hover:text-accent-foreground h-10 py-2 px-4"
      >
        Cancel
      </Link>
      <Button type="submit" disabled={pending}>
        {pending ? "Creating..." : "Create"}
      </Button>
    </>
  );

  return (
    <FormLayout
      title={dictionary.createTitle}
      description={dictionary.createDescription}
      error={!state.success && state.error ? state.error : undefined}
      formAction={formAction}
      actions={actions}
      className="max-w-2xl mx-auto"
    >
      <div className="space-y-4">
        {/* Name field */}
        <div className="space-y-2">
          <Label htmlFor="name">Name</Label>
          <Input
            id="name"
            name="name"
            placeholder="Enter name"
            required
            aria-invalid={!state.success ? "true" : "false"}
          />
        </div>

        {/* Description field */}
        <div className="space-y-2">
          <Label htmlFor="description">{dictionary.description}</Label>
          <Textarea id="description" name="description" placeholder="Enter description" rows={4} />
        </div>

        {/* Status field */}
        <div className="space-y-2">
          <Label htmlFor="status">Status</Label>
          <Select name="status" defaultValue="active">
            <SelectTrigger id="status">
              <SelectValue placeholder="Select status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="inactive">Inactive</SelectItem>
              <SelectItem value="draft">Draft</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </FormLayout>
  );
}
