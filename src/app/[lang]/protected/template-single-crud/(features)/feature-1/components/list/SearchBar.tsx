"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import FEATURE_CONFIG from "../../lib/config";
import { FeatureDictionary } from "../../lib/types";

interface SearchBarProps {
  lang: string;
  dictionary: FeatureDictionary;
}

/**
 * Simple search bar component that creates a link with search params
 */
export function SearchBar({ lang, dictionary: _dictionary }: SearchBarProps) {
  const searchParams = useSearchParams();
  const [searchTerm, setSearchTerm] = useState(searchParams.get("search") || "");

  // Create URL for search
  const createSearchUrl = () => {
    const params = new URLSearchParams();
    if (searchTerm) params.set("search", searchTerm);
    params.set("page", "1"); // Reset to page 1 when searching
    return `/${lang}${FEATURE_CONFIG.routes.LIST}?${params.toString()}`;
  };

  return (
    <div className="flex gap-2 mb-6">
      <Input
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        placeholder="Search items..."
        className="max-w-sm"
      />
      <Button asChild>
        <Link href={createSearchUrl()}>Search</Link>
      </Button>
    </div>
  );
}
