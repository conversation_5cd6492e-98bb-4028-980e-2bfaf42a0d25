"use client";
import { Feature1Item, FeatureDictionary } from "../lib/types";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import FEATURE_CONFIG from "../lib/config";
import { DetailLayout } from "@/components/layouts";
import { Badge } from "@/components/ui/badge";
import { H4, Small } from "@/components/typography";

interface ViewProps {
  lang: string;
  item: Feature1Item;
  dictionary: FeatureDictionary;
}

/**
 * Component for viewing a Feature 1 item with delete functionality
 * Uses the DetailLayout component from the design system
 */
export function View({ lang, item, dictionary }: ViewProps) {
  // Create the status badge
  const statusBadge = (
    <Badge
      variant={
        item.status === "active" ? "default" : item.status === "inactive" ? "secondary" : "outline"
      }
    >
      {item.status === "active"
        ? dictionary.status.active
        : item.status === "inactive"
          ? dictionary.status.inactive
          : dictionary.status.draft}
    </Badge>
  );

  // Create the footer actions
  const footerActions = (
    <div className="flex justify-between w-full">
      <Link
        href={`/${lang}${FEATURE_CONFIG.routes.LIST}`}
        className="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-input hover:bg-accent hover:text-accent-foreground h-10 py-2 px-4"
      >
        {dictionary.backToList}
      </Link>

      <div className="flex gap-2">
        <Button variant="outline" asChild>
          <Link href={`/${lang}${FEATURE_CONFIG.routes.EDIT(item.id)}`}>
            {dictionary.actions.edit}
          </Link>
        </Button>

        <Button variant="destructive" asChild>
          <Link href={`/${lang}${FEATURE_CONFIG.routes.REMOVE(item.id)}`}>
            {dictionary.actions.remove}
          </Link>
        </Button>
      </div>
    </div>
  );

  // Create the tabs for the detail layout
  const detailTabs = [
    {
      value: "details",
      label: "Details",
      content: (
        <div className="space-y-6 py-4">
          {/* Item details */}
          <div>
            <H4>{dictionary.fields.description}</H4>
            <p className="mt-1">{item.description || "No description provided"}</p>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Small className="font-medium text-muted-foreground">
                {dictionary.fields.createdAt}
              </Small>
              <p className="mt-1">{new Date(item.created_at).toLocaleString()}</p>
            </div>
            <div>
              <Small className="font-medium text-muted-foreground">
                {dictionary.fields.updatedAt}
              </Small>
              <p className="mt-1">{new Date(item.updated_at).toLocaleString()}</p>
            </div>
          </div>

          <div>
            <Small className="font-medium text-muted-foreground">Organization ID</Small>
            <p className="mt-1">{item.organization_id}</p>
          </div>
        </div>
      ),
    },
  ];

  return (
    <DetailLayout
      title={
        <div className="flex items-center gap-2">
          {item.name}
          {statusBadge}
        </div>
      }
      description={dictionary.viewTitle}
      tabs={detailTabs}
      footer={footerActions}
      className="max-w-2xl mx-auto"
    />
  );
}
