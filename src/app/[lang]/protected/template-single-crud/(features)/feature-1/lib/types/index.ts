import { Dictionary } from "@/lib/i18n/services/I18nService";
import { DOMAIN_ID } from "../../../../lib/config/domain";
import { FEATURE_ID } from "../config/feature";

/**
 * Feature 1 Item interface
 * Represents a single item in Feature 1
 */
export interface Feature1Item {
  id: string;
  name: string;
  description: string | null;
  status: "active" | "inactive" | "draft";
  created_at: string;
  updated_at: string;
  organization_id: string;
}

/**
 * Feature 1 Item Insert interface
 * Used when creating a new Feature 1 item
 */
export interface Feature1ItemInsert {
  name: string;
  description: string | null;
  status: "active" | "inactive" | "draft";
  organization_id: string;
}

/**
 * Feature 1 Item Update interface
 * Used when updating an existing Feature 1 item
 */
export interface Feature1ItemUpdate {
  name?: string;
  description?: string | null;
  status?: "active" | "inactive" | "draft";
  updated_at: string;
}

export type FeatureDictionary = Dictionary[typeof DOMAIN_ID][typeof FEATURE_ID];
