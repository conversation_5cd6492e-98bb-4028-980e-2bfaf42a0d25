import { Dictionary } from "@/lib/i18n/services/I18nService";
import { DOMAIN_ID } from "../../../../lib/config/domain";
import { FEATURE_ID } from "../config/feature";
import { FeatureDictionary } from "../types";

/**
 * Get the domain feature dictionary from the main dictionary
 * @param dictionary The main dictionary
 * @returns The domain feature dictionary
 */
export function getDomainFeatureDictionary(dictionary: Dictionary): FeatureDictionary {
  return dictionary[DOMAIN_ID][FEATURE_ID];
}
