/**
 * Feature 1 Permissions
 *
 * This file defines all permissions related to Feature 1.
 * These permissions can be imported and composed in the central authorization system.
 *
 * This file now uses the centralized configuration from the feature config.
 */

import FEATURE_CONFIG from "../config/feature";

// Re-export the permissions from the feature config
export const FEATURE1_DOMAIN = FEATURE_CONFIG.permissionDomain;
export const FEATURE1_PERMISSIONS = FEATURE_CONFIG.permissions;
export const FEATURE1_ROUTE_PERMISSIONS = FEATURE_CONFIG.routePermissions;

// Export the security configuration
const FEATURE1_SECURITY = {
  domain: FEATURE1_DOMAIN,
  permissions: FEATURE1_PERMISSIONS,
  routePermissions: FEATURE1_ROUTE_PERMISSIONS,
};

export default FEATURE1_SECURITY;
