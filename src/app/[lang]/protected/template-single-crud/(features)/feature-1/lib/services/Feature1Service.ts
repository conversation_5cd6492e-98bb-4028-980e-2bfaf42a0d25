import { createClient } from "@/lib/supabase/server";
import { logger } from "@/lib/logger/services/LoggerService";
import { Feature1Item, Feature1ItemInsert, Feature1ItemUpdate } from "../types";
import { ServiceResponse, successResponse, errorResponse } from "@/lib/types/responses";

/**
 * Service for managing Feature 1 items
 * This is a singleton class that provides methods for CRUD operations on Feature 1 items
 */
export class Feature1Service {
  /**
   * Private constructor to prevent direct instantiation
   * Use the static methods instead
   */
  private constructor() {}

  /**
   * List Feature 1 items with pagination
   * @param page Page number (1-based)
   * @param pageSize Number of items per page
   * @returns Service response with paginated array of Feature 1 items and count
   */
  static async list(
    page: number = 1,
    pageSize: number = 10
  ): Promise<ServiceResponse<{ items: Feature1Item[]; total: number }>> {
    const supabase = await createClient();

    // Calculate offset
    const offset = (page - 1) * pageSize;

    try {
      // Get count of all items
      const { count, error: countError } = await supabase
        // @ts-expect-error - Only allowed in template. Remove when implementing
        .from("feature1_items")
        .select("*", { count: "exact", head: true });

      if (countError) {
        logger.error(`Error counting Feature 1 items: ${countError.message}`);
        return errorResponse(countError, `Failed to count Feature 1 items: ${countError.message}`);
      }

      // Get paginated items
      const { data, error } = await supabase
        // @ts-expect-error - Only allowed in template. Remove when implementing
        .from("feature1_items")
        .select("*")
        .order("created_at", { ascending: false })
        .range(offset, offset + pageSize - 1);

      if (error) {
        logger.error(`Error listing Feature 1 items: ${error.message}`);
        return errorResponse(error, `Failed to list Feature 1 items: ${error.message}`);
      }

      return successResponse(
        {
          items: data as Feature1Item[],
          total: count || 0,
        },
        `Successfully retrieved ${data.length} Feature 1 items (page ${page} of ${Math.ceil((count || 0) / pageSize)})`
      );
    } catch (error) {
      logger.error(`Unexpected error listing Feature 1 items: ${error}`);
      return errorResponse(error, `Unexpected error listing Feature 1 items`);
    }
  }

  /**
   * Read a Feature 1 item by ID
   * @param id The ID of the Feature 1 item
   * @returns Service response with the Feature 1 item
   */
  static async view(id: string): Promise<ServiceResponse<Feature1Item>> {
    try {
      const supabase = await createClient();

      const { data, error } = await supabase
        // @ts-expect-error - Only allowed in template. Remove when implementing
        .from("feature1_items")
        .select("*")
        .eq("id", id)
        .single();

      if (error) {
        if (error.code === "PGRST116") {
          // Record not found
          return errorResponse(error, `Feature 1 item with ID ${id} not found`);
        }

        logger.error(`Error reading Feature 1 item: ${error.message}`);
        return errorResponse(error, `Failed to read Feature 1 item: ${error.message}`);
      }

      return successResponse(data as Feature1Item, "Successfully retrieved Feature 1 item");
    } catch (error) {
      logger.error(`Unexpected error reading Feature 1 item: ${error}`);
      return errorResponse(error, `Unexpected error reading Feature 1 item`);
    }
  }

  /**
   * Search Feature 1 items by name or description with pagination
   * @param query Search query
   * @param page Page number (1-based)
   * @param pageSize Number of items per page
   * @returns Service response with paginated array of matching Feature 1 items and count
   */
  static async search(
    query: string,
    page: number = 1,
    pageSize: number = 10
  ): Promise<ServiceResponse<{ items: Feature1Item[]; total: number }>> {
    if (!query || query.trim() === "") {
      return this.list(page, pageSize);
    }

    const supabase = await createClient();
    const searchTerm = `%${query.toLowerCase()}%`;

    // Calculate offset
    const offset = (page - 1) * pageSize;

    try {
      // Get count of matching items
      const { count, error: countError } = await supabase
        // @ts-expect-error - Only allowed in template. Remove when implementing
        .from("feature1_items")
        .select("*", { count: "exact", head: true })
        .or(`name.ilike.${searchTerm},description.ilike.${searchTerm}`);

      if (countError) {
        logger.error(`Error counting search results: ${countError.message}`);
        return errorResponse(countError, `Failed to count search results: ${countError.message}`);
      }

      // Get paginated search results
      const { data, error } = await supabase
        // @ts-expect-error - Only allowed in template. Remove when implementing
        .from("feature1_items")
        .select("*")
        .or(`name.ilike.${searchTerm},description.ilike.${searchTerm}`)
        .order("created_at", { ascending: false })
        .range(offset, offset + pageSize - 1);

      if (error) {
        logger.error(`Error searching Feature 1 items: ${error.message}`);
        return errorResponse(error, `Failed to search Feature 1 items: ${error.message}`);
      }

      return successResponse(
        {
          items: data as Feature1Item[],
          total: count || 0,
        },
        `Found ${data.length} Feature 1 items matching "${query}" (page ${page} of ${Math.ceil((count || 0) / pageSize)})`
      );
    } catch (error) {
      logger.error(`Unexpected error searching Feature 1 items: ${error}`);
      return errorResponse(error, `Unexpected error searching Feature 1 items`);
    }
  }

  /**
   * Create a new Feature 1 item
   * @param item The Feature 1 item to create
   * @returns Service response with the created Feature 1 item
   */
  static async create(item: Feature1ItemInsert): Promise<ServiceResponse<Feature1Item>> {
    try {
      const supabase = await createClient();

      // @ts-expect-error - Only allowed in template. Remove when implementing
      const { data, error } = await supabase.from("feature1_items").insert(item).select().single();

      if (error) {
        logger.error(`Error creating Feature 1 item: ${error.message}`);
        return errorResponse(error, `Failed to create Feature 1 item: ${error.message}`);
      }

      return successResponse(data as Feature1Item, "Successfully created Feature 1 item");
    } catch (error) {
      logger.error(`Unexpected error creating Feature 1 item: ${error}`);
      return errorResponse(error, `Unexpected error creating Feature 1 item`);
    }
  }

  /**
   * Update an existing Feature 1 item
   * @param id The ID of the Feature 1 item to update
   * @param item The updated Feature 1 item data
   * @returns Service response with the updated Feature 1 item
   */
  static async edit(id: string, item: Feature1ItemUpdate): Promise<ServiceResponse<Feature1Item>> {
    try {
      const supabase = await createClient();

      const { data, error } = await supabase
        // @ts-expect-error - Only allowed in template. Remove when implementing
        .from("feature1_items")
        .update(item)
        .eq("id", id)
        .select()
        .single();

      if (error) {
        logger.error(`Error updating Feature 1 item: ${error.message}`);
        return errorResponse(error, `Failed to update Feature 1 item: ${error.message}`);
      }

      return successResponse(data as Feature1Item, "Successfully updated Feature 1 item");
    } catch (error) {
      logger.error(`Unexpected error updating Feature 1 item: ${error}`);
      return errorResponse(error, `Unexpected error updating Feature 1 item`);
    }
  }

  /**
   * Remove a Feature 1 item (soft delete by setting status to inactive)
   * @param id The ID of the Feature 1 item to remove
   * @returns Service response
   */
  static async remove(id: string): Promise<ServiceResponse<null>> {
    try {
      const supabase = await createClient();

      const { error } = await supabase
        // @ts-expect-error - Only allowed in template. Remove when implementing
        .from("feature1_items")
        .update({ status: "inactive", updated_at: new Date().toISOString() })
        .eq("id", id);

      if (error) {
        logger.error(`Error removing Feature 1 item: ${error.message}`);
        return errorResponse(error, `Failed to remove Feature 1 item: ${error.message}`);
      }

      return successResponse(null, "Successfully removed Feature 1 item");
    } catch (error) {
      logger.error(`Unexpected error removing Feature 1 item: ${error}`);
      return errorResponse(error, `Unexpected error removing Feature 1 item`);
    }
  }
}
