import { redirect } from "next/navigation";
import FEATURE_CONFIG from "./lib/config";

interface Feature1PageProps {
  params: Promise<{
    lang: string;
  }>;
}

/**
 * Default page for Feature 1 in Template Single CRUD
 * Redirects to the list page
 */
export default async function Feature1Page({ params }: Feature1PageProps) {
  // Await the params
  const resolvedParams = await params;

  // Redirect to the list page
  redirect(`/${resolvedParams.lang}${FEATURE_CONFIG.routes.LIST}`);
}
