"use server";

import { logger } from "@/lib/logger/services/LoggerService";
import { Feature1Service } from "../lib/services/Feature1Service";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { FEATURE1_PERMISSIONS } from "../lib/security/permissions";
import { ActionState, errorActionState, successActionState } from "@/lib/types/responses";
import { Feature1Item } from "../lib/types";

interface ViewParams {
  id: string;
}

/**
 * Get a Feature 1 item by ID
 * @param _prevState Previous state
 * @param params Object containing the item ID
 * @returns The Feature 1 item
 */
export const view = requirePermission(FEATURE1_PERMISSIONS.VIEW)(async (
  _prevState: ActionState<Feature1Item>,
  params: ViewParams
): Promise<ActionState<Feature1Item>> => {
  try {
    const { id } = params;

    // Validate required fields
    if (!id) {
      return errorActionState("Item ID is required");
    }

    // Get the Feature 1 item
    const result = await Feature1Service.view(id);

    if (!result.success) {
      logger.error(`Error retrieving Feature 1 item: ${result.error}`);
      return errorActionState(`Failed to retrieve Feature 1 item: ${result.error}`);
    }

    // Return the item
    return successActionState(result.data);
  } catch (error) {
    logger.error(`Unexpected error retrieving Feature 1 item: ${error}`);
    return errorActionState(`An unexpected error occurred: ${error}`);
  }
});
