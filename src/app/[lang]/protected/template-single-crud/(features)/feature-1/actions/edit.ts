"use server";

import { revalidatePath } from "next/cache";
import { logger } from "@/lib/logger/services/LoggerService";
import { Feature1Service } from "../lib/services/Feature1Service";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { FEATURE1_PERMISSIONS } from "../lib/security/permissions";
import { ActionState, errorActionState, successActionState } from "@/lib/types/responses";
import { Feature1Item } from "../lib/types";
import { auth } from "@/lib/authentication/services/AuthenticationService";
import FEATURE_CONFIG from "../lib/config";

/**
 * Update an existing Feature 1 item
 * @param prevState Previous state
 * @param params Object containing item data
 * @returns Result of the update operation
 */
export const edit = requirePermission(FEATURE1_PERMISSIONS.EDIT)(async (
  _prevState: ActionState<Feature1Item>,
  formData: FormData
): Promise<ActionState<Feature1Item>> => {
  try {
    const name = formData.get("name") as string;
    const description = formData.get("description") as string;
    const status = formData.get("status") as "active" | "inactive" | "draft";
    const profile = await auth.getCurrentUserProfile();
    //const organizationId = profile?.organizationId;
    const lang = profile?.language;
    const id = _prevState.data?.id;

    // Validate required fields
    if (!id) {
      return errorActionState("Item ID is required");
    }

    if (!name) {
      return errorActionState("Name is required");
    }

    // Update the Feature 1 item
    const result = await Feature1Service.edit(id, {
      name,
      description: description || null,
      status,
      updated_at: new Date().toISOString(),
    });

    if (!result.success) {
      logger.error(`Error updating Feature 1 item: ${result.error}`);
      return errorActionState(`Failed to update Feature 1 item: ${result.error}`);
    }

    // Revalidate the Feature 1 item pages
    revalidatePath(`/${lang}${FEATURE_CONFIG.routes.LIST}`);
    revalidatePath(`/${lang}${FEATURE_CONFIG.routes.VIEW(id)}`);

    // Return success with the updated item
    return successActionState(result.data);
  } catch (error) {
    logger.error(`Unexpected error updating Feature 1 item: ${error}`);
    return errorActionState(`An unexpected error occurred: ${error}`);
  }
});
