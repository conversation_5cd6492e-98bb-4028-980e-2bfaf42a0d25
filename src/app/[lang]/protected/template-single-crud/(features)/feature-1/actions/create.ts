"use server";

import { revalidatePath } from "next/cache";
import { logger } from "@/lib/logger/services/LoggerService";
import { Feature1Service } from "../lib/services/Feature1Service";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { FEATURE1_PERMISSIONS } from "../lib/security/permissions";
import { redirect } from "next/navigation";
import { ActionState, errorActionState } from "@/lib/types/responses";
import { Feature1Item } from "../lib/types";
import { auth } from "@/lib/authentication/services/AuthenticationService";
import FEATURE_CONFIG from "../lib/config";

/**
 * Create a new Feature 1 item
 * @param prevState Previous state
 * @param formData Form data
 * @returns Result of the create operation
 */
export const create = requirePermission(FEATURE1_PERMISSIONS.CREATE)(async (
  _prevState: ActionState<Feature1Item>,
  formData: FormData
): Promise<ActionState<Feature1Item>> => {
  try {
    // Get form data
    const name = formData.get("name") as string;
    const description = formData.get("description") as string;
    const status = formData.get("status") as "active" | "inactive" | "draft";
    const profile = await auth.getCurrentUserProfile();
    const organizationId = profile?.organizationId;
    const lang = profile?.language;

    // Validate required fields
    if (!name) {
      return errorActionState("Name is required");
    }

    if (!organizationId) {
      return errorActionState("Organization ID is required");
    }

    // Create the Feature 1 item
    const result = await Feature1Service.create({
      name,
      description: description || null,
      status: status || "active",
      organization_id: organizationId,
    });

    if (!result.success) {
      logger.error(`Error creating Feature 1 item: ${result.error}`);
      return errorActionState(
        `An error occurred while creating the Feature 1 item: ${result.message}`
      );
    }

    // Revalidate the Feature 1 items list page
    revalidatePath(`/${lang}${FEATURE_CONFIG.routes.LIST}`);

    // Redirect to the detail page - this will prevent the function from returning
    // The redirect function throws an error internally, so no code after this will execute
    redirect(`/${lang}${FEATURE_CONFIG.routes.VIEW(result.data?.id as string)}`);
  } catch (error) {
    logger.error(`Unexpected error creating Feature 1 item: ${error}`);
    return errorActionState(`An unexpected error occurred: ${error}`);
  }
});
