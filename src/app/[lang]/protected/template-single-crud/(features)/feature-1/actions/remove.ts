"use server";

import { revalidatePath } from "next/cache";
import { logger } from "@/lib/logger/services/LoggerService";
import { Feature1Service } from "../lib/services/Feature1Service";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { FEATURE1_PERMISSIONS } from "../lib/security/permissions";
import { ActionState, errorActionState } from "@/lib/types/responses";
import { Feature1Item } from "../lib/types";
import { redirect } from "next/navigation";
import FEATURE_CONFIG from "../lib/config";

// Using the standardized ActionState type from @/lib/types/responses

/**
 * Remove a Feature 1 item (soft delete by setting status to inactive)
 * @param prevState Previous state
 * @param formData Form data
 * @returns Result of the remove operation
 */
export const remove = requirePermission(FEATURE1_PERMISSIONS.REMOVE)(async (
  _prevState: ActionState<Feature1Item>,
  formData: FormData
): Promise<ActionState<Feature1Item>> => {
  try {
    // Get form data
    const id = _prevState.data?.id;
    const lang = formData.get("lang") as string;

    // Validate required fields
    if (!id) {
      return errorActionState("Item ID is required");
    }

    // Remove the Feature 1 item
    const result = await Feature1Service.remove(id);

    if (!result.success) {
      logger.error(`Error removing Feature 1 item: ${result.error}`);
      return errorActionState(`Failed to remove Feature 1 item: ${result.message}`);
    }

    // Revalidate the Feature 1 items list page
    revalidatePath(`/${lang}${FEATURE_CONFIG.routes.LIST}`);

    // Redirect to the list page - this will prevent the function from returning
    // The redirect function throws an error internally, so no code after this will execute
    redirect(`/${lang}${FEATURE_CONFIG.routes.LIST}`);
  } catch (error) {
    logger.error(`Unexpected error removing Feature 1 item: ${error}`);
    return errorActionState(`An unexpected error occurred: ${error}`);
  }
});
