import { redirect } from "next/navigation";
import DOMAIN_CONFIG from "./lib/config";

interface TemplatePageProps {
  params: Promise<{
    lang: string;
  }>;
}

/**
 * Default page for the Template Single CRUD feature
 * Redirects to the feature-1 page
 */
export default async function TemplatePage({ params }: TemplatePageProps) {
  // Await the params
  const resolvedParams = await params;

  // Redirect to the feature-1 page
  redirect(`/${resolvedParams.lang}${DOMAIN_CONFIG.basePath}/(features)/feature-1`);
}
