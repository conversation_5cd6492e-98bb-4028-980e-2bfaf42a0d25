import { render, screen } from "@testing-library/react";
import ProtectedLayout from "../layout";
import { i18n } from "@/lib/i18n/services/I18nService";

// Mock the i18n service
jest.mock("@/lib/i18n/services/I18nService", () => ({
  i18n: {
    getDictionary: jest.fn().mockResolvedValue({
      navigation: {
        dashboard: "Dashboard",
        contacts: "Contacts",
        requests: "Requests",
        cases: "Cases",
        scheduling: "Scheduling",
        notes: "Notes",
        reports: "Reports",
        settings: "Settings",
      },
      common: {
        profile: "Profile",
        account: "Account",
        signOut: "Sign Out",
      },
    }),
  },
}));

// Import the dictionary types
import type { SidebarDictionary } from "../components/sidebar";
import type { NavbarDictionary } from "../components/navbar";

// Mock the components
jest.mock("../components/sidebar", () => ({
  Sidebar: ({ lang }: { lang: string; dictionary: SidebarDictionary }) => (
    <div data-testid="sidebar">Sidebar (Lang: {lang})</div>
  ),
}));

jest.mock("../components/navbar", () => ({
  Navbar: ({ lang }: { lang: string; dictionary: NavbarDictionary }) => (
    <div data-testid="navbar">Navbar (Lang: {lang})</div>
  ),
}));

describe("ProtectedLayout", () => {
  it("renders the layout with sidebar and navbar", async () => {
    const params = Promise.resolve({ lang: "en" });
    const children = <div data-testid="content">Test Content</div>;

    render(await ProtectedLayout({ children, params }));

    expect(screen.getByTestId("sidebar")).toBeInTheDocument();
    expect(screen.getByTestId("navbar")).toBeInTheDocument();
    expect(screen.getByTestId("content")).toBeInTheDocument();

    // Check if i18n.getDictionary was called with the correct language
    expect(i18n.getDictionary).toHaveBeenCalledWith("en");
  });
});
