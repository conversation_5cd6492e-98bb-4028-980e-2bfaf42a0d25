"use server";

import { ProfileService } from "../lib/services/ProfileService";
import { ServiceUpdate } from "../lib/types";
import { revalidatePath } from "next/cache";
import { logger } from "@/lib/logger/services/LoggerService";

type ActionState = {
  success: boolean;
  error: string;
};

/**
 * Update an existing service
 */
export async function updateService(
  _prevState: ActionState,
  formData: FormData
): Promise<ActionState> {
  // Get form data
  const id = formData.get("id") as string;
  const name = formData.get("name") as string;
  const description = formData.get("description") as string;
  const priceStr = formData.get("price") as string;
  const durationStr = formData.get("duration") as string;
  const status = formData.get("status") as "active" | "inactive";
  const lang = formData.get("lang") as string;

  // Validate required fields
  if (!id) {
    return { success: false, error: "Service ID is required" };
  }

  if (!name) {
    return { success: false, error: "Name is required" };
  }

  if (!description) {
    return { success: false, error: "Description is required" };
  }

  if (!priceStr) {
    return { success: false, error: "Price is required" };
  }

  if (!durationStr) {
    return { success: false, error: "Duration is required" };
  }

  // Parse numeric values
  const price = parseFloat(priceStr);
  const duration = parseInt(durationStr, 10);

  // Validate numeric values
  if (isNaN(price) || price < 0) {
    return { success: false, error: "Price must be a positive number" };
  }

  if (isNaN(duration) || duration < 1) {
    return { success: false, error: "Duration must be a positive number" };
  }

  // Create service update object
  const serviceUpdate: ServiceUpdate = {
    name,
    description,
    price,
    duration,
    status: status || "active",
    updated_at: new Date().toISOString(),
  };

  try {
    // Update the service
    await ProfileService.updateService(id, serviceUpdate);
  } catch (error) {
    logger.error(`Error updating service: ${error}`);
    return {
      success: false,
      error: `Failed to update service: ${error}`,
    };
  }

  // Revalidate the organization profile page
  revalidatePath(`/${lang}/protected/organization/profile`);

  // Return success
  return {
    success: true,
    error: "",
  };
}
