"use server";

import { ProfileService } from "../lib/services/ProfileService";
import { OrganizationUpdate, EmailsJson, PhonesJson } from "../lib/types";
import { revalidatePath } from "next/cache";
import { logger } from "@/lib/logger/services/LoggerService";

type ActionState = {
  success: boolean;
  error: string;
};

export async function updateProfile(
  _prevState: ActionState,
  formData: FormData
): Promise<ActionState> {
  // Get form data
  const name = formData.get("name") as string;
  const address = formData.get("address") as string;
  const phone = formData.get("phone") as string;
  const email = formData.get("email") as string;
  const website = formData.get("website") as string;
  const supportEmail = formData.get("supportEmail") as string;
  const fax = formData.get("fax") as string;
  const lang = formData.get("lang") as string;

  // Validate required fields
  if (!name) {
    return { success: false, error: "Name is required" };
  }

  if (!email) {
    return { success: false, error: "Email is required" };
  }

  if (!phone) {
    return { success: false, error: "Phone is required" };
  }

  // Validate email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return { success: false, error: "Invalid email format" };
  }

  if (supportEmail && !emailRegex.test(supportEmail)) {
    return { success: false, error: "Invalid support email format" };
  }

  // Validate phone format (simple validation)
  const phoneRegex = /^[0-9\-\+\(\)\s\.]{7,20}$/;
  if (!phoneRegex.test(phone)) {
    return { success: false, error: "Invalid phone format" };
  }

  if (fax && !phoneRegex.test(fax)) {
    return { success: false, error: "Invalid fax format" };
  }

  // Create emails and phones JSON objects
  const emails: EmailsJson = {
    info: email,
  };

  if (supportEmail) {
    emails.support = supportEmail;
  }

  const phones: PhonesJson = {
    main: phone,
  };

  if (fax) {
    phones.fax = fax;
  }

  // Create organization update object
  const organizationUpdate: OrganizationUpdate = {
    name,
    address: address || null,
    emails,
    phones,
    website_url: website || null,
    updated_at: new Date().toISOString(),
  };

  try {
    // Update the organization
    await ProfileService.updateOrganization(organizationUpdate);
  } catch (error) {
    logger.error(`Error updating organization profile: ${error}`);
    return {
      success: false,
      error: `Failed to update organization profile: ${error}`,
    };
  }

  // Revalidate the organization profile page
  revalidatePath(`/${lang}/protected/organization/profile`);

  // Return success
  return {
    success: true,
    error: "",
  };
}
