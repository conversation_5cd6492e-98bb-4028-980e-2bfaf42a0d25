"use server";

import { ProfileService } from "../lib/services/ProfileService";
import { BusinessHoursUpdate, DayOfWeek } from "../lib/types";
import { revalidatePath } from "next/cache";
import { logger } from "@/lib/logger/services/LoggerService";

type ActionState = {
  success: boolean;
  error: string;
};

/**
 * Update business hours for the organization
 */
export async function updateBusinessHours(
  _prevState: ActionState,
  formData: FormData
): Promise<ActionState> {
  // Get form data
  const id = formData.get("id") as string;
  const dayOfWeekStr = formData.get("dayOfWeek") as string;
  const startTime = formData.get("startTime") as string;
  const endTime = formData.get("endTime") as string;
  const locationId = formData.get("locationId") as string;
  const isClosed = formData.has("isClosed");
  const lang = formData.get("lang") as string;

  // Validate required fields
  if (!id) {
    return { success: false, error: "Business hours ID is required" };
  }

  if (!dayOfWeekStr) {
    return { success: false, error: "Day of week is required" };
  }

  // Parse day of week
  const dayOfWeek = parseInt(dayOfWeekStr, 10) as DayOfWeek;

  // If not closed, validate time fields
  if (!isClosed) {
    if (!startTime) {
      return { success: false, error: "Start time is required" };
    }

    if (!endTime) {
      return { success: false, error: "End time is required" };
    }

    // Validate that start time is before end time
    if (startTime >= endTime) {
      return { success: false, error: "Start time must be before end time" };
    }
  }

  try {
    // Create business hours update object
    const businessHoursUpdate: BusinessHoursUpdate = {
      day_of_week: dayOfWeek,
      start_time: isClosed ? "00:00" : startTime,
      end_time: isClosed ? "00:00" : endTime,
      location_id: locationId === "all" ? null : locationId,
      is_closed: isClosed,
      updated_at: new Date().toISOString(),
    };

    // Update the business hours
    await ProfileService.updateBusinessHours(id, businessHoursUpdate);

    // Revalidate the business hours page
    revalidatePath(`/${lang}/protected/organization/profile/business-hours`);

    // Return success
    return { success: true, error: "" };
  } catch (error) {
    logger.error(`Error updating business hours: ${error}`);
    return {
      success: false,
      error: error instanceof Error ? error.message : "An unexpected error occurred",
    };
  }
}
