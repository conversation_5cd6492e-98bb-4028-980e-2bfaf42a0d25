"use server";

import { ProfileService } from "../lib/services/ProfileService";
import { revalidatePath } from "next/cache";
import { logger } from "@/lib/logger/services/LoggerService";

/**
 * Delete a service (soft delete by setting status to inactive)
 */
export async function deleteService(id: string): Promise<{ success: boolean; error?: string }> {
  // Validate required fields
  if (!id) {
    return { success: false, error: "Service ID is required" };
  }

  try {
    // Delete the service (soft delete)
    await ProfileService.deleteService(id);
  } catch (error) {
    logger.error(`Error deleting service: ${error}`);
    return {
      success: false,
      error: `Failed to delete service: ${error}`,
    };
  }

  // Revalidate the organization profile page
  // We don't have the language from the form data, so we'll revalidate both languages
  revalidatePath(`/fr/protected/organization/profile`);
  revalidatePath(`/en/protected/organization/profile`);

  // Return success
  return {
    success: true,
  };
}
