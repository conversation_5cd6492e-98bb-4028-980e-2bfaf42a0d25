"use server";

import { ProfileService } from "../lib/services/ProfileService";
import { RoomInsert } from "../lib/types";
import { revalidatePath } from "next/cache";
import { logger } from "@/lib/logger/services/LoggerService";

type ActionState = {
  success: boolean;
  error: string;
};

/**
 * Create a new room for the organization
 */
export async function createRoom(
  _prevState: ActionState,
  formData: FormData
): Promise<ActionState> {
  // Get form data
  const name = formData.get("name") as string;
  const description = formData.get("description") as string;
  const capacityStr = formData.get("capacity") as string;
  const locationId = formData.get("locationId") as string;
  const status = formData.get("status") as "active" | "inactive";
  const featuresJson = formData.get("features") as string;
  const lang = formData.get("lang") as string;

  // Validate required fields
  if (!name) {
    return { success: false, error: "Room name is required" };
  }

  if (!locationId) {
    return { success: false, error: "Location is required" };
  }

  if (!capacityStr) {
    return { success: false, error: "Capacity is required" };
  }

  // Parse capacity to number
  const capacity = parseInt(capacityStr, 10);
  if (isNaN(capacity) || capacity <= 0) {
    return { success: false, error: "Capacity must be a positive number" };
  }

  // Validate features JSON format (even though we don't store it in the database)
  try {
    if (featuresJson) {
      JSON.parse(featuresJson);
    }
  } catch {
    return { success: false, error: "Invalid features format" };
  }

  try {
    // Get the current organization to use its ID
    const organization = await ProfileService.getCurrentOrganization();
    if (!organization) {
      return { success: false, error: "Organization not found" };
    }

    // Create room object
    const room: RoomInsert = {
      name,
      description: description || null,
      capacity,
      location_id: locationId,
      organization_id: organization.id,
      status: status || "active",
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    // Create the room
    await ProfileService.createRoom(room);

    // Revalidate the rooms page
    revalidatePath(`/${lang}/protected/organization/profile/rooms`);

    // Return success
    return { success: true, error: "" };
  } catch (error) {
    logger.error(`Error creating room: ${error}`);
    return {
      success: false,
      error: error instanceof Error ? error.message : "An unexpected error occurred",
    };
  }
}
