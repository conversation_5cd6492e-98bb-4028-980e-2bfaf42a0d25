"use server";

import { ProfileService } from "../lib/services/ProfileService";
import { revalidatePath } from "next/cache";
import { logger } from "@/lib/logger/services/LoggerService";

/**
 * Delete a location (soft delete by setting status to inactive)
 */
export async function deleteLocation(id: string): Promise<{ success: boolean; error?: string }> {
  // Validate required fields
  if (!id) {
    return { success: false, error: "Location ID is required" };
  }

  try {
    // Delete the location (soft delete)
    await ProfileService.deleteLocation(id);
  } catch (error) {
    logger.error(`Error deleting location: ${error}`);
    return {
      success: false,
      error: `Failed to delete location: ${error}`,
    };
  }

  // Revalidate the organization profile page
  // We don't have the language from the form data, so we'll revalidate both languages
  revalidatePath(`/fr/protected/organization/profile`);
  revalidatePath(`/en/protected/organization/profile`);

  // Return success
  return {
    success: true,
  };
}
