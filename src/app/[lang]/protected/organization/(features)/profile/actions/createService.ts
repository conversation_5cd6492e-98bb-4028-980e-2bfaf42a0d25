"use server";

import { ProfileService } from "../lib/services/ProfileService";
import { ServiceInsert } from "../lib/types";
import { revalidatePath } from "next/cache";
import { logger } from "@/lib/logger/services/LoggerService";

type ActionState = {
  success: boolean;
  error: string;
};

/**
 * Create a new service for the organization
 */
export async function createService(
  _prevState: ActionState,
  formData: FormData
): Promise<ActionState> {
  // Get form data
  const name = formData.get("name") as string;
  const description = formData.get("description") as string;
  const priceStr = formData.get("price") as string;
  const durationStr = formData.get("duration") as string;
  const status = formData.get("status") as "active" | "inactive";
  const lang = formData.get("lang") as string;

  // Validate required fields
  if (!name) {
    return { success: false, error: "Name is required" };
  }

  if (!description) {
    return { success: false, error: "Description is required" };
  }

  if (!priceStr) {
    return { success: false, error: "Price is required" };
  }

  if (!durationStr) {
    return { success: false, error: "Duration is required" };
  }

  // Parse numeric values
  const price = parseFloat(priceStr);
  const duration = parseInt(durationStr, 10);

  // Validate numeric values
  if (isNaN(price) || price < 0) {
    return { success: false, error: "Price must be a positive number" };
  }

  if (isNaN(duration) || duration < 1) {
    return { success: false, error: "Duration must be a positive number" };
  }

  try {
    // Get the current organization to use its ID
    const organization = await ProfileService.getCurrentOrganization();
    if (!organization) {
      return { success: false, error: "Organization not found" };
    }

    // Create service object
    const service: ServiceInsert = {
      name,
      description,
      price,
      duration,
      status: status || "active",
      organization_id: organization.id,
    };

    // Create the service
    await ProfileService.createService(service);
  } catch (error) {
    // If the error is related to the table not existing
    if (
      error instanceof Error &&
      (error.message.includes("does not exist") ||
        (typeof error === "object" && error !== null && "code" in error && error.code === "42P01"))
    ) {
      return {
        success: false,
        error:
          "The services feature is not yet available. Please contact your administrator to enable this feature.",
      };
    }

    logger.error(`Error creating service: ${error}`);
    return {
      success: false,
      error: `Failed to create service: ${error}`,
    };
  }

  // Revalidate the organization profile page
  revalidatePath(`/${lang}/protected/organization/profile`);

  // Return success
  return {
    success: true,
    error: "",
  };
}
