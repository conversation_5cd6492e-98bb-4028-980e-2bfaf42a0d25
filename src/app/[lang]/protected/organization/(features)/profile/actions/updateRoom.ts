"use server";

import { ProfileService } from "../lib/services/ProfileService";
import { RoomUpdate } from "../lib/types";
import { revalidatePath } from "next/cache";
import { logger } from "@/lib/logger/services/LoggerService";

type ActionState = {
  success: boolean;
  error: string;
};

/**
 * Update a room for the organization
 */
export async function updateRoom(
  _prevState: ActionState,
  formData: FormData
): Promise<ActionState> {
  // Get form data
  const id = formData.get("id") as string;
  const name = formData.get("name") as string;
  const description = formData.get("description") as string;
  const capacityStr = formData.get("capacity") as string;
  const locationId = formData.get("locationId") as string;
  const status = formData.get("status") as "active" | "inactive";
  const featuresJson = formData.get("features") as string;
  const lang = formData.get("lang") as string;

  // Validate required fields
  if (!id) {
    return { success: false, error: "Room ID is required" };
  }

  if (!name) {
    return { success: false, error: "Room name is required" };
  }

  if (!locationId) {
    return { success: false, error: "Location is required" };
  }

  if (!capacityStr) {
    return { success: false, error: "Capacity is required" };
  }

  // Parse capacity to number
  const capacity = parseInt(capacityStr, 10);
  if (isNaN(capacity) || capacity <= 0) {
    return { success: false, error: "Capacity must be a positive number" };
  }

  // Validate features JSON format (even though we don't store it in the database)
  try {
    if (featuresJson) {
      JSON.parse(featuresJson);
    }
  } catch {
    return { success: false, error: "Invalid features format" };
  }

  try {
    // Create room update object
    const roomUpdate: RoomUpdate = {
      name,
      description: description || null,
      capacity,
      location_id: locationId,
      status: status || "active",
      updated_at: new Date().toISOString(),
    };

    // Update the room
    await ProfileService.updateRoom(id, roomUpdate);

    // Revalidate the rooms page
    revalidatePath(`/${lang}/protected/organization/profile/rooms`);

    // Return success
    return { success: true, error: "" };
  } catch (error) {
    logger.error(`Error updating room: ${error}`);
    return {
      success: false,
      error: error instanceof Error ? error.message : "An unexpected error occurred",
    };
  }
}
