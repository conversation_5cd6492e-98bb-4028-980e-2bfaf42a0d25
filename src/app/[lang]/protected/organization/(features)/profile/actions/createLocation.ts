"use server";

import { ProfileService } from "../lib/services/ProfileService";
import { LocationInsert } from "../lib/types";
import { revalidatePath } from "next/cache";
import { logger } from "@/lib/logger/services/LoggerService";

type ActionState = {
  success: boolean;
  error: string;
};

/**
 * Create a new location for the organization
 */
export async function createLocation(
  _prevState: ActionState,
  formData: FormData
): Promise<ActionState> {
  // Get form data
  const name = formData.get("name") as string;
  const address = formData.get("address") as string;
  const phone = formData.get("phone") as string;
  const email = formData.get("email") as string;
  const status = formData.get("status") as "active" | "inactive";
  const lang = formData.get("lang") as string;

  // Validate required fields
  if (!name) {
    return { success: false, error: "Name is required" };
  }

  if (!address) {
    return { success: false, error: "Address is required" };
  }

  if (!email) {
    return { success: false, error: "Email is required" };
  }

  if (!phone) {
    return { success: false, error: "Phone is required" };
  }

  // Validate email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return { success: false, error: "Invalid email format" };
  }

  // Validate phone format (simple validation)
  const phoneRegex = /^[0-9\-\+\(\)\s\.]{7,20}$/;
  if (!phoneRegex.test(phone)) {
    return { success: false, error: "Invalid phone format" };
  }

  // Create emails and phones JSON objects
  const emails = { main: email };
  const phones = { main: phone };

  try {
    // Get the current organization to use its ID
    const organization = await ProfileService.getCurrentOrganization();
    if (!organization) {
      return { success: false, error: "Organization not found" };
    }

    // Create location object
    const location: LocationInsert = {
      name,
      address,
      emails,
      phones,
      status: status || "active",
      organization_id: organization.id,
    };

    // Create the location
    await ProfileService.createLocation(location);
  } catch (error) {
    logger.error(`Error creating location: ${error}`);
    return {
      success: false,
      error: `Failed to create location: ${error}`,
    };
  }

  // Revalidate the organization profile page
  revalidatePath(`/${lang}/protected/organization/profile`);

  // Return success
  return {
    success: true,
    error: "",
  };
}
