"use server";

import { ProfileService } from "../lib/services/ProfileService";
import { revalidatePath } from "next/cache";
import { logger } from "@/lib/logger/services/LoggerService";

/**
 * Delete a room for the organization
 * This is a soft delete that sets the status to "inactive"
 */
export async function deleteRoom(id: string, lang: string): Promise<void> {
  try {
    // Delete the room (soft delete)
    await ProfileService.deleteRoom(id);

    // Revalidate the rooms page
    revalidatePath(`/${lang}/protected/organization/profile/rooms`);
  } catch (error) {
    logger.error(`Error deleting room: ${error}`);
    throw error;
  }
}
