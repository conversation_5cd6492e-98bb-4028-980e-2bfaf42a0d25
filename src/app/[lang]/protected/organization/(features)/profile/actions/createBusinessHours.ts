"use server";

import { ProfileService } from "../lib/services/ProfileService";
import { BusinessHoursInsert, DayOfWeek } from "../lib/types";
import { revalidatePath } from "next/cache";
import { logger } from "@/lib/logger/services/LoggerService";

type ActionState = {
  success: boolean;
  error: string;
};

/**
 * Create new business hours for the organization
 */
export async function createBusinessHours(
  _prevState: ActionState,
  formData: FormData
): Promise<ActionState> {
  // Get form data
  const dayOfWeekStr = formData.get("dayOfWeek") as string;
  const startTime = formData.get("startTime") as string;
  const endTime = formData.get("endTime") as string;
  const locationId = formData.get("locationId") as string;
  const isClosed = formData.has("isClosed");
  const lang = formData.get("lang") as string;

  // Validate required fields
  if (!dayOfWeekStr) {
    return { success: false, error: "Day of week is required" };
  }

  // Parse day of week
  const dayOfWeek = parseInt(dayOfWeekStr, 10) as DayOfWeek;

  // If not closed, validate time fields
  if (!isClosed) {
    if (!startTime) {
      return { success: false, error: "Start time is required" };
    }

    if (!endTime) {
      return { success: false, error: "End time is required" };
    }

    // Validate that start time is before end time
    if (startTime >= endTime) {
      return { success: false, error: "Start time must be before end time" };
    }
  }

  try {
    // Get the current organization to use its ID
    const organization = await ProfileService.getCurrentOrganization();
    if (!organization) {
      return { success: false, error: "Organization not found" };
    }

    // Create business hours object
    const businessHours: BusinessHoursInsert = {
      day_of_week: dayOfWeek,
      start_time: isClosed ? "00:00" : startTime,
      end_time: isClosed ? "00:00" : endTime,
      location_id: locationId === "all" ? null : locationId,
      is_closed: isClosed,
      organization_id: organization.id,
    };

    // Create the business hours
    await ProfileService.createBusinessHours(businessHours);

    // Revalidate the business hours page
    revalidatePath(`/${lang}/protected/organization/profile/business-hours`);

    // Return success
    return { success: true, error: "" };
  } catch (error) {
    logger.error(`Error creating business hours: ${error}`);
    return {
      success: false,
      error: error instanceof Error ? error.message : "An unexpected error occurred",
    };
  }
}
