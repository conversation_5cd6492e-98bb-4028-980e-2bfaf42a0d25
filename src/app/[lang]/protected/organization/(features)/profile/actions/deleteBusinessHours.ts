"use server";

import { ProfileService } from "../lib/services/ProfileService";
import { revalidatePath } from "next/cache";
import { logger } from "@/lib/logger/services/LoggerService";

/**
 * Delete business hours for the organization
 */
export async function deleteBusinessHours(id: string, lang: string): Promise<void> {
  try {
    // Delete the business hours
    await ProfileService.deleteBusinessHours(id);

    // Revalidate the business hours page
    revalidatePath(`/${lang}/protected/organization/profile/business-hours`);
  } catch (error) {
    logger.error(`Error deleting business hours: ${error}`);
    throw error;
  }
}
