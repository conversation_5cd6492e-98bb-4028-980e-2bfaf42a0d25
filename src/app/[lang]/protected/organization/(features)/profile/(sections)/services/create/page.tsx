import { i18n } from "@/lib/i18n/services/I18nService";
import { redirect } from "next/navigation";
import { createClient } from "@/lib/supabase/server";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { CreateServiceForm } from "../../../components/services/CreateServiceForm";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { ArrowLeft } from "lucide-react";

interface CreateServicePageProps {
  params: Promise<{ lang: string }>;
}

export default async function CreateServicePage({ params }: CreateServicePageProps) {
  const { lang } = await params;

  // Get the current user
  const supabase = await createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user) {
    redirect(`/${lang}/login`);
  }

  // Get dictionary for translations
  const dictionary = (await i18n.getDictionary(lang)) as {
    organization?: {
      addService?: string;
      addServiceDescription?: string;
      name?: string;
      description?: string;
      price?: string;
      duration?: string;
      status?: string;
      active?: string;
      inactive?: string;
      serviceCreated?: string;
    };
    common?: {
      back?: string;
      save?: string;
      saving?: string;
      required?: string;
    };
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{dictionary.organization?.addService || "Add Service"}</CardTitle>
        <CardDescription>
          {dictionary.organization?.addServiceDescription ||
            "Add a new service for your organization"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <CreateServiceForm lang={lang} dictionary={dictionary} />
      </CardContent>
      <CardFooter className="flex justify-between">
        <Link href={`/${lang}/protected/organization/profile/services`}>
          <Button variant="outline">
            <ArrowLeft className="h-4 w-4 mr-2" />
            {dictionary.common?.back || "Back"}
          </Button>
        </Link>
      </CardFooter>
    </Card>
  );
}
