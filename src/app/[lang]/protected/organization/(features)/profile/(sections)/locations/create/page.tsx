import { i18n } from "@/lib/i18n/services/I18nService";
import { redirect } from "next/navigation";
import { createClient } from "@/lib/supabase/server";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { CreateLocationForm } from "../../../components/locations/CreateLocationForm";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { ArrowLeft } from "lucide-react";

interface CreateLocationPageProps {
  params: Promise<{ lang: string }>;
}

export default async function CreateLocationPage({ params }: CreateLocationPageProps) {
  const { lang } = await params;

  // Get the current user
  const supabase = await createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user) {
    redirect(`/${lang}/login`);
  }

  // Get dictionary for translations
  const dictionary = i18n.getDictionary(lang);

  return (
    <Card>
      <CardHeader>
        <CardTitle>{dictionary.organization.addLocation}</CardTitle>
        <CardDescription>{"Add a new location for your organization"}</CardDescription>
      </CardHeader>
      <CardContent>
        <CreateLocationForm lang={lang} dictionary={dictionary} />
      </CardContent>
      <CardFooter className="flex justify-between">
        <Link href={`/${lang}/protected/organization/profile/locations`}>
          <Button variant="outline">
            <ArrowLeft className="h-4 w-4 mr-2" />
            {"Back"}
          </Button>
        </Link>
      </CardFooter>
    </Card>
  );
}
