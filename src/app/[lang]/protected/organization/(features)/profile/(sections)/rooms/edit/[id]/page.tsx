import { i18n } from "@/lib/i18n/services/I18nService";
import { redirect } from "next/navigation";
import { createClient } from "@/lib/supabase/server";
import {
  Card,
  CardContent,
  CardDescription,
  <PERSON>Footer,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { EditRoomForm } from "../../../../components/rooms/EditRoomForm";
import { ProfileService } from "../../../../lib/services/ProfileService";
import { Skeleton } from "@/components/ui/skeleton";
import { Suspense } from "react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";

interface EditRoomPageProps {
  params: Promise<{ lang: string; id: string }>;
}

export default async function EditRoomPage({ params }: EditRoomPageProps) {
  const { lang, id } = await params;

  // Get the current user
  const supabase = await createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user) {
    redirect(`/${lang}/login`);
  }

  // Get dictionary for translations
  const dictionary = (await i18n.getDictionary(lang)) as {
    organization?: {
      editRoom?: string;
      editRoomDescription?: string;
      name?: string;
      description?: string;
      capacity?: string;
      location?: string;
      status?: string;
      active?: string;
      inactive?: string;
      features?: string;
      roomUpdated?: string;
      addCustomFeature?: string;
      featureName?: string;
      featureDescription?: string;
      add?: string;
      cancel?: string;
    };
    common?: {
      back?: string;
      save?: string;
      saving?: string;
      required?: string;
    };
  };

  // Get the room to edit
  const room = await ProfileService.getRoomById(id);
  if (!room) {
    // If no room is found, redirect to the rooms list
    redirect(`/${lang}/protected/organization/profile/rooms`);
  }

  // Get locations for the dropdown
  const locations = await ProfileService.getLocations();

  return (
    <Card>
      <CardHeader>
        <CardTitle>{dictionary.organization?.editRoom || "Edit Room"}</CardTitle>
        <CardDescription>
          {dictionary.organization?.editRoomDescription || "Edit room details and features"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Suspense fallback={<Skeleton className="h-[400px] w-full" />}>
          <EditRoomForm lang={lang} dictionary={dictionary} room={room} locations={locations} />
        </Suspense>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Link href={`/${lang}/protected/organization/profile/rooms`}>
          <Button variant="outline">
            <ArrowLeft className="h-4 w-4 mr-2" />
            {dictionary.common?.back || "Back"}
          </Button>
        </Link>
      </CardFooter>
    </Card>
  );
}
