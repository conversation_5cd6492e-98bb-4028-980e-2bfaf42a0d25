import { i18n } from "@/lib/i18n/services/I18nService";
import { redirect } from "next/navigation";
import { createClient } from "@/lib/supabase/server";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { LocationsListView } from "../../components/locations/LocationsListView";
import { ProfileService } from "../../lib/services/ProfileService";
import { Skeleton } from "@/components/ui/skeleton";
import { Suspense } from "react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";

interface LocationsPageProps {
  params: Promise<{ lang: string }>;
}

export default async function LocationsPage({ params }: LocationsPageProps) {
  const { lang } = await params;

  // Get the current user
  const supabase = await createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user) {
    redirect(`/${lang}/login`);
  }

  // Get dictionary for translations
  const dictionary = i18n.getDictionary(lang);

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>
            {typeof dictionary.organization.locations === "string"
              ? dictionary.organization.locations
              : "Locations"}
          </CardTitle>
          <CardDescription>
            {typeof dictionary.organization.locationsDescription === "string"
              ? dictionary.organization.locationsDescription
              : "Manage locations for your organization"}
          </CardDescription>
        </div>
        <Link href={`/${lang}/protected/organization/profile/locations/create`}>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            {typeof dictionary.organization.addLocation === "string"
              ? dictionary.organization.addLocation
              : "Add Location"}
          </Button>
        </Link>
      </CardHeader>
      <CardContent>
        <Suspense fallback={<Skeleton className="h-[400px] w-full" />}>
          <LocationsListView
            lang={lang}
            dictionary={dictionary}
            locations={await ProfileService.getLocations()}
          />
        </Suspense>
      </CardContent>
    </Card>
  );
}
