import { i18n } from "@/lib/i18n/services/I18nService";
import { redirect } from "next/navigation";
import { createClient } from "@/lib/supabase/server";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import Link from "next/link";

interface ProfileSectionsLayoutProps {
  children: React.ReactNode;
  params: Promise<{ lang: string }>;
}

export default async function ProfileSectionsLayout({
  children,
  params,
}: ProfileSectionsLayoutProps) {
  const { lang } = await params;

  // Get the current user
  const supabase = await createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user) {
    redirect(`/${lang}/login`);
  }

  // Get dictionary for translations
  const dictionary = (await i18n.getDictionary(lang)) as {
    organization?: {
      profileManagement?: string;
      profileManagementDescription?: string;
      generalInfo?: string;
      locations?: string;
      services?: string;
      businessHours?: string;
      rooms?: string;
      users?: string;
    };
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-8">
        <Tabs defaultValue="general" className="w-full">
          <TabsList className="mb-4">
            <Link href={`/${lang}/protected/organization/profile/general`} className="w-full">
              <TabsTrigger value="general" className="w-full">
                {dictionary.organization?.generalInfo}
              </TabsTrigger>
            </Link>
            <Link href={`/${lang}/protected/organization/profile/locations`} className="w-full">
              <TabsTrigger value="locations" className="w-full">
                {dictionary.organization?.locations}
              </TabsTrigger>
            </Link>
            <Link href={`/${lang}/protected/organization/profile/services`} className="w-full">
              <TabsTrigger value="services" className="w-full">
                {dictionary.organization?.services}
              </TabsTrigger>
            </Link>
            <Link
              href={`/${lang}/protected/organization/profile/business-hours`}
              className="w-full"
            >
              <TabsTrigger value="business-hours" className="w-full">
                {dictionary.organization?.businessHours}
              </TabsTrigger>
            </Link>
            <Link href={`/${lang}/protected/organization/profile/rooms`} className="w-full">
              <TabsTrigger value="rooms" className="w-full">
                {dictionary.organization?.rooms}
              </TabsTrigger>
            </Link>
            <Link href={`/${lang}/protected/user/management/list`} className="w-full">
              <TabsTrigger value="users" className="w-full">
                {dictionary.organization?.users}
              </TabsTrigger>
            </Link>
          </TabsList>
        </Tabs>

        {children}
      </div>
    </div>
  );
}
