import { i18n } from "@/lib/i18n/services/I18nService";
import { redirect } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { BusinessHoursForm } from "../../../../components/business-hours/BusinessHoursForm";
import { ProfileService } from "../../../../lib/services/ProfileService";
import { Skeleton } from "@/components/ui/skeleton";
import { Suspense } from "react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";

interface EditBusinessHoursPageProps {
  params: Promise<{ lang: string; id: string }>;
}

export default async function EditBusinessHoursPage({ params }: EditBusinessHoursPageProps) {
  const { lang, id } = await params;

  // Get dictionary for translations
  const dictionary = i18n.getDictionary(lang);

  // Get the business hours to edit
  const businessHours = await ProfileService.getBusinessHoursById(id);
  if (!businessHours) {
    // If no business hours is found, redirect to the business hours list
    redirect(`/${lang}/protected/organization/profile/business-hours`);
  }

  // Get locations for the dropdown
  const locations = await ProfileService.getLocations();

  return (
    <Card>
      <CardHeader>
        <CardTitle>{dictionary.organization.editBusinessHours}</CardTitle>
        <CardDescription>{"Edit business hours for your organization"}</CardDescription>
      </CardHeader>
      <CardContent>
        <Suspense fallback={<Skeleton className="h-[400px] w-full" />}>
          <BusinessHoursForm
            lang={lang}
            dictionary={dictionary}
            businessHours={businessHours}
            locations={locations}
            isEdit={true}
          />
        </Suspense>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Link href={`/${lang}/protected/organization/profile/business-hours`}>
          <Button variant="outline">
            <ArrowLeft className="h-4 w-4 mr-2" />
            {"Back"}
          </Button>
        </Link>
      </CardFooter>
    </Card>
  );
}
