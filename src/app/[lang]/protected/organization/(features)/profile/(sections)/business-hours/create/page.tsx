import { i18n } from "@/lib/i18n/services/I18nService";
import { redirect } from "next/navigation";
import { createClient } from "@/lib/supabase/server";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { BusinessHoursForm } from "../../../components/business-hours/BusinessHoursForm";
import { ProfileService } from "../../../lib/services/ProfileService";
import { Skeleton } from "@/components/ui/skeleton";
import { Suspense } from "react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";

interface CreateBusinessHoursPageProps {
  params: Promise<{ lang: string }>;
}

export default async function CreateBusinessHoursPage({ params }: CreateBusinessHoursPageProps) {
  const { lang } = await params;

  // Get the current user
  const supabase = await createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user) {
    redirect(`/${lang}/login`);
  }

  // Get dictionary for translations
  const dictionary = i18n.getDictionary(lang);

  // Get locations for the dropdown
  const locations = await ProfileService.getLocations();

  return (
    <Card>
      <CardHeader>
        <CardTitle>{dictionary.organization.addBusinessHours}</CardTitle>
        <CardDescription>{"Add new business hours for your organization"}</CardDescription>
      </CardHeader>
      <CardContent>
        <Suspense fallback={<Skeleton className="h-[400px] w-full" />}>
          <BusinessHoursForm lang={lang} dictionary={dictionary} locations={locations} />
        </Suspense>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Link href={`/${lang}/protected/organization/profile/business-hours`}>
          <Button variant="outline">
            <ArrowLeft className="h-4 w-4 mr-2" />
            {"Back"}
          </Button>
        </Link>
      </CardFooter>
    </Card>
  );
}
