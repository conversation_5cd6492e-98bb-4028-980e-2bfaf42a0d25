import { i18n } from "@/lib/i18n/services/I18nService";
import { redirect } from "next/navigation";
import { createClient } from "@/lib/supabase/server";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { RoomsListView } from "../../components/rooms/RoomsListView";
import { ProfileService } from "../../lib/services/ProfileService";
import { Skeleton } from "@/components/ui/skeleton";
import { Suspense } from "react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";

interface RoomsPageProps {
  params: Promise<{ lang: string }>;
}

export default async function RoomsPage({ params }: RoomsPageProps) {
  const { lang } = await params;

  // Get the current user
  const supabase = await createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user) {
    redirect(`/${lang}/login`);
  }

  // Get dictionary for translations
  const dictionary = (await i18n.getDictionary(lang)) as {
    organization?: {
      rooms?: string;
      roomsDescription?: string;
      addRoom?: string;
      noRooms?: string;
      filterByLocation?: string;
      allLocations?: string;
      capacity?: string;
      location?: string;
      features?: string;
      deleteRoom?: string;
      deleteRoomConfirm?: string;
      cancel?: string;
      delete?: string;
    };
    common?: {
      edit?: string;
      delete?: string;
    };
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>{dictionary.organization?.rooms || "Rooms"}</CardTitle>
          <CardDescription>
            {dictionary.organization?.roomsDescription ||
              "Manage rooms for your organization's locations"}
          </CardDescription>
        </div>
        <Link href={`/${lang}/protected/organization/profile/rooms/create`}>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            {dictionary.organization?.addRoom || "Add Room"}
          </Button>
        </Link>
      </CardHeader>
      <CardContent>
        <Suspense fallback={<Skeleton className="h-[400px] w-full" />}>
          <RoomsListView
            lang={lang}
            dictionary={dictionary}
            rooms={await ProfileService.getRooms()}
            locations={await ProfileService.getLocations()}
          />
        </Suspense>
      </CardContent>
    </Card>
  );
}
