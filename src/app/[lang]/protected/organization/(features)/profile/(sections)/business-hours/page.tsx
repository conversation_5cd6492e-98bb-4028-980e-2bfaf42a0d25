import { i18n } from "@/lib/i18n/services/I18nService";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { BusinessHoursListView } from "../../components/business-hours/BusinessHoursListView";
import { ProfileService } from "../../lib/services/ProfileService";
import { Skeleton } from "@/components/ui/skeleton";
import { Suspense } from "react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";

interface BusinessHoursPageProps {
  params: Promise<{ lang: string }>;
}

export default async function BusinessHoursPage({ params }: BusinessHoursPageProps) {
  const { lang } = await params;

  // Get dictionary for translations
  const dictionary = i18n.getDictionary(lang);

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>{dictionary.organization.businessHours}</CardTitle>
          <CardDescription>{dictionary.organization.businessHoursDescription}</CardDescription>
        </div>
        <Link href={`/${lang}/protected/organization/profile/business-hours/create`}>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            {dictionary.organization.addBusinessHours}
          </Button>
        </Link>
      </CardHeader>
      <CardContent>
        <Suspense fallback={<Skeleton className="h-[400px] w-full" />}>
          <BusinessHoursListView
            lang={lang}
            dictionary={dictionary}
            businessHours={await ProfileService.getBusinessHours()}
            locations={await ProfileService.getLocations()}
          />
        </Suspense>
      </CardContent>
    </Card>
  );
}
