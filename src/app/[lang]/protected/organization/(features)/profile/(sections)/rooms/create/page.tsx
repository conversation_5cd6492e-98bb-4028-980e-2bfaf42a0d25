import { i18n } from "@/lib/i18n/services/I18nService";
import { redirect } from "next/navigation";
import { createClient } from "@/lib/supabase/server";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { CreateRoomForm } from "../../../components/rooms/CreateRoomForm";
import { ProfileService } from "../../../lib/services/ProfileService";
import { Skeleton } from "@/components/ui/skeleton";
import { Suspense } from "react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";

interface CreateRoomPageProps {
  params: Promise<{ lang: string }>;
}

export default async function CreateRoomPage({ params }: CreateRoomPageProps) {
  const { lang } = await params;

  // Get the current user
  const supabase = await createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user) {
    redirect(`/${lang}/login`);
  }

  // Get dictionary for translations
  const dictionary = (await i18n.getDictionary(lang)) as {
    organization?: {
      addRoom?: string;
      addRoomDescription?: string;
      name?: string;
      description?: string;
      capacity?: string;
      location?: string;
      status?: string;
      active?: string;
      inactive?: string;
      features?: string;
      roomCreated?: string;
      addCustomFeature?: string;
      featureName?: string;
      featureDescription?: string;
      add?: string;
      cancel?: string;
    };
    common?: {
      back?: string;
      save?: string;
      saving?: string;
      required?: string;
    };
  };

  // Get locations for the dropdown
  const locations = await ProfileService.getLocations();

  // If there are no locations, redirect to the locations page
  if (locations.length === 0) {
    redirect(`/${lang}/protected/organization/profile/locations`);
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{dictionary.organization?.addRoom || "Add Room"}</CardTitle>
        <CardDescription>
          {dictionary.organization?.addRoomDescription || "Add a new room to one of your locations"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Suspense fallback={<Skeleton className="h-[400px] w-full" />}>
          <CreateRoomForm lang={lang} dictionary={dictionary} locations={locations} />
        </Suspense>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Link href={`/${lang}/protected/organization/profile/rooms`}>
          <Button variant="outline">
            <ArrowLeft className="h-4 w-4 mr-2" />
            {dictionary.common?.back || "Back"}
          </Button>
        </Link>
      </CardFooter>
    </Card>
  );
}
