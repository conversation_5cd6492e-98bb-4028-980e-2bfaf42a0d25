import { i18n } from "@/lib/i18n/services/I18nService";
import { redirect } from "next/navigation";
import { createClient } from "@/lib/supabase/server";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

interface UsersPageProps {
  params: Promise<{ lang: string }>;
}

export default async function UsersPage({ params }: UsersPageProps) {
  const { lang } = await params;

  // Get the current user
  const supabase = await createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user) {
    redirect(`/${lang}/login`);
  }

  // Get dictionary for translations
  const dictionary = (await i18n.getDictionary(lang)) as {
    organization?: {
      users?: string;
      usersDescription?: string;
    };
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{dictionary.organization?.users}</CardTitle>
        <CardDescription>{dictionary.organization?.usersDescription}</CardDescription>
      </CardHeader>
      <CardContent>
        {/* Users management will go here */}
        <p className="text-muted-foreground">Users management coming soon...</p>
      </CardContent>
    </Card>
  );
}
