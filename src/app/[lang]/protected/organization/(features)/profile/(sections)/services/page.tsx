import { i18n } from "@/lib/i18n/services/I18nService";
import { redirect } from "next/navigation";
import { createClient } from "@/lib/supabase/server";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ServicesListView } from "../../components/services/ServicesListView";
import { ProfileService } from "../../lib/services/ProfileService";
import { Skeleton } from "@/components/ui/skeleton";
import { Suspense } from "react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";

interface ServicesPageProps {
  params: Promise<{ lang: string }>;
}

export default async function ServicesPage({ params }: ServicesPageProps) {
  const { lang } = await params;

  // Get the current user
  const supabase = await createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user) {
    redirect(`/${lang}/login`);
  }

  // Get dictionary for translations
  const dictionary = (await i18n.getDictionary(lang)) as {
    organization?: {
      services?: string;
      servicesDescription?: string;
      addService?: string;
      noServices?: string;
    };
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>
            {typeof dictionary.organization?.services === "string"
              ? dictionary.organization.services
              : "Services"}
          </CardTitle>
          <CardDescription>
            {typeof dictionary.organization?.servicesDescription === "string"
              ? dictionary.organization.servicesDescription
              : "Manage services for your organization"}
          </CardDescription>
        </div>
        <Link href={`/${lang}/protected/organization/profile/services/create`}>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            {dictionary.organization?.addService || "Add Service"}
          </Button>
        </Link>
      </CardHeader>
      <CardContent>
        <Suspense fallback={<Skeleton className="h-[400px] w-full" />}>
          <ServicesListView
            lang={lang}
            dictionary={dictionary}
            services={await ProfileService.getServices()}
          />
        </Suspense>
      </CardContent>
    </Card>
  );
}
