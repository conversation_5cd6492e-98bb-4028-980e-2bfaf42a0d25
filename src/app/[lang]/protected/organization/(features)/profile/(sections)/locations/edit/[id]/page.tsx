import { i18n } from "@/lib/i18n/services/I18nService";
import { redirect, notFound } from "next/navigation";
import { createClient } from "@/lib/supabase/server";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { EditLocationForm } from "../../../../components/locations/EditLocationForm";
import { ProfileService } from "../../../../lib/services/ProfileService";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { ArrowLeft } from "lucide-react";

interface EditLocationPageProps {
  params: Promise<{
    lang: string;
    id: string;
  }>;
}

export default async function EditLocationPage({ params }: EditLocationPageProps) {
  const resolvedParams = await params;
  const { lang, id } = resolvedParams;

  // Get the current user
  const supabase = await createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user) {
    redirect(`/${lang}/login`);
  }

  // Get dictionary for translations
  const dictionary = i18n.getDictionary(lang);

  // Get the location
  const locations = await ProfileService.getLocations();
  const location = locations.find((loc) => loc.id === id);

  if (!location) {
    notFound();
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{"Edit Location"}</CardTitle>
        <CardDescription>{"Edit location details"}</CardDescription>
      </CardHeader>
      <CardContent>
        <EditLocationForm lang={lang} dictionary={dictionary} location={location} />
      </CardContent>
      <CardFooter className="flex justify-between">
        <Link href={`/${lang}/protected/organization/profile/locations`}>
          <Button variant="outline">
            <ArrowLeft className="h-4 w-4 mr-2" />
            {"Back"}
          </Button>
        </Link>
      </CardFooter>
    </Card>
  );
}
