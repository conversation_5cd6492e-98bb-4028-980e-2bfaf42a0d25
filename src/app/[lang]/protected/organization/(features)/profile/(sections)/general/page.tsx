import { i18n } from "@/lib/i18n/services/I18nService";
import { redirect } from "next/navigation";
import { createClient } from "@/lib/supabase/server";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ProfileForm } from "../../components/general/ProfileForm";
import { ProfileService } from "../../lib/services/ProfileService";
import { Skeleton } from "@/components/ui/skeleton";
import { Suspense } from "react";

interface GeneralProfilePageProps {
  params: Promise<{ lang: string }>;
}

export default async function GeneralProfilePage({ params }: GeneralProfilePageProps) {
  const { lang } = await params;

  // Get the current user
  const supabase = await createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user) {
    redirect(`/${lang}/login`);
  }

  // Get dictionary for translations
  const dictionary = i18n.getDictionary(lang);

  // Get the current organization
  const organization = await ProfileService.getCurrentOrganization();

  if (!organization) {
    // If no organization is found, redirect to the dashboard
    redirect(`/${lang}/protected/dashboard`);
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>
          {typeof dictionary.organization.generalInfo === "string"
            ? dictionary.organization.generalInfo
            : "General Information"}
        </CardTitle>
        <CardDescription>
          {typeof dictionary.organization.generalInfoDescription === "string"
            ? dictionary.organization.generalInfoDescription
            : "Manage general information for your organization"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Suspense fallback={<Skeleton className="h-[400px] w-full" />}>
          <ProfileForm lang={lang} dictionary={dictionary} organization={organization} />
        </Suspense>
      </CardContent>
    </Card>
  );
}
