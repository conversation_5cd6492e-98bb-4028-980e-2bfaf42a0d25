import { i18n } from "@/lib/i18n/services/I18nService";
import { redirect, notFound } from "next/navigation";
import { createClient } from "@/lib/supabase/server";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { EditServiceForm } from "../../../../components/services/EditServiceForm";
import { ProfileService } from "../../../../lib/services/ProfileService";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { ArrowLeft } from "lucide-react";

interface EditServicePageProps {
  params: Promise<{
    lang: string;
    id: string;
  }>;
}

export default async function EditServicePage({ params }: EditServicePageProps) {
  const resolvedParams = await params;
  const { lang, id } = resolvedParams;

  // Get the current user
  const supabase = await createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user) {
    redirect(`/${lang}/login`);
  }

  // Get dictionary for translations
  const dictionary = (await i18n.getDictionary(lang)) as {
    organization?: {
      editService?: string;
      editServiceDescription?: string;
      name?: string;
      description?: string;
      price?: string;
      duration?: string;
      status?: string;
      active?: string;
      inactive?: string;
      serviceUpdated?: string;
    };
    common?: {
      back?: string;
      save?: string;
      saving?: string;
      required?: string;
    };
  };

  // Get the service
  const services = await ProfileService.getServices();
  const service = services.find((svc) => svc.id === id);

  if (!service) {
    notFound();
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{dictionary.organization?.editService || "Edit Service"}</CardTitle>
        <CardDescription>
          {dictionary.organization?.editServiceDescription || "Edit service details"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <EditServiceForm lang={lang} dictionary={dictionary} service={service} />
      </CardContent>
      <CardFooter className="flex justify-between">
        <Link href={`/${lang}/protected/organization/profile/services`}>
          <Button variant="outline">
            <ArrowLeft className="h-4 w-4 mr-2" />
            {dictionary.common?.back || "Back"}
          </Button>
        </Link>
      </CardFooter>
    </Card>
  );
}
