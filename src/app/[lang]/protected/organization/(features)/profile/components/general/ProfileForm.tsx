"use client";

import { useActionState } from "react";
import { updateProfile } from "../../actions/updateProfile";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Organization, EmailsJson, PhonesJson } from "../../lib/types";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { CheckCircle2, AlertCircle } from "lucide-react";
import { Dictionary } from "@/lib/i18n/services/I18nService";

interface ProfileFormProps {
  lang: string;
  dictionary: Dictionary;
  organization: Organization;
}

export function ProfileForm({ lang, dictionary, organization }: ProfileFormProps) {
  const [state, formAction, pending] = useActionState(updateProfile, {
    success: false,
    error: "",
  });

  // Parse emails and phones from JSONB
  const emails = organization.emails as EmailsJson;
  const phones = organization.phones as PhonesJson;

  return (
    <div className="space-y-6">
      {state.success && (
        <Alert className="bg-green-50 border-green-200">
          <CheckCircle2 className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-600">
            {dictionary.organization?.profileUpdated}
          </AlertDescription>
        </Alert>
      )}

      {state.error && (
        <Alert className="bg-red-50 border-red-200">
          <AlertCircle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-600">{state.error}</AlertDescription>
        </Alert>
      )}

      <form action={formAction} className="space-y-6">
        <input type="hidden" name="lang" value={lang} />

        <div className="space-y-4">
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="name">
                {"Name"} <span className="text-red-500">*</span>
              </Label>
              <Input id="name" name="name" defaultValue={organization.name} required />
            </div>

            <div className="space-y-2">
              <Label htmlFor="address">{"Address"}</Label>
              <Input id="address" name="address" defaultValue={organization.address || ""} />
            </div>
          </div>

          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="email">
                {"Email"} <span className="text-red-500">*</span>
              </Label>
              <Input
                id="email"
                name="email"
                type="email"
                defaultValue={emails?.info || ""}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="supportEmail">{"Support Email"}</Label>
              <Input
                id="supportEmail"
                name="supportEmail"
                type="email"
                defaultValue={emails?.support || ""}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="phone">
                {"Phone"} <span className="text-red-500">*</span>
              </Label>
              <Input id="phone" name="phone" defaultValue={phones?.main || ""} required />
            </div>

            <div className="space-y-2">
              <Label htmlFor="fax">{"Fax"}</Label>
              <Input id="fax" name="fax" defaultValue={phones?.fax || ""} />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="website">{"Website"}</Label>
            <Input id="website" name="website" defaultValue={organization.website_url || ""} />
          </div>
        </div>

        <div className="flex justify-end space-x-4">
          <Button type="submit" disabled={pending}>
            {pending ? "Saving..." : "Save"}
          </Button>
        </div>
      </form>
    </div>
  );
}
