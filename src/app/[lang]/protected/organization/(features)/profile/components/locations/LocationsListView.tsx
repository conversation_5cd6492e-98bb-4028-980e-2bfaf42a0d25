"use client";

import { Location } from "../../lib/types";
import { LocationCard } from "./LocationCard";

interface LocationsListViewProps {
  locations: Location[];
  lang: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  dictionary: any;
}

export function LocationsListView({ locations, lang, dictionary }: LocationsListViewProps) {
  if (locations.length === 0) {
    return (
      <div className="text-center p-6 bg-muted/10 rounded-md">
        <p className="text-muted-foreground">
          {dictionary.organization?.noLocations || "No locations found. Add your first location."}
        </p>
      </div>
    );
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {locations.map((location) => (
        <LocationCard key={location.id} location={location} lang={lang} dictionary={dictionary} />
      ))}
    </div>
  );
}
