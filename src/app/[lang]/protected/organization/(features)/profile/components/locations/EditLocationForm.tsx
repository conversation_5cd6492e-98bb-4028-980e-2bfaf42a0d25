"use client";

import { useActionState } from "react";
import { updateLocation } from "../../actions/updateLocation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Location } from "../../lib/types";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { CheckCircle2, AlertCircle } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface EditLocationFormProps {
  lang: string;
  dictionary: {
    organization?: {
      name?: string;
      address?: string;
      email?: string;
      phone?: string;
      status?: string;
      active?: string;
      inactive?: string;
      locationUpdated?: string;
    };
    common?: {
      save?: string;
      saving?: string;
      required?: string;
    };
  };
  location: Location;
  onSuccess?: () => void;
}

// Define the action state type for the form
type _ActionState = {
  success: boolean;
  error: string;
};

export function EditLocationForm({ lang, dictionary, location, onSuccess }: EditLocationFormProps) {
  const [state, formAction, pending] = useActionState(updateLocation, {
    success: false,
    error: "",
  });

  // Call onSuccess callback when the action is successful
  if (state.success && onSuccess) {
    onSuccess();
  }

  return (
    <div className="space-y-6">
      {state.success && (
        <Alert className="bg-green-50 border-green-200">
          <CheckCircle2 className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-600">
            {dictionary.organization?.locationUpdated || "Location updated successfully"}
          </AlertDescription>
        </Alert>
      )}

      {state.error && (
        <Alert className="bg-red-50 border-red-200">
          <AlertCircle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-600">{state.error}</AlertDescription>
        </Alert>
      )}

      <form action={formAction} className="space-y-6">
        <input type="hidden" name="lang" value={lang} />
        <input type="hidden" name="id" value={location.id} />

        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">
              {dictionary.organization?.name || "Name"} <span className="text-red-500">*</span>
            </Label>
            <Input id="name" name="name" defaultValue={location.name} required />
          </div>

          <div className="space-y-2">
            <Label htmlFor="address">
              {dictionary.organization?.address || "Address"}{" "}
              <span className="text-red-500">*</span>
            </Label>
            <Input id="address" name="address" defaultValue={location.address || ""} required />
          </div>

          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="email">
                {dictionary.organization?.email || "Email"} <span className="text-red-500">*</span>
              </Label>
              <Input
                id="email"
                name="email"
                type="email"
                defaultValue={
                  typeof location.emails === "object"
                    ? (location.emails as Record<string, string>)?.main || ""
                    : ""
                }
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="phone">
                {dictionary.organization?.phone || "Phone"} <span className="text-red-500">*</span>
              </Label>
              <Input
                id="phone"
                name="phone"
                defaultValue={
                  typeof location.phones === "object"
                    ? (location.phones as Record<string, string>)?.main || ""
                    : ""
                }
                required
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="status">
              {dictionary.organization?.status || "Status"} <span className="text-red-500">*</span>
            </Label>
            <Select name="status" defaultValue={location.status || "active"}>
              <SelectTrigger>
                <SelectValue placeholder={dictionary.organization?.status || "Status"} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="active">
                  {dictionary.organization?.active || "Active"}
                </SelectItem>
                <SelectItem value="inactive">
                  {dictionary.organization?.inactive || "Inactive"}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="flex justify-end space-x-4">
          <Button type="submit" disabled={pending}>
            {pending ? dictionary.common?.saving || "Saving..." : dictionary.common?.save || "Save"}
          </Button>
        </div>
      </form>
    </div>
  );
}
