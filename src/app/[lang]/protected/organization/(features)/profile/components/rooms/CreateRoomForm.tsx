"use client";

import { useActionState } from "react";
import { createRoom } from "../../actions/createRoom";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { CheckCircle2, AlertCircle } from "lucide-react";
import { Location, RoomFeaturesJson } from "../../lib/types";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { RoomFeatureSelector } from "./RoomFeatureSelector";
import { useState } from "react";

interface CreateRoomFormProps {
  lang: string;
  locations: Location[];
  dictionary: {
    organization?: {
      name?: string;
      description?: string;
      capacity?: string;
      location?: string;
      status?: string;
      active?: string;
      inactive?: string;
      features?: string;
      roomCreated?: string;
      addCustomFeature?: string;
      featureName?: string;
      featureDescription?: string;
      add?: string;
      cancel?: string;
    };
    common?: {
      save?: string;
      saving?: string;
      required?: string;
    };
  };
}

export function CreateRoomForm({ lang, locations, dictionary }: CreateRoomFormProps) {
  const [state, formAction, pending] = useActionState(createRoom, {
    success: false,
    error: "",
  });

  const [selectedFeatures, setSelectedFeatures] = useState<RoomFeaturesJson>([]);

  // Handle features change
  const handleFeaturesChange = (features: RoomFeaturesJson) => {
    setSelectedFeatures(features);
  };

  return (
    <div className="space-y-6">
      {state.success && (
        <Alert className="bg-green-50 border-green-200">
          <CheckCircle2 className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-600">
            {dictionary.organization?.roomCreated || "Room created successfully"}
          </AlertDescription>
        </Alert>
      )}

      {state.error && (
        <Alert className="bg-red-50 border-red-200">
          <AlertCircle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-600">{state.error}</AlertDescription>
        </Alert>
      )}

      <form action={formAction} className="space-y-6">
        <input type="hidden" name="lang" value={lang} />

        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">
              {dictionary.organization?.name || "Name"} <span className="text-red-500">*</span>
            </Label>
            <Input id="name" name="name" placeholder="e.g., Conference Room A" required />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">
              {dictionary.organization?.description || "Description"}
            </Label>
            <Textarea
              id="description"
              name="description"
              placeholder="e.g., Large conference room with projector"
              rows={3}
            />
          </div>

          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="capacity">
                {dictionary.organization?.capacity || "Capacity"}{" "}
                <span className="text-red-500">*</span>
              </Label>
              <Input
                id="capacity"
                name="capacity"
                type="number"
                min="1"
                placeholder="e.g., 10"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="locationId">
                {dictionary.organization?.location || "Location"}{" "}
                <span className="text-red-500">*</span>
              </Label>
              <Select name="locationId" required>
                <SelectTrigger id="locationId">
                  <SelectValue
                    placeholder={dictionary.organization?.location || "Select location"}
                  />
                </SelectTrigger>
                <SelectContent>
                  {locations.map((location) => (
                    <SelectItem key={location.id} value={location.id}>
                      {location.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="status">{dictionary.organization?.status || "Status"}</Label>
            <Select name="status" defaultValue="active">
              <SelectTrigger id="status">
                <SelectValue placeholder={dictionary.organization?.status || "Status"} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="active">
                  {dictionary.organization?.active || "Active"}
                </SelectItem>
                <SelectItem value="inactive">
                  {dictionary.organization?.inactive || "Inactive"}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="features">{dictionary.organization?.features || "Features"}</Label>
            <RoomFeatureSelector
              selectedFeatures={selectedFeatures}
              onChange={handleFeaturesChange}
              dictionary={dictionary}
            />
          </div>
        </div>

        <Button type="submit" disabled={pending} className="w-full">
          {pending ? dictionary.common?.saving || "Saving..." : dictionary.common?.save || "Save"}
        </Button>
      </form>
    </div>
  );
}
