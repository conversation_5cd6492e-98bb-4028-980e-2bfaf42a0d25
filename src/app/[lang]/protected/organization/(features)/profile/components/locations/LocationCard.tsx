"use client";

import { useState } from "react";
import { Location } from "../../lib/types";
import { Button } from "@/components/ui/button";
import { Pencil, Trash2 } from "lucide-react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { deleteLocation } from "../../actions/deleteLocation";
import Link from "next/link";

interface LocationCardProps {
  location: Location;
  lang: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  dictionary: any;
}

export function LocationCard({ location, lang, dictionary }: LocationCardProps) {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const handleDeleteLocation = () => {
    setIsDeleteDialogOpen(true);
  };

  const confirmDeleteLocation = async () => {
    await deleteLocation(location.id);
    setIsDeleteDialogOpen(false);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-start">
          <CardTitle className="text-xl">{location.name}</CardTitle>
          <Badge variant={location.status === "active" ? "default" : "secondary"}>
            {location.status === "active"
              ? dictionary.organization?.active || "Active"
              : dictionary.organization?.inactive || "Inactive"}
          </Badge>
        </div>
        <CardDescription>{location.address}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div className="text-sm">
            <span className="font-medium">{dictionary.organization?.phone}: </span>
            {typeof location.phones === "object"
              ? (location.phones as Record<string, string>)?.main || ""
              : ""}
          </div>
          <div className="text-sm">
            <span className="font-medium">{dictionary.organization?.email}: </span>
            {typeof location.emails === "object"
              ? (location.emails as Record<string, string>)?.main || ""
              : ""}
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-end space-x-2">
        <Link href={`/${lang}/protected/organization/profile/locations/edit/${location.id}`}>
          <Button variant="outline" size="sm">
            <Pencil className="h-4 w-4 mr-2" />
            {dictionary.common?.edit || "Edit"}
          </Button>
        </Link>
        <Button variant="destructive" size="sm" onClick={handleDeleteLocation}>
          <Trash2 className="h-4 w-4 mr-2" />
          {dictionary.common?.delete || "Delete"}
        </Button>
      </CardFooter>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {dictionary.organization?.deleteLocation || "Delete Location"}
            </DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p>
              {dictionary.organization?.deleteLocationConfirm ||
                "Are you sure you want to delete this location?"}
            </p>
            <p className="font-medium mt-2">{location.name}</p>
          </div>
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              {dictionary.organization?.cancel || "Cancel"}
            </Button>
            <Button variant="destructive" onClick={confirmDeleteLocation}>
              {dictionary.organization?.delete || "Delete"}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </Card>
  );
}
