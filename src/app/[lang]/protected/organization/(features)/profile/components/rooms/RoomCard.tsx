"use client";

import { Room, RoomFeature } from "../../lib/types";
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Pencil, Trash2, Users } from "lucide-react";
import Link from "next/link";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useState } from "react";
import { deleteRoom } from "../../actions/deleteRoom";

interface RoomCardProps {
  room: Room;
  locationName: string;
  lang: string;
  dictionary: {
    organization?: {
      capacity?: string;
      location?: string;
      features?: string;
      deleteRoom?: string;
      deleteRoomConfirm?: string;
      cancel?: string;
      delete?: string;
    };
    common?: {
      edit?: string;
      delete?: string;
    };
  };
}

export function RoomCard({ room, locationName, lang, dictionary }: RoomCardProps) {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // Handle delete button click
  const handleDeleteClick = () => {
    setIsDeleteDialogOpen(true);
  };

  // Handle delete confirmation
  const handleDeleteConfirm = async () => {
    await deleteRoom(room.id, lang);
    setIsDeleteDialogOpen(false);
  };

  // Parse features from JSON (if available)
  const features: RoomFeature[] = [];

  return (
    <Card className="h-full flex flex-col">
      <CardContent className="pt-6 flex-grow">
        <div className="flex justify-between items-start mb-4">
          <div>
            <h3 className="text-lg font-semibold">{room.name}</h3>
            <p className="text-sm text-muted-foreground">{locationName}</p>
          </div>
          <Badge variant={room.status === "active" ? "default" : "secondary"}>{room.status}</Badge>
        </div>

        {room.description && (
          <p className="text-sm text-muted-foreground mb-4">{room.description}</p>
        )}

        <div className="flex items-center mb-4">
          <Users className="h-4 w-4 mr-2 text-muted-foreground" />
          <span className="text-sm">
            {dictionary.organization?.capacity}: {room.capacity}
          </span>
        </div>

        {features.length > 0 && (
          <div className="mt-4">
            <p className="text-sm font-medium mb-2">{dictionary.organization?.features}:</p>
            <div className="flex flex-wrap gap-2">
              {features.map((feature) => (
                <Badge key={feature.id} variant="outline">
                  {feature.name}
                </Badge>
              ))}
            </div>
          </div>
        )}
      </CardContent>

      <CardFooter className="border-t pt-4 flex justify-between">
        <Link href={`/${lang}/protected/organization/profile/rooms/edit/${room.id}`}>
          <Button variant="outline" size="sm">
            <Pencil className="h-4 w-4 mr-2" />
            {dictionary.common?.edit || "Edit"}
          </Button>
        </Link>
        <Button variant="outline" size="sm" onClick={handleDeleteClick}>
          <Trash2 className="h-4 w-4 mr-2" />
          {dictionary.common?.delete || "Delete"}
        </Button>
      </CardFooter>

      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {dictionary.organization?.deleteRoom || "Delete Room"}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {dictionary.organization?.deleteRoomConfirm ||
                "Are you sure you want to delete this room?"}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{dictionary.organization?.cancel || "Cancel"}</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              className="bg-destructive text-destructive-foreground"
            >
              {dictionary.organization?.delete || "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Card>
  );
}
