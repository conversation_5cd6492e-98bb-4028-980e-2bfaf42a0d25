"use client";

import { Service } from "../../lib/types";
import { ServiceCard } from "./ServiceCard";

interface ServicesListViewProps {
  services: Service[];
  lang: string;
  dictionary: {
    organization?: {
      noServices?: string;
      price?: string;
      duration?: string;
      minutes?: string;
      active?: string;
      inactive?: string;
      deleteService?: string;
      deleteServiceConfirm?: string;
      cancel?: string;
      delete?: string;
    };
    common?: {
      edit?: string;
      delete?: string;
    };
  };
}

export function ServicesListView({ services, lang, dictionary }: ServicesListViewProps) {
  if (services.length === 0) {
    return (
      <div className="text-center p-6 bg-muted/10 rounded-md">
        <p className="text-muted-foreground">
          {dictionary.organization?.noServices || "No services found. Add your first service."}
        </p>
      </div>
    );
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {services.map((service) => (
        <ServiceCard key={service.id} service={service} lang={lang} dictionary={dictionary} />
      ))}
    </div>
  );
}
