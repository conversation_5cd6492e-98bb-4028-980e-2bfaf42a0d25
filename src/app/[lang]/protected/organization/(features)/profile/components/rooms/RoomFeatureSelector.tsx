"use client";

import { useState, useEffect } from "react";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { COMMON_ROOM_FEATURES, RoomFeature, RoomFeaturesJson } from "../../lib/types";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Plus, X } from "lucide-react";

interface RoomFeatureSelectorProps {
  selectedFeatures: RoomFeaturesJson;
  onChange: (features: RoomFeaturesJson) => void;
  dictionary: {
    organization?: {
      features?: string;
      addCustomFeature?: string;
      featureName?: string;
      featureDescription?: string;
      add?: string;
      cancel?: string;
    };
  };
}

export function RoomFeatureSelector({
  selectedFeatures,
  onChange,
  dictionary,
}: RoomFeatureSelectorProps) {
  const [features, setFeatures] = useState<RoomFeaturesJson>(selectedFeatures || []);
  const [customFeatureName, setCustomFeatureName] = useState("");
  const [customFeatureDescription, setCustomFeatureDescription] = useState("");
  const [showCustomFeatureForm, setShowCustomFeatureForm] = useState(false);

  // Update parent component when features change
  useEffect(() => {
    onChange(features);
  }, [features, onChange]);

  // Check if a feature is selected
  const isFeatureSelected = (featureId: string) => {
    return features.some((feature) => feature.id === featureId);
  };

  // Handle feature selection
  const handleFeatureChange = (featureId: string, checked: boolean) => {
    if (checked) {
      // Add feature
      const featureToAdd = COMMON_ROOM_FEATURES.find((feature) => feature.id === featureId);
      if (featureToAdd) {
        setFeatures([...features, featureToAdd]);
      }
    } else {
      // Remove feature
      setFeatures(features.filter((feature) => feature.id !== featureId));
    }
  };

  // Handle custom feature addition
  const handleAddCustomFeature = () => {
    if (customFeatureName.trim()) {
      const newFeature: RoomFeature = {
        id: `custom-${Date.now()}`,
        name: customFeatureName.trim(),
        description: customFeatureDescription.trim() || undefined,
      };
      setFeatures([...features, newFeature]);
      setCustomFeatureName("");
      setCustomFeatureDescription("");
      setShowCustomFeatureForm(false);
    }
  };

  // Handle custom feature removal
  const handleRemoveFeature = (featureId: string) => {
    setFeatures(features.filter((feature) => feature.id !== featureId));
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-wrap gap-2 mb-4">
        {features.map((feature) => (
          <Badge key={feature.id} variant="secondary" className="flex items-center gap-1">
            {feature.name}
            <button
              type="button"
              onClick={() => handleRemoveFeature(feature.id)}
              className="ml-1 rounded-full hover:bg-muted p-0.5"
            >
              <X className="h-3 w-3" />
            </button>
          </Badge>
        ))}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {COMMON_ROOM_FEATURES.map((feature) => (
          <div key={feature.id} className="flex items-start space-x-2">
            <Checkbox
              id={`feature-${feature.id}`}
              checked={isFeatureSelected(feature.id)}
              onCheckedChange={(checked) => handleFeatureChange(feature.id, checked as boolean)}
            />
            <div className="grid gap-1.5 leading-none">
              <Label htmlFor={`feature-${feature.id}`} className="text-sm font-medium">
                {feature.name}
              </Label>
              {feature.description && (
                <p className="text-xs text-muted-foreground">{feature.description}</p>
              )}
            </div>
          </div>
        ))}
      </div>

      {showCustomFeatureForm ? (
        <div className="border rounded-md p-4 mt-4">
          <h4 className="text-sm font-medium mb-2">
            {dictionary.organization?.addCustomFeature || "Add Custom Feature"}
          </h4>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="customFeatureName">
                {dictionary.organization?.featureName || "Feature Name"}
              </Label>
              <Input
                id="customFeatureName"
                value={customFeatureName}
                onChange={(e) => setCustomFeatureName(e.target.value)}
                placeholder="e.g., Soundproof"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="customFeatureDescription">
                {dictionary.organization?.featureDescription || "Description (optional)"}
              </Label>
              <Input
                id="customFeatureDescription"
                value={customFeatureDescription}
                onChange={(e) => setCustomFeatureDescription(e.target.value)}
                placeholder="e.g., Room is soundproofed for privacy"
              />
            </div>
            <div className="flex justify-end space-x-2">
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => setShowCustomFeatureForm(false)}
              >
                {dictionary.organization?.cancel || "Cancel"}
              </Button>
              <Button
                type="button"
                size="sm"
                onClick={handleAddCustomFeature}
                disabled={!customFeatureName.trim()}
              >
                {dictionary.organization?.add || "Add"}
              </Button>
            </div>
          </div>
        </div>
      ) : (
        <Button
          type="button"
          variant="outline"
          size="sm"
          className="mt-2"
          onClick={() => setShowCustomFeatureForm(true)}
        >
          <Plus className="h-4 w-4 mr-2" />
          {dictionary.organization?.addCustomFeature || "Add Custom Feature"}
        </Button>
      )}

      <input type="hidden" name="features" value={JSON.stringify(features)} />
    </div>
  );
}
