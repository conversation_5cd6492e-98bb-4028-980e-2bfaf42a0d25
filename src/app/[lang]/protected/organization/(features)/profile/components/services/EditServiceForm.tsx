"use client";

import { useActionState } from "react";
import { updateService } from "../../actions/updateService";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Service } from "../../lib/types";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { CheckCircle2, AlertCircle } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";

interface EditServiceFormProps {
  lang: string;
  dictionary: {
    organization?: {
      name?: string;
      description?: string;
      price?: string;
      duration?: string;
      status?: string;
      active?: string;
      inactive?: string;
      serviceUpdated?: string;
    };
    common?: {
      save?: string;
      saving?: string;
      required?: string;
    };
  };
  service: Service;
  onSuccess?: () => void;
}

// Define the action state type for the form
type _ActionState = {
  success: boolean;
  error: string;
};

export function EditServiceForm({ lang, dictionary, service, onSuccess }: EditServiceFormProps) {
  const [state, formAction, pending] = useActionState(updateService, {
    success: false,
    error: "",
  });

  // Call onSuccess callback when the action is successful
  if (state.success && onSuccess) {
    onSuccess();
  }

  return (
    <div className="space-y-6">
      {state.success && (
        <Alert className="bg-green-50 border-green-200">
          <CheckCircle2 className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-600">
            {dictionary.organization?.serviceUpdated || "Service updated successfully"}
          </AlertDescription>
        </Alert>
      )}

      {state.error && (
        <Alert className="bg-red-50 border-red-200">
          <AlertCircle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-600">{state.error}</AlertDescription>
        </Alert>
      )}

      <form action={formAction} className="space-y-6">
        <input type="hidden" name="lang" value={lang} />
        <input type="hidden" name="id" value={service.id} />

        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">
              {dictionary.organization?.name || "Name"} <span className="text-red-500">*</span>
            </Label>
            <Input id="name" name="name" defaultValue={service.name} required />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">
              {dictionary.organization?.description || "Description"}
            </Label>
            <Textarea
              id="description"
              name="description"
              rows={3}
              defaultValue={service.description || ""}
            />
          </div>

          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="price">
                {dictionary.organization?.price || "Price"} <span className="text-red-500">*</span>
              </Label>
              <Input
                id="price"
                name="price"
                type="number"
                step="0.01"
                min="0"
                defaultValue={service.price}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="duration">
                {dictionary.organization?.duration || "Duration"}{" "}
                <span className="text-red-500">*</span>
              </Label>
              <Input
                id="duration"
                name="duration"
                type="number"
                min="1"
                defaultValue={service.duration}
                required
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="status">
              {dictionary.organization?.status || "Status"} <span className="text-red-500">*</span>
            </Label>
            <Select name="status" defaultValue={service.status || "active"}>
              <SelectTrigger>
                <SelectValue placeholder={dictionary.organization?.status || "Status"} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="active">
                  {dictionary.organization?.active || "Active"}
                </SelectItem>
                <SelectItem value="inactive">
                  {dictionary.organization?.inactive || "Inactive"}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="flex justify-end space-x-4">
          <Button type="submit" disabled={pending}>
            {pending ? dictionary.common?.saving || "Saving..." : dictionary.common?.save || "Save"}
          </Button>
        </div>
      </form>
    </div>
  );
}
