"use client";

import { Room, Location } from "../../lib/types";
import { RoomCard } from "./RoomCard";
import { Card, CardContent } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { useState } from "react";

interface RoomsListViewProps {
  rooms: Room[];
  locations: Location[];
  lang: string;
  dictionary: {
    organization?: {
      noRooms?: string;
      filterByLocation?: string;
      allLocations?: string;
      capacity?: string;
      location?: string;
      features?: string;
      deleteRoom?: string;
      deleteRoomConfirm?: string;
      cancel?: string;
      delete?: string;
    };
    common?: {
      edit?: string;
      delete?: string;
    };
  };
}

export function RoomsListView({ rooms, locations, lang, dictionary }: RoomsListViewProps) {
  const [selectedLocationId, setSelectedLocationId] = useState<string>("all");

  // Get location name by ID
  const getLocationName = (locationId: string) => {
    const location = locations.find((loc) => loc.id === locationId);
    return location ? location.name : "";
  };

  // Filter rooms by location
  const filteredRooms =
    selectedLocationId === "all"
      ? rooms
      : rooms.filter((room) => room.location_id === selectedLocationId);

  if (rooms.length === 0) {
    return (
      <Card>
        <CardContent className="pt-6">
          <p className="text-center text-muted-foreground">
            {dictionary.organization?.noRooms || "No rooms found. Add your first room."}
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="w-full sm:w-64">
          <Label htmlFor="locationFilter" className="mb-2 block">
            {dictionary.organization?.filterByLocation || "Filter by Location"}
          </Label>
          <Select
            value={selectedLocationId}
            onValueChange={(value) => setSelectedLocationId(value)}
          >
            <SelectTrigger id="locationFilter">
              <SelectValue placeholder={dictionary.organization?.allLocations || "All Locations"} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">
                {dictionary.organization?.allLocations || "All Locations"}
              </SelectItem>
              {locations.map((location) => (
                <SelectItem key={location.id} value={location.id}>
                  {location.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredRooms.map((room) => (
          <RoomCard
            key={room.id}
            room={room}
            locationName={getLocationName(room.location_id)}
            lang={lang}
            dictionary={dictionary}
          />
        ))}
      </div>
    </div>
  );
}
