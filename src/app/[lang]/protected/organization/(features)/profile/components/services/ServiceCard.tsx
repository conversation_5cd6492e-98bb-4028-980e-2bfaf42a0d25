"use client";

import { useState } from "react";
import { Service } from "../../lib/types";
import { Button } from "@/components/ui/button";
import { Pencil, Trash2 } from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { deleteService } from "../../actions/deleteService";
import Link from "next/link";

interface ServiceCardProps {
  service: Service;
  lang: string;
  dictionary: {
    organization?: {
      price?: string;
      duration?: string;
      minutes?: string;
      active?: string;
      inactive?: string;
      deleteService?: string;
      deleteServiceConfirm?: string;
      cancel?: string;
      delete?: string;
    };
    common?: {
      edit?: string;
      delete?: string;
    };
  };
}

export function ServiceCard({ service, lang, dictionary }: ServiceCardProps) {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const handleDeleteService = () => {
    setIsDeleteDialogOpen(true);
  };

  const confirmDeleteService = async () => {
    await deleteService(service.id);
    setIsDeleteDialogOpen(false);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-start">
          <CardTitle className="text-xl">{service.name}</CardTitle>
          <Badge variant={service.status === "active" ? "default" : "secondary"}>
            {service.status === "active"
              ? dictionary.organization?.active || "Active"
              : dictionary.organization?.inactive || "Inactive"}
          </Badge>
        </div>
        <CardDescription>{service.description}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div className="text-sm">
            <span className="font-medium">{dictionary.organization?.price}: </span>$
            {service.price.toFixed(2)}
          </div>
          <div className="text-sm">
            <span className="font-medium">{dictionary.organization?.duration}: </span>
            {service.duration} {dictionary.organization?.minutes || "minutes"}
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-end space-x-2">
        <Link href={`/${lang}/protected/organization/profile/services/edit/${service.id}`}>
          <Button variant="outline" size="sm">
            <Pencil className="h-4 w-4 mr-2" />
            {dictionary.common?.edit || "Edit"}
          </Button>
        </Link>
        <Button variant="destructive" size="sm" onClick={handleDeleteService}>
          <Trash2 className="h-4 w-4 mr-2" />
          {dictionary.common?.delete || "Delete"}
        </Button>
      </CardFooter>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{dictionary.organization?.deleteService || "Delete Service"}</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p>
              {dictionary.organization?.deleteServiceConfirm ||
                "Are you sure you want to delete this service?"}
            </p>
            <p className="font-medium mt-2">{service.name}</p>
          </div>
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              {dictionary.organization?.cancel || "Cancel"}
            </Button>
            <Button variant="destructive" onClick={confirmDeleteService}>
              {dictionary.organization?.delete || "Delete"}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </Card>
  );
}
