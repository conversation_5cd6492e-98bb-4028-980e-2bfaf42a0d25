import { createClient } from "@/lib/supabase/server";
import {
  Organization,
  OrganizationUpdate,
  Location,
  LocationInsert,
  LocationUpdate,
  Service,
  ServiceInsert,
  ServiceUpdate,
  BusinessHours,
  BusinessHoursInsert,
  BusinessHoursUpdate,
  Room,
  RoomInsert,
  RoomUpdate,
} from "../types";
import { logger } from "@/lib/logger/services/LoggerService";

/**
 * Service for managing organization profile
 * This service is used by Directors to manage their own organization
 */
export class ProfileService {
  /**
   * Get the current user's organization
   * Only returns the organization that the current user belongs to
   */
  static async getCurrentOrganization(): Promise<Organization | null> {
    try {
      const supabase = await createClient();

      const { data, error } = await supabase.from("organizations").select("*").single();

      if (error) {
        if (error.code === "PGRST116") {
          // No organization found
          return null;
        }
        logger.error(`Error fetching organization: ${error.message}`);
        throw error;
      }

      return data;
    } catch (error) {
      logger.error(`Unexpected error in getCurrentOrganization: ${error}`);
      throw error;
    }
  }

  /**
   * Update the current user's organization
   * Only allows updating the organization that the current user belongs to
   */
  static async updateOrganization(updates: OrganizationUpdate): Promise<Organization> {
    try {
      const supabase = await createClient();

      // Get the current organization first
      const { data: currentOrg, error: fetchError } = await supabase
        .from("organizations")
        .select("id")
        .single();

      if (fetchError) {
        logger.error(`Error fetching organization: ${fetchError.message}`);
        throw fetchError;
      }

      // Update the organization
      const { data, error } = await supabase
        .from("organizations")
        .update(updates)
        .eq("id", currentOrg.id)
        .select()
        .single();

      if (error) {
        logger.error(`Error updating organization: ${error.message}`);
        throw error;
      }

      return data;
    } catch (error) {
      logger.error(`Unexpected error in updateOrganization: ${error}`);
      throw error;
    }
  }

  /**
   * Get all locations for the current user's organization
   */
  static async getLocations(): Promise<Location[]> {
    try {
      const supabase = await createClient();

      const { data, error } = await supabase
        .from("locations")
        .select("*")
        .order("name", { ascending: true });

      if (error) {
        logger.error(`Error fetching locations: ${error.message}`);
        throw error;
      }

      return data || [];
    } catch (error) {
      logger.error(`Unexpected error in getLocations: ${error}`);
      throw error;
    }
  }

  /**
   * Create a new location for the current user's organization
   */
  static async createLocation(location: LocationInsert): Promise<Location> {
    try {
      const supabase = await createClient();

      const { data, error } = await supabase.from("locations").insert(location).select().single();

      if (error) {
        logger.error(`Error creating location: ${error.message}`);
        throw error;
      }

      return data;
    } catch (error) {
      logger.error(`Unexpected error in createLocation: ${error}`);
      throw error;
    }
  }

  /**
   * Update a location for the current user's organization
   */
  static async updateLocation(id: string, updates: LocationUpdate): Promise<Location> {
    try {
      const supabase = await createClient();

      const { data, error } = await supabase
        .from("locations")
        .update(updates)
        .eq("id", id)
        .select()
        .single();

      if (error) {
        logger.error(`Error updating location: ${error.message}`);
        throw error;
      }

      return data;
    } catch (error) {
      logger.error(`Unexpected error in updateLocation: ${error}`);
      throw error;
    }
  }

  /**
   * Delete a location for the current user's organization
   * This is a soft delete that sets the status to "inactive"
   */
  static async deleteLocation(id: string): Promise<void> {
    try {
      const supabase = await createClient();

      const { error } = await supabase
        .from("locations")
        .update({ status: "inactive" })
        .eq("id", id);

      if (error) {
        logger.error(`Error deleting location: ${error.message}`);
        throw error;
      }
    } catch (error) {
      logger.error(`Unexpected error in deleteLocation: ${error}`);
      throw error;
    }
  }

  /**
   * Get all services for the current user's organization
   */
  static async getServices(): Promise<Service[]> {
    try {
      const supabase = await createClient();

      const { data, error } = await supabase
        .from("services")
        .select("*")
        .order("name", { ascending: true });

      if (error) {
        // If the table doesn't exist, return an empty array
        if (error.code === "42P01") {
          // PostgreSQL code for "relation does not exist"
          logger.warn("Services table does not exist yet. Returning empty array.");
          return [];
        }

        logger.error(`Error fetching services: ${error.message}`);
        throw error;
      }

      return (data as Service[]) || [];
    } catch (error) {
      // If the error is related to the table not existing, return an empty array
      if (error instanceof Error && error.message.includes("does not exist")) {
        logger.warn("Services table does not exist yet. Returning empty array.");
        return [];
      }

      logger.error(`Unexpected error in getServices: ${error}`);
      return []; // Return empty array instead of throwing to prevent page errors
    }
  }

  /**
   * Create a new service for the current user's organization
   */
  static async createService(service: ServiceInsert): Promise<Service> {
    try {
      const supabase = await createClient();

      const { data, error } = await supabase.from("services").insert(service).select().single();

      if (error) {
        logger.error(`Error creating service: ${error.message}`);
        throw error;
      }

      return data as Service;
    } catch (error) {
      logger.error(`Unexpected error in createService: ${error}`);
      throw error;
    }
  }

  /**
   * Update a service for the current user's organization
   */
  static async updateService(id: string, updates: ServiceUpdate): Promise<Service> {
    try {
      const supabase = await createClient();

      const { data, error } = await supabase
        .from("services")
        .update(updates)
        .eq("id", id)
        .select()
        .single();

      if (error) {
        logger.error(`Error updating service: ${error.message}`);
        throw error;
      }

      return data as Service;
    } catch (error) {
      logger.error(`Unexpected error in updateService: ${error}`);
      throw error;
    }
  }

  /**
   * Delete a service for the current user's organization
   * This is a soft delete that sets the status to "inactive"
   */
  static async deleteService(id: string): Promise<void> {
    try {
      const supabase = await createClient();

      const { error } = await supabase.from("services").update({ status: "inactive" }).eq("id", id);

      if (error) {
        logger.error(`Error deleting service: ${error.message}`);
        throw error;
      }
    } catch (error) {
      logger.error(`Unexpected error in deleteService: ${error}`);
      throw error;
    }
  }

  /**
   * Get all business hours for the current user's organization
   */
  static async getBusinessHours(): Promise<BusinessHours[]> {
    try {
      const supabase = await createClient();

      const { data, error } = await supabase
        .from("business_hours")
        .select("*")
        .order("day_of_week", { ascending: true })
        .order("start_time", { ascending: true });

      if (error) {
        logger.error(`Error fetching business hours: ${error.message}`);
        throw error;
      }

      return data || [];
    } catch (error) {
      logger.error(`Unexpected error in getBusinessHours: ${error}`);
      throw error;
    }
  }

  /**
   * Get business hours for a specific day
   */
  static async getBusinessHoursByDay(dayOfWeek: number): Promise<BusinessHours[]> {
    try {
      const supabase = await createClient();

      const { data, error } = await supabase
        .from("business_hours")
        .select("*")
        .eq("day_of_week", dayOfWeek)
        .order("start_time", { ascending: true });

      if (error) {
        logger.error(`Error fetching business hours for day ${dayOfWeek}: ${error.message}`);
        throw error;
      }

      return data || [];
    } catch (error) {
      logger.error(`Unexpected error in getBusinessHoursByDay: ${error}`);
      throw error;
    }
  }

  /**
   * Get a specific business hours entry by ID
   */
  static async getBusinessHoursById(id: string): Promise<BusinessHours | null> {
    try {
      const supabase = await createClient();

      const { data, error } = await supabase
        .from("business_hours")
        .select("*")
        .eq("id", id)
        .single();

      if (error) {
        if (error.code === "PGRST116") {
          // No business hours found
          return null;
        }
        logger.error(`Error fetching business hours: ${error.message}`);
        throw error;
      }

      return data;
    } catch (error) {
      logger.error(`Unexpected error in getBusinessHoursById: ${error}`);
      throw error;
    }
  }

  /**
   * Create new business hours for the current user's organization
   */
  static async createBusinessHours(businessHours: BusinessHoursInsert): Promise<BusinessHours> {
    try {
      const supabase = await createClient();

      const { data, error } = await supabase
        .from("business_hours")
        .insert(businessHours)
        .select()
        .single();

      if (error) {
        logger.error(`Error creating business hours: ${error.message}`);
        throw error;
      }

      return data;
    } catch (error) {
      logger.error(`Unexpected error in createBusinessHours: ${error}`);
      throw error;
    }
  }

  /**
   * Update business hours for the current user's organization
   */
  static async updateBusinessHours(
    id: string,
    updates: BusinessHoursUpdate
  ): Promise<BusinessHours> {
    try {
      const supabase = await createClient();

      const { data, error } = await supabase
        .from("business_hours")
        .update(updates)
        .eq("id", id)
        .select()
        .single();

      if (error) {
        logger.error(`Error updating business hours: ${error.message}`);
        throw error;
      }

      return data;
    } catch (error) {
      logger.error(`Unexpected error in updateBusinessHours: ${error}`);
      throw error;
    }
  }

  /**
   * Delete business hours for the current user's organization
   */
  static async deleteBusinessHours(id: string): Promise<void> {
    try {
      const supabase = await createClient();

      const { error } = await supabase.from("business_hours").delete().eq("id", id);

      if (error) {
        logger.error(`Error deleting business hours: ${error.message}`);
        throw error;
      }
    } catch (error) {
      logger.error(`Unexpected error in deleteBusinessHours: ${error}`);
      throw error;
    }
  }

  /**
   * Get all rooms for the current user's organization
   */
  static async getRooms(): Promise<Room[]> {
    try {
      const supabase = await createClient();

      const { data, error } = await supabase
        .from("rooms")
        .select("*")
        .order("name", { ascending: true });

      if (error) {
        logger.error(`Error fetching rooms: ${error.message}`);
        throw error;
      }

      return data || [];
    } catch (error) {
      logger.error(`Unexpected error in getRooms: ${error}`);
      throw error;
    }
  }

  /**
   * Get rooms for a specific location
   */
  static async getRoomsByLocation(locationId: string): Promise<Room[]> {
    try {
      const supabase = await createClient();

      const { data, error } = await supabase
        .from("rooms")
        .select("*")
        .eq("location_id", locationId)
        .order("name", { ascending: true });

      if (error) {
        logger.error(`Error fetching rooms for location ${locationId}: ${error.message}`);
        throw error;
      }

      return data || [];
    } catch (error) {
      logger.error(`Unexpected error in getRoomsByLocation: ${error}`);
      throw error;
    }
  }

  /**
   * Get a specific room by ID
   */
  static async getRoomById(id: string): Promise<Room | null> {
    try {
      const supabase = await createClient();

      const { data, error } = await supabase.from("rooms").select("*").eq("id", id).single();

      if (error) {
        if (error.code === "PGRST116") {
          // No room found
          return null;
        }
        logger.error(`Error fetching room: ${error.message}`);
        throw error;
      }

      return data;
    } catch (error) {
      logger.error(`Unexpected error in getRoomById: ${error}`);
      throw error;
    }
  }

  /**
   * Create a new room for the current user's organization
   */
  static async createRoom(room: RoomInsert): Promise<Room> {
    try {
      const supabase = await createClient();

      const { data, error } = await supabase.from("rooms").insert(room).select().single();

      if (error) {
        logger.error(`Error creating room: ${error.message}`);
        throw error;
      }

      return data;
    } catch (error) {
      logger.error(`Unexpected error in createRoom: ${error}`);
      throw error;
    }
  }

  /**
   * Update a room for the current user's organization
   */
  static async updateRoom(id: string, updates: RoomUpdate): Promise<Room> {
    try {
      const supabase = await createClient();

      const { data, error } = await supabase
        .from("rooms")
        .update(updates)
        .eq("id", id)
        .select()
        .single();

      if (error) {
        logger.error(`Error updating room: ${error.message}`);
        throw error;
      }

      return data;
    } catch (error) {
      logger.error(`Unexpected error in updateRoom: ${error}`);
      throw error;
    }
  }

  /**
   * Delete a room for the current user's organization
   * This is a soft delete that sets the status to "inactive"
   */
  static async deleteRoom(id: string): Promise<void> {
    try {
      const supabase = await createClient();

      const { error } = await supabase.from("rooms").update({ status: "inactive" }).eq("id", id);

      if (error) {
        logger.error(`Error deleting room: ${error.message}`);
        throw error;
      }
    } catch (error) {
      logger.error(`Unexpected error in deleteRoom: ${error}`);
      throw error;
    }
  }
}
