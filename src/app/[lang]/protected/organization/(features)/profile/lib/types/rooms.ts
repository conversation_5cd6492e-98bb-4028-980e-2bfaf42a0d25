import { Database } from "@/lib/types/database.types";

// Room types
export type Room = Database["public"]["Tables"]["rooms"]["Row"];
export type RoomInsert = Database["public"]["Tables"]["rooms"]["Insert"];
export type RoomUpdate = Database["public"]["Tables"]["rooms"]["Update"];

// Room feature type
export type RoomFeature = {
  id: string;
  name: string;
  description?: string;
};

// Room features array type
export type RoomFeaturesJson = RoomFeature[];

// Form data type for room creation/editing
export type RoomFormData = {
  name: string;
  description?: string;
  capacity: number;
  locationId: string;
  status: "active" | "inactive";
  features: RoomFeaturesJson;
};

// Common room features (predefined options)
export const COMMON_ROOM_FEATURES: RoomFeature[] = [
  {
    id: "projector",
    name: "Projector",
    description: "Room equipped with a projector",
  },
  {
    id: "whiteboard",
    name: "Whiteboard",
    description: "Room has a whiteboard",
  },
  {
    id: "videoConference",
    name: "Video Conference",
    description: "Room equipped for video conferencing",
  },
  {
    id: "audioRecording",
    name: "Audio Recording",
    description: "Room has audio recording capabilities",
  },
  {
    id: "videoRecording",
    name: "Video Recording",
    description: "Room has video recording capabilities",
  },
  {
    id: "childFriendly",
    name: "Child-Friendly",
    description: "Room designed to be child-friendly",
  },
  {
    id: "privacyScreen",
    name: "Privacy Screen",
    description: "Room has privacy screens or dividers",
  },
  {
    id: "accessibleEntrance",
    name: "Accessible Entrance",
    description: "Room has wheelchair accessible entrance",
  },
  {
    id: "observationWindow",
    name: "Observation Window",
    description: "Room has one-way observation window",
  },
];
