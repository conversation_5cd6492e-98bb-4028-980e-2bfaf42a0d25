import { Database } from "@/lib/types/database.types";
import { RoomFeaturesJson } from "./rooms";

// Organization types
export type Organization = Database["public"]["Tables"]["organizations"]["Row"];
export type OrganizationInsert = Database["public"]["Tables"]["organizations"]["Insert"];
export type OrganizationUpdate = Database["public"]["Tables"]["organizations"]["Update"];

// Organization status
export type OrganizationStatus = "active" | "inactive" | "suspended";

// Location types
export type Location = Database["public"]["Tables"]["locations"]["Row"];
export type LocationInsert = Database["public"]["Tables"]["locations"]["Insert"];
export type LocationUpdate = Database["public"]["Tables"]["locations"]["Update"];

// Room types
export type Room = Database["public"]["Tables"]["rooms"]["Row"];
export type RoomInsert = Database["public"]["Tables"]["rooms"]["Insert"];
export type RoomUpdate = Database["public"]["Tables"]["rooms"]["Update"];

// Re-export room types from rooms.ts
export type { RoomFeature, RoomFeaturesJson } from "./rooms";
export { COMMON_ROOM_FEATURES } from "./rooms";

// Service types
export type Service = {
  id: string;
  name: string;
  description: string | null;
  price: number;
  duration: number;
  status: "active" | "inactive";
  organization_id: string;
  created_at: string | null;
  updated_at: string | null;
};

export type ServiceInsert = {
  name: string;
  description: string | null;
  price: number;
  duration: number;
  status: "active" | "inactive";
  organization_id: string;
  created_at?: string | null;
  updated_at?: string | null;
};

export type ServiceUpdate = {
  name?: string;
  description?: string | null;
  price?: number;
  duration?: number;
  status?: "active" | "inactive";
  updated_at?: string | null;
};

// Business hours types
export type BusinessHours = Database["public"]["Tables"]["business_hours"]["Row"];
export type BusinessHoursInsert = Database["public"]["Tables"]["business_hours"]["Insert"];
export type BusinessHoursUpdate = Database["public"]["Tables"]["business_hours"]["Update"];

// Day of week type (0-6, where 0 is Sunday)
export type DayOfWeek = 0 | 1 | 2 | 3 | 4 | 5 | 6;

// Day names for display
export const DAY_NAMES = {
  0: "Sunday",
  1: "Monday",
  2: "Tuesday",
  3: "Wednesday",
  4: "Thursday",
  5: "Friday",
  6: "Saturday",
};

// Day names in French
export const DAY_NAMES_FR = {
  0: "Dimanche",
  1: "Lundi",
  2: "Mardi",
  3: "Mercredi",
  4: "Jeudi",
  5: "Vendredi",
  6: "Samedi",
};

// Form data types
export type OrganizationProfileFormData = {
  name: string;
  address: string;
  phone: string;
  email: string;
  website?: string;
  status: OrganizationStatus;
};

export type LocationFormData = {
  name: string;
  address: string;
  phone: string;
  email: string;
  status: "active" | "inactive";
};

export type RoomFormData = {
  name: string;
  description?: string;
  capacity: number;
  locationId: string;
  status: "active" | "inactive";
  features: RoomFeaturesJson;
};

export type ServiceFormData = {
  name: string;
  description: string;
  price: number;
  duration: number;
  status: "active" | "inactive";
};

export type BusinessHoursFormData = {
  dayOfWeek: DayOfWeek;
  startTime: string;
  endTime: string;
  locationId?: string | null;
  isClosed: boolean;
};

// JSONB types
export interface EmailsJson {
  info?: string;
  main?: string;
  support?: string;
  [key: string]: string | undefined;
}

export interface PhonesJson {
  main?: string;
  fax?: string;
  [key: string]: string | undefined;
}
