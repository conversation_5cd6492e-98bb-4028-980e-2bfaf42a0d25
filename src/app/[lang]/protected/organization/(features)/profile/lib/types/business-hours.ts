import { Database } from "@/lib/types/database.types";

// Business hours types
export type BusinessHours = Database["public"]["Tables"]["business_hours"]["Row"];
export type BusinessHoursInsert = Database["public"]["Tables"]["business_hours"]["Insert"];
export type BusinessHoursUpdate = Database["public"]["Tables"]["business_hours"]["Update"];

// Day of week type (0-6, where 0 is Sunday)
export type DayOfWeek = 0 | 1 | 2 | 3 | 4 | 5 | 6;

// Day names for display
export const DAY_NAMES = {
  0: "Sunday",
  1: "Monday",
  2: "Tuesday",
  3: "Wednesday",
  4: "Thursday",
  5: "Friday",
  6: "Saturday",
};

// Day names in French
export const DAY_NAMES_FR = {
  0: "Dimanche",
  1: "Lundi",
  2: "Mardi",
  3: "Mercredi",
  4: "<PERSON>udi",
  5: "Vendredi",
  6: "Samedi",
};

// Business hours form data
export type BusinessHoursFormData = {
  dayOfWeek: DayOfWeek;
  startTime: string;
  endTime: string;
  locationId?: string | null;
  isClosed: boolean;
};
