import { AuthorizationRequired } from "@/lib/authorization/components/AuthorizationRequired";
import { i18n } from "@/lib/i18n/services/I18nService";
import { PERMISSIONS } from "@/lib/authorization/config/permissions";

export default async function OrganizationProfileLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ lang: string }>;
}) {
  const { lang } = await params;

  // Get dictionary for translations
  const dictionary = i18n.getDictionary(lang);
  const orgDict = dictionary.organization;

  return (
    <AuthorizationRequired permission={PERMISSIONS.ORGANIZATION.PROFILE.MANAGE} fallback={<></>}>
      <div className="container py-6">
        <div className="mb-6">
          <h1 className="text-3xl font-bold">
            {typeof orgDict.profileManagement === "string"
              ? orgDict.profileManagement
              : "Organization Profile"}
          </h1>
          <p className="text-muted-foreground">
            {typeof orgDict.profileManagementDescription === "string"
              ? orgDict.profileManagementDescription
              : "Manage your organization profile"}
          </p>
        </div>
        {children}
      </div>
    </AuthorizationRequired>
  );
}
