"use client";

import { useActionState } from "react";
import { updateOrganization } from "../../actions/updateOrganization";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Organization } from "../../lib/types";
import { useRouter } from "next/navigation";

// Types for JSONB fields
interface EmailsJson {
  info?: string;
  main?: string;
  support?: string;
  [key: string]: string | undefined;
}

interface PhonesJson {
  main?: string;
  fax?: string;
  [key: string]: string | undefined;
}

interface OrganizationEditFormProps {
  lang: string;
  dictionary: Record<string, string>;
  organization: Organization;
}

export function OrganizationEditForm({
  lang,
  dictionary,
  organization,
}: OrganizationEditFormProps) {
  const router = useRouter();
  const [state, formAction, pending] = useActionState(updateOrganization, {
    success: false,
    error: "",
  });

  // Parse emails and phones from JSONB
  const emails = organization.emails as EmailsJson;
  const phones = organization.phones as PhonesJson;

  // Handle cancel button click
  const handleCancel = () => {
    router.back();
  };

  return (
    <Card className="w-full max-w-3xl mx-auto">
      <CardHeader>
        <CardTitle>{dictionary.editOrganization || "Edit Organization"}</CardTitle>
        <CardDescription>
          {dictionary.organizationDetails || "Update the organization details"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form action={formAction} className="space-y-6">
          {/* Hidden fields */}
          <input type="hidden" name="lang" value={lang} />
          <input type="hidden" name="id" value={organization.id} />

          <div className="space-y-4">
            <div className="grid grid-cols-1 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">
                  {dictionary.name || "Organization Name"} <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="name"
                  name="name"
                  defaultValue={organization.name}
                  placeholder={dictionary.name || "Organization Name"}
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-1 gap-4">
              <div className="space-y-2">
                <Label htmlFor="address">{dictionary.address || "Address"}</Label>
                <Input
                  id="address"
                  name="address"
                  defaultValue={organization.address || ""}
                  placeholder={dictionary.address || "Address"}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="phone">
                  {dictionary.phone || "Phone Number"} <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="phone"
                  name="phone"
                  defaultValue={phones?.main || ""}
                  placeholder={dictionary.phone || "Phone Number"}
                  required
                  type="tel"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="fax">{dictionary.fax || "Fax Number"}</Label>
                <Input
                  id="fax"
                  name="fax"
                  defaultValue={phones?.fax || ""}
                  placeholder={dictionary.fax || "Fax Number"}
                  type="tel"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="email">
                  {dictionary.email || "Email Address"} <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="email"
                  name="email"
                  defaultValue={emails?.info || emails?.main || ""}
                  placeholder={dictionary.email || "Email Address"}
                  required
                  type="email"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="supportEmail">{dictionary.supportEmail || "Support Email"}</Label>
                <Input
                  id="supportEmail"
                  name="supportEmail"
                  defaultValue={emails?.support || ""}
                  placeholder={dictionary.supportEmail || "Support Email"}
                  type="email"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="website">{dictionary.website || "Website"}</Label>
                <Input
                  id="website"
                  name="website"
                  defaultValue={organization.website_url || ""}
                  placeholder={dictionary.website || "Website"}
                  type="url"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="status">{dictionary.status || "Status"}</Label>
                <Select name="status" defaultValue={organization.status}>
                  <SelectTrigger id="status">
                    <SelectValue placeholder={dictionary.status || "Status"} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">{dictionary.active || "Active"}</SelectItem>
                    <SelectItem value="inactive">{dictionary.inactive || "Inactive"}</SelectItem>
                    <SelectItem value="suspended">{dictionary.suspended || "Suspended"}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Display error message if there is one */}
          {state.error && (
            <div className="bg-red-50 p-4 rounded-md text-red-500 text-sm">{state.error}</div>
          )}

          {/* Display success message if the operation was successful */}
          {state.success && (
            <div className="bg-green-50 p-4 rounded-md text-green-500 text-sm">
              Organization updated successfully!
            </div>
          )}

          <div className="flex justify-end space-x-4">
            <Button variant="outline" type="button" onClick={handleCancel}>
              {dictionary.cancel || "Cancel"}
            </Button>
            <Button type="submit" disabled={pending}>
              {pending ? "Updating..." : dictionary.update || "Update Organization"}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
