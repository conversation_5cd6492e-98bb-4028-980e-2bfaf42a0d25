"use client";

import { useActionState } from "react";
import { assignDirector } from "../../actions/assignDirector";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

interface DirectorFormProps {
  lang: string;
  dictionary: Record<string, string>;
  organizationId: string;
}

export function DirectorForm({ lang, dictionary, organizationId }: DirectorFormProps) {
  const [state, formAction, pending] = useActionState(assignDirector, {
    success: false,
    error: null,
  });

  return (
    <Card className="w-full max-w-3xl mx-auto mt-8">
      <CardHeader>
        <CardTitle>{dictionary.assignDirector || "Assign Director"}</CardTitle>
        <CardDescription>
          {dictionary.assignDirectorDescription || "Assign a director to this organization"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form action={formAction} className="space-y-6">
          {/* Hidden fields */}
          <input type="hidden" name="lang" value={lang} />
          <input type="hidden" name="organizationId" value={organizationId} />

          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="firstName">
                  {dictionary.firstName || "First Name"} <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="firstName"
                  name="firstName"
                  placeholder={dictionary.firstName || "First Name"}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="lastName">
                  {dictionary.lastName || "Last Name"} <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="lastName"
                  name="lastName"
                  placeholder={dictionary.lastName || "Last Name"}
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="email">
                  {dictionary.email || "Email"} <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  placeholder={dictionary.email || "Email"}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone">{dictionary.phone || "Phone Number"}</Label>
                <Input
                  id="phone"
                  name="phone"
                  type="tel"
                  placeholder={dictionary.phone || "Phone Number"}
                />
              </div>
            </div>
          </div>

          {/* Display error message if there is one */}
          {state.error && (
            <div className="bg-red-50 p-4 rounded-md text-red-500 text-sm">{state.error}</div>
          )}

          {/* Display success message if the operation was successful */}
          {state.success && (
            <div className="bg-green-50 p-4 rounded-md text-green-500 text-sm">
              {dictionary.directorAssigned || "Director assigned successfully!"}
            </div>
          )}

          <div className="flex justify-end">
            <Button type="submit" disabled={pending}>
              {pending
                ? dictionary.assigning || "Assigning..."
                : dictionary.assignDirector || "Assign Director"}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
