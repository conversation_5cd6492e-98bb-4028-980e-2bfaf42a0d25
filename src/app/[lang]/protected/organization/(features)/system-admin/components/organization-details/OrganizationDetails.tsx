"use client";

import Link from "next/link";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Organization } from "../../lib/types";
import { PencilIcon, ArrowLeftIcon } from "lucide-react";

// Types for JSONB fields
interface EmailsJson {
  info?: string;
  main?: string;
  support?: string;
  [key: string]: string | undefined;
}

interface PhonesJson {
  main?: string;
  fax?: string;
  [key: string]: string | undefined;
}

interface OrganizationDetailsProps {
  lang: string;
  dictionary: {
    form?: Record<string, string>;
    backToList?: string;
    edit?: string;
    createdAt?: string;
    updatedAt?: string;
    [key: string]: unknown;
  };
  organization: Organization;
}

export function OrganizationDetails({ lang, dictionary, organization }: OrganizationDetailsProps) {
  // Parse emails and phones from JSONB
  const emails = organization.emails as EmailsJson;
  const phones = organization.phones as PhonesJson;

  // Format status for display
  const getStatusClass = (status: string) => {
    switch (status) {
      case "active":
        return "text-green-600";
      case "suspended":
        return "text-red-600";
      default:
        return "text-yellow-600";
    }
  };

  return (
    <Card className="w-full max-w-3xl mx-auto">
      <CardHeader>
        <CardTitle>{organization.name}</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="text-lg font-medium mb-2">
              {dictionary.form?.contactInfo || "Contact Information"}
            </h3>
            <div className="space-y-2">
              {emails && (emails.info || emails.main) && (
                <div className="flex">
                  <span className="font-medium w-24">{dictionary.form?.email || "Email"}:</span>
                  <span>{emails.info || emails.main}</span>
                </div>
              )}

              {emails && emails.support && (
                <div className="flex">
                  <span className="font-medium w-24">
                    {dictionary.form?.supportEmail || "Support"}:
                  </span>
                  <span>{emails.support}</span>
                </div>
              )}

              {phones && phones.main && (
                <div className="flex">
                  <span className="font-medium w-24">{dictionary.form?.phone || "Phone"}:</span>
                  <span>{phones.main}</span>
                </div>
              )}

              {phones && phones.fax && (
                <div className="flex">
                  <span className="font-medium w-24">{dictionary.form?.fax || "Fax"}:</span>
                  <span>{phones.fax}</span>
                </div>
              )}

              {organization.website_url && (
                <div className="flex">
                  <span className="font-medium w-24">{dictionary.form?.website || "Website"}:</span>
                  <span>
                    <a
                      href={organization.website_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline"
                    >
                      {organization.website_url}
                    </a>
                  </span>
                </div>
              )}
            </div>
          </div>

          <div>
            <h3 className="text-lg font-medium mb-2">
              {dictionary.form?.organizationDetails || "Organization Details"}
            </h3>
            <div className="space-y-2">
              <div className="flex">
                <span className="font-medium w-24">{dictionary.form?.status || "Status"}:</span>
                <span className={`capitalize ${getStatusClass(organization.status)}`}>
                  {organization.status}
                </span>
              </div>

              {organization.address && (
                <div className="flex">
                  <span className="font-medium w-24">{dictionary.form?.address || "Address"}:</span>
                  <span>{organization.address}</span>
                </div>
              )}

              <div className="flex">
                <span className="font-medium w-24">{dictionary.createdAt || "Created"}:</span>
                <span>
                  {organization.created_at
                    ? new Date(organization.created_at).toLocaleDateString()
                    : "-"}
                </span>
              </div>

              <div className="flex">
                <span className="font-medium w-24">{dictionary.updatedAt || "Updated"}:</span>
                <span>
                  {organization.updated_at
                    ? new Date(organization.updated_at).toLocaleDateString()
                    : "-"}
                </span>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Link href={`/${lang}/protected/organization/system-admin/list`}>
          <Button variant="outline">
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            {dictionary.backToList || "Back to List"}
          </Button>
        </Link>
        <Link href={`/${lang}/protected/organization/system-admin/edit/${organization.id}`}>
          <Button>
            <PencilIcon className="h-4 w-4 mr-2" />
            {dictionary.edit || "Edit"}
          </Button>
        </Link>
      </CardFooter>
    </Card>
  );
}
