"use client";

import { createOrganization } from "../../actions";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useActionState } from "react";

interface OrganizationFormProps {
  lang: string;
  dictionary:
    | Record<string, string>
    | {
        createOrganization?: string;
        organizationDetails?: string;
        name?: string;
        address?: string;
        phone?: string;
        email?: string;
        website?: string;
        status?: string;
        active?: string;
        inactive?: string;
        suspended?: string;
        create?: string;
        cancel?: string;
        required?: string;
        invalidEmail?: string;
        invalidPhone?: string;
      };
}

export function OrganizationForm({ lang, dictionary }: OrganizationFormProps) {
  const [state, formAction, pending] = useActionState(createOrganization, {
    success: false,
    error: "",
  });

  return (
    <Card className="w-full max-w-3xl mx-auto">
      <CardHeader>
        <CardTitle>{dictionary.createOrganization || "Create Organization"}</CardTitle>
        <CardDescription>
          {dictionary.organizationDetails || "Enter the details for the new organization"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form action={formAction} className="space-y-6">
          {/* Hidden field for language */}
          <input type="hidden" name="lang" value={lang} />

          <div className="space-y-4">
            <div className="grid grid-cols-1 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">
                  {dictionary.name || "Organization Name"} <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="name"
                  name="name"
                  placeholder={dictionary.name || "Organization Name"}
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-1 gap-4">
              <div className="space-y-2">
                <Label htmlFor="address">{dictionary.address || "Address"}</Label>
                <Input id="address" name="address" placeholder={dictionary.address || "Address"} />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="phone">
                  {dictionary.phone || "Phone Number"} <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="phone"
                  name="phone"
                  placeholder={dictionary.phone || "Phone Number"}
                  required
                  type="tel"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">
                  {dictionary.email || "Email Address"} <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="email"
                  name="email"
                  placeholder={dictionary.email || "Email Address"}
                  required
                  type="email"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="website">{dictionary.website || "Website"}</Label>
                <Input
                  id="website"
                  name="website"
                  placeholder={dictionary.website || "Website"}
                  type="url"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="status">{dictionary.status || "Status"}</Label>
                <Select name="status" defaultValue="active">
                  <SelectTrigger id="status">
                    <SelectValue placeholder={dictionary.status || "Status"} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">{dictionary.active || "Active"}</SelectItem>
                    <SelectItem value="inactive">{dictionary.inactive || "Inactive"}</SelectItem>
                    <SelectItem value="suspended">{dictionary.suspended || "Suspended"}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Display error message if there is one */}
          {state.error && (
            <div className="bg-red-50 p-4 rounded-md text-red-500 text-sm">{state.error}</div>
          )}

          {/* Display success message if the operation was successful */}
          {state.success && (
            <div className="bg-green-50 p-4 rounded-md text-green-500 text-sm">
              Organization created successfully!
            </div>
          )}

          <div className="flex justify-end space-x-4">
            <Button variant="outline" type="button">
              {dictionary.cancel || "Cancel"}
            </Button>
            <Button type="submit" disabled={pending}>
              {pending ? "Creating..." : dictionary.create || "Create Organization"}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
