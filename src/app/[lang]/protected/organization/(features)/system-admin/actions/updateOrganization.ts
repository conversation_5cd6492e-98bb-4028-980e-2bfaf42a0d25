"use server";

import { logger } from "@/lib/logger/services/LoggerService";
import { OrganizationService } from "../lib/services/OrganizationService";
import { OrganizationUpdate } from "../lib/types";
import { revalidatePath } from "next/cache";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { PERMISSIONS } from "@/lib/authorization/config/permissions";

interface UpdateOrganizationResult {
  success: boolean;
  error: string;
}

/**
 * Update an existing organization
 * This action is only accessible to system administrators
 */
export const updateOrganization = requirePermission(PERMISSIONS.ORGANIZATION.SYSTEM_ADMIN.UPDATE)(
  async (
    prevState: UpdateOrganizationResult,
    formData: FormData
  ): Promise<UpdateOrganizationResult> => {
    try {
      // Get form data
      const id = formData.get("id") as string;
      const lang = formData.get("lang") as string;
      const name = formData.get("name") as string;
      const address = formData.get("address") as string;
      const phone = formData.get("phone") as string;
      const fax = formData.get("fax") as string;
      const email = formData.get("email") as string;
      const supportEmail = formData.get("supportEmail") as string;
      const website = formData.get("website") as string;
      const status = formData.get("status") as string;

      // Validate required fields
      if (!id || !name || !phone || !email) {
        return {
          success: false,
          error: "Missing required fields",
        };
      }

      // Validate email format
      const emailRegex = /^[A-Za-z0-9._%-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,4}$/;
      if (!emailRegex.test(email)) {
        return {
          success: false,
          error: "Invalid email format",
        };
      }

      // Validate support email if provided
      if (supportEmail && !emailRegex.test(supportEmail)) {
        return {
          success: false,
          error: "Invalid support email format",
        };
      }

      // Validate phone format (simple validation)
      const phoneRegex = /^[0-9\(\)\-\+\s\.]{7,20}$/;
      if (!phoneRegex.test(phone)) {
        return {
          success: false,
          error: "Invalid phone format",
        };
      }

      // Validate fax number if provided
      if (fax && !phoneRegex.test(fax)) {
        return {
          success: false,
          error: "Invalid fax number format",
        };
      }

      // Create emails and phones JSON objects
      const emails = {
        info: email,
        support: supportEmail || undefined,
      };

      const phones = {
        main: phone,
        fax: fax || undefined,
      };

      // Create organization update object
      const organizationUpdate: OrganizationUpdate = {
        name,
        address: address || null,
        emails,
        phones,
        website_url: website || null,
        status,
        updated_at: new Date().toISOString(),
      };

      // Update the organization
      await OrganizationService.updateOrganization(id, organizationUpdate);

      // Revalidate the organization list page
      revalidatePath(`/${lang}/protected/organization/system-admin/list`);

      // Return success
      return {
        success: true,
        error: "",
      };
    } catch (error) {
      logger.error(`Error updating organization: ${error}`);
      return {
        success: false,
        error: `An error occurred while updating the organization: ${error}`,
      };
    }
  }
);
