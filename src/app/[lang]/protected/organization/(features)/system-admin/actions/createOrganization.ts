"use server";

import { revalidatePath } from "next/cache";
import { OrganizationService } from "../lib/services/OrganizationService";
import { logger } from "@/lib/logger/services/LoggerService";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { PERMISSIONS } from "@/lib/authorization/config/permissions";

// Define the return type for the action
type CreateOrganizationResult = {
  success?: boolean;
  organizationId?: string;
  error?: string | null;
};

/**
 * Create a new organization
 * This action is only accessible to system administrators
 */
export const createOrganization = requirePermission(PERMISSIONS.ORGANIZATION.SYSTEM_ADMIN.CREATE)(
  async (
    prevState: CreateOrganizationResult,
    formData: FormData
  ): Promise<CreateOrganizationResult> => {
    try {
      // Get form data
      const name = formData.get("name") as string;
      const address = formData.get("address") as string;
      const phone = formData.get("phone") as string;
      const email = formData.get("email") as string;
      const website = formData.get("website") as string;
      const status = formData.get("status") as string;

      // Validate form data
      if (!name) {
        return { error: "Organization name is required" };
      }

      if (!phone) {
        return { error: "Phone number is required" };
      }

      if (!email) {
        return { error: "Email is required" };
      }

      // Validate email format
      const emailRegex = /^[A-Za-z0-9._%-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,4}$/;
      if (!emailRegex.test(email)) {
        return { error: "Invalid email format" };
      }

      // Validate phone format (simple validation)
      const phoneRegex = /^[0-9\-\+\(\)\s\.]{7,20}$/;
      if (!phoneRegex.test(phone)) {
        return { error: "Invalid phone format" };
      }

      // Create emails and phones objects
      const emails = {
        info: email,
      };

      const phones = {
        main: phone,
      };

      // Create organization
      const organization = await OrganizationService.createOrganization({
        name,
        address: address || null,
        phones,
        emails,
        website_url: website || null,
        status: (status as "active" | "inactive" | "suspended") || "active",
      });

      // Get the language from the form data for revalidation
      const lang = formData.get("lang") as string;

      // Return success with the organization ID
      const result = {
        success: true,
        organizationId: organization.id,
        error: null,
      };

      // Revalidate the organizations list page outside of try/catch
      revalidatePath(`/${lang}/protected/organization/(features)/system-admin/(pages)/list`);

      return result;
    } catch (error) {
      logger.error(`Error creating organization: ${error}`);
      logger.error(`Unexpected error in createOrganization: ${error}`);
      return { error: `An error occurred while creating the organization: ${error}` };
    }
  }
);
