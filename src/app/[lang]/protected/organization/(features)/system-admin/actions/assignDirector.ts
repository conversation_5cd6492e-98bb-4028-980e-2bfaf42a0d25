"use server";

import { revalidatePath } from "next/cache";
import { redirect } from "next/navigation";
import { OrganizationService } from "../lib/services/OrganizationService";
import { SystemAdminService } from "../lib/services/SystemAdminService";
import { logger } from "@/lib/logger/services/LoggerService";
import { createServiceClient } from "@/lib/supabase/service";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { PERMISSIONS } from "@/lib/authorization/config/permissions";

// Define the return type for the action
type AssignDirectorResult = {
  success?: boolean;
  error?: string | null;
};

/**
 * Assign a director to an organization
 * This action is only accessible to system administrators
 */
export const assignDirector = requirePermission(PERMISSIONS.ORGANIZATION.SYSTEM_ADMIN.UPDATE)(
  async (prevState: AssignDirectorResult, formData: FormData): Promise<AssignDirectorResult> => {
    try {
      // Get form data
      const organizationId = formData.get("organizationId") as string;
      const email = formData.get("email") as string;
      const firstName = formData.get("firstName") as string;
      const lastName = formData.get("lastName") as string;
      const phone = formData.get("phone") as string;

      // Validate form data
      if (!organizationId) {
        return { error: "Organization ID is required" };
      }

      if (!email) {
        return { error: "Email is required" };
      }

      if (!firstName) {
        return { error: "First name is required" };
      }

      if (!lastName) {
        return { error: "Last name is required" };
      }

      // Check if the organization exists
      const organization = await OrganizationService.getOrganizationById(organizationId);
      if (!organization) {
        return { error: "Organization not found" };
      }

      // Create Supabase client
      const supabase = await createServiceClient();

      // Check if the user already exists by listing users and filtering by email
      const { data: usersList, error: listError } = await supabase.auth.admin.listUsers();

      let userId: string | undefined;

      if (listError) {
        logger.error(`Error listing users: ${listError.message}`);
        return { error: "Error checking existing users" };
      }

      // Find user with matching email
      const existingUser = usersList.users.find((user) => user.email === email);

      if (existingUser) {
        // User exists, use their ID
        userId = existingUser.id;
      } else {
        // Create a new user
        const { data: newUser, error: createError } = await supabase.auth.admin.createUser({
          email,
          password: "Password123!", // Generate a random password
          email_confirm: true,
          user_metadata: {
            first_name: firstName,
            last_name: lastName,
            phone: phone,
          },
        });

        if (createError) {
          logger.error(`Error creating user: ${createError.message}`);
          return { error: "Error creating user" };
        }

        userId = newUser.user.id;
      }

      // Assign the user as the director of the organization
      await SystemAdminService.assignDirector(userId, organizationId);

      // Return success
      return { success: true, error: null };
    } catch (error) {
      logger.error(`Error assigning director: ${error}`);
      return { error: "An unexpected error occurred" };
    }
  }
);

/**
 * Assign a director and redirect to the organization detail page
 * This action is used when we want to redirect after assigning a director
 */
export const assignDirectorAndRedirect = requirePermission(
  PERMISSIONS.ORGANIZATION.SYSTEM_ADMIN.UPDATE
)(async (formData: FormData): Promise<void> => {
  // Get the language and organization ID for redirection
  const lang = formData.get("lang") as string;
  const organizationId = formData.get("organizationId") as string;

  // Call the assignDirector action
  const result = await assignDirector({}, formData);

  // If there was an error, throw it to prevent redirection
  if (result.error) {
    throw new Error(result.error);
  }

  // Revalidate the organization detail page
  revalidatePath(`/${lang}/protected/organization/system-admin/${organizationId}`);

  // Redirect to the organization detail page
  redirect(`/${lang}/protected/organization/system-admin/${organizationId}`);
});
