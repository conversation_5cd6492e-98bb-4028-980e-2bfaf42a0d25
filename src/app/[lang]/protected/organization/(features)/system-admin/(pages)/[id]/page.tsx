import { SystemAdminService } from "../../lib/services/SystemAdminService";
import { OrganizationService } from "../../lib/services/OrganizationService";
import { OrganizationDetails } from "../../components/organization-details/OrganizationDetails";
import { DirectorForm } from "../../components/director-form/DirectorForm";
import { forbidden, notFound } from "next/navigation";
import { i18n } from "@/lib/i18n/services/I18nService";

interface OrganizationDetailsPageProps {
  params: Promise<{
    lang: string;
    id: string;
  }>;
}

export default async function OrganizationDetailsPage({ params }: OrganizationDetailsPageProps) {
  // Await the params
  const resolvedParams = await params;

  // Check if the user is a system admin
  const isSystemAdmin = await SystemAdminService.isSystemAdmin();

  // If not a system admin, redirect to forbidden page
  if (!isSystemAdmin) {
    forbidden();
  }

  // Get translations
  const dictionary = i18n.getDictionary(resolvedParams.lang);

  // Get the organization by ID
  const organization = await OrganizationService.getOrganizationById(resolvedParams.id);

  // If organization not found, redirect to not found page
  if (!organization) {
    notFound();
  }

  return (
    <div className="container py-10">
      <h1 className="text-2xl font-bold mb-6">
        {(dictionary.organization?.details as string) || "Organization Details"}
      </h1>
      <OrganizationDetails
        lang={resolvedParams.lang}
        dictionary={dictionary.organization as Record<string, unknown>}
        organization={organization}
      />

      {/* Director Assignment Form */}
      <h2 className="text-xl font-bold mt-10 mb-4">
        {(dictionary.organization?.assignDirector as string) || "Assign Director"}
      </h2>
      <DirectorForm
        lang={resolvedParams.lang}
        dictionary={dictionary.organization?.form as Record<string, string>}
        organizationId={resolvedParams.id}
      />
    </div>
  );
}
