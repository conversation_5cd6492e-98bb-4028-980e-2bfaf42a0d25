import { SystemAdminService } from "../../lib/services/SystemAdminService";
import { OrganizationForm } from "../../components/organization-form/OrganizationForm";
import { forbidden } from "next/navigation";
import { i18n } from "@/lib/i18n/services/I18nService";

interface CreateOrganizationPageProps {
  params: Promise<{
    lang: string;
  }>;
}

export default async function CreateOrganizationPage({ params }: CreateOrganizationPageProps) {
  // Await the params
  const resolvedParams = await params;

  // Check if the user is a system admin
  const isSystemAdmin = await SystemAdminService.isSystemAdmin();

  // If not a system admin, redirect to forbidden page
  if (!isSystemAdmin) {
    forbidden();
  }

  // Get translations
  const dictionary = i18n.getDictionary(resolvedParams.lang);

  return (
    <div className="container py-10">
      <h1 className="text-2xl font-bold mb-6">
        {(dictionary.organization?.createNew as string) || "Create New Organization"}
      </h1>
      <OrganizationForm
        lang={resolvedParams.lang}
        dictionary={dictionary.organization?.form as Record<string, string>}
      />
    </div>
  );
}
