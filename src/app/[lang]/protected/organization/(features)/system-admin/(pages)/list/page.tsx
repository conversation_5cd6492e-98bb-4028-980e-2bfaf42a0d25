import Link from "next/link";
import { SystemAdminService } from "../../lib/services/SystemAdminService";
import { OrganizationService } from "../../lib/services/OrganizationService";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, Card<PERSON><PERSON>le, CardFooter } from "@/components/ui/card";
import { forbidden } from "next/navigation";
import { PencilIcon, EyeIcon } from "lucide-react";
import { i18n } from "@/lib/i18n/services/I18nService";

// Types for JSONB fields
interface EmailsJson {
  info?: string;
  main?: string;
  support?: string;
  [key: string]: string | undefined;
}

interface PhonesJson {
  main?: string;
  fax?: string;
  [key: string]: string | undefined;
}

interface OrganizationListPageProps {
  params: Promise<{
    lang: string;
  }>;
}

export default async function OrganizationListPage({ params }: OrganizationListPageProps) {
  // Await the params
  const resolvedParams = await params;

  // Check if the user is a system admin
  const isSystemAdmin = await SystemAdminService.isSystemAdmin();

  // If not a system admin, redirect to forbidden page
  if (!isSystemAdmin) {
    forbidden();
  }

  // Get translations
  const dictionary = i18n.getDictionary(resolvedParams.lang);

  // Get all organizations
  const organizations = await OrganizationService.getAllOrganizations();

  return (
    <div className="container py-10">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">
          {(dictionary.organization?.list as string) || "Organizations"}
        </h1>
        <Link href={`/${resolvedParams.lang}/protected/organization/system-admin/create`}>
          <Button>
            {(dictionary.organization?.createNew as string) || "Create New Organization"}
          </Button>
        </Link>
      </div>

      {organizations.length === 0 ? (
        <Card>
          <CardContent className="pt-6">
            <p className="text-center text-muted-foreground">
              {(dictionary.organization?.noOrganizations as string) ||
                "No organizations found. Create your first organization to get started."}
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {organizations.map((org) => (
            <Card key={org.id} className="h-full">
              <CardHeader>
                <CardTitle>{org.name}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-sm">
                  <p>
                    <span className="font-medium">
                      {((dictionary.organization?.form as Record<string, string>)
                        ?.status as string) || "Status"}
                      :{" "}
                    </span>
                    <span
                      className={`capitalize ${
                        org.status === "active"
                          ? "text-green-600"
                          : org.status === "suspended"
                            ? "text-red-600"
                            : "text-yellow-600"
                      }`}
                    >
                      {org.status}
                    </span>
                  </p>
                  {org.emails && (
                    <p>
                      <span className="font-medium">
                        {((dictionary.organization?.form as Record<string, string>)
                          ?.email as string) || "Email"}
                        :{" "}
                      </span>
                      {(org.emails as EmailsJson).info || (org.emails as EmailsJson).main}
                    </p>
                  )}
                  {org.phones && (
                    <p>
                      <span className="font-medium">
                        {((dictionary.organization?.form as Record<string, string>)
                          ?.phone as string) || "Phone"}
                        :{" "}
                      </span>
                      {(org.phones as PhonesJson).main}
                    </p>
                  )}
                </div>
              </CardContent>
              <CardFooter className="flex justify-end gap-2">
                <Link
                  href={`/${resolvedParams.lang}/protected/organization/system-admin/${org.id}`}
                >
                  <Button variant="outline" size="sm">
                    <EyeIcon className="h-4 w-4 mr-2" />
                    View
                  </Button>
                </Link>
                <Link
                  href={`/${resolvedParams.lang}/protected/organization/system-admin/edit/${org.id}`}
                >
                  <Button variant="default" size="sm">
                    <PencilIcon className="h-4 w-4 mr-2" />
                    Edit
                  </Button>
                </Link>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
