import { createClient } from "@/lib/supabase/server";
import { logger } from "@/lib/logger/services/LoggerService";
import { UserRole, UserRoleInsert } from "../types";

/**
 * Service for system administration operations
 */
export class SystemAdminService {
  /**
   * Check if the current user is a system administrator
   */
  static async isSystemAdmin(): Promise<boolean> {
    try {
      const supabase = await createClient();

      const {
        data: { session },
      } = await supabase.auth.getSession();

      if (!session?.user) {
        return false;
      }

      const { data, error } = await supabase
        .from("user_roles")
        .select("role")
        .eq("user_id", session.user.id)
        .eq("role", "SystemAdmin")
        .maybeSingle();

      if (error) {
        logger.error(`Error checking system admin status: ${error.message}`);
        return false;
      }

      return !!data;
    } catch (error) {
      logger.error(`Unexpected error in isSystemAdmin: ${error}`);
      return false;
    }
  }

  /**
   * Assign a user as the director of an organization
   * Only accessible to system administrators
   */
  static async assignDirector(userId: string, organizationId: string): Promise<UserRole> {
    try {
      const supabase = await createClient();

      // Check if the user already has a role
      const { data: existingRole, error: checkError } = await supabase
        .from("user_roles")
        .select("*")
        .eq("user_id", userId)
        .maybeSingle();

      if (checkError) {
        logger.error(`Error checking existing role: ${checkError.message}`);
        throw checkError;
      }

      // If the user already has a role, update it
      if (existingRole) {
        const { data, error } = await supabase
          .from("user_roles")
          .update({
            role: "Director",
            organization_id: organizationId,
          })
          .eq("user_id", userId)
          .select()
          .single();

        if (error) {
          logger.error(`Error updating user role: ${error.message}`);
          throw error;
        }

        return data;
      }

      // Otherwise, create a new role
      const newRole: UserRoleInsert = {
        user_id: userId,
        role: "Director",
        organization_id: organizationId,
      };

      const { data, error } = await supabase.from("user_roles").insert(newRole).select().single();

      if (error) {
        logger.error(`Error assigning director role: ${error.message}`);
        throw error;
      }

      return data;
    } catch (error) {
      logger.error(`Unexpected error in assignDirector: ${error}`);
      throw error;
    }
  }

  /**
   * Assign a user as a system administrator
   * Only accessible to existing system administrators
   */
  static async assignSystemAdmin(userId: string): Promise<UserRole> {
    try {
      const supabase = await createClient();

      // Check if the user already has a role
      const { data: existingRole, error: checkError } = await supabase
        .from("user_roles")
        .select("*")
        .eq("user_id", userId)
        .maybeSingle();

      if (checkError) {
        logger.error(`Error checking existing role: ${checkError.message}`);
        throw checkError;
      }

      // If the user already has a role, update it
      if (existingRole) {
        const { data, error } = await supabase
          .from("user_roles")
          .update({
            role: "SystemAdmin",
          })
          .eq("user_id", userId)
          .select()
          .single();

        if (error) {
          logger.error(`Error updating user role: ${error.message}`);
          throw error;
        }

        return data;
      }

      // Otherwise, create a new role
      const newRole: UserRoleInsert = {
        user_id: userId,
        role: "SystemAdmin",
      };

      const { data, error } = await supabase.from("user_roles").insert(newRole).select().single();

      if (error) {
        logger.error(`Error assigning system admin role: ${error.message}`);
        throw error;
      }

      return data;
    } catch (error) {
      logger.error(`Unexpected error in assignSystemAdmin: ${error}`);
      throw error;
    }
  }
}
