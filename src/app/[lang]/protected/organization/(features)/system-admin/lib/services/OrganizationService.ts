import { createClient } from "@/lib/supabase/server";
import { logger } from "@/lib/logger/services/LoggerService";
import { Organization, OrganizationInsert, OrganizationUpdate } from "../types";

/**
 * Service for managing organizations
 */
export class OrganizationService {
  /**
   * Get all organizations
   * Only accessible to system administrators
   */
  static async getAllOrganizations(): Promise<Organization[]> {
    try {
      const supabase = await createClient();

      const { data, error } = await supabase
        .from("organizations")
        .select("*")
        .order("name", { ascending: true });

      if (error) {
        logger.error(`Error fetching organizations: ${error.message}`);
        throw error;
      }

      return data || [];
    } catch (error) {
      logger.error(`Unexpected error in getAllOrganizations: ${error}`);
      throw error;
    }
  }

  /**
   * Get an organization by ID
   * Only accessible to system administrators or members of the organization
   */
  static async getOrganizationById(id: string): Promise<Organization | null> {
    try {
      const supabase = await createClient();

      const { data, error } = await supabase
        .from("organizations")
        .select("*")
        .eq("id", id)
        .single();

      if (error) {
        if (error.code === "PGRST116") {
          // Record not found
          return null;
        }

        logger.error(`Error fetching organization: ${error.message}`);
        throw error;
      }

      return data;
    } catch (error) {
      logger.error(`Unexpected error in getOrganizationById: ${error}`);
      throw error;
    }
  }

  /**
   * Create a new organization
   * Only accessible to system administrators
   */
  static async createOrganization(organization: OrganizationInsert): Promise<Organization> {
    try {
      const supabase = await createClient();

      const { data, error } = await supabase
        .from("organizations")
        .insert(organization)
        .select()
        .single();

      if (error) {
        logger.error(`Error creating organization: ${error.message}`);
        throw error;
      }

      return data;
    } catch (error) {
      logger.error(`Unexpected error in createOrganization: ${error}`);
      throw error;
    }
  }

  /**
   * Update an organization
   * Only accessible to system administrators or directors of the organization
   */
  static async updateOrganization(id: string, updates: OrganizationUpdate): Promise<Organization> {
    try {
      const supabase = await createClient();

      const { data, error } = await supabase
        .from("organizations")
        .update(updates)
        .eq("id", id)
        .select()
        .single();

      if (error) {
        logger.error(`Error updating organization: ${error.message}`);
        throw error;
      }

      return data;
    } catch (error) {
      logger.error(`Unexpected error in updateOrganization: ${error}`);
      throw error;
    }
  }

  /**
   * Delete an organization
   * Only accessible to system administrators
   * This is a soft delete that sets the status to "inactive"
   */
  static async deleteOrganization(id: string): Promise<void> {
    try {
      const supabase = await createClient();

      const { error } = await supabase
        .from("organizations")
        .update({ status: "inactive" })
        .eq("id", id);

      if (error) {
        logger.error(`Error deleting organization: ${error.message}`);
        throw error;
      }
    } catch (error) {
      logger.error(`Unexpected error in deleteOrganization: ${error}`);
      throw error;
    }
  }
}
