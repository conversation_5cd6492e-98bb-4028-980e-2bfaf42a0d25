import { Database } from "@/lib/types/database.types";

// Organization types
export type Organization = Database["public"]["Tables"]["organizations"]["Row"];
export type OrganizationInsert = Database["public"]["Tables"]["organizations"]["Insert"];
export type OrganizationUpdate = Database["public"]["Tables"]["organizations"]["Update"];

// Organization status
export type OrganizationStatus = "active" | "inactive" | "suspended";

// User role types
export type UserRole = Database["public"]["Tables"]["user_roles"]["Row"];
export type UserRoleInsert = Database["public"]["Tables"]["user_roles"]["Insert"];
export type UserRoleUpdate = Database["public"]["Tables"]["user_roles"]["Update"];

// Role types
export type Role = "Director" | "Coordinator" | "SocialWorker" | "SystemAdmin";

// Organization creation form data
export type OrganizationFormData = {
  name: string;
  address: string;
  city: string;
  province: string;
  postalCode: string;
  phone: string;
  email: string;
  website?: string;
  status: OrganizationStatus;
};

// Director assignment form data
export type DirectorAssignmentFormData = {
  organizationId: string;
  email: string;
  firstName: string;
  lastName: string;
  phone: string;
};
