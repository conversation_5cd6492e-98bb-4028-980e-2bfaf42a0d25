import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import Link from "next/link";
import { i18n } from "@/lib/i18n/services/I18nService";

interface ProfileLayoutProps {
  children: React.ReactNode;
  params: Promise<{
    lang: string;
  }>;
}

export default async function ProfileLayout({ children, params }: ProfileLayoutProps) {
  // Await the params
  const { lang } = await params;

  // Get dictionary for translations
  const dictionary = i18n.getDictionary(lang);

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">
          {dictionary.user?.profile || "Profile"}
        </h1>
        <p className="text-muted-foreground">
          {dictionary.user?.profileDescription ||
            "Manage your profile information and preferences."}
        </p>
      </div>
      <Tabs defaultValue="personal" className="space-y-4">
        <TabsList>
          <TabsTrigger value="personal" asChild>
            <Link href={`/${lang}/protected/user/profile/personal`}>
              {dictionary.user?.personal || "Personal"}
            </Link>
          </TabsTrigger>
          <TabsTrigger value="account" asChild>
            <Link href={`/${lang}/protected/user/profile/account`}>
              {dictionary.user?.account || "Account"}
            </Link>
          </TabsTrigger>
          <TabsTrigger value="settings" asChild>
            <Link href={`/${lang}/protected/user/profile/settings`}>
              {dictionary.user?.settings || "Settings"}
            </Link>
          </TabsTrigger>
        </TabsList>
        <div className="p-1">{children}</div>
      </Tabs>
    </div>
  );
}
