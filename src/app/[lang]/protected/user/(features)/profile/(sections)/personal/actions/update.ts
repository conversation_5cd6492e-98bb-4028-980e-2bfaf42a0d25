"use server";

import { revalidatePath } from "next/cache";
import { logger } from "@/lib/logger/services/LoggerService";
import { UserProfileService } from "../../../../../lib/services/UserProfileService";
import { PersonalInfoUpdate } from "../../../../../lib/types";
import { withPermission } from "@/lib/authorization/utils/withPermission";

/**
 * Update the user's personal information
 * @param prevState Previous state
 * @param formData Form data
 * @returns Result of the update operation
 */
export const updatePersonalInfo = withPermission(
  "user:profile:update",
  async (
    prevState: {
      success: boolean;
      message: string;
      errors: Record<string, string[]>;
    },
    formData: FormData
  ) => {
    try {
      logger.info("Updating personal information");

      // Extract form data
      const firstName = formData.get("firstName") as string;
      const lastName = formData.get("lastName") as string;

      // Validate form data
      const errors: Record<string, string[]> = {};

      // First name validation
      if (!firstName?.trim()) {
        errors.firstName = ["First name is required"];
      } else {
        const trimmedFirstName = firstName.trim();

        // Length validation
        if (trimmedFirstName.length < 2) {
          errors.firstName = ["First name must be at least 2 characters"];
        } else if (trimmedFirstName.length > 50) {
          errors.firstName = ["First name must be at most 50 characters"];
        }

        // Character validation - allow letters, spaces, hyphens, apostrophes, and accented characters
        else if (!/^[a-zA-ZÀ-ÿ\s'-]+$/.test(trimmedFirstName)) {
          errors.firstName = ["First name contains invalid characters"];
        }

        // Prevent SQL injection patterns
        else if (
          /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|ALTER|UNION|JOIN|WHERE)\b)|[;]/.test(
            trimmedFirstName.toUpperCase()
          )
        ) {
          errors.firstName = ["First name contains invalid characters"];
          logger.warn("Potential SQL injection attempt in first name", {
            firstName: trimmedFirstName,
          });
        }
      }

      // Last name validation
      if (!lastName?.trim()) {
        errors.lastName = ["Last name is required"];
      } else {
        const trimmedLastName = lastName.trim();

        // Length validation
        if (trimmedLastName.length < 2) {
          errors.lastName = ["Last name must be at least 2 characters"];
        } else if (trimmedLastName.length > 50) {
          errors.lastName = ["Last name must be at most 50 characters"];
        }

        // Character validation - allow letters, spaces, hyphens, apostrophes, and accented characters
        else if (!/^[a-zA-ZÀ-ÿ\s'-]+$/.test(trimmedLastName)) {
          errors.lastName = ["Last name contains invalid characters"];
        }

        // Prevent SQL injection patterns
        else if (
          /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|ALTER|UNION|JOIN|WHERE)\b)|[;]/.test(
            trimmedLastName.toUpperCase()
          )
        ) {
          errors.lastName = ["Last name contains invalid characters"];
          logger.warn("Potential SQL injection attempt in last name", {
            lastName: trimmedLastName,
          });
        }
      }

      if (Object.keys(errors).length > 0) {
        logger.warn("Validation errors in personal info update", { errors });
        return { success: false, message: "Validation failed", errors };
      }

      // Update user profile
      const updateData: PersonalInfoUpdate = {
        firstName: firstName.trim(),
        lastName: lastName.trim(),
      };

      const updatedProfile = await UserProfileService.updatePersonalInfo(updateData);

      if (!updatedProfile) {
        logger.error("Failed to update personal information");
        return {
          success: false,
          message: "Failed to update personal information",
        };
      }

      logger.info("Personal information updated successfully");

      // Revalidate the profile page
      revalidatePath("/[lang]/protected/user/profile");

      return {
        success: true,
        message: "Personal information updated successfully",
      };
    } catch (error: unknown) {
      const err = error as Error & { code?: string; details?: string };
      logger.error(`Error updating personal information: ${err.message}`);

      // Categorize errors for better client feedback
      if (err.message?.includes("auth") || err.code === "PGRST301") {
        return {
          success: false,
          message: "Authentication error. Please log in again.",
        };
      } else if (err.message?.includes("network") || err.code === "PGRST499") {
        return {
          success: false,
          message: "Network error. Please check your connection and try again.",
        };
      } else if (err.code === "PGRST409") {
        return {
          success: false,
          message: "Conflict error. The profile may have been updated by another session.",
        };
      } else {
        return {
          success: false,
          message: "An unexpected error occurred. Please try again later.",
        };
      }
    }
  }
);
