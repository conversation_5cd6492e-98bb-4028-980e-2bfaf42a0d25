"use server";

import { revalidatePath } from "next/cache";
import { logger } from "@/lib/logger/services/LoggerService";
import { UserProfileService } from "../../../../../lib/services/UserProfileService";
import { withPermission } from "@/lib/authorization/utils/withPermission";

/**
 * Update the user's personal and contact information
 * @param prevState Previous state
 * @param formData Form data
 * @returns Result of the update operation
 */
export const updatePersonal = withPermission(
  "user:profile:update",
  async (
    prevState: {
      success: boolean;
      error: string;
    },
    formData: FormData
  ) => {
    try {
      logger.info("Updating user personal information");

      // Extract form data
      const firstName = formData.get("firstName") as string;
      const lastName = formData.get("lastName") as string;
      const email = formData.get("email") as string;
      const phone = formData.get("phone") as string;

      // Validate form data
      if (!firstName?.trim()) {
        return {
          success: false,
          error: "First name is required",
        };
      }

      if (!lastName?.trim()) {
        return {
          success: false,
          error: "Last name is required",
        };
      }

      if (!email?.trim()) {
        return {
          success: false,
          error: "Email is required",
        };
      }

      // Email format validation
      if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
        return {
          success: false,
          error: "Invalid email format",
        };
      }

      // Phone validation (optional field)
      if (phone && !/^[\d\s\-\+\(\)]+$/.test(phone)) {
        return {
          success: false,
          error: "Invalid phone number format",
        };
      }

      // Update user profile
      const updatedProfile = await UserProfileService.updatePersonalInfo({
        firstName: firstName.trim(),
        lastName: lastName.trim(),
        email: email.trim(),
        phone: phone?.trim() || null,
      });

      if (!updatedProfile) {
        logger.error("Failed to update personal information");
        return {
          success: false,
          error: "Failed to update personal information",
        };
      }

      logger.info("Personal information updated successfully");

      // Revalidate the profile page
      revalidatePath("/[lang]/protected/user/profile");

      return {
        success: true,
        error: "",
      };
    } catch (error: unknown) {
      const err = error as Error & { code?: string; details?: string };
      logger.error(`Error updating personal information: ${err.message}`);

      return {
        success: false,
        error: "An unexpected error occurred. Please try again later.",
      };
    }
  }
);
