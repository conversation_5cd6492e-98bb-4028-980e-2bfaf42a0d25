"use client";

import { useActionState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { updatePersonal } from "../actions/updatePersonal";
import { UserProfile } from "../../../../../lib/types";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { CheckCircle2, AlertCircle } from "lucide-react";

interface PersonalFormProps {
  lang: string;
  dictionary: {
    user?: {
      firstName?: string;
      lastName?: string;
      email?: string;
      phone?: string;
      profileUpdated?: string;
      personalInfo?: string;
      contactInfo?: string;
    };
    common?: {
      save?: string;
      saving?: string;
      required?: string;
    };
  };
  profile: UserProfile;
}

export function PersonalForm({ lang, dictionary, profile }: PersonalFormProps) {
  const [state, formAction, pending] = useActionState(updatePersonal, {
    success: false,
    error: "",
  });

  return (
    <div className="space-y-6">
      {state.success && (
        <Alert className="bg-green-50 border-green-200">
          <CheckCircle2 className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-600">
            {dictionary.user?.profileUpdated || "Profile updated successfully"}
          </AlertDescription>
        </Alert>
      )}

      {state.error && (
        <Alert className="bg-red-50 border-red-200">
          <AlertCircle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-600">{state.error}</AlertDescription>
        </Alert>
      )}

      <form action={formAction} className="space-y-6">
        <input type="hidden" name="lang" value={lang} />

        <div className="space-y-6">
          {/* Personal Information Section */}
          <div>
            <h3 className="text-lg font-medium mb-4">
              {dictionary.user?.personalInfo || "Personal Information"}
            </h3>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="firstName">
                  {dictionary.user?.firstName || "First Name"}{" "}
                  <span className="text-red-500">*</span>
                </Label>
                <Input id="firstName" name="firstName" defaultValue={profile.firstName} required />
              </div>
              <div className="space-y-2">
                <Label htmlFor="lastName">
                  {dictionary.user?.lastName || "Last Name"} <span className="text-red-500">*</span>
                </Label>
                <Input id="lastName" name="lastName" defaultValue={profile.lastName} required />
              </div>
            </div>
          </div>

          {/* Contact Information Section */}
          <div>
            <h3 className="text-lg font-medium mb-4">
              {dictionary.user?.contactInfo || "Contact Information"}
            </h3>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="email">
                  {dictionary.user?.email || "Email"} <span className="text-red-500">*</span>
                </Label>
                <Input id="email" name="email" type="email" defaultValue={profile.email} required />
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone">{dictionary.user?.phone || "Phone"}</Label>
                <Input id="phone" name="phone" type="tel" defaultValue={profile.phone || ""} />
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-end space-x-4">
          <Button type="submit" disabled={pending}>
            {pending ? dictionary.common?.saving || "Saving..." : dictionary.common?.save || "Save"}
          </Button>
        </div>
      </form>
    </div>
  );
}
