import { render, screen } from "@testing-library/react";
import { PersonalForm } from "../components/PersonalForm";
import { UserProfile } from "../../../../../lib/types";

// Mock the necessary dependencies
jest.mock("../actions/updatePersonal", () => ({
  updatePersonal: jest.fn(),
}));

// Mock useActionState
jest.mock("react", () => ({
  ...jest.requireActual("react"),
  useActionState: jest.fn().mockReturnValue([{ success: false, error: "" }, jest.fn(), false]),
}));

describe("PersonalForm", () => {
  const mockProfile: UserProfile = {
    id: "test-user-id",
    firstName: "John",
    lastName: "Doe",
    email: "<EMAIL>",
    language: "en",
    notificationPreferences: {
      email: true,
      sms: false,
      inApp: true,
    },
  };

  const mockDictionary = {
    lang: "en",
    user: {
      firstName: "First Name",
      lastName: "Last Name",
      profileUpdated: "Profile Updated",
    },
    common: {
      save: "Save",
      cancel: "Cancel",
    },
    errors: {
      networkError: "Network error",
      authError: "Authentication error",
      unexpectedError: "Unexpected error",
      errorTitle: "Error",
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock is handled by the useActionState mock
  });

  it("renders the form with initial values", () => {
    render(<PersonalForm lang="en" dictionary={mockDictionary} profile={mockProfile} />);

    // Check if form fields are rendered with correct values
    const firstNameInput = screen.getByLabelText(/First Name/i) as HTMLInputElement;
    const lastNameInput = screen.getByLabelText(/Last Name/i) as HTMLInputElement;
    const emailInput = screen.getByLabelText(/Email/i) as HTMLInputElement;

    expect(firstNameInput.value).toBe("John");
    expect(lastNameInput.value).toBe("Doe");
    expect(emailInput.value).toBe("<EMAIL>");

    // Check if save button is rendered
    expect(screen.getByRole("button", { name: /Save/i })).toBeInTheDocument();
  });
});
