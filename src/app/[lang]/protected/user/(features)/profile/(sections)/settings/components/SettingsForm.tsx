"use client";

import { useActionState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { updateSettings } from "../actions/update";
import { UserProfile } from "../../../../../lib/types";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { CheckCircle2, AlertCircle } from "lucide-react";

interface SettingsFormProps {
  lang: string;
  dictionary: {
    user?: {
      language?: string;
      notifications?: string;
      emailNotifications?: string;
      smsNotifications?: string;
      inAppNotifications?: string;
      profileUpdated?: string;
    };
    common?: {
      save?: string;
      saving?: string;
      english?: string;
      french?: string;
    };
  };
  profile: UserProfile;
}

export function SettingsForm({ lang, dictionary, profile }: SettingsFormProps) {
  const [state, formAction, pending] = useActionState(updateSettings, {
    success: false,
    error: "",
    newLanguage: "",
  });

  // Server action will handle language change navigation if needed

  return (
    <div className="space-y-6">
      {state.success && (
        <Alert className="bg-green-50 border-green-200">
          <CheckCircle2 className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-600">
            {dictionary.user?.profileUpdated || "Settings updated successfully"}
          </AlertDescription>
        </Alert>
      )}

      {state.error && (
        <Alert className="bg-red-50 border-red-200">
          <AlertCircle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-600">{state.error}</AlertDescription>
        </Alert>
      )}

      <form action={formAction} className="space-y-6">
        <input type="hidden" name="lang" value={lang} />

        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="language">{dictionary.user?.language || "Language"}</Label>
            <Select name="language" defaultValue={profile.language}>
              <SelectTrigger id="language">
                <SelectValue placeholder="Select language" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="en">{dictionary.common?.english || "English"}</SelectItem>
                <SelectItem value="fr">{dictionary.common?.french || "French"}</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Separator className="my-4" />

          <div className="space-y-2">
            <Label>{dictionary.user?.notifications || "Notifications"}</Label>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="emailNotifications"
                  name="emailNotifications"
                  defaultChecked={profile.notificationPreferences.email}
                />
                <Label htmlFor="emailNotifications" className="font-normal">
                  {dictionary.user?.emailNotifications || "Email Notifications"}
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="smsNotifications"
                  name="smsNotifications"
                  defaultChecked={profile.notificationPreferences.sms}
                />
                <Label htmlFor="smsNotifications" className="font-normal">
                  {dictionary.user?.smsNotifications || "SMS Notifications"}
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="inAppNotifications"
                  name="inAppNotifications"
                  defaultChecked={profile.notificationPreferences.inApp}
                />
                <Label htmlFor="inAppNotifications" className="font-normal">
                  {dictionary.user?.inAppNotifications || "In-App Notifications"}
                </Label>
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-end space-x-4">
          <Button type="submit" disabled={pending}>
            {pending ? dictionary.common?.saving || "Saving..." : dictionary.common?.save || "Save"}
          </Button>
        </div>
      </form>
    </div>
  );
}
