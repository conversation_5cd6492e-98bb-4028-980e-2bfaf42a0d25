import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { UserProfileService } from "../../../../lib/services/UserProfileService";
import { PersonalForm } from "./components/PersonalForm";
import { i18n } from "@/lib/i18n/services/I18nService";

interface PersonalPageProps {
  params: Promise<{
    lang: string;
  }>;
}

export default async function PersonalPage({ params }: PersonalPageProps) {
  // Await the params
  const { lang } = await params;

  // Get user profile
  const profile = await UserProfileService.getCurrentUserProfile();
  if (!profile) {
    // Handle case where profile couldn't be loaded
    throw new Error("Failed to load user profile");
  }

  // Get dictionary for translations
  const dictionary = i18n.getDictionary(lang);

  return (
    <Card>
      <CardHeader>
        <CardTitle>{dictionary.user.personal}</CardTitle>
        <CardDescription>{dictionary.user.personalDescription}</CardDescription>
      </CardHeader>
      <CardContent>
        <PersonalForm lang={lang} dictionary={dictionary} profile={profile} />
      </CardContent>
    </Card>
  );
}
