import { UserProfileService } from "../../../../lib/services/UserProfileService";
import { AccountForm } from "./components/AccountForm";
import { i18n } from "@/lib/i18n/services/I18nService";

interface AccountPageProps {
  params: Promise<{
    lang: string;
  }>;
}

export default async function AccountPage({ params }: AccountPageProps) {
  // Await the params
  const { lang } = await params;

  // Get user profile
  const profile = await UserProfileService.getCurrentUserProfile();
  if (!profile) {
    // Handle case where profile couldn't be loaded
    throw new Error("Failed to load user profile");
  }

  // Get dictionary for translations
  const dictionary = i18n.getDictionary(lang);

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">{dictionary.user?.account || "Account"}</h3>
        <p className="text-sm text-muted-foreground">
          {dictionary.user?.accountDescription || "Manage your account information and password."}
        </p>
      </div>
      <AccountForm lang={lang} dictionary={dictionary} profile={profile} />
    </div>
  );
}
