"use client";

import { useActionState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { resetPassword } from "../actions/resetPassword";
import { UserProfile } from "../../../../../lib/types";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { CheckCircle2, AlertCircle, Mail, Shield } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

interface AccountFormProps {
  lang: string;
  dictionary: {
    user?: {
      account?: string;
      accountInfo?: string;
      email?: string;
      role?: string;
      resetPassword?: string;
      resetPasswordDescription?: string;
      resetPasswordSuccess?: string;
      roles?: {
        [key: string]: string;
      };
    };
    common?: {
      save?: string;
      saving?: string;
      send?: string;
      sending?: string;
    };
  };
  profile: UserProfile;
}

export function AccountForm({ lang, dictionary, profile }: AccountFormProps) {
  const [state, formAction, pending] = useActionState(resetPassword, {
    success: false,
    error: "",
  });

  return (
    <div className="space-y-6">
      {state.success && (
        <Alert className="bg-green-50 border-green-200">
          <CheckCircle2 className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-600">
            {dictionary.user?.resetPasswordSuccess || "Password reset email sent successfully"}
          </AlertDescription>
        </Alert>
      )}

      {state.error && (
        <Alert className="bg-red-50 border-red-200">
          <AlertCircle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-600">{state.error}</AlertDescription>
        </Alert>
      )}

      {/* Account Information Card */}
      <Card>
        <CardHeader>
          <CardTitle>{dictionary.user?.accountInfo || "Account Information"}</CardTitle>
          <CardDescription>{dictionary.user?.account || "Your account details"}</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="email-display">{dictionary.user?.email || "Email"}</Label>
              <div className="flex items-center space-x-2 border rounded-md p-2 bg-muted/50">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <span id="email-display" className="text-sm">
                  {profile.email}
                </span>
              </div>
              <p className="text-xs text-muted-foreground">Email cannot be changed directly</p>
            </div>
            <div className="space-y-2">
              <Label htmlFor="role-display">{dictionary.user?.role || "Role"}</Label>
              <div className="flex items-center space-x-2 border rounded-md p-2 bg-muted/50">
                <Shield className="h-4 w-4 text-muted-foreground" />
                <span id="role-display" className="text-sm">
                  {profile.role}
                </span>
              </div>
              <p className="text-xs text-muted-foreground">Role is assigned by administrators</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Password Reset Card */}
      <Card>
        <CardHeader>
          <CardTitle>{dictionary.user?.resetPassword || "Reset Password"}</CardTitle>
          <CardDescription>
            {dictionary.user?.resetPasswordDescription ||
              "Send a password reset link to your email address"}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form action={formAction} className="space-y-4">
            <input type="hidden" name="lang" value={lang} />
            <input type="hidden" name="email" value={profile.email} />

            <div className="flex justify-end space-x-4">
              <Button type="submit" disabled={pending}>
                {pending
                  ? dictionary.common?.sending || "Sending..."
                  : dictionary.common?.send || "Send Reset Link"}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
