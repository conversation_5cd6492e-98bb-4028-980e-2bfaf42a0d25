"use server";

import { revalidatePath } from "next/cache";
import { logger } from "@/lib/logger/services/LoggerService";
import { UserProfileService } from "../../../../../lib/services/UserProfileService";
import { SettingsUpdate, NotificationPreferences } from "../../../../../lib/types";
import { withPermission } from "@/lib/authorization/utils/withPermission";

/**
 * Update the user's settings
 * @param prevState Previous state
 * @param formData Form data
 * @returns Result of the update operation
 */
export const updateSettings = withPermission(
  "user:profile:update",
  async (
    prevState: {
      success: boolean;
      error: string;
      newLanguage?: string;
    },
    formData: FormData
  ) => {
    try {
      logger.info("Updating user settings");

      // Extract form data
      const language = formData.get("language") as string;
      const emailNotifications = formData.get("emailNotifications") === "on";
      const smsNotifications = formData.get("smsNotifications") === "on";
      const inAppNotifications = formData.get("inAppNotifications") === "on";

      // Validate form data
      const errors: Record<string, string[]> = {};

      // Language validation
      if (!language) {
        errors.language = ["Language selection is required"];
      } else if (!["en", "fr"].includes(language)) {
        errors.language = ["Invalid language selection. Only English and French are supported."];
        logger.warn("Invalid language selection", { language });
      }

      // Notification preferences validation - ensure they are boolean values
      const notificationTypes = {
        email: emailNotifications,
        sms: smsNotifications,
        inApp: inAppNotifications,
      };

      // Check for potential tampering with notification values
      Object.entries(notificationTypes).forEach(([type, value]) => {
        if (typeof value !== "boolean") {
          logger.warn(`Invalid notification preference type for ${type}`, { value });
          errors[`${type}Notifications`] = ["Invalid notification preference value"];
        }
      });

      if (Object.keys(errors).length > 0) {
        logger.warn("Validation errors in settings update", { errors });
        return {
          success: false,
          error: "Validation failed",
          errors,
        };
      }

      // Prepare notification preferences
      const notificationPreferences: NotificationPreferences = {
        email: emailNotifications,
        sms: smsNotifications,
        inApp: inAppNotifications,
      };

      // Update user profile
      const updateData: SettingsUpdate = {
        language: language as "en" | "fr",
        notificationPreferences,
      };

      const updatedProfile = await UserProfileService.updateSettings(updateData);

      if (!updatedProfile) {
        logger.error("Failed to update settings");
        return {
          success: false,
          error: "Failed to update settings",
        };
      }

      logger.info("Settings updated successfully");

      // Revalidate the profile page
      revalidatePath("/[lang]/protected/user/profile");

      return {
        success: true,
        error: "",
        newLanguage: language,
      };
    } catch (error: unknown) {
      const err = error as Error & { code?: string; details?: string };
      logger.error(`Error updating settings: ${err.message}`);

      return {
        success: false,
        error: "An unexpected error occurred. Please try again later.",
      };
    }
  }
);
