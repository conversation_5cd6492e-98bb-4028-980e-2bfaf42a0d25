import { updatePersonal } from "../actions/updatePersonal";
import { UserProfileService } from "../../../../../lib/services/UserProfileService";
import { logger } from "@/lib/logger/services/LoggerService";
import { revalidatePath } from "next/cache";

// Mock the necessary dependencies
jest.mock("../../../../../lib/services/UserProfileService", () => ({
  UserProfileService: {
    getCurrentUserProfile: jest.fn(),
    updatePersonalInfo: jest.fn(),
  },
}));

jest.mock("@/lib/logger/services/LoggerService", () => ({
  logger: {
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
  },
}));

jest.mock("next/cache", () => ({
  revalidatePath: jest.fn(),
}));

jest.mock("@/lib/authorization/utils/withPermission", () => ({
  withPermission: jest.fn((permission, fn) => fn),
}));

describe("updatePersonal", () => {
  const mockPrevState = {
    success: false,
    error: "",
  };

  const mockFormData = new FormData();

  beforeEach(() => {
    jest.clearAllMocks();

    // Set up form data
    mockFormData.set("firstName", "John");
    mockFormData.set("lastName", "Doe");
    mockFormData.set("email", "<EMAIL>");
    mockFormData.set("phone", "");

    // Mock current user
    (UserProfileService.getCurrentUserProfile as jest.Mock).mockResolvedValue({
      id: "test-user-id",
      firstName: "Old First",
      lastName: "Old Last",
      email: "<EMAIL>",
    });

    // Mock successful profile update
    (UserProfileService.updatePersonalInfo as jest.Mock).mockResolvedValue({
      id: "test-user-id",
      firstName: "John",
      lastName: "Doe",
      email: "<EMAIL>",
    });
  });

  it("updates personal information successfully", async () => {
    const result = await updatePersonal(mockPrevState, mockFormData);

    // Check if the user profile was updated with correct data
    expect(UserProfileService.updatePersonalInfo).toHaveBeenCalledWith({
      firstName: "John",
      lastName: "Doe",
      email: "<EMAIL>",
      phone: null,
    });

    // Check if the path was revalidated
    expect(revalidatePath).toHaveBeenCalledWith("/[lang]/protected/user/profile");

    // Check if logger was called
    expect(logger.info).toHaveBeenCalledWith("Personal information updated successfully");

    // Check the result
    expect(result).toEqual({
      success: true,
      error: "",
    });
  });

  it("validates form data and returns errors", async () => {
    // Set up invalid form data
    const invalidFormData = new FormData();
    invalidFormData.set("firstName", "");
    invalidFormData.set("lastName", "");

    const result = await updatePersonal(mockPrevState, invalidFormData);

    // Check if validation errors were returned
    expect(result).toEqual({
      success: false,
      error: "First name is required",
    });

    // Check if the user profile was not updated
    expect(UserProfileService.updatePersonalInfo).not.toHaveBeenCalled();
  });

  it("handles email validation", async () => {
    // Create form data with invalid email
    const invalidEmailFormData = new FormData();
    invalidEmailFormData.set("firstName", "John");
    invalidEmailFormData.set("lastName", "Doe");
    invalidEmailFormData.set("email", "invalid-email");

    const result = await updatePersonal(mockPrevState, invalidEmailFormData);

    // Check if validation error was returned
    expect(result).toEqual({
      success: false,
      error: "Invalid email format",
    });

    // Check if the user profile was not updated
    expect(UserProfileService.updatePersonalInfo).not.toHaveBeenCalled();
  });

  it("handles case when profile update fails", async () => {
    // Mock profile update failure
    (UserProfileService.updatePersonalInfo as jest.Mock).mockResolvedValue(null);

    const result = await updatePersonal(mockPrevState, mockFormData);

    // Check if error was returned
    expect(result).toEqual({
      success: false,
      error: "Failed to update personal information",
    });

    // Check if logger was called
    expect(logger.error).toHaveBeenCalledWith("Failed to update personal information");
  });

  it("handles unexpected errors", async () => {
    // Mock unexpected error
    const errorMessage = "Unexpected error";
    (UserProfileService.updatePersonalInfo as jest.Mock).mockRejectedValue(new Error(errorMessage));

    const result = await updatePersonal(mockPrevState, mockFormData);

    // Check if error was logged
    expect(logger.error).toHaveBeenCalledWith(
      `Error updating personal information: ${errorMessage}`
    );

    // Check if error was returned
    expect(result).toEqual({
      success: false,
      error: "An unexpected error occurred. Please try again later.",
    });
  });
});
