import { Skeleton } from "@/components/ui/skeleton";
import { <PERSON>, Card<PERSON>ontent, CardHeader } from "@/components/ui/card";

export default function SettingsLoading() {
  return (
    <Card>
      <CardHeader>
        <Skeleton className="h-6 w-1/3" />
        <Skeleton className="h-4 w-2/3 mt-2" />
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Skeleton className="h-4 w-1/4" />
          <Skeleton className="h-10 w-full" />
        </div>
        <div className="space-y-2">
          <Skeleton className="h-4 w-1/4" />
          <div className="space-y-2">
            <Skeleton className="h-6 w-full" />
            <Skeleton className="h-6 w-full" />
            <Skeleton className="h-6 w-full" />
          </div>
        </div>
        <div className="space-y-2 pt-4">
          <Skeleton className="h-10 w-24 mr-2 inline-block" />
          <Skeleton className="h-10 w-24 inline-block" />
        </div>
      </CardContent>
    </Card>
  );
}
