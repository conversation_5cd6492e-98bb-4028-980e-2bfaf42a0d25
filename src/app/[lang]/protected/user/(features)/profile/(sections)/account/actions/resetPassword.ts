"use server";

import { logger } from "@/lib/logger/services/LoggerService";
import { createClient } from "@/lib/supabase/server";
import { withPermission } from "@/lib/authorization/utils/withPermission";

/**
 * Send a password reset email to the user
 * @param prevState Previous state
 * @param formData Form data
 * @returns Result of the operation
 */
export const resetPassword = withPermission(
  "user:profile:update",
  async (
    prevState: {
      success: boolean;
      error: string;
    },
    formData: FormData
  ) => {
    try {
      logger.info("Sending password reset email");

      // Extract form data
      const email = formData.get("email") as string;
      const lang = formData.get("lang") as string;

      // Validate email
      if (!email?.trim()) {
        return {
          success: false,
          error: "Email is required",
        };
      }

      // Email format validation
      if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
        return {
          success: false,
          error: "Invalid email format",
        };
      }

      // Get Supabase client
      const supabase = await createClient();

      // Send password reset email
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/${lang}/auth/reset-password`,
      });

      if (error) {
        logger.error(`Error sending password reset email: ${error.message}`);
        return {
          success: false,
          error: "Failed to send password reset email",
        };
      }

      logger.info("Password reset email sent successfully");

      return {
        success: true,
        error: "",
      };
    } catch (error: unknown) {
      const err = error as Error & { code?: string; details?: string };
      logger.error(`Error sending password reset email: ${err.message}`);

      return {
        success: false,
        error: "An unexpected error occurred. Please try again later.",
      };
    }
  }
);
