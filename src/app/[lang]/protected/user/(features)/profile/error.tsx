"use client";

import { useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card";
import { AlertCircle, RefreshCw, LogOut } from "lucide-react";
import { useRouter } from "next/navigation";
import { logger } from "@/lib/logger/services/LoggerService";

interface ErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function ProfileError({ error, reset }: ErrorProps) {
  const router = useRouter();

  // Log the error for debugging
  useEffect(() => {
    logger.error(`Profile error: ${error.message}`);
  }, [error]);

  // Determine if this is an authentication error
  const isAuthError =
    error.message.includes("auth") ||
    error.message.includes("login") ||
    error.message.includes("unauthorized") ||
    error.message.includes("permission");

  // Determine if this is a network error
  const isNetworkError =
    error.message.includes("network") ||
    error.message.includes("fetch") ||
    error.message.includes("connection");

  // Handle redirect to login
  const handleLoginRedirect = () => {
    router.push("/login");
  };

  return (
    <Card className="border-destructive">
      <CardHeader>
        <CardTitle className="flex items-center text-destructive">
          <AlertCircle className="h-5 w-5 mr-2" />
          {isAuthError
            ? "Authentication Error"
            : isNetworkError
              ? "Network Error"
              : "Error Loading Profile"}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-muted-foreground">
          {isAuthError
            ? "There was an authentication error. You may need to log in again."
            : isNetworkError
              ? "There was a network error. Please check your connection and try again."
              : "There was an error loading your profile information. Please try again."}
        </p>
        <p className="text-sm text-destructive mt-2">{error.message}</p>
        {error.digest && (
          <p className="text-xs text-muted-foreground mt-1">Error ID: {error.digest}</p>
        )}
      </CardContent>
      <CardFooter className="flex gap-2">
        <Button onClick={reset} variant="outline" className="flex items-center">
          <RefreshCw className="h-4 w-4 mr-2" />
          Try Again
        </Button>
        {isAuthError && (
          <Button onClick={handleLoginRedirect} variant="default" className="flex items-center">
            <LogOut className="h-4 w-4 mr-2" />
            Go to Login
          </Button>
        )}
      </CardFooter>
    </Card>
  );
}
