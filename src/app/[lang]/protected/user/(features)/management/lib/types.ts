/**
 * Dictionary type for user management components
 */
export interface UserManagementDictionary {
  user?: {
    management?: {
      title?: string;
      description?: string;
      userList?: string;
      userListDescription?: string;
      createUser?: string;
      createUserDescription?: string;
      editUser?: string;
      editUserDescription?: string;
      userDetails?: string;
      userCreated?: string;
      userUpdated?: string;
      emailCannotBeChanged?: string;
      noUsersFound?: string;
      filterByRole?: string;
    };
    firstName?: string;
    lastName?: string;
    email?: string;
    phone?: string;
    role?: string;
    name?: string;
    selectRole?: string;
    personalInfo?: string;
    contactInfo?: string;
    systemInfo?: string;
    createdAt?: string;
    updatedAt?: string;
    roles?: {
      director?: string;
      coordinator?: string;
      socialWorker?: string;
      systemAdmin?: string;
    };
  };
  common?: {
    save?: string;
    saving?: string;
    required?: string;
    back?: string;
    edit?: string;
    search?: string;
    all?: string;
    actions?: string;
  };
}
