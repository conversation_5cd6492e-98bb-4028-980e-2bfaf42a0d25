import { redirect } from "next/navigation";
import { Metadata } from "next";
import { i18n } from "@/lib/i18n/services/I18nService";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ lang: string }>;
}): Promise<Metadata> {
  const resolvedParams = await params;
  const dictionary = i18n.getDictionary(resolvedParams.lang);

  return {
    title: dictionary.user?.management?.title || "User Management",
    description: dictionary.user?.management?.description || "Manage users in your organization",
  };
}

export default function UserManagementPage() {
  // Redirect to the list page
  redirect("./management/list");
}
