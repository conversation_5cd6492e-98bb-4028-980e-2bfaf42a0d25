"use client";

import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON><PERSON>,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { FileQuestion } from "lucide-react";
import { useParams, useRouter } from "next/navigation";
import { useEffect, useState } from "react";

export default function UserManagementNotFound() {
  const params = useParams();
  const lang = (params?.lang as string) || "en";
  const router = useRouter();
  const [dictionary, setDictionary] = useState<{
    user?: {
      management?: {
        userNotFound?: string;
        userNotFoundDescription?: string;
        userList?: string;
      };
    };
  }>({
    user: {
      management: {
        userNotFound: "User Not Found",
        userNotFoundDescription:
          "The user you are looking for does not exist or you don't have permission to view it.",
        userList: "Return to User List",
      },
    },
  });

  // Fetch dictionary on client side
  useEffect(() => {
    const fetchDictionary = async () => {
      try {
        const response = await fetch(`/api/i18n?lang=${lang}`);
        if (response.ok) {
          const data = await response.json();
          setDictionary(data);
        }
      } catch (error) {
        console.error("Failed to load translations:", error);
      }
    };

    fetchDictionary();
  }, [lang]);

  return (
    <Card className="mx-auto max-w-md">
      <CardHeader>
        <div className="flex items-center gap-2">
          <FileQuestion className="h-5 w-5 text-muted-foreground" />
          <CardTitle>{dictionary.user?.management?.userNotFound || "User Not Found"}</CardTitle>
        </div>
        <CardDescription>
          {dictionary.user?.management?.userNotFoundDescription ||
            "The user you are looking for does not exist or you don't have permission to view it."}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-muted-foreground">This could be because:</p>
        <ul className="list-disc pl-5 text-sm text-muted-foreground mt-2 space-y-1">
          <li>The user has been deleted</li>
          <li>The user ID is incorrect</li>
          <li>You don't have permission to view this user</li>
        </ul>
      </CardContent>
      <CardFooter>
        <Button
          onClick={() => router.push(`/${lang}/protected/user/management`)}
          className="w-full"
        >
          {dictionary.user?.management?.userList || "Return to User List"}
        </Button>
      </CardFooter>
    </Card>
  );
}
