import { redirect } from "next/navigation";
import { createClient } from "@/lib/supabase/server";
import { i18n } from "@/lib/i18n/services/I18nService";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { UserManagementService } from "../../../../../lib/services/UserManagementService";
import { UserForm } from "../../../components/UserForm";
import { AuthorizationRequired } from "@/lib/authorization/components/AuthorizationRequired";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { ArrowLeft } from "lucide-react";
import { PERMISSIONS } from "@/lib/authorization/config/permissions";

interface EditUserPageProps {
  params: Promise<{
    lang: string;
    id: string;
  }>;
}

export default async function EditUserPage({ params }: EditUserPageProps) {
  const { lang, id } = await params;

  // Get the current user
  const supabase = await createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user) {
    redirect(`/${lang}/login`);
  }

  // Get dictionary for translations
  const dictionary = i18n.getDictionary(lang);

  // Get the user to edit
  const userToEdit = await UserManagementService.getUserProfile(id);
  if (!userToEdit) {
    redirect(`/${lang}/protected/user/management/list`);
  }

  // Get the current user's organization
  const { data: roleData } = await supabase
    .from("user_roles")
    .select("organization_id")
    .eq("user_id", user.id)
    .single();

  if (!roleData?.organization_id) {
    redirect(`/${lang}/protected/dashboard`);
  }

  // Check if the user belongs to the current user's organization
  if (userToEdit.organizationId !== roleData.organization_id) {
    redirect(`/${lang}/protected/user/management/list`);
  }

  return (
    <AuthorizationRequired permission={PERMISSIONS.USER.MANAGEMENT.UPDATE}>
      <Card>
        <CardHeader>
          <CardTitle>
            {dictionary.user.management.editUser}: {userToEdit.firstName} {userToEdit.lastName}
          </CardTitle>
          <CardDescription>{dictionary.user.management.editUserDescription}</CardDescription>
        </CardHeader>
        <CardContent>
          <UserForm
            lang={lang}
            dictionary={dictionary}
            organizationId={roleData.organization_id}
            mode="edit"
            user={userToEdit}
          />
        </CardContent>
        <CardFooter className="flex justify-between">
          <Link href={`/${lang}/protected/user/management/view/${id}`}>
            <Button variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              {"Back"}
            </Button>
          </Link>
        </CardFooter>
      </Card>
    </AuthorizationRequired>
  );
}
