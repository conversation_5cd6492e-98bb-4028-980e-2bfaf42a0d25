import { redirect } from "next/navigation";
import { createClient } from "@/lib/supabase/server";
import { i18n } from "@/lib/i18n/services/I18nService";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { UserManagementService } from "../../../../../lib/services/UserManagementService";
import { AuthorizationRequired } from "@/lib/authorization/components/AuthorizationRequired";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { ArrowLeft, Pencil } from "lucide-react";
import { PERMISSIONS } from "@/lib/authorization/config/permissions";
import { UserView } from "../../../components/UserView";

interface ViewUserPageProps {
  params: Promise<{
    lang: string;
    id: string;
  }>;
}

export default async function ViewUserPage({ params }: ViewUserPageProps) {
  const { lang, id } = await params;

  // Get the current user
  const supabase = await createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user) {
    redirect(`/${lang}/login`);
  }

  // Get dictionary for translations
  const dictionary = i18n.getDictionary(lang);

  // Get the user to view
  const userToView = await UserManagementService.getUserProfile(id);
  if (!userToView) {
    redirect(`/${lang}/protected/user/management/list`);
  }

  // Get the current user's organization
  const { data: roleData } = await supabase
    .from("user_roles")
    .select("organization_id")
    .eq("user_id", user.id)
    .single();

  if (!roleData?.organization_id) {
    redirect(`/${lang}/protected/dashboard`);
  }

  // Check if the user belongs to the current user's organization
  if (userToView.organizationId !== roleData.organization_id) {
    redirect(`/${lang}/protected/user/management/list`);
  }

  return (
    <AuthorizationRequired permission={PERMISSIONS.USER.MANAGEMENT.READ}>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>
              {userToView.firstName} {userToView.lastName}
            </CardTitle>
            <CardDescription>{dictionary.user.management.userDetails}</CardDescription>
          </div>
          <AuthorizationRequired permission={PERMISSIONS.USER.MANAGEMENT.UPDATE}>
            <Link href={`/${lang}/protected/user/management/edit/${id}`}>
              <Button variant="outline">
                <Pencil className="h-4 w-4 mr-2" />
                {"Edit"}
              </Button>
            </Link>
          </AuthorizationRequired>
        </CardHeader>
        <CardContent>
          <UserView user={userToView} dictionary={dictionary} />
        </CardContent>
        <CardFooter className="flex justify-between">
          <Link href={`/${lang}/protected/user/management/list`}>
            <Button variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              {"Back"}
            </Button>
          </Link>
        </CardFooter>
      </Card>
    </AuthorizationRequired>
  );
}
