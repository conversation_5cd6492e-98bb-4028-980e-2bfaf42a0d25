import { redirect } from "next/navigation";
import { createClient } from "@/lib/supabase/server";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { UserManagementService } from "../../../../lib/services/UserManagementService";
import { UserList } from "../../components/UserList";
import { RoleService } from "@/lib/authorization/services/RoleService";
import { AuthorizationRequired } from "@/lib/authorization/components/AuthorizationRequired";
import { PERMISSIONS } from "@/lib/authorization/config/permissions";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { Plus } from "lucide-react";
import { i18n } from "@/lib/i18n/services/I18nService";

interface UserListPageProps {
  params: Promise<{
    lang: string;
  }>;
}

export default async function UserListPage({ params }: UserListPageProps) {
  const { lang } = await params;

  // Get the current user
  const supabase = await createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user) {
    redirect(`/${lang}/login`);
  }

  // Get dictionary for translations
  const dictionary = i18n.getDictionary(lang);

  // Get the current user's role (for authorization)
  await RoleService.getCurrentUserRole();

  // Get the current user's organization
  const { data: roleData } = await supabase
    .from("user_roles")
    .select("organization_id")
    .eq("user_id", user.id)
    .single();

  if (!roleData?.organization_id) {
    redirect(`/${lang}/protected/dashboard`);
  }

  // Get all users for the organization
  const users = await UserManagementService.getUsersByOrganization(roleData.organization_id);

  return (
    <AuthorizationRequired permission={PERMISSIONS.USER.MANAGEMENT.LIST}>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>{dictionary.user.management.userList}</CardTitle>
            <CardDescription>{dictionary.user.management.userListDescription}</CardDescription>
          </div>
          <AuthorizationRequired permission={PERMISSIONS.USER.MANAGEMENT.CREATE}>
            <Link href={`/${lang}/protected/user/management/create`}>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                {dictionary.user.management.createUser}
              </Button>
            </Link>
          </AuthorizationRequired>
        </CardHeader>
        <CardContent>
          <UserList users={users} lang={lang} dictionary={dictionary} />
        </CardContent>
      </Card>
    </AuthorizationRequired>
  );
}
