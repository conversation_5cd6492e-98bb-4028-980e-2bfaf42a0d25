import { redirect } from "next/navigation";
import { createClient } from "@/lib/supabase/server";
import { i18n } from "@/lib/i18n/services/I18nService";
import {
  Card,
  CardContent,
  CardDescription,
  <PERSON>Footer,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { UserForm } from "../../components/UserForm";
import { AuthorizationRequired } from "@/lib/authorization/components/AuthorizationRequired";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { ArrowLeft } from "lucide-react";
import { PERMISSIONS } from "@/lib/authorization/config/permissions";

interface CreateUserPageProps {
  params: Promise<{
    lang: string;
  }>;
}

export default async function CreateUserPage({ params }: CreateUserPageProps) {
  const { lang } = await params;

  // Get the current user
  const supabase = await createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user) {
    redirect(`/${lang}/login`);
  }

  // Get dictionary for translations
  const dictionary = i18n.getDictionary(lang);

  // Get the current user's organization
  const { data: roleData } = await supabase
    .from("user_roles")
    .select("organization_id")
    .eq("user_id", user.id)
    .single();

  if (!roleData?.organization_id) {
    redirect(`/${lang}/protected/dashboard`);
  }

  return (
    <AuthorizationRequired permission={PERMISSIONS.USER.MANAGEMENT.CREATE}>
      <Card>
        <CardHeader>
          <CardTitle>{dictionary.user.management.createUser}</CardTitle>
          <CardDescription>{dictionary.user.management.createUserDescription}</CardDescription>
        </CardHeader>
        <CardContent>
          <UserForm
            lang={lang}
            dictionary={dictionary}
            organizationId={roleData.organization_id}
            mode="create"
          />
        </CardContent>
        <CardFooter className="flex justify-between">
          <Link href={`/${lang}/protected/user/management/list`}>
            <Button variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              {"Back"}
            </Button>
          </Link>
        </CardFooter>
      </Card>
    </AuthorizationRequired>
  );
}
