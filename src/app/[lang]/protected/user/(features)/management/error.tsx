"use client";

import { useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { AlertCircle } from "lucide-react";
import { logger } from "@/lib/logger/services/LoggerService";

export default function UserManagementError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log the error to the server
    logger.error(
      `User Management Error: ${error.message}`,
      error,
      error.digest ? { digest: error.digest } : undefined
    );
  }, [error]);

  return (
    <Card className="mx-auto max-w-md">
      <CardHeader>
        <div className="flex items-center gap-2">
          <AlertCircle className="h-5 w-5 text-destructive" />
          <CardTitle>Something went wrong</CardTitle>
        </div>
        <CardDescription>There was an error loading the user management page.</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="text-sm text-muted-foreground">
          <p>Error: {error.message}</p>
          {error.digest && <p className="mt-2 text-xs">Error ID: {error.digest}</p>}
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={() => (window.location.href = window.location.pathname)}>
          Go Back
        </Button>
        <Button onClick={reset}>Try Again</Button>
      </CardFooter>
    </Card>
  );
}
