import { Suspense } from "react";
import { <PERSON>ada<PERSON> } from "next";
import { Card, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { i18n } from "@/lib/i18n/services/I18nService";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ lang: string }>;
}): Promise<Metadata> {
  const resolvedParams = await params;
  const dictionary = i18n.getDictionary(resolvedParams.lang);

  return {
    title: dictionary.user?.management?.title || "User Management",
    description: dictionary.user?.management?.description || "Manage users in your organization",
  };
}

export default async function UserManagementLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ lang: string }>;
}) {
  const { lang } = await params;
  const dictionary = i18n.getDictionary(lang);

  return (
    <div className="flex flex-col gap-8 pb-10">
      <Card>
        <CardHeader>
          <CardTitle>{dictionary.user?.management?.title || "User Management"}</CardTitle>
          <CardDescription>
            {dictionary.user?.management?.description || "Manage users in your organization"}
          </CardDescription>
        </CardHeader>
      </Card>

      <Tabs defaultValue="list" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="list" asChild>
            <a href={`/${lang}/protected/user/management`}>
              {dictionary.user?.management?.userList || "User List"}
            </a>
          </TabsTrigger>
          <TabsTrigger value="create" asChild>
            <a href={`/${lang}/protected/user/management/create`}>
              {dictionary.user?.management?.createUser || "Create User"}
            </a>
          </TabsTrigger>
        </TabsList>
        <TabsContent value="list" className="w-full">
          <Suspense
            fallback={
              <Card className="p-4">
                <Skeleton className="h-[400px] w-full" />
              </Card>
            }
          >
            {children}
          </Suspense>
        </TabsContent>
        <TabsContent value="create" className="w-full">
          <Suspense
            fallback={
              <Card className="p-4">
                <Skeleton className="h-[400px] w-full" />
              </Card>
            }
          >
            {children}
          </Suspense>
        </TabsContent>
      </Tabs>
    </div>
  );
}
