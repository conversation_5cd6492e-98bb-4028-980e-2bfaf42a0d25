"use client";

import { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { UserProfile } from "../../../lib/types";
import { Button } from "@/components/ui/button";
import { Eye, Pencil } from "lucide-react";
import Link from "next/link";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { UserManagementDictionary } from "../lib/types";

interface UserListProps {
  users: UserProfile[];
  lang: string;
  dictionary: UserManagementDictionary;
}

export function UserList({ users, lang, dictionary }: UserListProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [roleFilter, setRoleFilter] = useState<string>("all");

  // Filter users based on search term and role filter
  const filteredUsers = users.filter((user) => {
    const matchesSearch =
      searchTerm === "" ||
      user.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesRole = roleFilter === "all" || user.role === roleFilter;

    return matchesSearch && matchesRole;
  });

  // Get role display name
  const getRoleDisplayName = (role?: string) => {
    if (!role) return "—";

    switch (role) {
      case "Director":
        return dictionary.user?.roles?.director || "Director";
      case "Coordinator":
        return dictionary.user?.roles?.coordinator || "Coordinator";
      case "SocialWorker":
        return dictionary.user?.roles?.socialWorker || "Social Worker";
      case "SystemAdmin":
        return dictionary.user?.roles?.systemAdmin || "System Admin";
      default:
        return role;
    }
  };

  // Get role badge color
  const getRoleBadgeVariant = (
    role?: string
  ): "default" | "destructive" | "outline" | "secondary" | null | undefined => {
    if (!role) return "secondary";

    switch (role) {
      case "Director":
        return "default";
      case "Coordinator":
        return "secondary";
      case "SocialWorker":
        return "outline";
      case "SystemAdmin":
        return "destructive";
      default:
        return "secondary";
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row justify-between gap-4">
        <div className="flex-1">
          <Input
            placeholder={dictionary.common?.search || "Search users..."}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="max-w-sm"
          />
        </div>
        <div className="w-full sm:w-48">
          <Select value={roleFilter} onValueChange={setRoleFilter}>
            <SelectTrigger>
              <SelectValue
                placeholder={dictionary.user?.management?.filterByRole || "Filter by role"}
              />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{dictionary.common?.all || "All roles"}</SelectItem>
              <SelectItem value="Director">
                {dictionary.user?.roles?.director || "Director"}
              </SelectItem>
              <SelectItem value="Coordinator">
                {dictionary.user?.roles?.coordinator || "Coordinator"}
              </SelectItem>
              <SelectItem value="SocialWorker">
                {dictionary.user?.roles?.socialWorker || "Social Worker"}
              </SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="border rounded-md">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>{dictionary.user?.name || "Name"}</TableHead>
              <TableHead>{dictionary.user?.email || "Email"}</TableHead>
              <TableHead>{dictionary.user?.phone || "Phone"}</TableHead>
              <TableHead>{dictionary.user?.role || "Role"}</TableHead>
              <TableHead className="text-right">
                {dictionary.common?.actions || "Actions"}
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredUsers.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
                  {dictionary.user?.management?.noUsersFound || "No users found"}
                </TableCell>
              </TableRow>
            ) : (
              filteredUsers.map((user) => (
                <TableRow key={user.id}>
                  <TableCell>
                    {user.firstName} {user.lastName}
                  </TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell>{user.phone || "—"}</TableCell>
                  <TableCell>
                    <Badge variant={getRoleBadgeVariant(user.role)}>
                      {getRoleDisplayName(user.role)}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Link href={`/${lang}/protected/user/management/view/${user.id}`}>
                        <Button variant="ghost" size="icon">
                          <Eye className="h-4 w-4" />
                        </Button>
                      </Link>
                      <Link href={`/${lang}/protected/user/management/edit/${user.id}`}>
                        <Button variant="ghost" size="icon">
                          <Pencil className="h-4 w-4" />
                        </Button>
                      </Link>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
