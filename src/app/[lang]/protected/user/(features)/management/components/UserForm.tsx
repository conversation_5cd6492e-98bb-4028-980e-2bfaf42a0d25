"use client";

import { useActionState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { createUser } from "../actions/createUser";
import { updateUser } from "../actions/updateUser";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { CheckCircle2, AlertCircle } from "lucide-react";
import { UserProfile } from "../../../lib/types";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { UserManagementDictionary } from "../lib/types";

interface UserFormProps {
  lang: string;
  dictionary: UserManagementDictionary;
  organizationId: string;
  mode: "create" | "edit";
  user?: UserProfile;
}

export function UserForm({ lang, dictionary, organizationId, mode, user }: UserFormProps) {
  const router = useRouter();

  const [state, formAction, pending] = useActionState(mode === "create" ? createUser : updateUser, {
    success: false,
    error: "",
    userId: "",
  });

  // Redirect to the user view page after successful creation
  useEffect(() => {
    if (state.success && state.userId) {
      router.push(`/${lang}/protected/user/management/view/${state.userId}`);
    }
  }, [state.success, state.userId, router, lang]);

  return (
    <div className="space-y-6">
      {state.success && (
        <Alert className="bg-green-50 border-green-200">
          <CheckCircle2 className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-600">
            {mode === "create"
              ? dictionary.user?.management?.userCreated || "User created successfully"
              : dictionary.user?.management?.userUpdated || "User updated successfully"}
          </AlertDescription>
        </Alert>
      )}

      {state.error && (
        <Alert className="bg-red-50 border-red-200">
          <AlertCircle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-600">{state.error}</AlertDescription>
        </Alert>
      )}

      <form action={formAction} className="space-y-6">
        <input type="hidden" name="lang" value={lang} />
        <input type="hidden" name="organizationId" value={organizationId} />
        {mode === "edit" && user && <input type="hidden" name="userId" value={user.id} />}

        <div className="space-y-6">
          {/* Personal Information Section */}
          <div>
            <h3 className="text-lg font-medium mb-4">
              {dictionary.user?.personalInfo || "Personal Information"}
            </h3>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="firstName">
                  {dictionary.user?.firstName || "First Name"}{" "}
                  <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="firstName"
                  name="firstName"
                  defaultValue={user?.firstName || ""}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="lastName">
                  {dictionary.user?.lastName || "Last Name"} <span className="text-red-500">*</span>
                </Label>
                <Input id="lastName" name="lastName" defaultValue={user?.lastName || ""} required />
              </div>
            </div>
          </div>

          {/* Contact Information Section */}
          <div>
            <h3 className="text-lg font-medium mb-4">
              {dictionary.user?.contactInfo || "Contact Information"}
            </h3>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="email">
                  {dictionary.user?.email || "Email"} <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  defaultValue={user?.email || ""}
                  required
                  readOnly={mode === "edit"}
                  className={mode === "edit" ? "bg-muted" : ""}
                />
                {mode === "edit" && (
                  <p className="text-xs text-muted-foreground mt-1">
                    {dictionary.user?.management?.emailCannotBeChanged || "Email cannot be changed"}
                  </p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone">{dictionary.user?.phone || "Phone"}</Label>
                <Input id="phone" name="phone" type="tel" defaultValue={user?.phone || ""} />
              </div>
            </div>
          </div>

          {/* Role Section */}
          <div>
            <h3 className="text-lg font-medium mb-4">{dictionary.user?.role || "Role"}</h3>
            <div className="space-y-2">
              <Label htmlFor="role">
                {dictionary.user?.role || "Role"} <span className="text-red-500">*</span>
              </Label>
              <Select name="role" defaultValue={user?.role || "SocialWorker"}>
                <SelectTrigger>
                  <SelectValue placeholder={dictionary.user?.selectRole || "Select role"} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Director">
                    {dictionary.user?.roles?.director || "Director"}
                  </SelectItem>
                  <SelectItem value="Coordinator">
                    {dictionary.user?.roles?.coordinator || "Coordinator"}
                  </SelectItem>
                  <SelectItem value="SocialWorker">
                    {dictionary.user?.roles?.socialWorker || "Social Worker"}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        <div className="flex justify-end">
          <Button type="submit" disabled={pending}>
            {pending ? dictionary.common?.saving || "Saving..." : dictionary.common?.save || "Save"}
          </Button>
        </div>
      </form>
    </div>
  );
}
