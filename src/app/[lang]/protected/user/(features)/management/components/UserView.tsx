"use client";

import { UserProfile } from "../../../lib/types";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { UserManagementDictionary } from "../lib/types";

interface UserViewProps {
  user: UserProfile;
  dictionary: UserManagementDictionary;
}

export function UserView({ user, dictionary }: UserViewProps) {
  // Get role display name
  const getRoleDisplayName = (role?: string) => {
    if (!role) return "—";

    switch (role) {
      case "Director":
        return dictionary.user?.roles?.director || "Director";
      case "Coordinator":
        return dictionary.user?.roles?.coordinator || "Coordinator";
      case "SocialWorker":
        return dictionary.user?.roles?.socialWorker || "Social Worker";
      case "SystemAdmin":
        return dictionary.user?.roles?.systemAdmin || "System Admin";
      default:
        return role;
    }
  };

  // Get role badge color
  const getRoleBadgeVariant = (
    role?: string
  ): "default" | "destructive" | "outline" | "secondary" | null | undefined => {
    if (!role) return "secondary";

    switch (role) {
      case "Director":
        return "default";
      case "Coordinator":
        return "secondary";
      case "SocialWorker":
        return "outline";
      case "SystemAdmin":
        return "destructive";
      default:
        return "secondary";
    }
  };

  // Format date
  const formatDate = (dateString?: string) => {
    if (!dateString) return "—";
    try {
      return format(new Date(dateString), "PPP");
    } catch {
      return dateString;
    }
  };

  return (
    <div className="space-y-6">
      {/* Personal Information Section */}
      <div>
        <h3 className="text-lg font-medium mb-4">
          {dictionary.user?.personalInfo || "Personal Information"}
        </h3>
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div className="space-y-2">
            <p className="text-sm font-medium text-muted-foreground">
              {dictionary.user?.firstName || "First Name"}
            </p>
            <p className="text-base">{user.firstName}</p>
          </div>
          <div className="space-y-2">
            <p className="text-sm font-medium text-muted-foreground">
              {dictionary.user?.lastName || "Last Name"}
            </p>
            <p className="text-base">{user.lastName}</p>
          </div>
        </div>
      </div>

      {/* Contact Information Section */}
      <div>
        <h3 className="text-lg font-medium mb-4">
          {dictionary.user?.contactInfo || "Contact Information"}
        </h3>
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div className="space-y-2">
            <p className="text-sm font-medium text-muted-foreground">
              {dictionary.user?.email || "Email"}
            </p>
            <p className="text-base">{user.email}</p>
          </div>
          <div className="space-y-2">
            <p className="text-sm font-medium text-muted-foreground">
              {dictionary.user?.phone || "Phone"}
            </p>
            <p className="text-base">{user.phone || "—"}</p>
          </div>
        </div>
      </div>

      {/* Role Section */}
      <div>
        <h3 className="text-lg font-medium mb-4">{dictionary.user?.role || "Role"}</h3>
        <div className="space-y-2">
          <p className="text-sm font-medium text-muted-foreground">
            {dictionary.user?.role || "Role"}
          </p>
          <Badge variant={getRoleBadgeVariant(user.role)}>{getRoleDisplayName(user.role)}</Badge>
        </div>
      </div>

      {/* System Information Section */}
      <div>
        <h3 className="text-lg font-medium mb-4">
          {dictionary.user?.systemInfo || "System Information"}
        </h3>
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div className="space-y-2">
            <p className="text-sm font-medium text-muted-foreground">
              {dictionary.user?.createdAt || "Created At"}
            </p>
            <p className="text-base">{formatDate(user.createdAt)}</p>
          </div>
          <div className="space-y-2">
            <p className="text-sm font-medium text-muted-foreground">
              {dictionary.user?.updatedAt || "Last Updated"}
            </p>
            <p className="text-base">{formatDate(user.updatedAt)}</p>
          </div>
        </div>
      </div>
    </div>
  );
}
