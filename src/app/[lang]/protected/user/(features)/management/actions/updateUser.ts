"use server";

import { revalidatePath } from "next/cache";
import { logger } from "@/lib/logger/services/LoggerService";
import { UserManagementService } from "../../../lib/services/UserManagementService";
import { UserRole } from "@/lib/authorization/types";
import { PERMISSIONS } from "@/lib/authorization/config/permissions";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";

// Define the action state type
type ActionState = {
  success: boolean;
  error: string;
  userId: string;
};

/**
 * Update an existing user
 * @param prevState Previous state
 * @param formData Form data
 * @returns Result of the update operation
 */
export const updateUser = requirePermission(PERMISSIONS.USER.MANAGEMENT.UPDATE)(async (
  prevState: ActionState,
  formData: FormData
): Promise<ActionState> => {
  try {
    // Get form data
    const userId = formData.get("userId") as string;
    const firstName = formData.get("firstName") as string;
    const lastName = formData.get("lastName") as string;
    const phone = formData.get("phone") as string;
    const role = formData.get("role") as UserRole;
    const lang = formData.get("lang") as string;

    // Validate required fields
    if (!userId) {
      return { success: false, error: "User ID is required", userId: "" };
    }

    if (!firstName) {
      return { success: false, error: "First name is required", userId };
    }

    if (!lastName) {
      return { success: false, error: "Last name is required", userId };
    }

    if (!role) {
      return { success: false, error: "Role is required", userId };
    }

    // Get the current user profile
    const currentUser = await UserManagementService.getUserProfile(userId);
    if (!currentUser) {
      return { success: false, error: "User not found", userId };
    }

    // Update personal information
    const personalInfoUpdate = {
      firstName,
      lastName,
      phone: phone || undefined,
    };

    // Update the user's personal information
    const updatedUser = await UserManagementService.updateUserProfile(userId, personalInfoUpdate);
    if (!updatedUser) {
      return { success: false, error: "Failed to update user information", userId };
    }

    // Update the user's role if it has changed
    if (currentUser.role !== role) {
      const roleUpdateResult = await UserManagementService.updateUserRole(userId, { role });
      if (!roleUpdateResult.success) {
        return {
          success: false,
          error: roleUpdateResult.error || "Failed to update user role",
          userId,
        };
      }
    }

    // Revalidate the user pages
    revalidatePath(`/${lang}/protected/user/management/list`);
    revalidatePath(`/${lang}/protected/user/management/view/${userId}`);
    revalidatePath(`/${lang}/protected/user/management/edit/${userId}`);

    return {
      success: true,
      error: "",
      userId,
    };
  } catch (error) {
    logger.error(`Error updating user: ${error}`);
    return {
      success: false,
      error: `Failed to update user: ${error}`,
      userId: "",
    };
  }
});
