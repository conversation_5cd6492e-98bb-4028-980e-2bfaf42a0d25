"use server";

import { revalidatePath } from "next/cache";
import { logger } from "@/lib/logger/services/LoggerService";
import { UserManagementService } from "../../../lib/services/UserManagementService";
import { UserRole } from "@/lib/authorization/types";
import { PERMISSIONS } from "@/lib/authorization/config/permissions";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";

// Define the action state type
type ActionState = {
  success: boolean;
  error: string;
  userId: string;
};

/**
 * Create a new user
 * @param prevState Previous state
 * @param formData Form data
 * @returns Result of the create operation
 */
export const createUser = requirePermission(PERMISSIONS.USER.MANAGEMENT.CREATE)(async (
  prevState: ActionState,
  formData: FormData
): Promise<ActionState> => {
  try {
    // Get form data
    const firstName = formData.get("firstName") as string;
    const lastName = formData.get("lastName") as string;
    const email = formData.get("email") as string;
    const phone = formData.get("phone") as string;
    const role = formData.get("role") as UserRole;
    const organizationId = formData.get("organizationId") as string;
    const lang = formData.get("lang") as string;

    // Validate required fields
    if (!firstName) {
      return { success: false, error: "First name is required", userId: "" };
    }

    if (!lastName) {
      return { success: false, error: "Last name is required", userId: "" };
    }

    if (!email) {
      return { success: false, error: "Email is required", userId: "" };
    }

    if (!role) {
      return { success: false, error: "Role is required", userId: "" };
    }

    if (!organizationId) {
      return { success: false, error: "Organization ID is required", userId: "" };
    }

    // Create the user
    const user = await UserManagementService.createUser({
      firstName,
      lastName,
      email,
      phone: phone || undefined,
      role,
      organizationId,
      language: lang as "en" | "fr",
    });

    if (!user) {
      return {
        success: false,
        error: "Failed to create user. The email may already be in use.",
        userId: "",
      };
    }

    // Revalidate the users list page
    revalidatePath(`/${lang}/protected/user/management/list`);

    return {
      success: true,
      error: "",
      userId: user.id,
    };
  } catch (error) {
    logger.error(`Error creating user: ${error}`);
    return {
      success: false,
      error: `Failed to create user: ${error}`,
      userId: "",
    };
  }
});
