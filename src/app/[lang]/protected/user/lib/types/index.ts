import { <PERSON>, <PERSON>son } from "@/lib/types/database.types";
import { UserRole } from "@/lib/authorization/types";

// Database types
export type UserProfileRow = Database["public"]["Tables"]["user_profiles"]["Row"];
export type UserProfileInsert = Database["public"]["Tables"]["user_profiles"]["Insert"];
export type UserProfileUpdate = Database["public"]["Tables"]["user_profiles"]["Update"];

// Application types
export interface NotificationPreferences {
  email: boolean;
  sms: boolean;
  inApp: boolean;
}

export interface UserProfile {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string | null;
  language: "en" | "fr";
  notificationPreferences: NotificationPreferences;
  organizationId?: string;
  role?: UserRole;
  createdAt?: string;
  updatedAt?: string;
}

// UserRole is imported from @/lib/authorization/types

export interface UserCreate {
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  language?: "en" | "fr";
  role: UserRole;
  organizationId?: string;
}

export interface UserRoleUpdate {
  role: UserRole;
}

// Update types for specific sections
export interface PersonalInfoUpdate {
  firstName: string;
  lastName: string;
  email?: string;
  phone?: string | null;
}

export interface ContactInfoUpdate {
  email: string;
  phone?: string;
}

export interface SettingsUpdate {
  language: "en" | "fr";
  notificationPreferences: NotificationPreferences;
}

// Mapper functions to convert between database and application types
export function mapDbProfileToUserProfile(dbProfile: UserProfileRow): UserProfile {
  // Parse notification preferences from JSON
  const notificationPrefs = dbProfile.notification_preferences as Record<string, boolean>;

  return {
    id: dbProfile.id,
    firstName: dbProfile.first_name,
    lastName: dbProfile.last_name,
    email: dbProfile.email,
    phone: dbProfile.phone || undefined,
    language: dbProfile.language as "en" | "fr",
    notificationPreferences: {
      email: notificationPrefs?.email ?? true,
      sms: notificationPrefs?.sms ?? false,
      inApp: notificationPrefs?.inApp ?? true,
    },
    organizationId: dbProfile.organization_id || undefined,
    createdAt: dbProfile.created_at || undefined,
    updatedAt: dbProfile.updated_at || undefined,
  };
}

export function mapUserProfileToDbUpdate(
  profile: Partial<UserProfile>
): Partial<UserProfileUpdate> {
  const dbUpdate: Partial<UserProfileUpdate> = {};

  if (profile.firstName !== undefined) {
    dbUpdate.first_name = profile.firstName;
  }

  if (profile.lastName !== undefined) {
    dbUpdate.last_name = profile.lastName;
  }

  if (profile.email !== undefined) {
    dbUpdate.email = profile.email;
  }

  if (profile.phone !== undefined) {
    dbUpdate.phone = profile.phone;
  }

  if (profile.language !== undefined) {
    dbUpdate.language = profile.language;
  }

  if (profile.notificationPreferences !== undefined) {
    dbUpdate.notification_preferences = profile.notificationPreferences as unknown as Json;
  }

  if (profile.organizationId !== undefined) {
    dbUpdate.organization_id = profile.organizationId;
  }

  return dbUpdate;
}
