import { createClient } from "@/lib/supabase/server";
import { logger } from "@/lib/logger/services/LoggerService";
import {
  UserProfile,
  mapDbProfileToUserProfile,
  mapUserProfileToDbUpdate,
  PersonalInfoUpdate,
  ContactInfoUpdate,
  SettingsUpdate,
  UserProfileRow,
} from "../types";
import { UserRole } from "@/lib/authorization/types";

/**
 * Service for managing user profiles using the authenticated user context
 */
export class UserProfileService {
  /**
   * Get the current user's profile
   * @returns Promise resolving to the user's profile
   */
  static async getCurrentUserProfile(): Promise<UserProfile | null> {
    try {
      const supabase = await createClient();
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        logger.warn("No active session");
        return null;
      }

      // Get the user profile from the database
      const { data, error } = await supabase
        .from("user_profiles")
        .select("*")
        .eq("id", user.id)
        .single();

      if (error) {
        logger.error(`Error fetching user profile: ${error.message}`);
        return null;
      }

      if (!data) {
        logger.warn(`No profile found for user ${user.id}`);
        return null;
      }

      // Get role data from user_roles
      const { data: roleData, error: roleError } = await supabase
        .from("user_roles")
        .select("role, organization_id")
        .eq("user_id", user.id)
        .single();

      if (roleError && !roleError.message.includes("No rows found")) {
        logger.error(`Error getting role data: ${roleError.message}`);
      }

      // Map database profile to application profile
      const profile = mapDbProfileToUserProfile(data as UserProfileRow);

      // Add role information if available
      if (roleData) {
        profile.role = roleData.role as UserRole;
        if (!profile.organizationId && roleData.organization_id) {
          profile.organizationId = roleData.organization_id;
        }
      }

      return profile;
    } catch (error) {
      logger.error(`Error getting current user profile: ${error}`);
      return null;
    }
  }

  /**
   * Update the current user's personal information
   * @param updates The personal information updates
   * @returns Promise resolving to the updated profile
   */
  static async updatePersonalInfo(updates: PersonalInfoUpdate): Promise<UserProfile | null> {
    try {
      const supabase = await createClient();
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        logger.warn("No active session");
        return null;
      }

      logger.info(`Updating personal information for user: ${user.id}`);

      // Convert the updates to database format
      const dbUpdates = mapUserProfileToDbUpdate(updates);

      // Update the profile in the database
      const { data, error } = await supabase
        .from("user_profiles")
        .update(dbUpdates)
        .eq("id", user.id)
        .select()
        .single();

      if (error) {
        logger.error(`Error updating personal information: ${error.message}`);
        return null;
      }

      if (!data) {
        logger.warn(`No profile found for user ${user.id} after update`);
        return null;
      }

      // Return updated profile
      return mapDbProfileToUserProfile(data as UserProfileRow);
    } catch (error) {
      logger.error(`Error updating personal information: ${error}`);
      return null;
    }
  }

  /**
   * Update the current user's contact information
   * @param updates The contact information updates
   * @returns Promise resolving to the updated profile
   */
  static async updateContactInfo(updates: ContactInfoUpdate): Promise<UserProfile | null> {
    try {
      const supabase = await createClient();
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        logger.warn("No active session");
        return null;
      }

      logger.info(`Updating contact information for user: ${user.id}`);

      // Convert the updates to database format
      const dbUpdates = mapUserProfileToDbUpdate(updates);

      // Update the profile in the database
      const { data, error } = await supabase
        .from("user_profiles")
        .update(dbUpdates)
        .eq("id", user.id)
        .select()
        .single();

      if (error) {
        logger.error(`Error updating contact information: ${error.message}`);
        return null;
      }

      if (!data) {
        logger.warn(`No profile found for user ${user.id} after update`);
        return null;
      }

      // Return updated profile
      return mapDbProfileToUserProfile(data as UserProfileRow);
    } catch (error) {
      logger.error(`Error updating contact information: ${error}`);
      return null;
    }
  }

  /**
   * Update the current user's settings
   * @param updates The settings updates
   * @returns Promise resolving to the updated profile
   */
  static async updateSettings(updates: SettingsUpdate): Promise<UserProfile | null> {
    try {
      const supabase = await createClient();
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        logger.warn("No active session");
        return null;
      }

      logger.info(`Updating settings for user: ${user.id}`);

      // Convert the updates to database format
      const dbUpdates = mapUserProfileToDbUpdate(updates);

      // Update the profile in the database
      const { data, error } = await supabase
        .from("user_profiles")
        .update(dbUpdates)
        .eq("id", user.id)
        .select()
        .single();

      if (error) {
        logger.error(`Error updating settings: ${error.message}`);
        return null;
      }

      if (!data) {
        logger.warn(`No profile found for user ${user.id} after update`);
        return null;
      }

      // Return updated profile
      return mapDbProfileToUserProfile(data as UserProfileRow);
    } catch (error) {
      logger.error(`Error updating settings: ${error}`);
      return null;
    }
  }
}
