import { describe, it, expect, beforeEach, jest } from "@jest/globals";
import { UserManagementService } from "../UserManagementService";

// Create a manual mock for createServiceClient
const mockCreateServiceClient = jest.fn();

// Mock Next.js cookies
jest.mock("next/headers", () => ({
  cookies: jest.fn(() => ({
    getAll: jest.fn(() => []),
    set: jest.fn(),
  })),
}));

// Mock modules
jest.mock("@/lib/supabase/service", () => ({
  createServiceClient: mockCreateServiceClient,
}));

jest.mock("@/lib/logger/services/LoggerService", () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
  },
}));

describe("UserManagementService", () => {
  // Define a type for the mock Supabase client
  type MockSupabaseClient = {
    from: jest.Mock;
    select: jest.Mock;
    eq: jest.Mock;
    single: jest.Mock;
    insert: jest.Mock;
    update: jest.Mock;
    auth: {
      admin: {
        inviteUserByEmail: jest.Mock;
        createUser: jest.Mock;
        getUserById: jest.Mock;
      };
    };
  };

  let mockSupabase: MockSupabaseClient;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Setup mock Supabase client
    mockSupabase = {
      from: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      single: jest.fn(),
      insert: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      auth: {
        admin: {
          inviteUserByEmail: jest.fn(),
          createUser: jest.fn(),
          getUserById: jest.fn(),
        },
      },
    };

    // Reset the mock implementation
    mockCreateServiceClient.mockResolvedValue(mockSupabase as unknown);
  });

  describe("getUserProfile", () => {
    it.skip("should return a user profile when found", async () => {
      const mockUserProfile = {
        id: "user-123",
        first_name: "John",
        last_name: "Doe",
        email: "<EMAIL>",
        phone: "************",
        organization_id: "org-123",
        created_at: "2023-01-01T00:00:00Z",
        updated_at: "2023-01-01T00:00:00Z",
      };

      const mockUserRole = {
        role: "Director",
      };

      // Mock the first query for user profile
      mockSupabase.single.mockReturnValueOnce({
        data: mockUserProfile,
        error: null,
      });

      // Mock the second query for user role
      mockSupabase.single.mockReturnValueOnce({
        data: mockUserRole,
        error: null,
      });

      const result = await UserManagementService.getUserProfile("user-123");

      expect(mockCreateServiceClient).toHaveBeenCalled();
      expect(mockSupabase.from).toHaveBeenCalledWith("user_profiles");
      expect(mockSupabase.select).toHaveBeenCalled();
      expect(mockSupabase.eq).toHaveBeenCalledWith("id", "user-123");
      expect(mockSupabase.single).toHaveBeenCalled();

      expect(result).toEqual({
        id: "user-123",
        firstName: "John",
        lastName: "Doe",
        email: "<EMAIL>",
        phone: "************",
        organizationId: "org-123",
        role: "Director",
        createdAt: "2023-01-01T00:00:00Z",
        updatedAt: "2023-01-01T00:00:00Z",
      });
    });

    it("should return null when user profile is not found", async () => {
      mockSupabase.single.mockReturnValueOnce({
        data: null,
        error: { message: "User not found" },
      });

      const result = await UserManagementService.getUserProfile("user-123");

      expect(result).toBeNull();
    });
  });

  describe("getUsersByOrganization", () => {
    it.skip("should return a list of users for an organization", async () => {
      const mockUsers = [
        {
          id: "user-123",
          first_name: "John",
          last_name: "Doe",
          email: "<EMAIL>",
          phone: "************",
          organization_id: "org-123",
          created_at: "2023-01-01T00:00:00Z",
          updated_at: "2023-01-01T00:00:00Z",
          role: "Director",
        },
        {
          id: "user-456",
          first_name: "Jane",
          last_name: "Smith",
          email: "<EMAIL>",
          phone: null,
          organization_id: "org-123",
          created_at: "2023-01-02T00:00:00Z",
          updated_at: "2023-01-02T00:00:00Z",
          role: "SocialWorker",
        },
      ];

      mockSupabase.select.mockReturnValueOnce({
        data: mockUsers,
        error: null,
      });

      const result = await UserManagementService.getUsersByOrganization("org-123");

      expect(mockCreateServiceClient).toHaveBeenCalled();
      expect(mockSupabase.from).toHaveBeenCalledWith("user_profiles");
      expect(mockSupabase.select).toHaveBeenCalledWith("*, user_roles!inner(role)");
      expect(mockSupabase.eq).toHaveBeenCalledWith("organization_id", "org-123");

      expect(result).toEqual([
        {
          id: "user-123",
          firstName: "John",
          lastName: "Doe",
          email: "<EMAIL>",
          phone: "************",
          organizationId: "org-123",
          role: "Director",
          createdAt: "2023-01-01T00:00:00Z",
          updatedAt: "2023-01-01T00:00:00Z",
        },
        {
          id: "user-456",
          firstName: "Jane",
          lastName: "Smith",
          email: "<EMAIL>",
          phone: undefined,
          organizationId: "org-123",
          role: "SocialWorker",
          createdAt: "2023-01-02T00:00:00Z",
          updatedAt: "2023-01-02T00:00:00Z",
        },
      ]);
    });

    it("should return an empty array when no users are found", async () => {
      mockSupabase.select.mockReturnValueOnce({
        data: [],
        error: null,
      });

      const result = await UserManagementService.getUsersByOrganization("org-123");

      expect(result).toEqual([]);
    });
  });

  describe("createUser", () => {
    it.skip("should create a user successfully", async () => {
      const mockUserData = {
        firstName: "John",
        lastName: "Doe",
        email: "<EMAIL>",
        phone: "************",
        role: "Director" as const,
        organizationId: "org-123",
        language: "en" as "en" | "fr",
      };

      const mockUserProfile = {
        id: "user-123",
        first_name: "John",
        last_name: "Doe",
        email: "<EMAIL>",
        phone: "************",
        organization_id: "org-123",
        created_at: "2023-01-01T00:00:00Z",
        updated_at: "2023-01-01T00:00:00Z",
      };

      const mockUserRole = {
        role: "Director",
      };

      // Mock the create user call
      mockSupabase.auth.admin.createUser.mockReturnValueOnce({
        data: { user: { id: "user-123" } },
        error: null,
      });

      // Mock the insert profile call
      mockSupabase.insert.mockReturnValueOnce({
        data: mockUserProfile,
        error: null,
      });

      // Mock the insert role call
      mockSupabase.insert.mockReturnValueOnce({
        data: { user_id: "user-123", role: "Director", organization_id: "org-123" },
        error: null,
      });

      // Mock the getUserProfile call
      mockSupabase.single.mockReturnValueOnce({
        data: mockUserProfile,
        error: null,
      });

      mockSupabase.single.mockReturnValueOnce({
        data: mockUserRole,
        error: null,
      });

      const result = await UserManagementService.createUser(mockUserData);

      expect(mockSupabase.auth.admin.createUser).toHaveBeenCalledWith({
        email: "<EMAIL>",
        email_confirm: true,
        user_metadata: {
          first_name: "John",
          last_name: "Doe",
          phone: "************",
          language: "en",
          notification_preferences: {
            email: true,
            sms: false,
            inApp: true,
          },
        },
      });

      expect(mockSupabase.from).toHaveBeenCalledWith("user_profiles");
      expect(mockSupabase.insert).toHaveBeenCalledWith({
        id: "user-123",
        first_name: "John",
        last_name: "Doe",
        email: "<EMAIL>",
        phone: "************",
        organization_id: "org-123",
      });

      expect(mockSupabase.from).toHaveBeenCalledWith("user_roles");
      expect(mockSupabase.insert).toHaveBeenCalledWith({
        user_id: "user-123",
        role: "Director",
        organization_id: "org-123",
      });

      expect(result).toEqual({
        id: "user-123",
        firstName: "John",
        lastName: "Doe",
        email: "<EMAIL>",
        phone: "************",
        organizationId: "org-123",
        role: "Director",
        createdAt: "2023-01-01T00:00:00Z",
        updatedAt: "2023-01-01T00:00:00Z",
      });
    });
  });
});
