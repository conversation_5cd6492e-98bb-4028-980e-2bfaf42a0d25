"use client";

import { useEffect } from "react";

/**
 * Root error component
 * This component is used to display errors that occur in the application
 * It is a client component because it uses the useEffect hook and handles errors
 */
export default function RootError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  // Log the error to an error reporting service
  useEffect(() => {
    console.error("Root error:", error);
    // Here you would typically log to an error reporting service
  }, [error]);

  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        minHeight: "100vh",
        padding: "1rem",
      }}
    >
      <div
        style={{
          width: "100%",
          maxWidth: "28rem",
          padding: "1.5rem",
          backgroundColor: "white",
          borderRadius: "0.5rem",
          boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
          display: "flex",
          flexDirection: "column",
          gap: "1.5rem",
        }}
      >
        <div
          style={{
            display: "flex",
            alignItems: "center",
            gap: "0.75rem",
            color: "#ef4444",
          }}
        >
          {/* Alert Circle Icon */}
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <circle cx="12" cy="12" r="10" />
            <line x1="12" y1="8" x2="12" y2="12" />
            <line x1="12" y1="16" x2="12.01" y2="16" />
          </svg>
          <h1 style={{ fontSize: "1.5rem", fontWeight: "bold", margin: 0 }}>
            Something went wrong!
          </h1>
        </div>

        <div
          style={{
            padding: "1rem",
            border: "1px solid #e5e7eb",
            borderRadius: "0.375rem",
            backgroundColor: "#f9fafb",
          }}
        >
          <p
            style={{
              fontSize: "0.875rem",
              fontFamily: "monospace",
              margin: 0,
            }}
          >
            {error.message || "An unexpected error occurred"}
          </p>
          {error.digest && (
            <p
              style={{
                marginTop: "0.5rem",
                fontSize: "0.75rem",
                color: "#6b7280",
              }}
            >
              Error ID: {error.digest}
            </p>
          )}
        </div>

        <div style={{ display: "flex", flexDirection: "column", gap: "0.75rem" }}>
          <button
            onClick={reset}
            style={{
              width: "100%",
              padding: "0.5rem 1rem",
              backgroundColor: "#3b82f6",
              color: "white",
              border: "none",
              borderRadius: "0.25rem",
              cursor: "pointer",
              fontWeight: "500",
            }}
          >
            Try again
          </button>
          <button
            onClick={() => (window.location.href = "/")}
            style={{
              width: "100%",
              padding: "0.5rem 1rem",
              backgroundColor: "transparent",
              color: "#374151",
              border: "1px solid #d1d5db",
              borderRadius: "0.25rem",
              cursor: "pointer",
              fontWeight: "500",
            }}
          >
            Go to homepage
          </button>
        </div>
      </div>
    </div>
  );
}
