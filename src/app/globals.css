@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

/* Basic styles for the application */
:root {
  /* Typography */
  --font-sans: '<PERSON>eist', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-mono: 'Geist Mono', ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;

  /* Border Radius */
  --radius-xs: 0.25rem;  /* 4px */
  --radius-sm: 0.375rem; /* 6px */
  --radius-md: 0.5rem;   /* 8px */
  --radius: 0.5rem;      /* 8px - Base radius */
  --radius-lg: 0.75rem;  /* 12px */
  --radius-xl: 1rem;     /* 16px */
  --radius-2xl: 1.5rem;  /* 24px */
  --radius-full: 9999px; /* For pills and circles */

  /* Animation & Transitions */
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --duration-slow: 350ms;
  --duration-slower: 500ms;

  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-bounce: cubic-bezier(0.34, 1.56, 0.64, 1);

  /* Base Colors */
  --background: oklch(1 0 0);
  --foreground: oklch(0.141 0.005 285.823);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.141 0.005 285.823);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.141 0.005 285.823);

  /* Primary Colors */
  --primary-50: oklch(0.9 0.05 259.815);
  --primary-100: oklch(0.85 0.08 259.815);
  --primary-200: oklch(0.8 0.11 259.815);
  --primary-300: oklch(0.75 0.14 259.815);
  --primary-400: oklch(0.7 0.17 259.815);
  --primary-500: oklch(0.623 0.214 259.815); /* Base primary */
  --primary-600: oklch(0.55 0.23 259.815);
  --primary-700: oklch(0.5 0.24 259.815);
  --primary-800: oklch(0.45 0.25 259.815);
  --primary-900: oklch(0.4 0.26 259.815);
  --primary: oklch(0.623 0.214 259.815);
  --primary-foreground: oklch(1 0 0); /* Pure white for better contrast */

  /* Secondary Colors */
  --secondary-50: oklch(0.99 0.001 286.375);
  --secondary-100: oklch(0.98 0.001 286.375);
  --secondary-200: oklch(0.967 0.001 286.375);
  --secondary-300: oklch(0.95 0.001 286.375);
  --secondary-400: oklch(0.93 0.001 286.375);
  --secondary-500: oklch(0.9 0.001 286.375); /* Base secondary */
  --secondary-600: oklch(0.85 0.001 286.375);
  --secondary-700: oklch(0.8 0.001 286.375);
  --secondary-800: oklch(0.75 0.001 286.375);
  --secondary-900: oklch(0.7 0.001 286.375);
  --secondary: oklch(0.967 0.001 286.375);
  --secondary-foreground: oklch(0.21 0.006 285.885);

  /* Accent Colors */
  --accent-50: oklch(0.99 0.001 286.375);
  --accent-100: oklch(0.98 0.001 286.375);
  --accent-200: oklch(0.967 0.001 286.375);
  --accent-300: oklch(0.95 0.001 286.375);
  --accent-400: oklch(0.93 0.001 286.375);
  --accent-500: oklch(0.9 0.001 286.375); /* Base accent */
  --accent-600: oklch(0.85 0.001 286.375);
  --accent-700: oklch(0.8 0.001 286.375);
  --accent-800: oklch(0.75 0.001 286.375);
  --accent-900: oklch(0.7 0.001 286.375);
  --accent: oklch(0.967 0.001 286.375);
  --accent-foreground: oklch(0.21 0.006 285.885);

  /* Muted Colors */
  --muted-50: oklch(0.99 0.001 286.375);
  --muted-100: oklch(0.98 0.001 286.375);
  --muted-200: oklch(0.967 0.001 286.375);
  --muted-300: oklch(0.95 0.001 286.375);
  --muted-400: oklch(0.93 0.001 286.375);
  --muted-500: oklch(0.9 0.001 286.375); /* Base muted */
  --muted-600: oklch(0.85 0.001 286.375);
  --muted-700: oklch(0.8 0.001 286.375);
  --muted-800: oklch(0.75 0.001 286.375);
  --muted-900: oklch(0.7 0.001 286.375);
  --muted: oklch(0.967 0.001 286.375);
  --muted-foreground: oklch(0.552 0.016 285.938);

  /* Status Colors */
  --success-50: oklch(0.95 0.05 145);
  --success-100: oklch(0.9 0.07 145);
  --success-200: oklch(0.85 0.09 145);
  --success-300: oklch(0.8 0.11 145);
  --success-400: oklch(0.75 0.13 145);
  --success-500: oklch(0.7 0.15 145); /* Base success */
  --success-600: oklch(0.65 0.17 145);
  --success-700: oklch(0.6 0.19 145);
  --success-800: oklch(0.55 0.21 145);
  --success-900: oklch(0.5 0.23 145);
  --success: var(--success-500);
  --success-foreground: oklch(0.985 0.002 247.839);

  --warning-50: oklch(0.95 0.05 85);
  --warning-100: oklch(0.9 0.07 85);
  --warning-200: oklch(0.85 0.09 85);
  --warning-300: oklch(0.8 0.11 85);
  --warning-400: oklch(0.75 0.13 85);
  --warning-500: oklch(0.7 0.15 85); /* Base warning */
  --warning-600: oklch(0.65 0.17 85);
  --warning-700: oklch(0.6 0.19 85);
  --warning-800: oklch(0.55 0.21 85);
  --warning-900: oklch(0.5 0.23 85);
  --warning: var(--warning-500);
  --warning-foreground: oklch(0.1 0.02 85);

  --info-50: oklch(0.95 0.05 230);
  --info-100: oklch(0.9 0.07 230);
  --info-200: oklch(0.85 0.09 230);
  --info-300: oklch(0.8 0.11 230);
  --info-400: oklch(0.75 0.13 230);
  --info-500: oklch(0.7 0.15 230); /* Base info */
  --info-600: oklch(0.65 0.17 230);
  --info-700: oklch(0.6 0.19 230);
  --info-800: oklch(0.55 0.21 230);
  --info-900: oklch(0.5 0.23 230);
  --info: var(--info-500);
  --info-foreground: oklch(0.985 0.002 247.839);

  --destructive-50: oklch(0.95 0.05 27.325);
  --destructive-100: oklch(0.9 0.07 27.325);
  --destructive-200: oklch(0.85 0.09 27.325);
  --destructive-300: oklch(0.8 0.11 27.325);
  --destructive-400: oklch(0.75 0.13 27.325);
  --destructive-500: oklch(0.7 0.15 27.325); /* Base destructive */
  --destructive-600: oklch(0.65 0.17 27.325);
  --destructive-700: oklch(0.6 0.19 27.325);
  --destructive-800: oklch(0.55 0.21 27.325);
  --destructive-900: oklch(0.5 0.23 27.325);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.985 0.002 247.839);

  /* UI Colors */
  --border: oklch(0.92 0.004 286.32);
  --input: oklch(0.92 0.004 286.32);
  --ring: oklch(0.623 0.214 259.815);

  /* Chart Colors */
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);

  /* Sidebar Colors */
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.141 0.005 285.823);
  --sidebar-primary: oklch(0.623 0.214 259.815);
  --sidebar-primary-foreground: oklch(1 0 0); /* Pure white for better contrast */
  --sidebar-accent: oklch(0.967 0.001 286.375);
  --sidebar-accent-foreground: oklch(0.21 0.006 285.885);
  --sidebar-border: oklch(0.92 0.004 286.32);
  --sidebar-ring: oklch(0.623 0.214 259.815);
}

body {
  font-family: var(--font-sans);
  margin: 0;
  padding: 0;
  background-color: #f8f9fa;
  color: #333;
}

.dark body {
  background-color: #1a1a1a;
  color: #f8f9fa;
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

.dark {
  /* Base Colors */
  --background: oklch(0.141 0.005 285.823);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.21 0.006 285.885);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.21 0.006 285.885);
  --popover-foreground: oklch(0.985 0 0);

  /* Primary Colors */
  --primary-50: oklch(0.85 0.05 262.881);
  --primary-100: oklch(0.8 0.08 262.881);
  --primary-200: oklch(0.75 0.11 262.881);
  --primary-300: oklch(0.7 0.14 262.881);
  --primary-400: oklch(0.65 0.17 262.881);
  --primary-500: oklch(0.6 0.2 262.881); /* Base primary */
  --primary-600: oklch(0.55 0.23 262.881);
  --primary-700: oklch(0.5 0.24 262.881);
  --primary-800: oklch(0.45 0.25 262.881);
  --primary-900: oklch(0.4 0.26 262.881);
  --primary: oklch(0.546 0.245 262.881);
  --primary-foreground: oklch(1 0 0); /* Pure white for better contrast */

  /* Secondary Colors */
  --secondary-50: oklch(0.4 0.001 286.033);
  --secondary-100: oklch(0.38 0.002 286.033);
  --secondary-200: oklch(0.36 0.003 286.033);
  --secondary-300: oklch(0.34 0.004 286.033);
  --secondary-400: oklch(0.32 0.005 286.033);
  --secondary-500: oklch(0.3 0.006 286.033); /* Base secondary */
  --secondary-600: oklch(0.28 0.006 286.033);
  --secondary-700: oklch(0.26 0.006 286.033);
  --secondary-800: oklch(0.24 0.006 286.033);
  --secondary-900: oklch(0.22 0.006 286.033);
  --secondary: oklch(0.274 0.006 286.033);
  --secondary-foreground: oklch(0.985 0 0);

  /* Accent Colors */
  --accent-50: oklch(0.4 0.001 286.033);
  --accent-100: oklch(0.38 0.002 286.033);
  --accent-200: oklch(0.36 0.003 286.033);
  --accent-300: oklch(0.34 0.004 286.033);
  --accent-400: oklch(0.32 0.005 286.033);
  --accent-500: oklch(0.3 0.006 286.033); /* Base accent */
  --accent-600: oklch(0.28 0.006 286.033);
  --accent-700: oklch(0.26 0.006 286.033);
  --accent-800: oklch(0.24 0.006 286.033);
  --accent-900: oklch(0.22 0.006 286.033);
  --accent: oklch(0.274 0.006 286.033);
  --accent-foreground: oklch(0.985 0 0);

  /* Muted Colors */
  --muted-50: oklch(0.4 0.001 286.033);
  --muted-100: oklch(0.38 0.002 286.033);
  --muted-200: oklch(0.36 0.003 286.033);
  --muted-300: oklch(0.34 0.004 286.033);
  --muted-400: oklch(0.32 0.005 286.033);
  --muted-500: oklch(0.3 0.006 286.033); /* Base muted */
  --muted-600: oklch(0.28 0.006 286.033);
  --muted-700: oklch(0.26 0.006 286.033);
  --muted-800: oklch(0.24 0.006 286.033);
  --muted-900: oklch(0.22 0.006 286.033);
  --muted: oklch(0.274 0.006 286.033);
  --muted-foreground: oklch(0.705 0.015 286.067);

  /* Status Colors */
  --success-50: oklch(0.4 0.05 145);
  --success-100: oklch(0.45 0.07 145);
  --success-200: oklch(0.5 0.09 145);
  --success-300: oklch(0.55 0.11 145);
  --success-400: oklch(0.6 0.13 145);
  --success-500: oklch(0.65 0.15 145); /* Base success */
  --success-600: oklch(0.7 0.17 145);
  --success-700: oklch(0.75 0.19 145);
  --success-800: oklch(0.8 0.21 145);
  --success-900: oklch(0.85 0.23 145);
  --success: var(--success-500);
  --success-foreground: oklch(0.1 0.02 145);

  --warning-50: oklch(0.4 0.05 85);
  --warning-100: oklch(0.45 0.07 85);
  --warning-200: oklch(0.5 0.09 85);
  --warning-300: oklch(0.55 0.11 85);
  --warning-400: oklch(0.6 0.13 85);
  --warning-500: oklch(0.65 0.15 85); /* Base warning */
  --warning-600: oklch(0.7 0.17 85);
  --warning-700: oklch(0.75 0.19 85);
  --warning-800: oklch(0.8 0.21 85);
  --warning-900: oklch(0.85 0.23 85);
  --warning: var(--warning-500);
  --warning-foreground: oklch(0.1 0.02 85);

  --info-50: oklch(0.4 0.05 230);
  --info-100: oklch(0.45 0.07 230);
  --info-200: oklch(0.5 0.09 230);
  --info-300: oklch(0.55 0.11 230);
  --info-400: oklch(0.6 0.13 230);
  --info-500: oklch(0.65 0.15 230); /* Base info */
  --info-600: oklch(0.7 0.17 230);
  --info-700: oklch(0.75 0.19 230);
  --info-800: oklch(0.8 0.21 230);
  --info-900: oklch(0.85 0.23 230);
  --info: var(--info-500);
  --info-foreground: oklch(0.1 0.02 230);

  --destructive-50: oklch(0.4 0.05 22.216);
  --destructive-100: oklch(0.45 0.07 22.216);
  --destructive-200: oklch(0.5 0.09 22.216);
  --destructive-300: oklch(0.55 0.11 22.216);
  --destructive-400: oklch(0.6 0.13 22.216);
  --destructive-500: oklch(0.65 0.15 22.216); /* Base destructive */
  --destructive-600: oklch(0.7 0.17 22.216);
  --destructive-700: oklch(0.75 0.19 22.216);
  --destructive-800: oklch(0.8 0.21 22.216);
  --destructive-900: oklch(0.85 0.23 22.216);
  --destructive: oklch(0.704 0.191 22.216);
  --destructive-foreground: oklch(0.985 0 0);

  /* UI Colors */
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.488 0.243 264.376);

  /* Chart Colors */
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);

  /* Sidebar Colors */
  --sidebar: oklch(0.21 0.006 285.885);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.546 0.245 262.881);
  --sidebar-primary-foreground: oklch(1 0 0); /* Pure white for better contrast */
  --sidebar-accent: oklch(0.274 0.006 286.033);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.488 0.243 264.376);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }

  /* Transition utilities */
  .transition-fast {
    transition-duration: var(--duration-fast);
    transition-timing-function: var(--ease-in-out);
  }

  .transition-normal {
    transition-duration: var(--duration-normal);
    transition-timing-function: var(--ease-in-out);
  }

  .transition-slow {
    transition-duration: var(--duration-slow);
    transition-timing-function: var(--ease-in-out);
  }

  .transition-bounce {
    transition-duration: var(--duration-normal);
    transition-timing-function: var(--ease-bounce);
  }

  /* Common transitions */
  .transition-colors {
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  }

  .transition-opacity {
    transition-property: opacity;
  }

  .transition-transform {
    transition-property: transform;
  }

  .transition-all {
    transition-property: all;
  }

  /* Page transitions */
  .page-enter {
    opacity: 0;
    transform: translateY(8px);
  }

  .page-enter-active {
    opacity: 1;
    transform: translateY(0);
    transition: opacity var(--duration-normal) var(--ease-out),
                transform var(--duration-normal) var(--ease-out);
  }

  .page-exit {
    opacity: 1;
    transform: translateY(0);
  }

  .page-exit-active {
    opacity: 0;
    transform: translateY(-8px);
    transition: opacity var(--duration-normal) var(--ease-in),
                transform var(--duration-normal) var(--ease-in);
  }

  /* Navigation transitions */
  .nav-item {
    position: relative;
    transition: color var(--duration-fast) var(--ease-in-out);
  }

  .nav-item::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: currentColor;
    transition: width var(--duration-normal) var(--ease-out);
  }

  .nav-item:hover::after,
  .nav-item.active::after {
    width: 100%;
  }

  /* Button transitions */
  .btn {
    transition: background-color var(--duration-fast) var(--ease-in-out),
                color var(--duration-fast) var(--ease-in-out),
                border-color var(--duration-fast) var(--ease-in-out),
                box-shadow var(--duration-fast) var(--ease-in-out);
  }

  .btn:active {
    transform: translateY(1px);
    transition: transform var(--duration-fast) var(--ease-in-out);
  }
}

/* Rich Text Editor Styles */
.ProseMirror {
  @apply outline-none;
}

.ProseMirror p {
  @apply my-2;
}

.ProseMirror h1 {
  @apply text-3xl font-bold my-4;
}

.ProseMirror h2 {
  @apply text-2xl font-bold my-3;
}

.ProseMirror h3 {
  @apply text-xl font-bold my-2;
}

.ProseMirror ul {
  @apply list-disc pl-6 my-2;
}

.ProseMirror ol {
  @apply list-decimal pl-6 my-2;
}

.ProseMirror blockquote {
  @apply border-l-4 border-primary pl-4 italic my-4;
}

.ProseMirror code {
  @apply bg-muted text-muted-foreground rounded px-1.5 py-0.5 font-mono;
}

.ProseMirror pre {
  @apply bg-muted text-muted-foreground rounded-md p-4 font-mono my-4 overflow-x-auto;
}

.ProseMirror table {
  @apply border-collapse table-auto w-full my-4;
}

.ProseMirror th {
  @apply border border-border p-2 bg-muted font-bold;
}

.ProseMirror td {
  @apply border border-border p-2;
}

.ProseMirror img {
  @apply max-w-full rounded-md my-4;
}

.ProseMirror a {
  @apply text-primary underline underline-offset-2;
}

.ProseMirror mark {
  @apply bg-yellow-200 dark:bg-yellow-800 rounded px-1;
}

/* Document Preview Styles */
.prose {
  @apply max-w-none;
}

.prose h1 {
  @apply text-3xl font-bold my-4;
}

.prose h2 {
  @apply text-2xl font-bold my-3;
}

.prose h3 {
  @apply text-xl font-bold my-2;
}

.prose ul {
  @apply list-disc pl-6 my-2;
}

.prose ol {
  @apply list-decimal pl-6 my-2;
}

.prose blockquote {
  @apply border-l-4 border-primary pl-4 italic my-4;
}

.prose code {
  @apply bg-muted text-muted-foreground rounded px-1.5 py-0.5 font-mono;
}

.prose pre {
  @apply bg-muted text-muted-foreground rounded-md p-4 font-mono my-4 overflow-x-auto;
}

.prose table {
  @apply border-collapse table-auto w-full my-4;
}

.prose th {
  @apply border border-border p-2 bg-muted font-bold;
}

.prose td {
  @apply border border-border p-2;
}

.prose img {
  @apply max-w-full rounded-md my-4;
}

.prose a {
  @apply text-primary underline underline-offset-2;
}

.prose mark {
  @apply bg-yellow-200 dark:bg-yellow-800 rounded px-1;
}

@media print {
  .prose {
    @apply text-black;
  }

  .prose a {
    @apply text-black no-underline;
  }
}

