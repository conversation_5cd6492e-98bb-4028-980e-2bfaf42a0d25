import { NextRequest, NextResponse } from "next/server";
import { updateSession } from "@/lib/supabase/middleware";
import { i18nMiddleware } from "@/lib/i18n/middleware/i18nMiddleware";
import { authMiddleware } from "@/lib/authentication/middleware/authMiddleware";
import { routeAuthorizationMiddleware } from "@/lib/authorization/middleware/routeAuthorizationMiddleware";

export async function middleware(request: NextRequest) {
  // First, handle internationalization
  const i18nResponse = i18nMiddleware(request);

  // If the i18n middleware returns a redirect, return it
  if (i18nResponse.status !== 200) {
    return i18nResponse;
  }

  // Handle authentication
  const authResponse = await authMiddleware(request);

  if (authResponse) {
    return authResponse;
  }

  // Handle authorization for protected routes
  if (request.nextUrl.pathname.includes("/protected")) {
    const authorizationResponse = await routeAuthorizationMiddleware(request);

    // If the authorization middleware returns a redirect, return it
    if (authorizationResponse.status !== 200) {
      return authorizationResponse;
    }
  }

  // Add the current pathname to the headers for use in the layout
  const requestHeaders = new Headers(request.headers);
  requestHeaders.set("x-pathname", request.nextUrl.pathname);

  // Update the session and return the response with the updated headers
  const response = await updateSession(request);

  // If updateSession returns a response, clone it and add the pathname header
  if (response) {
    const newResponse = NextResponse.next({
      request: {
        headers: requestHeaders,
      },
    });

    // Copy all headers from the original response
    response.headers.forEach((value, key) => {
      newResponse.headers.set(key, value);
    });

    return newResponse;
  }

  // If no response from updateSession, just return a new response with the pathname header
  return NextResponse.next({
    request: {
      headers: requestHeaders,
    },
  });
}

export const config = {
  matcher: [
    // Skip all internal paths (_next)
    "/((?!_next|api|public|favicon.ico).*)",
  ],
};
