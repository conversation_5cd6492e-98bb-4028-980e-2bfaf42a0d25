# Logger Service

This directory contains the Logger Service implementation for the application. The Logger Service provides a centralized way to log messages at different levels and with different integrations.

## Overview

The Logger Service is built using `@brainstack/log` and `@brainstack/inject`. It provides methods for logging messages at different levels (error, warn, info, log, verbose) and supports adding custom integrations for different logging backends.

## Usage

### Basic Usage

```typescript
import { container } from '@/lib/di';
import { LoggerService } from '@/lib/logger/services/LoggerService';

// Get the logger service from the container
const logger = container.getInstance(LoggerService);

// Log messages at different levels
logger.log('This is a log message');
logger.info('This is an info message');
logger.warn('This is a warning message');
logger.error('This is an error message');
logger.verbose('This is a verbose message');
```

### Logging with Context

```typescript
import { container } from '@/lib/di';
import { LoggerService } from '@/lib/logger/services/LoggerService';

// Get the logger service from the container
const logger = container.getInstance(LoggerService);

// Log messages with context
const context = { userId: '123', action: 'login' };
logger.info('User logged in', context);
```

### Logging Errors

```typescript
import { container } from '@/lib/di';
import { LoggerService } from '@/lib/logger/services/LoggerService';

// Get the logger service from the container
const logger = container.getInstance(LoggerService);

try {
  // Some code that might throw an error
  throw new Error('Something went wrong');
} catch (error) {
  // Log the error with context
  logger.error('An error occurred', error, { userId: '123', action: 'processData' });
}
```

### Changing Log Level

```typescript
import { container } from '@/lib/di';
import { LoggerService, LogLevel } from '@/lib/logger/services/LoggerService';

// Get the logger service from the container
const logger = container.getInstance(LoggerService);

// Change the log level
logger.setLogLevel(LogLevel.VERBOSE); // Show all logs
logger.setLogLevel(LogLevel.ERROR); // Show only error logs
```

### Adding Custom Integrations

```typescript
import { container } from '@/lib/di';
import { LoggerService } from '@/lib/logger/services/LoggerService';
import { LoggerIntegration } from '@brainstack/log';

// Get the logger service from the container
const logger = container.getInstance(LoggerService);

// Create a custom integration
const customIntegration: LoggerIntegration = {
  log: (message) => { /* Custom log implementation */ },
  info: (message) => { /* Custom info implementation */ },
  warn: (message) => { /* Custom warn implementation */ },
  error: (message) => { /* Custom error implementation */ },
  verbose: (message) => { /* Custom verbose implementation */ },
};

// Add the custom integration
logger.addIntegration(customIntegration);
```

## Log Levels

The Logger Service supports the following log levels:

- `LogLevel.ERROR` (0): Only error messages are logged
- `LogLevel.WARN` (1): Error and warning messages are logged
- `LogLevel.INFO` (2): Error, warning, and info messages are logged
- `LogLevel.LOG` (3): Error, warning, info, and log messages are logged
- `LogLevel.VERBOSE` (4): All messages are logged

## API Reference

### `LoggerService`

#### Methods

- `setLogLevel(level: LogLevel): void`: Sets the log level
- `getLogLevel(): number`: Gets the current log level
- `addIntegration(integration: LoggerIntegration): void`: Adds a logger integration
- `removeIntegration(integration: LoggerIntegration): void`: Removes a logger integration
- `log(message: string, context?: Record<string, any>): void`: Logs a general message
- `info(message: string, context?: Record<string, any>): void`: Logs an informational message
- `warn(message: string, context?: Record<string, any>): void`: Logs a warning message
- `error(message: string, error?: Error, context?: Record<string, any>): void`: Logs an error message
- `verbose(message: string, context?: Record<string, any>): void`: Logs a verbose message

### `LogLevel` Enum

- `ERROR` (0)
- `WARN` (1)
- `INFO` (2)
- `LOG` (3)
- `VERBOSE` (4)
