import {
  createLogger,
  LoggerIntegration,
  Logger,
  LogLevel as BrainstackLogLevel,
} from "@brainstack/log";

/**
 * Log levels
 */
export enum LogLevel {
  ERROR = BrainstackLogLevel.ERROR,
  WARNING = BrainstackLogLevel.WARNING,
  INFO = BrainstackLogLevel.INFO,
  LOG = BrainstackLogLevel.LOG,
  VERBOSE = BrainstackLogLevel.VERBOSE,
}

/**
 * Logger service for application-wide logging
 * Uses @brainstack/log for logging functionality
 */
export class LoggerService {
  private logger: Logger;
  private integrations: LoggerIntegration[] = [];

  /**
   * Creates a new instance of LoggerService
   * Initializes with console integration and INFO log level by default
   */
  constructor() {
    // Initialize with console integration and INFO level by default
    this.logger = createLogger(LogLevel.INFO, []);
  }

  /**
   * Changes the log level
   * @param level The new log level
   */
  public setLogLevel(level: LogLevel): void {
    this.logger.changeLogLevel(level);
  }

  /**
   * Gets the current log level
   * @returns The current log level
   */
  public getLogLevel(): number {
    return this.logger.level;
  }

  /**
   * Adds a logger integration
   * @param integration The logger integration to add
   */
  public addIntegration(integration: LoggerIntegration): void {
    this.integrations.push(integration);
    this.logger.addIntegration(integration);
  }

  /**
   * Removes a logger integration
   * @param integration The logger integration to remove
   */
  public removeIntegration(integration: LoggerIntegration): void {
    const index = this.integrations.indexOf(integration);
    if (index !== -1) {
      this.integrations.splice(index, 1);
      this.logger.removeIntegration(integration);
    }
  }

  /**
   * Gets all integrations
   * @returns Array of logger integrations
   */
  public getIntegrations(): LoggerIntegration[] {
    return [...this.integrations];
  }

  /**
   * Logs a general message
   * @param message The message to log
   * @param context Optional context data
   */
  public log(message: string, context?: Record<string, unknown>): void {
    if (context) {
      this.logger.log({ message, ...context });
    } else {
      this.logger.log(message);
    }
  }

  /**
   * Logs an informational message
   * @param message The message to log
   * @param context Optional context data
   */
  public info(message: string, context?: Record<string, unknown>): void {
    if (context) {
      this.logger.info({ message, ...context });
    } else {
      this.logger.info(message);
    }
  }

  /**
   * Logs a warning message
   * @param message The message to log
   * @param context Optional context data
   */
  public warn(message: string, context?: Record<string, unknown>): void {
    if (context) {
      this.logger.warn({ message, ...context });
    } else {
      this.logger.warn(message);
    }
  }

  /**
   * Logs an error message
   * @param message The message to log
   * @param error Optional error object
   * @param context Optional context data
   */
  public error(message: string, error?: Error, context?: Record<string, unknown>): void {
    if (error || context) {
      this.logger.error({
        message,
        ...(error && { error: error.message, stack: error.stack }),
        ...(context || {}),
      });
    } else {
      this.logger.error(message);
    }
  }

  /**
   * Logs a verbose message
   * @param message The message to log
   * @param context Optional context data
   */
  public verbose(message: string, context?: Record<string, unknown>): void {
    if (context) {
      this.logger.verbose({ message, ...context });
    } else {
      this.logger.verbose(message);
    }
  }
}

export const logger = new LoggerService();
