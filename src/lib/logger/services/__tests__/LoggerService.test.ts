import { LoggerService, LogLevel } from "../LoggerService";
import { LoggerIntegration } from "@brainstack/log";

// Mock the @brainstack/log module
jest.mock("@brainstack/log", () => {
  return {
    createLogger: jest.fn(() => ({
      level: 2, // Default to INFO level
      integrations: [],
      changeLogLevel: jest.fn(),
      addIntegration: jest.fn(),
      removeIntegration: jest.fn(),
      log: jest.fn(),
      info: jest.fn(),
      warn: jest.fn(),
      error: jest.fn(),
      verbose: jest.fn(),
    })),
    LogLevel: {
      ERROR: 0,
      WARNING: 1,
      INFO: 2,
      LOG: 3,
      VERBOSE: 4,
    },
  };
});

describe("LoggerService", () => {
  let loggerService: LoggerService;
  let mockIntegration: LoggerIntegration;

  beforeEach(() => {
    // Create a mock integration
    mockIntegration = {
      log: jest.fn(),
      info: jest.fn(),
      warn: jest.fn(),
      error: jest.fn(),
      verbose: jest.fn(),
    };

    // Create a new instance of LoggerService
    loggerService = new LoggerService();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("setLogLevel and getLogLevel", () => {
    it("should set the log level correctly", () => {
      // Set the log level to ERROR
      loggerService.setLogLevel(LogLevel.ERROR);

      // Check if the logger's changeLogLevel method was called with the correct level
      expect(loggerService["logger"].changeLogLevel).toHaveBeenCalledWith(LogLevel.ERROR);
    });

    it("should get the log level correctly", () => {
      // Mock the logger's level property
      loggerService["logger"].level = LogLevel.ERROR;

      // Get the log level
      const level = loggerService.getLogLevel();

      // Check if the correct level is returned
      expect(level).toBe(LogLevel.ERROR);
    });
  });

  describe("addIntegration, removeIntegration, and getIntegrations", () => {
    it("should add integrations correctly", () => {
      // Add the mock integration
      loggerService.addIntegration(mockIntegration);

      // Check if the logger's addIntegration method was called with the correct integration
      expect(loggerService["logger"].addIntegration).toHaveBeenCalledWith(mockIntegration);
    });

    it("should remove integrations correctly", () => {
      // Add the mock integration first
      loggerService.addIntegration(mockIntegration);

      // Then remove it
      loggerService.removeIntegration(mockIntegration);

      // Check if the logger's removeIntegration method was called with the correct integration
      expect(loggerService["logger"].removeIntegration).toHaveBeenCalledWith(mockIntegration);
    });

    it("should get integrations correctly", () => {
      // Add the mock integration
      loggerService.addIntegration(mockIntegration);

      // Get the integrations
      const integrations = loggerService.getIntegrations();

      // Check if the integrations array contains the mock integration
      expect(integrations).toContain(mockIntegration);

      // Check if the returned array is a copy, not the original
      expect(integrations).not.toBe(loggerService["integrations"]);
    });
  });

  describe("log methods", () => {
    it("should log messages correctly", () => {
      // Log messages at different levels
      loggerService.log("Log message");
      loggerService.info("Info message");
      loggerService.warn("Warn message");
      loggerService.error("Error message");
      loggerService.verbose("Verbose message");

      // Check if the logger methods were called with the correct messages
      expect(loggerService["logger"].log).toHaveBeenCalledWith("Log message");
      expect(loggerService["logger"].info).toHaveBeenCalledWith("Info message");
      expect(loggerService["logger"].warn).toHaveBeenCalledWith("Warn message");
      expect(loggerService["logger"].error).toHaveBeenCalledWith("Error message");
      expect(loggerService["logger"].verbose).toHaveBeenCalledWith("Verbose message");
    });

    it("should log messages with context correctly", () => {
      // Log messages with context at different levels
      const context = { userId: "123", action: "test" };

      loggerService.log("Log message", context);
      loggerService.info("Info message", context);
      loggerService.warn("Warn message", context);
      loggerService.error("Error message", undefined, context);
      loggerService.verbose("Verbose message", context);

      // Check if the logger methods were called with the correct messages and context
      expect(loggerService["logger"].log).toHaveBeenCalledWith({
        message: "Log message",
        ...context,
      });
      expect(loggerService["logger"].info).toHaveBeenCalledWith({
        message: "Info message",
        ...context,
      });
      expect(loggerService["logger"].warn).toHaveBeenCalledWith({
        message: "Warn message",
        ...context,
      });
      expect(loggerService["logger"].error).toHaveBeenCalledWith({
        message: "Error message",
        ...context,
      });
      expect(loggerService["logger"].verbose).toHaveBeenCalledWith({
        message: "Verbose message",
        ...context,
      });
    });

    it("should log errors with error objects correctly", () => {
      // Create an error object
      const error = new Error("Test error");

      // Log an error message with the error object
      loggerService.error("Error message", error);

      // Check if the logger's error method was called with the correct message and error details
      expect(loggerService["logger"].error).toHaveBeenCalledWith({
        message: "Error message",
        error: error.message,
        stack: error.stack,
      });
    });

    it("should log errors with error objects and context correctly", () => {
      // Create an error object and context
      const error = new Error("Test error");
      const context = { userId: "123", action: "test" };

      // Log an error message with the error object and context
      loggerService.error("Error message", error, context);

      // Check if the logger's error method was called with the correct message, error details, and context
      expect(loggerService["logger"].error).toHaveBeenCalledWith({
        message: "Error message",
        error: error.message,
        stack: error.stack,
        ...context,
      });
    });
  });
});
