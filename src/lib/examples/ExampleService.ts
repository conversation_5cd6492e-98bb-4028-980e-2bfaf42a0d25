import { createClient } from "@/lib/supabase/server";
import { logger } from "@/lib/logger/services/LoggerService";
import { ServiceResponse, successResponse, errorResponse } from "@/lib/types/responses";

/**
 * Example Item type
 *
 * In a real application, this would be imported from Supabase generated types.
 * For example:
 * import { Database } from "@/lib/types/database.types";
 * type ExampleItem = Database["public"]["Tables"]["example_items"]["Row"];
 */
interface ExampleItem {
  id: string;
  name: string;
  description: string | null;
  created_at: string;
  updated_at: string;
  status: "active" | "inactive" | "draft";
}

/**
 * Example Item Update type
 *
 * In a real application, this would be derived from the Supabase generated types.
 * For example:
 * type ExampleItemUpdate = Partial<Omit<ExampleItem, "id" | "created_at">>;
 */
interface ExampleItemUpdate {
  name?: string;
  description?: string | null;
  status?: "active" | "inactive" | "draft";
}

/**
 * Example service demonstrating the use of standard response types
 */
export class ExampleService {
  /**
   * Get an item by ID
   * @param id The item ID
   * @returns Standard service response
   */
  static async read(id: string): Promise<ServiceResponse<ExampleItem>> {
    try {
      const supabase = await createClient();

      const { data, error } = await supabase
        // @ts-expect-error - Only allowed in template. Remove when implementing
        .from("example_items")
        .select("*")
        .eq("id", id)
        .single();

      if (error) {
        logger.error(`Error fetching item: ${error.message}`);
        return errorResponse(error, `Failed to fetch item: ${error.message}`);
      }

      if (!data) {
        return errorResponse(null, `Item with ID ${id} not found`);
      }

      return successResponse(data as ExampleItem, "Item retrieved successfully");
    } catch (error) {
      logger.error(`Unexpected error fetching item: ${error}`);
      return errorResponse(error, "An unexpected error occurred");
    }
  }

  /**
   * Create a new item
   * @param item The item to create
   * @returns Standard service response
   */
  static async create(
    item: Omit<ExampleItem, "id" | "created_at" | "updated_at">
  ): Promise<ServiceResponse<ExampleItem>> {
    try {
      const supabase = await createClient();

      // @ts-expect-error - Only allowed in template. Remove when implementing
      const { data, error } = await supabase.from("example_items").insert(item).select().single();

      if (error) {
        logger.error(`Error creating item: ${error.message}`);
        return errorResponse(error, `Failed to create item: ${error.message}`);
      }

      return successResponse(data as ExampleItem, "Item created successfully");
    } catch (error) {
      logger.error(`Unexpected error creating item: ${error}`);
      return errorResponse(error, "An unexpected error occurred");
    }
  }

  /**
   * Update an existing item
   * @param id The item ID
   * @param updates The updates to apply
   * @returns Standard service response
   */
  static async update(
    id: string,
    updates: ExampleItemUpdate
  ): Promise<ServiceResponse<ExampleItem>> {
    try {
      const supabase = await createClient();

      const { data, error } = await supabase
        // @ts-expect-error - Only allowed in template. Remove when implementing
        .from("example_items")
        .update(updates)
        .eq("id", id)
        .select()
        .single();

      if (error) {
        logger.error(`Error updating item: ${error.message}`);
        return errorResponse(error, `Failed to update item: ${error.message}`);
      }

      return successResponse(data as ExampleItem, "Item updated successfully");
    } catch (error) {
      logger.error(`Unexpected error updating item: ${error}`);
      return errorResponse(error, "An unexpected error occurred");
    }
  }
}
