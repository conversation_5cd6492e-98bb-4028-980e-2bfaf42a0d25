"use server";

import { revalidatePath } from "next/cache";
import { redirect } from "next/navigation";
import { ActionState, errorActionState, successActionState } from "@/lib/types/responses";
import { ExampleService } from "./ExampleService";
import { logger } from "@/lib/logger/services/LoggerService";

/**
 * Example Item type
 *
 * In a real application, this would be imported from Supabase generated types.
 * For example:
 * import { Database } from "@/lib/types/database.types";
 * type ExampleItem = Database["public"]["Tables"]["example_items"]["Row"];
 */
interface ExampleItem {
  id: string;
  name: string;
  description: string | null;
  created_at: string;
  updated_at: string;
  status: "active" | "inactive" | "draft";
}

/**
 * Example Item Update type
 *
 * In a real application, this would be derived from the Supabase generated types.
 * For example:
 * type ExampleItemUpdate = Partial<Omit<ExampleItem, "id" | "created_at">>;
 */
interface ExampleItemUpdate {
  name?: string;
  description?: string | null;
  status?: "active" | "inactive" | "draft";
}

/**
 * Example server action that returns a state (for forms that stay on the same page)
 */
export async function updateExampleItem(
  _prevState: ActionState<ExampleItem>, // Prefix with underscore to indicate it's intentionally unused
  formData: FormData
): Promise<ActionState<ExampleItem>> {
  // Extract form data
  const id = formData.get("id") as string;
  const name = formData.get("name") as string;
  const description = formData.get("description") as string;

  // Validate input
  if (!id || !name) {
    return errorActionState("ID and name are required");
  }

  // Call service
  const response = await ExampleService.update(id, { name, description } as ExampleItemUpdate);

  // Handle error
  if (!response.success) {
    // Convert null to undefined for the logger
    const error = response.error || undefined;
    logger.error(`Failed to update item: ${response.message}`, error);
    return errorActionState(response.message);
  }

  // Revalidate paths
  revalidatePath(`/examples/${id}`);

  // Return success state
  return successActionState(response.data);
}

/**
 * Example server action that redirects on success
 */
export async function createExampleItem(
  _prevState: ActionState<ExampleItem>, // Prefix with underscore to indicate it's intentionally unused
  formData: FormData
): Promise<ActionState<ExampleItem>> {
  // Extract form data
  const name = formData.get("name") as string;
  const description = formData.get("description") as string;

  // Validate input
  if (!name) {
    return errorActionState("Name is required");
  }

  // Call service
  const response = await ExampleService.create({ name, description } as ExampleItem);

  // Handle error
  if (!response.success) {
    // Convert null to undefined for the logger
    const error = response.error || undefined;
    logger.error(`Failed to create item: ${response.message}`, error);
    return errorActionState(response.message);
  }

  // Revalidate paths
  revalidatePath("/examples");

  // Redirect on success - this prevents the function from returning
  redirect(`/examples/${response?.data?.id}`);

  // TypeScript needs a return statement for type checking
  // This code will never execute due to the redirect
  throw new Error("Unreachable code - redirect should have prevented execution reaching here");
}
