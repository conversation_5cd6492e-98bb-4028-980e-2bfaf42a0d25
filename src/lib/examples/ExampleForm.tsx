"use client";

import { useActionState } from "react";
import { updateExampleItem } from "./exampleActions";
import { initialActionState } from "@/lib/types/responses";
import { Dictionary } from "../i18n/services/I18nService";

interface ExampleFormProps {
  item: {
    id: string;
    name: string;
    description?: string;
  };
  dictionary: Dictionary;
}

export function ExampleForm({ item, dictionary }: ExampleFormProps) {
  // Initialize action state with our standard initial state
  const [state, formAction, pending] = useActionState(updateExampleItem, initialActionState());

  return (
    <div className="space-y-6">
      {/* Success message */}
      {state.success && (
        <div className="bg-green-50 border border-green-200 rounded-md p-4">
          <p className="text-green-600">Item updated successfully</p>
        </div>
      )}

      {/* Error message */}
      {state.error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <p className="text-red-600">{state.error}</p>
        </div>
      )}

      {/* Form */}
      <form action={formAction} className="space-y-4">
        {/* Hidden ID field */}
        <input type="hidden" name="id" value={item.id} />

        {/* Name field */}
        <div className="space-y-2">
          <label htmlFor="name" className="block text-sm font-medium">
            {dictionary.user.name || "Name"}
          </label>
          <input
            id="name"
            name="name"
            type="text"
            defaultValue={item.name}
            required
            className="w-full rounded-md border p-2"
          />
        </div>

        {/* Description field */}
        <div className="space-y-2">
          <label htmlFor="description" className="block text-sm font-medium">
            {dictionary.user.personalDescription || "Description"}
          </label>
          <textarea
            id="description"
            name="description"
            defaultValue={item.description || ""}
            rows={3}
            className="w-full rounded-md border p-2"
          />
        </div>

        {/* Submit button */}
        <div className="flex justify-end">
          <button
            type="submit"
            disabled={pending}
            className="rounded-md bg-blue-600 px-4 py-2 text-white"
          >
            {pending
              ? dictionary.common.saving || "Saving..."
              : dictionary.common.save || "Save Changes"}
          </button>
        </div>
      </form>
    </div>
  );
}
