/**
 * Creates a URL with the current language
 *
 * @param lang The current language
 * @param path The path to navigate to
 * @returns The full URL with language
 *
 * @example
 * ```ts
 * const url = createUrl("en", "/users");
 * // Returns "/en/users"
 * ```
 */
export function createUrl(lang: string, path: string): string {
  // Ensure path starts with a slash
  const normalizedPath = path.startsWith("/") ? path : `/${path}`;
  return `/${lang}${normalizedPath}`;
}

/**
 * Creates CRUD URLs for a feature
 *
 * @param lang The current language
 * @param basePath The base path for the feature
 * @returns An object with CRUD URLs
 *
 * @example
 * ```ts
 * const urls = createCrudUrls("en", "/protected/users");
 * // Returns {
 * //   list: "/en/protected/users/list",
 * //   create: "/en/protected/users/create",
 * //   view: (id) => "/en/protected/users/${id}/view",
 * //   edit: (id) => "/en/protected/users/${id}/edit",
 * //   remove: (id) => "/en/protected/users/${id}/remove"
 * // }
 * ```
 */
export function createCrudUrls(lang: string, basePath: string) {
  // Ensure basePath starts with a slash
  const normalizedBasePath = basePath.startsWith("/") ? basePath : `/${basePath}`;

  return {
    list: createUrl(lang, `${normalizedBasePath}/list`),
    create: createUrl(lang, `${normalizedBasePath}/create`),
    view: (id: string) => createUrl(lang, `${normalizedBasePath}/${id}/view`),
    edit: (id: string) => createUrl(lang, `${normalizedBasePath}/${id}/edit`),
    remove: (id: string) => createUrl(lang, `${normalizedBasePath}/${id}/remove`),
  };
}
