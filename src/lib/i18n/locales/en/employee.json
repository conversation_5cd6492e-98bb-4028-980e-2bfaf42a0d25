{"title": "Employee Management", "description": "Manage employees, their roles, and permissions", "availability": {"title": "Employee Availability", "description": "Manage employee availability, time-off requests, and exceptions", "listTitle": "Employee Availability", "listDescription": "Select an employee to manage their availability", "createTitle": "Create New", "editTitle": "Edit", "createDescription": "Create a new availability record", "editDescription": "Edit the availability record", "viewDescription": "View availability details", "noItems": "No records found", "backToList": "Back to List", "cancelButton": "Cancel", "saveButton": "Save", "editButton": "Edit", "deleteButton": "Delete", "submitting": "Saving...", "errorSubmitting": "Error submitting form", "errorDeleting": "Error deleting record", "errorProcessing": "Error processing request", "requiredFieldsError": "Please fill in all required fields", "startTime": "Start Time", "endTime": "End Time", "tabs": {"regularHours": "Regular Hours", "timeOff": "Time Off", "exceptions": "Exceptions"}, "daysOfWeek": {"monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday"}, "regularHoursTitle": "Weekly Availability", "regularHoursDescription": "Set your regular weekly availability", "timeOff": {"title": "Time Off", "createTitle": "Request Time Off", "editTitle": "Edit Time Off Request", "createDescription": "Submit a new time off request", "editDescription": "Edit your time off request", "listTitle": "Time Off Requests", "noRecords": "No time off requests found", "addButton": "Request Time Off", "startDate": "Start Date", "endDate": "End Date", "startTime": "Start Time (optional)", "endTime": "End Time (optional)", "type": "Type", "selectType": "Select type", "description": "Reason/Notes", "createSuccess": "Time off request created successfully", "updateSuccess": "Time off request updated successfully", "deleteSuccess": "Time off request deleted successfully", "approveSuccess": "Time off request approved", "rejectSuccess": "Time off request rejected", "cancelSuccess": "Time off request cancelled", "deleteConfirmTitle": "Delete Time Off Request", "deleteConfirmMessage": "Are you sure you want to delete this time off request? This action cannot be undone.", "approveButton": "Approve", "rejectButton": "Reject", "cancelButton": "Cancel Request", "types": {"vacation": "Vacation", "sickLeave": "Sick Leave", "personalLeave": "Personal Leave", "other": "Other"}, "status": {"pending": "Pending", "approved": "Approved", "rejected": "Rejected", "cancelled": "Cancelled"}}, "exceptions": {"title": "Availability Exceptions", "createTitle": "Create Exception", "editTitle": "Edit Exception", "createDescription": "Create a one-time exception to regular availability", "editDescription": "Edit availability exception", "listTitle": "Availability Exceptions", "noRecords": "No exceptions found", "addButton": "Add Exception", "date": "Date", "reason": "Reason", "reasonPlaceholder": "Explain why this exception is needed", "availableLabel": "Available", "unavailableLabel": "Unavailable", "createSuccess": "Exception created successfully", "updateSuccess": "Exception updated successfully", "deleteSuccess": "Exception deleted successfully", "deleteConfirmTitle": "Delete Exception", "deleteConfirmMessage": "Are you sure you want to delete this exception? This action cannot be undone."}, "timeOffTypes": {"vacation": "Vacation", "sick_leave": "Sick Leave", "personal_leave": "Personal Leave", "other": "Other"}, "status": {"pending": "Pending", "approved": "Approved", "rejected": "Rejected", "cancelled": "Cancelled"}}}