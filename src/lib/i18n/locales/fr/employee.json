{"title": "Gestion des Employés", "description": "<PERSON><PERSON><PERSON> les employés, leurs rôles et leurs permissions", "availability": {"title": "Disponibilité des Employés", "description": "Gérer la disponibilité des employés, les demandes de congé et les exceptions", "listTitle": "Disponibilité des Employés", "listDescription": "Sélectionnez un employé pour gérer sa disponibilité", "createTitle": "Créer Nouveau", "editTitle": "Modifier", "createDescription": "Créer un nouvel enregistrement de disponibilité", "editDescription": "Modifier l'enregistrement de disponibilité", "viewDescription": "Voir les détails de disponibilité", "noItems": "Aucun enregistrement trouvé", "backToList": "Retour à la Liste", "cancelButton": "Annuler", "saveButton": "Enregistrer", "editButton": "Modifier", "deleteButton": "<PERSON><PERSON><PERSON><PERSON>", "submitting": "Enregistrement...", "errorSubmitting": "Erreur lors de la soumission du formulaire", "errorDeleting": "<PERSON><PERSON><PERSON> lors de <PERSON>", "errorProcessing": "Erreur lors du traitement de la demande", "requiredFieldsError": "Veuillez remplir tous les champs obligatoires", "startTime": "<PERSON><PERSON> d<PERSON>", "endTime": "Heure de fin", "tabs": {"regularHours": "Heures Régulières", "timeOff": "<PERSON><PERSON><PERSON>", "exceptions": "Exceptions"}, "daysOfWeek": {"monday": "<PERSON><PERSON>", "tuesday": "<PERSON><PERSON>", "wednesday": "<PERSON><PERSON><PERSON><PERSON>", "thursday": "<PERSON><PERSON>", "friday": "<PERSON><PERSON><PERSON><PERSON>", "saturday": "<PERSON><PERSON>", "sunday": "<PERSON><PERSON><PERSON>"}, "regularHoursTitle": "Disponibilité Hebdomadaire", "regularHoursDescription": "Définir votre disponibilité hebdomadaire régulière", "timeOff": {"title": "<PERSON><PERSON><PERSON>", "createTitle": "<PERSON><PERSON><PERSON> un Con<PERSON>", "editTitle": "Modifier la Demande de Congé", "createDescription": "Soumettre une nouvelle demande de congé", "editDescription": "Modifier votre demande de congé", "listTitle": "<PERSON><PERSON><PERSON>", "noRecords": "Aucune demande de congé trouvée", "addButton": "<PERSON><PERSON><PERSON> un Con<PERSON>", "startDate": "Date de début", "endDate": "Date de fin", "startTime": "Heure de début (optionnel)", "endTime": "Heure de fin (optionnel)", "type": "Type", "selectType": "Sélectionner le type", "description": "Raison/Notes", "createSuccess": "<PERSON><PERSON><PERSON> de congé créée avec succès", "updateSuccess": "<PERSON><PERSON><PERSON> de congé mise à jour avec succès", "deleteSuccess": "<PERSON><PERSON><PERSON> de congé supprimée avec succès", "approveSuccess": "<PERSON><PERSON><PERSON> de congé approuvée", "rejectSuccess": "<PERSON><PERSON><PERSON> de congé rejetée", "cancelSuccess": "<PERSON><PERSON><PERSON> de congé annulée", "deleteConfirmTitle": "Su<PERSON><PERSON>er la Demande de Congé", "deleteConfirmMessage": "Êtes-vous sûr de vouloir supprimer cette demande de congé? Cette action ne peut pas être annulée.", "approveButton": "Approuver", "rejectButton": "<PERSON><PERSON><PERSON>", "cancelButton": "Annuler la Demande", "types": {"vacation": "Vacances", "sickLeave": "Congé Maladie", "personalLeave": "Congé Personnel", "other": "<PERSON><PERSON>"}, "status": {"pending": "En Attente", "approved": "Approu<PERSON><PERSON>", "rejected": "<PERSON><PERSON><PERSON>", "cancelled": "<PERSON><PERSON><PERSON>"}}, "exceptions": {"title": "Exceptions de Disponibilité", "createTitle": "<PERSON><PERSON><PERSON> une Exception", "editTitle": "Modifier l'Exception", "createDescription": "<PERSON><PERSON><PERSON> une exception ponctuelle à la disponibilité régulière", "editDescription": "Modifier l'exception de disponibilité", "listTitle": "Exceptions de Disponibilité", "noRecords": "Aucune exception trouvée", "addButton": "Ajouter une Exception", "date": "Date", "reason": "<PERSON>son", "reasonPlaceholder": "Expliquez pourquoi cette exception est nécessaire", "availableLabel": "Disponible", "unavailableLabel": "Indisponible", "createSuccess": "Exception créée avec succès", "updateSuccess": "Exception mise à jour avec succès", "deleteSuccess": "Exception supprimée avec succès", "deleteConfirmTitle": "Supprimer l'Exception", "deleteConfirmMessage": "Êtes-vous sûr de vouloir supprimer cette exception? Cette action ne peut pas être annulée."}, "timeOffTypes": {"vacation": "Vacances", "sick_leave": "Congé Maladie", "personal_leave": "Congé Personnel", "other": "<PERSON><PERSON>"}, "status": {"pending": "En Attente", "approved": "Approu<PERSON><PERSON>", "rejected": "<PERSON><PERSON><PERSON>", "cancelled": "<PERSON><PERSON><PERSON>"}}}