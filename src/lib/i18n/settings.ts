// Define the list of all available locales
export const locales = ["en", "fr"] as const;
export type Locale = (typeof locales)[number];

// Define the default locale
export const defaultLocale: Locale = "fr";

// Define the paths that should not be internationalized
export const pathnames = {
  "/": "/",
  "/dashboard": "/dashboard",
} as const;

// Create a type for the pathnames
export type Pathnames = keyof typeof pathnames;
