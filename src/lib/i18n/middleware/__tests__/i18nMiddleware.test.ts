import { i18nMiddleware } from "../i18nMiddleware";

// Mock the settings module
jest.mock("@/lib/i18n/settings", () => ({
  defaultLocale: "fr",
  locales: ["en", "fr"],
}));

// Import after mocking
import { defaultLocale } from "@/lib/i18n/settings";

// Mock NextRequest and NextResponse
jest.mock("next/server", () => ({
  NextRequest: jest.fn().mockImplementation((url, options = {}) => {
    const headers = options.headers || {};
    return {
      nextUrl: new URL(url),
      url,
      headers: {
        get: jest.fn().mockImplementation((header) => {
          if (header === "Accept-Language") {
            return headers["Accept-Language"] || null;
          }
          return headers[header] || null;
        }),
      },
    };
  }),
  NextResponse: {
    redirect: jest.fn().mockImplementation((url) => ({
      status: 307,
      url: url.toString(),
      cookies: {
        set: jest.fn(),
      },
      headers: {
        set: jest.fn(),
      },
    })),
    next: jest.fn().mockImplementation(() => ({
      status: 200,
      headers: {
        set: jest.fn(),
      },
    })),
  },
}));

// Import after mocking
// eslint-disable-next-line @typescript-eslint/no-require-imports
const { NextRequest } = require("next/server");

describe("i18nMiddleware", () => {
  // Reset mocks before each test
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should redirect to default locale if no locale is in the pathname", () => {
    // Create a mock request with no Accept-Language header
    const request = new NextRequest(new URL("https://example.com/dashboard"));

    // Call the middleware
    const response = i18nMiddleware(request);

    // Check if the response is a redirect
    expect(response.status).toBe(307);

    // Check if the redirect URL includes the default locale
    expect(response.url).toBe(`https://example.com/${defaultLocale}/dashboard`);
  });

  it("should redirect to default locale for root path", () => {
    // Create a mock request
    const request = new NextRequest(new URL("https://example.com/"));

    // Call the middleware
    const response = i18nMiddleware(request);

    // Check if the response is a redirect
    expect(response.status).toBe(307);

    // Check if the redirect URL includes the default locale
    expect(response.url).toBe(`https://example.com/${defaultLocale}`);
  });

  it("should preserve search params when redirecting", () => {
    // Create a mock request with search params
    const request = new NextRequest(
      new URL("https://example.com/dashboard?param1=value1&param2=value2")
    );

    // Call the middleware
    const response = i18nMiddleware(request);

    // Check if the response is a redirect
    expect(response.status).toBe(307);

    // Check if the redirect URL includes the default locale and search params
    expect(response.url).toBe(
      `https://example.com/${defaultLocale}/dashboard?param1=value1&param2=value2`
    );
  });

  it("should not redirect if locale is already in the pathname", () => {
    // Create a mock request with locale in the pathname
    const request = new NextRequest(new URL(`https://example.com/${defaultLocale}/dashboard`));

    // Call the middleware
    const response = i18nMiddleware(request);

    // Check if the response is not a redirect
    expect(response.status).toBe(200);
  });

  it("should use the preferred locale from Accept-Language header", () => {
    // Create a mock request with Accept-Language header
    const request = new NextRequest(new URL("https://example.com/dashboard"), {
      headers: {
        "Accept-Language": "en-US,en;q=0.9,fr;q=0.8",
      },
    });

    // Call the middleware
    const response = i18nMiddleware(request);

    // Check if the response is a redirect
    expect(response.status).toBe(307);

    // Check if the redirect URL includes the preferred locale (en)
    expect(response.url).toBe("https://example.com/en/dashboard");
  });

  it("should fallback to default locale if Accept-Language header doesn't match any supported locale", () => {
    // Create a mock request with Accept-Language header that doesn't match any supported locale
    const request = new NextRequest(new URL("https://example.com/dashboard"), {
      headers: {
        "Accept-Language": "de-DE,de;q=0.9,es;q=0.8",
      },
    });

    // Call the middleware
    const response = i18nMiddleware(request);

    // Check if the response is a redirect
    expect(response.status).toBe(307);

    // Check if the redirect URL includes the default locale
    expect(response.url).toBe(`https://example.com/${defaultLocale}/dashboard`);
  });

  it("should fallback to default locale if Accept-Language header is not present", () => {
    // Create a mock request without Accept-Language header
    const request = new NextRequest(new URL("https://example.com/dashboard"));

    // Call the middleware
    const response = i18nMiddleware(request);

    // Check if the response is a redirect
    expect(response.status).toBe(307);

    // Check if the redirect URL includes the default locale
    expect(response.url).toBe(`https://example.com/${defaultLocale}/dashboard`);
  });

  it("should handle language switch with __pathname=current parameter", () => {
    // Create a mock request with __pathname=current parameter
    const request = new NextRequest(new URL("https://example.com/en/dashboard?__pathname=current"));

    // Call the middleware
    const response = i18nMiddleware(request);

    // Check if the response is a redirect
    expect(response.status).toBe(307);

    // Check if the __pathname parameter is removed from the URL
    expect(response.url).not.toContain("__pathname=current");

    // Check if the cookies.set method was called with the correct parameters
    expect(response.cookies.set).toHaveBeenCalledWith("currentPath", "/dashboard");
  });
});
