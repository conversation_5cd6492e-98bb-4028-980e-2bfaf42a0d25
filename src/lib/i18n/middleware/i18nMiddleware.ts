import { NextRequest, NextResponse } from "next/server";
import { defaultLocale, locales } from "@/lib/i18n/settings";

/**
 * Middleware for handling internationalization in URLs
 * @param request Next.js request object
 * @returns Next.js response object
 */
export function i18nMiddleware(request: NextRequest): NextResponse {
  // Get the pathname and search params from the request
  const { pathname } = request.nextUrl;
  const searchParams = request.nextUrl.searchParams;

  // Check if this is a language switch with the special __pathname parameter
  const isLanguageSwitch = searchParams.has("__pathname");

  if (isLanguageSwitch && searchParams.get("__pathname") === "current") {
    // Extract the current locale from the pathname
    const currentLocale = pathname.split("/")[1];

    // Get the current path without the locale prefix
    const pathWithoutLocale = pathname.replace(new RegExp(`^/${currentLocale}`), "");

    // Create a new URL with the same path but without the __pathname parameter
    const newUrl = new URL(request.url);
    newUrl.pathname = pathname;
    newUrl.searchParams.delete("__pathname");

    // Create a response that will be modified
    const response = NextResponse.redirect(newUrl);

    // Store the current path in a cookie for client-side access
    response.cookies.set("currentPath", pathWithoutLocale);

    return response;
  }

  // Check if the pathname already has a locale
  const pathnameHasLocale = locales.some(
    (locale) => pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`
  );

  // If the pathname doesn't have a locale, redirect to the default locale
  if (!pathnameHasLocale) {
    // Get the preferred locale from the Accept-Language header
    const preferredLocale = getPreferredLocale(request);

    // Create a new URL with the preferred locale
    const newUrl = new URL(`/${preferredLocale}${pathname === "/" ? "" : pathname}`, request.url);

    // Preserve the search params
    newUrl.search = request.nextUrl.search;

    // Return a redirect response
    return NextResponse.redirect(newUrl);
  }

  // For all requests, add the current pathname to the headers for server components
  const response = NextResponse.next();
  response.headers.set("x-pathname", pathname);

  // Continue with the request if the pathname already has a locale
  return response;
}

/**
 * Gets the preferred locale from the Accept-Language header
 * @param request Next.js request object
 * @returns Preferred locale
 */
function getPreferredLocale(request: NextRequest): string {
  // Get the Accept-Language header
  const acceptLanguage = request.headers.get("Accept-Language");

  if (!acceptLanguage) {
    return defaultLocale;
  }

  // Parse the Accept-Language header
  const acceptedLanguages = acceptLanguage
    .split(",")
    .map((lang) => {
      const [language, quality = "1"] = lang.trim().split(";q=");
      return { language: language.split("-")[0], quality: parseFloat(quality) };
    })
    .sort((a, b) => b.quality - a.quality);

  // Find the first accepted language that matches a supported locale
  const preferredLanguage = acceptedLanguages.find((lang) =>
    locales.some((locale) => locale === lang.language)
  );

  // Return the preferred language or the default locale
  return preferredLanguage ? preferredLanguage.language : defaultLocale;
}
