# Internationalization (i18n) Service

This directory contains the Internationalization Service implementation for the RQRSDA application. The I18nService provides a centralized way to handle translations and localization with a structured approach to organizing translations.

## Overview

The Internationalization Service is built using `@brainstack/inject` for dependency injection. It provides methods for loading dictionaries for different locales, accessing translations, and handling language switching through URL-based navigation.

## Components

### I18nService

The `I18nService` is responsible for loading and managing dictionaries for different locales. It supports:

- Loading dictionaries from locale files
- Caching dictionaries to avoid repeated imports
- Fallback to default locale if a translation is not available
- Fallback to an empty dictionary with default values if all else fails
- Domain-specific dictionaries through nested objects

### i18nMiddleware

The `i18nMiddleware` is responsible for handling language parameters in URLs. It supports:

- Redirecting to the default locale if no locale is specified
- Preserving search parameters when redirecting
- Using the preferred locale from the Accept-Language header
- Fallback to default locale if the preferred locale is not supported

### LanguageToggle Component

The `LanguageToggle` component provides a simple way to switch between languages:

- Server component that renders a button to switch languages
- Uses direct URL links for language switching without client-side state
- Automatically displays the name of the target language in the current language

## Usage

### Using the I18nService

```typescript
import { i18n } from '@/lib/i18n/services/I18nService';

// In a server component
export default async function MyComponent({ params }: { params: { lang: string } }) {
  // Get the dictionary for the current locale
  const dictionary = await i18n.getDictionary(params.lang);

  // Use translations
  return (
    <div>
      <h1>{dictionary.common.welcome}</h1>
      <p>{dictionary.home.description}</p>
    </div>
  );
}
```

### Using the LanguageToggle Component

```tsx
import { LanguageToggle } from '@/components/language-toggle';

export default function Header({ lang }: { lang: string }) {
  return (
    <header>
      <LanguageToggle currentLang={lang} />
    </header>
  );
}
```

### Using the i18nMiddleware

The `i18nMiddleware` is automatically integrated into the main middleware in `src/middleware.ts`. It will handle language parameters in URLs and redirect to the appropriate locale.

### Directory Structure

```
src/
  lib/
    i18n/
      locales/
        en.json       # English translations
        fr.json       # French translations
      services/
        I18nService.ts  # Service for handling translations
      middleware/
        i18nMiddleware.ts  # Middleware for URL-based language detection
      settings.ts      # Configuration for supported locales
      index.ts         # Type definitions and utility functions
  components/
    language-toggle.tsx  # Component for switching languages
```

## Dictionary Structure

The dictionary is structured with top-level sections for different parts of the application:

```json
{
  "common": {
    "appName": "RQRSDA",
    "welcome": "Welcome",
    "loading": "Loading...",
    "save": "Save",
    "create": "Create",
    "all": "All",
    "language": "Language",
    "french": "French",
    "english": "English",
    "help": "Help",
    "contactUs": "Contact Us"
  },
  "home": {
    "title": "RQRSDA 2025",
    "description": "A modern application with dark mode support",
    "demoText": "This is a demonstration of the design system...",
    "themeToggleText": "Try toggling between light and dark mode...",
    "getStarted": "Get Started"
  }
}
```

## Adding a New Locale

To add a new locale:

1. Add the locale to the `locales` array in `src/lib/i18n/settings.ts`
2. Create a new locale file in `src/lib/i18n/locales/`
3. Update the `Dictionary` type in `src/lib/i18n/index.ts` if needed
4. Update the `createEmptyDictionary` method in `I18nService.ts` to include fallback values

## Adding a New Section

To add a new section to the dictionaries:

1. Add the section to all locale files (e.g., `en.json` and `fr.json`)
2. Update the `Dictionary` type in `src/lib/i18n/index.ts` to include the new section
3. Update the `createEmptyDictionary` method in `I18nService.ts` to include fallback values

## Best Practices

1. **Use nested sections**: Organize translations by section (e.g., `common`, `home`, `auth`)
2. **Use consistent keys**: Use the same keys across all locale files
3. **Use variables**: Use variables in translations for dynamic content (e.g., `Welcome, {name}`)
4. **Keep translations simple**: Avoid complex formatting in translations
5. **Use fallbacks**: Always provide fallbacks in the `createEmptyDictionary` method
6. **Update all files**: When adding a new key, update all locale files and the Dictionary type
