"use client";

import React, { createContext, useContext, useEffect, useState } from "react";
import { createClient } from "@/lib/supabase/client";
import { showNotification } from "@/components/ui/notification";
import { RealtimeChannel } from "@supabase/supabase-js";
import { Database } from "@/lib/types/database.types";

// Define the notification type from the database
export type Notification = Database["public"]["Tables"]["notifications"]["Row"];

// Define the context type
type NotificationContextType = {
  notifications: Notification[];
  unreadCount: number;
  markAsRead: (id: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  loading: boolean;
  error: string | null;
};

// Create the context
const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export function NotificationProvider({ children }: { children: React.ReactNode }) {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [channel, setChannel] = useState<RealtimeChannel | null>(null);

  useEffect(() => {
    // Create Supabase client
    const supabase = createClient();

    // Fetch initial notifications
    const fetchNotifications = async () => {
      try {
        setLoading(true);
        const { data, error } = await supabase
          .from("notifications")
          .select("*")
          .order("created_at", { ascending: false });

        if (error) {
          throw error;
        }

        if (data) {
          setNotifications(data);
          setUnreadCount(data.filter((n) => !n.read).length);
        }
      } catch (err) {
        console.error("Error fetching notifications:", err);
        setError("Failed to load notifications");
      } finally {
        setLoading(false);
      }
    };

    // Set up realtime subscription
    const setupSubscription = async () => {
      try {
        // Subscribe to notifications table
        const newChannel = supabase
          .channel("notifications-channel")
          .on(
            "postgres_changes",
            {
              event: "INSERT",
              schema: "public",
              table: "notifications",
            },
            (payload) => {
              const newNotification = payload.new as Notification;

              // Show toast notification for new notifications
              showNotification({
                title: newNotification.title,
                description: newNotification.message,
                type: (newNotification.type as "success" | "error" | "info" | "warning") || "info",
              });

              // Update notifications state
              setNotifications((prev) => [newNotification, ...prev]);
              setUnreadCount((prev) => prev + 1);
            }
          )
          .on(
            "postgres_changes",
            {
              event: "UPDATE",
              schema: "public",
              table: "notifications",
            },
            (payload) => {
              const updatedNotification = payload.new as Notification;

              // Update notifications state and recalculate unread count
              setNotifications((currentNotifications) => {
                const updatedNotifications = currentNotifications.map((n) =>
                  n.id === updatedNotification.id ? updatedNotification : n
                );
                setUnreadCount(updatedNotifications.filter((n) => !n.read).length);
                return updatedNotifications;
              });
            }
          )
          .subscribe();

        setChannel(newChannel);
      } catch (err) {
        console.error("Error setting up realtime subscription:", err);
        setError("Failed to set up realtime notifications");
      }
    };

    // Initialize
    fetchNotifications();
    setupSubscription();

    // Cleanup
    return () => {
      if (channel) {
        supabase.removeChannel(channel);
      }
    };
  }, []);

  // Mark a notification as read
  const markAsRead = async (id: string) => {
    try {
      const supabase = createClient();
      const { error } = await supabase.from("notifications").update({ read: true }).eq("id", id);

      if (error) {
        throw error;
      }

      // Update local state
      setNotifications((prev) => prev.map((n) => (n.id === id ? { ...n, read: true } : n)));
      setUnreadCount((prev) => prev - 1);
    } catch (err) {
      console.error("Error marking notification as read:", err);
      setError("Failed to mark notification as read");
    }
  };

  // Mark all notifications as read
  const markAllAsRead = async () => {
    try {
      const supabase = createClient();
      const { error } = await supabase
        .from("notifications")
        .update({ read: true })
        .eq("read", false);

      if (error) {
        throw error;
      }

      // Update local state
      setNotifications((prev) => prev.map((n) => ({ ...n, read: true })));
      setUnreadCount(0);
    } catch (err) {
      console.error("Error marking all notifications as read:", err);
      setError("Failed to mark all notifications as read");
    }
  };

  const value = {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    loading,
    error,
  };

  return <NotificationContext.Provider value={value}>{children}</NotificationContext.Provider>;
}

// Custom hook to use the notification context
export function useNotifications() {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error("useNotifications must be used within a NotificationProvider");
  }
  return context;
}
