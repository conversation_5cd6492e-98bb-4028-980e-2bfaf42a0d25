import { ReactNode } from "react";
import { Permission } from "../types";
import { PermissionService } from "../services/PermissionService";
import { auth } from "@/lib/authentication/services/AuthenticationService";

interface AuthorizationRequiredProps {
  permission: Permission;
  children: ReactNode;
  fallback?: ReactNode;
}

/**
 * Server component that conditionally renders its children based on whether the current user has a permission
 * This is secure because the permission check happens on the server
 */
export async function AuthorizationRequired({
  permission,
  children,
  fallback = null,
}: AuthorizationRequiredProps) {
  try {
    // Get the current user's role
    const role = await auth.getCurrentUserRole();

    if (!role) {
      return <>{fallback}</>;
    }

    // Check if the user has the required permission
    const hasPermission = PermissionService.hasPermission(role, permission);

    // Render children if user has permission, otherwise render fallback
    return hasPermission ? <>{children}</> : <>{fallback}</>;
  } catch (error) {
    console.error(`Error checking permission ${permission}:`, error);

    // In case of error, don't render the protected content
    return <>{fallback}</>;
  }
}
