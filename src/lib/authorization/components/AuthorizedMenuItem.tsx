import Link from "next/link";
import { cn } from "@/lib/utils";
import { AuthorizationRequired } from "./AuthorizationRequired";
import { Permission } from "../types";
import { LucideIcon } from "lucide-react";

interface AuthorizedMenuItemProps {
  title: string;
  href: string;
  icon?: LucideIcon;
  permission: Permission;
  lang: string;
  pathname: string;
  dictionary?: Record<string, string>;
}

/**
 * A menu item that is only rendered if the user has the required permission
 *
 * This component combines permission checking with menu item rendering,
 * making it easier to create permission-based navigation.
 *
 * @example
 * ```tsx
 * <AuthorizedMenuItem
 *   title="organizationAdmin"
 *   href="/organization/system-admin/list"
 *   icon={Building}
 *   permission={PERMISSIONS.ORGANIZATION.SYSTEM_ADMIN.LIST}
 *   lang={lang}
 *   pathname={pathname}
 *   dictionary={dictionary}
 * />
 * ```
 */
export async function AuthorizedMenuItem({
  title,
  href,
  icon: Icon,
  permission,
  lang,
  pathname,
  dictionary,
}: AuthorizedMenuItemProps) {
  return (
    <AuthorizationRequired permission={permission}>
      <Link
        href={`/${lang}/protected${href}`}
        className={cn(
          "flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium",
          pathname.includes(href)
            ? "bg-primary text-primary-foreground"
            : "hover:bg-accent hover:text-accent-foreground"
        )}
      >
        {Icon && <Icon className="h-4 w-4" />}
        <span>{dictionary?.[title] || title}</span>
      </Link>
    </AuthorizationRequired>
  );
}
