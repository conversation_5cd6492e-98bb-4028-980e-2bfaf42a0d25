import { logger } from "@/lib/logger/services/LoggerService";
import { UserRole } from "../types";
import { auth } from "@/lib/authentication/services/AuthenticationService";

/**
 * Service for managing user roles
 */
export class RoleService {
  /**
   * Get the current user's role
   * Uses AuthenticationService which caches the role to reduce database calls
   * @returns Promise resolving to the user's role
   */
  static async getCurrentUserRole(): Promise<UserRole> {
    try {
      // For testing purposes, check if there's a role in localStorage (client-side only)
      if (typeof window !== "undefined") {
        const testRole = localStorage.getItem("testUserRole");
        if (testRole) {
          logger.info(`Using test role from localStorage: ${testRole}`);
          return testRole as UserRole;
        }

        // For client components, return a default role
        // In a real app, you would get this from a client-side API call
        return "SocialWorker";
      }

      // Use the cached role from AuthenticationService
      const role = await auth.getCurrentUserRole();

      if (role) {
        logger.info(`Using cached role from AuthenticationService: ${role}`);
        return role;
      }

      logger.warn("No role found in AuthenticationService, using default role: Director");
      return "Director"; // Default for development
    } catch (error) {
      logger.error(`Error getting current user role: ${error}`);

      // For development purposes, return a default role instead of failing
      logger.warn("Returning default role (Director) due to error in getCurrentUserRole");
      return "Director";
    }
  }

  /**
   * Get a user's role by user ID
   * Uses AuthenticationService's fetchUserRole method which is optimized for role fetching
   * @param userId The user ID
   * @returns Promise resolving to the user's role
   */
  static async getUserRole(userId: string): Promise<UserRole> {
    try {
      logger.info(`Getting role for user: ${userId}`);

      // Check if this is the current user
      const currentUser = await auth.getCurrentUser();

      if (currentUser && currentUser.id === userId) {
        // Use the cached role from AuthenticationService for the current user
        const role = await auth.getCurrentUserRole();

        if (role) {
          logger.info(`Using cached role from AuthenticationService for current user: ${role}`);
          return role;
        }
      }

      // For other users, we need to fetch the role directly
      // We'll use a private method from AuthenticationService that's exposed for this purpose
      const role = await (auth as any).fetchUserRole(userId);

      if (role) {
        logger.info(`Role for user ${userId}: ${role}`);
        return role;
      }

      logger.warn(`No role found for user ${userId}, using default role: Director`);
      return "Director"; // Default for development
    } catch (error) {
      logger.error(`Error getting user role: ${error}`);

      // For development purposes, return a default role instead of failing
      logger.warn("Returning default role (Director) due to error");
      return "Director";
    }
  }

  /**
   * Check if a role is valid
   * @param role The role to check
   * @returns True if the role is valid, false otherwise
   */
  static isValidRole(role: string): boolean {
    return ["Director", "Coordinator", "SocialWorker"].includes(role);
  }

  /**
   * Get the display name for a role
   * @param role The role
   * @returns The display name for the role
   */
  static getDisplayName(role: UserRole): string {
    const displayNames: Record<UserRole, string> = {
      Director: "Director",
      Coordinator: "Coordinator",
      SocialWorker: "Social Worker",
      SystemAdmin: "System Administrator",
    };

    return displayNames[role] || role;
  }
}
