import { PermissionService } from "../PermissionService";
import { RoleService } from "../RoleService";
import { UserRole } from "../../types";

// Mock the RoleService
jest.mock("../RoleService", () => ({
  RoleService: {
    getCurrentUserRole: jest.fn(),
  },
}));

// Mock the ConfigurationService
jest.mock("../ConfigurationService", () => ({
  ConfigurationService: {
    getPermissionsForRole: jest.fn(),
  },
}));

// Mock the logger
jest.mock("@/lib/logger/services/LoggerService", () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
  },
}));

// Mock the forbidden function
jest.mock("next/navigation", () => ({
  forbidden: jest.fn(),
}));

describe("PermissionService", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("permissionMatches", () => {
    it("should return true for wildcard permission", () => {
      expect(PermissionService.permissionMatches("*", "user:profile:read")).toBe(true);
    });

    it("should return true for exact match", () => {
      expect(PermissionService.permissionMatches("user:profile:read", "user:profile:read")).toBe(
        true
      );
    });

    it("should return true for domain wildcard", () => {
      expect(PermissionService.permissionMatches("user:*", "user:profile:read")).toBe(true);
    });

    it("should return true for feature wildcard", () => {
      expect(PermissionService.permissionMatches("user:profile:*", "user:profile:read")).toBe(true);
    });

    it("should return false for non-matching permission", () => {
      expect(PermissionService.permissionMatches("user:profile:write", "user:profile:read")).toBe(
        false
      );
    });
  });

  describe("hasPermission", () => {
    beforeEach(() => {
      jest.spyOn(PermissionService, "getPermissionsForRole").mockReturnValue(["user:profile:read"]);
    });

    it("should return true when user has the permission", () => {
      const result = PermissionService.hasPermission("Director" as UserRole, "user:profile:read");
      expect(result).toBe(true);
    });

    it("should return false when user does not have the permission", () => {
      const result = PermissionService.hasPermission("Director" as UserRole, "user:profile:write");
      expect(result).toBe(false);
    });
  });

  describe("validateCurrentUserPermission", () => {
    // Mock window to simulate server-side environment
    const originalWindow = global.window;

    beforeEach(() => {
      // Remove window to simulate server environment
      delete global.window;
    });

    afterEach(() => {
      // Restore window after tests
      global.window = originalWindow;
    });

    it("should not call forbidden when user has permission", async () => {
      (RoleService.getCurrentUserRole as jest.Mock).mockResolvedValue("Director");
      jest.spyOn(PermissionService, "hasPermission").mockReturnValue(true);

      const { forbidden } = await import("next/navigation");

      await PermissionService.validateCurrentUserPermission("user:profile:read");

      expect(forbidden).not.toHaveBeenCalled();
    });

    it("should call forbidden when user does not have permission", async () => {
      (RoleService.getCurrentUserRole as jest.Mock).mockResolvedValue("SocialWorker");
      jest.spyOn(PermissionService, "hasPermission").mockReturnValue(false);

      const { forbidden } = await import("next/navigation");

      await PermissionService.validateCurrentUserPermission("admin:settings:update");

      expect(forbidden).toHaveBeenCalled();
    });

    it("should call forbidden when there is an error", async () => {
      (RoleService.getCurrentUserRole as jest.Mock).mockRejectedValue(new Error("Test error"));

      const { forbidden } = await import("next/navigation");

      await PermissionService.validateCurrentUserPermission("user:profile:read");

      expect(forbidden).toHaveBeenCalled();
    });
  });
});
