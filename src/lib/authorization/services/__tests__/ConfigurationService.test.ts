import { ConfigurationService } from "../ConfigurationService";
import { UserRole } from "../../types";
import { PERMISSIONS } from "../../config/permissions";

// Mock the logger
jest.mock("@/lib/logger/services/LoggerService", () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
  },
}));

describe("ConfigurationService", () => {
  beforeEach(() => {
    // Reset the configuration before each test
    ConfigurationService.resetConfiguration();
    jest.clearAllMocks();
  });

  describe("getPermissionsForRole", () => {
    it("should return permissions for Director role", () => {
      const permissions = ConfigurationService.getPermissionsForRole("Director" as UserRole);
      expect(permissions).toContain(PERMISSIONS.USER.PROFILE.ALL);
      expect(permissions).toContain(PERMISSIONS.ORGANIZATION.PROFILE.ALL);
    });

    it("should return permissions for Coordinator role", () => {
      const permissions = ConfigurationService.getPermissionsForRole("Coordinator" as UserRole);
      expect(permissions).toContain(PERMISSIONS.USER.PROFILE.ALL);
      expect(permissions).toContain(PERMISSIONS.ORGANIZATION.PROFILE.ALL);
    });

    it("should return permissions for SocialWorker role", () => {
      const permissions = ConfigurationService.getPermissionsForRole("SocialWorker" as UserRole);
      expect(permissions).toContain(PERMISSIONS.USER.PROFILE.ALL);
      expect(permissions).toContain(PERMISSIONS.ORGANIZATION.PROFILE.READ);
    });

    it("should return permissions for SystemAdmin role", () => {
      const permissions = ConfigurationService.getPermissionsForRole("SystemAdmin" as UserRole);
      expect(permissions).toContain(PERMISSIONS.ALL);
    });

    it("should return empty array for unknown role", () => {
      const permissions = ConfigurationService.getPermissionsForRole("Unknown" as UserRole);
      expect(permissions).toEqual([]);
    });
  });

  // Permission management methods removed as they are out of scope for MVP

  describe("resetConfiguration", () => {
    it("should reset configuration to default", () => {
      // Call resetConfiguration
      ConfigurationService.resetConfiguration();

      // Check that permissions are set to default
      const directorPermissions = ConfigurationService.getPermissionsForRole(
        "Director" as UserRole
      );
      expect(directorPermissions).toContain(PERMISSIONS.USER.PROFILE.ALL);
      expect(directorPermissions).toContain(PERMISSIONS.ORGANIZATION.PROFILE.ALL);

      const systemAdminPermissions = ConfigurationService.getPermissionsForRole(
        "SystemAdmin" as UserRole
      );
      expect(systemAdminPermissions).toContain(PERMISSIONS.ALL);
    });
  });
});
