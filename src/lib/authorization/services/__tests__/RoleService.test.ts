import { RoleService } from "../RoleService";
import { UserRole } from "../../types";

// Mock the createClient function
jest.mock("@/lib/supabase/server", () => ({
  createClient: jest.fn().mockImplementation(() => ({
    auth: {
      getSession: jest.fn().mockResolvedValue({
        data: {
          session: {
            user: {
              id: "test-user-id",
            },
          },
        },
      }),
    },
    from: jest.fn().mockImplementation(() => ({
      select: jest.fn().mockImplementation(() => ({
        eq: jest.fn().mockImplementation(() => ({
          single: jest.fn().mockResolvedValue({
            data: {
              role: "Director",
            },
            error: null,
          }),
        })),
      })),
    })),
  })),
}));

// Mock the logger
jest.mock("@/lib/logger/services/LoggerService", () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
  },
}));

describe("RoleService", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("getCurrentUserRole", () => {
    it("should return the default role when running in test environment", async () => {
      const role = await RoleService.getCurrentUserRole();
      // Our implementation now returns "SocialWorker" for client components in tests
      expect(role).toBe("SocialWorker");
    });

    it("should return a default role when there is no active session", async () => {
      // Get the mocked createClient function
      const { createClient } = await import("@/lib/supabase/server");
      createClient.mockImplementationOnce(() => ({
        auth: {
          getUser: jest.fn().mockResolvedValue({
            data: {
              user: null,
            },
          }),
        },
      }));

      // Our implementation now returns a default role instead of throwing
      const role = await RoleService.getCurrentUserRole();
      expect(role).toBe("SocialWorker");
    });
  });

  describe("getUserRole", () => {
    it("should return the user role for a given user ID", async () => {
      const role = await RoleService.getUserRole("test-user-id");
      expect(role).toBe("Director");
    });

    it("should return a default role when there is a database error", async () => {
      // Get the mocked createClient function
      const { createClient } = await import("@/lib/supabase/server");
      createClient.mockImplementationOnce(() => ({
        from: jest.fn().mockImplementation(() => ({
          select: jest.fn().mockImplementation(() => ({
            eq: jest.fn().mockImplementation(() => ({
              single: jest.fn().mockResolvedValue({
                data: null,
                error: {
                  message: "Database error",
                },
              }),
            })),
          })),
        })),
      }));

      // Our implementation now returns a default role instead of throwing
      const role = await RoleService.getUserRole("test-user-id");
      expect(role).toBe("Director");
    });
  });

  describe("isValidRole", () => {
    it("should return true for valid roles", () => {
      expect(RoleService.isValidRole("Director")).toBe(true);
      expect(RoleService.isValidRole("Coordinator")).toBe(true);
      expect(RoleService.isValidRole("SocialWorker")).toBe(true);
    });

    it("should return false for invalid roles", () => {
      expect(RoleService.isValidRole("InvalidRole")).toBe(false);
    });
  });

  describe("getDisplayName", () => {
    it("should return the display name for a role", () => {
      expect(RoleService.getDisplayName("Director" as UserRole)).toBe("Director");
      expect(RoleService.getDisplayName("Coordinator" as UserRole)).toBe("Coordinator");
      expect(RoleService.getDisplayName("SocialWorker" as UserRole)).toBe("Social Worker");
    });

    it("should return the role itself for unknown roles", () => {
      expect(RoleService.getDisplayName("Unknown" as UserRole)).toBe("Unknown");
    });
  });
});
