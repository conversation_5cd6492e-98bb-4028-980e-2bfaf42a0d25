import { logger } from "@/lib/logger/services/LoggerService";
import { RoleService } from "./RoleService";
import { ConfigurationService } from "./ConfigurationService";
import { Permission, UserRole } from "../types";
import { forbidden } from "next/navigation";

/**
 * Service for checking permissions
 */
export class PermissionService {
  /**
   * Check if a permission matches a required permission
   * @param userPermission The user's permission
   * @param requiredPermission The required permission
   * @returns True if the permission matches, false otherwise
   */
  static permissionMatches(userPermission: Permission, requiredPermission: Permission): boolean {
    // Wildcard permission
    if (userPermission === "*") {
      return true;
    }

    // Exact match
    if (userPermission === requiredPermission) {
      return true;
    }

    // Domain wildcard (domain:*)
    if (userPermission.endsWith(":*") && userPermission.split(":").length === 2) {
      const userDomain = userPermission.split(":")[0];
      const requiredDomain = requiredPermission.split(":")[0];

      if (userDomain === requiredDomain) {
        return true;
      }
    }

    // Feature wildcard (domain:feature:*)
    if (userPermission.endsWith(":*") && userPermission.split(":").length === 3) {
      const [userDomain, userFeature] = userPermission.split(":");
      const [requiredDomain, requiredFeature] = requiredPermission.split(":");

      if (userDomain === requiredDomain && userFeature === requiredFeature) {
        return true;
      }
    }

    return false;
  }

  /**
   * Check if a role has a permission
   * @param role The role
   * @param requiredPermission The required permission
   * @returns True if the role has the permission, false otherwise
   */
  static hasPermission(role: UserRole, requiredPermission: Permission): boolean {
    const permissions = this.getPermissionsForRole(role);

    return permissions.some((permission) => this.permissionMatches(permission, requiredPermission));
  }

  /**
   * Validate that a role has a permission
   * @param role The role
   * @param permission The required permission
   * @throws Error if the role does not have the permission
   */
  static validatePermission(role: UserRole, permission: Permission): void {
    if (!this.hasPermission(role, permission)) {
      logger.error(`Permission denied: ${role} does not have ${permission}`);
      throw new Error(`Permission denied: ${role} does not have ${permission}`);
    }
  }

  /**
   * Validate that the current user has a permission
   * @param permission The required permission
   * @returns Promise resolving when validation is complete
   * @throws Error if the user does not have the permission
   */
  static async validateCurrentUserPermission(permission: Permission): Promise<void> {
    try {
      // Skip validation in client components
      if (typeof window !== "undefined") {
        logger.warn(
          "validateCurrentUserPermission called in client component - skipping server-side validation"
        );
        return;
      }

      const role = await RoleService.getCurrentUserRole();
      const allowed = this.hasPermission(role, permission);

      if (!allowed) {
        logger.error(`Permission denied: ${role} does not have ${permission}`);
        forbidden();
      }
    } catch (error) {
      logger.error(`Error validating permission: ${error}`);
      forbidden();
    }
  }

  /**
   * Get the permissions for a role
   * @param role The role
   * @returns The permissions for the role
   */
  static getPermissionsForRole(role: UserRole): Permission[] {
    return ConfigurationService.getPermissionsForRole(role);
  }
}
