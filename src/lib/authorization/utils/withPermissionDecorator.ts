import { Permission } from "../types";
import { withPermission } from "./withPermission";

/**
 * Creates a decorator-like function for protecting server actions with permissions
 * @param permission The required permission
 * @returns A function that wraps server actions with permission checks
 *
 * Example usage:
 * ```typescript
 * export const updateUser = requirePermission(PERMISSIONS.USER.MANAGEMENT.UPDATE)(
 *   async (userId, data) => {
 *     // Implementation
 *   }
 * );
 * ```
 */
export function requirePermission(permission: Permission) {
  return function <T, Args extends unknown[]>(
    action: (...args: Args) => Promise<T>
  ): (...args: Args) => Promise<T> {
    return withPermission(permission, action);
  };
}
