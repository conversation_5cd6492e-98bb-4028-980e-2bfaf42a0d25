import { PermissionService } from "../services/PermissionService";
import { Permission } from "../types";

/**
 * Wrap a server action with a permission check
 * @param permission The required permission
 * @param action The server action to wrap
 * @returns A new server action that checks permissions before executing
 */
export function withPermission<T, Args extends unknown[]>(
  permission: Permission,
  action: (...args: Args) => Promise<T>
): (...args: Args) => Promise<T> {
  return async (...args: Args) => {
    // Check permission first
    await PermissionService.validateCurrentUserPermission(permission);

    // If we get here, the user has permission, so execute the action
    return action(...args);
  };
}
