import { requirePermission } from "../withPermissionDecorator";
import * as withPermissionModule from "../withPermission";

jest.mock("../withPermission");

describe("requirePermission", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should return a function that wraps the action with withPermission", async () => {
    // Mock the withPermission function
    const mockWithPermission = jest.spyOn(withPermissionModule, "withPermission");
    mockWithPermission.mockImplementation((permission, action) => {
      return async (...args: unknown[]) => {
        return await action(...args);
      };
    });

    // Create a mock action
    const mockAction = jest.fn().mockResolvedValue("result");

    // Create a decorated action
    const decoratedAction = requirePermission("test:permission")(mockAction);

    // Call the decorated action
    const result = await decoratedAction("arg1", "arg2");

    // Verify that withPermission was called with the correct arguments
    expect(mockWithPermission).toHaveBeenCalledWith("test:permission", mockAction);

    // Verify that the mock action was called with the correct arguments
    expect(mockAction).toHaveBeenCalledWith("arg1", "arg2");

    // Verify that the result is correct
    expect(result).toBe("result");
  });
});
