import { matchRoute, getPermissionForRoute } from "../routeMatcher";

describe("routeMatcher", () => {
  describe("matchRoute", () => {
    it("should match exact routes", () => {
      expect(matchRoute("/en/protected/dashboard", "/en/protected/dashboard")).toBe(true);
    });

    it("should match routes with language parameter", () => {
      expect(matchRoute("/en/protected/dashboard", "/[lang]/protected/dashboard")).toBe(true);
      expect(matchRoute("/fr/protected/dashboard", "/[lang]/protected/dashboard")).toBe(true);
    });

    it("should match routes with ID parameter", () => {
      expect(matchRoute("/en/protected/users/123", "/[lang]/protected/users/[id]")).toBe(true);
      expect(matchRoute("/fr/protected/users/abc", "/[lang]/protected/users/[id]")).toBe(true);
    });

    it("should match routes with multiple parameters", () => {
      expect(
        matchRoute(
          "/en/protected/organizations/456/users/123",
          "/[lang]/protected/organizations/[orgId]/users/[id]"
        )
      ).toBe(true);
    });

    it("should not match different routes", () => {
      expect(matchRoute("/en/protected/dashboard", "/en/protected/users")).toBe(false);
      expect(matchRoute("/en/protected/users/123", "/[lang]/protected/users")).toBe(false);
      expect(matchRoute("/en/protected/users", "/[lang]/protected/users/[id]")).toBe(false);
    });
  });

  describe("getPermissionForRoute", () => {
    const routePermissions = {
      "/[lang]/protected/dashboard": "dashboard:view",
      "/[lang]/protected/users": "users:list",
      "/[lang]/protected/users/[id]": "users:view",
      "/[lang]/protected/users/[id]/edit": "users:edit",
    };

    it("should return the correct permission for a matching route", () => {
      expect(getPermissionForRoute("/en/protected/dashboard", routePermissions)).toBe(
        "dashboard:view"
      );
      expect(getPermissionForRoute("/fr/protected/users", routePermissions)).toBe("users:list");
      expect(getPermissionForRoute("/en/protected/users/123", routePermissions)).toBe("users:view");
      expect(getPermissionForRoute("/fr/protected/users/abc/edit", routePermissions)).toBe(
        "users:edit"
      );
    });

    it("should return null for a non-matching route", () => {
      expect(getPermissionForRoute("/en/protected/unknown", routePermissions)).toBeNull();
      expect(getPermissionForRoute("/en/public/dashboard", routePermissions)).toBeNull();
    });
  });
});
