import { withPermission } from "../withPermission";
import { PermissionService } from "../../services/PermissionService";

// Mock the PermissionService
jest.mock("../../services/PermissionService", () => ({
  PermissionService: {
    validateCurrentUserPermission: jest.fn(),
  },
}));

describe("withPermission", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should call validateCurrentUserPermission with the correct permission", async () => {
    // Create a mock action
    const mockAction = jest.fn().mockResolvedValue("result");

    // Create a wrapped action
    const wrappedAction = withPermission("test:permission", mockAction);

    // Call the wrapped action
    await wrappedAction("arg1", "arg2");

    // Check that validateCurrentUserPermission was called with the correct permission
    expect(PermissionService.validateCurrentUserPermission).toHaveBeenCalledWith("test:permission");
  });

  it("should call the original action with the correct arguments", async () => {
    // Create a mock action
    const mockAction = jest.fn().mockResolvedValue("result");

    // Create a wrapped action
    const wrappedAction = withPermission("test:permission", mockAction);

    // Call the wrapped action
    await wrappedAction("arg1", "arg2");

    // Check that the original action was called with the correct arguments
    expect(mockAction).toHaveBeenCalledWith("arg1", "arg2");
  });

  it("should return the result of the original action", async () => {
    // Create a mock action
    const mockAction = jest.fn().mockResolvedValue("result");

    // Create a wrapped action
    const wrappedAction = withPermission("test:permission", mockAction);

    // Call the wrapped action
    const result = await wrappedAction();

    // Check that the result is correct
    expect(result).toBe("result");
  });

  it("should throw an error if validateCurrentUserPermission throws an error", async () => {
    // Make validateCurrentUserPermission throw an error
    (PermissionService.validateCurrentUserPermission as jest.Mock).mockRejectedValue(
      new Error("Permission denied")
    );

    // Create a mock action
    const mockAction = jest.fn().mockResolvedValue("result");

    // Create a wrapped action
    const wrappedAction = withPermission("test:permission", mockAction);

    // Call the wrapped action and expect it to throw an error
    await expect(wrappedAction()).rejects.toThrow("Permission denied");

    // Check that the original action was not called
    expect(mockAction).not.toHaveBeenCalled();
  });
});
