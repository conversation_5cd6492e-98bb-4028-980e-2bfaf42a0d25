/**
 * Check if a route matches a pattern with parameters
 * @param route The actual route (e.g., "/en/protected/users/123")
 * @param pattern The route pattern (e.g., "/[lang]/protected/users/[id]")
 * @returns True if the route matches the pattern
 */
export function matchRoute(route: string, pattern: string): boolean {
  // Convert pattern to regex
  // Replace [param] with a regex group that matches any segment
  const regexPattern = pattern.replace(/\//g, "\\/").replace(/\[([^\]]+)\]/g, "([^/]+)");

  const regex = new RegExp(`^${regexPattern}$`);
  return regex.test(route);
}

/**
 * Find the permission required for a given route
 * @param route The route to check
 * @param routePermissions The mapping of route patterns to permissions
 * @returns The required permission or null if no match
 */
export function getPermissionForRoute(
  route: string,
  routePermissions: Record<string, string>
): string | null {
  for (const [pattern, permission] of Object.entries(routePermissions)) {
    if (matchRoute(route, pattern)) {
      return permission;
    }
  }

  return null;
}
