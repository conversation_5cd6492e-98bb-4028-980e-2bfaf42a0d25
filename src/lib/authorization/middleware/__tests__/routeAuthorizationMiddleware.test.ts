import { NextRequest, NextResponse } from "next/server";
import { routeAuthorizationMiddleware } from "../routeAuthorizationMiddleware";
import { getPermissionForRoute } from "../../utils/routeMatcher";
import { PermissionService } from "../../services/PermissionService";
import { RoleService } from "../../services/RoleService";

// Mock the dependencies
jest.mock("../../utils/routeMatcher", () => ({
  getPermissionForRoute: jest.fn(),
}));

jest.mock("../../services/PermissionService", () => ({
  PermissionService: {
    hasPermission: jest.fn(),
  },
}));

jest.mock("../../services/RoleService", () => ({
  RoleService: {
    getCurrentUserRole: jest.fn(),
  },
}));

jest.mock("@/lib/logger/services/LoggerService", () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
  },
}));

jest.mock("next/server", () => ({
  NextResponse: {
    next: jest.fn().mockReturnValue({ type: "next" }),
    redirect: jest.fn().mockImplementation((url) => ({ type: "redirect", url })),
  },
}));

describe("routeAuthorizationMiddleware", () => {
  let mockRequest: NextRequest;

  beforeEach(() => {
    jest.clearAllMocks();

    // Create a mock NextRequest
    mockRequest = {
      nextUrl: {
        pathname: "/en/protected/dashboard",
        href: "http://localhost:3000/en/protected/dashboard",
      },
      url: "http://localhost:3000/en/protected/dashboard",
    } as unknown as NextRequest;
  });

  it("should skip authorization for non-protected routes", async () => {
    // Set up a non-protected route
    mockRequest = {
      nextUrl: {
        pathname: "/en/public/page",
        href: "http://localhost:3000/en/public/page",
      },
      url: "http://localhost:3000/en/public/page",
    } as unknown as NextRequest;

    const result = await routeAuthorizationMiddleware(mockRequest);

    expect(result).toEqual({ type: "next" });
    expect(NextResponse.next).toHaveBeenCalled();
    expect(getPermissionForRoute).not.toHaveBeenCalled();
  });

  it("should allow access when no permission is required", async () => {
    // Set up getPermissionForRoute to return null (no permission required)
    (getPermissionForRoute as jest.Mock).mockReturnValue(null);

    const result = await routeAuthorizationMiddleware(mockRequest);

    expect(result).toEqual({ type: "next" });
    expect(NextResponse.next).toHaveBeenCalled();
    expect(RoleService.getCurrentUserRole).not.toHaveBeenCalled();
  });

  it("should allow access when user has permission", async () => {
    // Set up getPermissionForRoute to return a permission
    (getPermissionForRoute as jest.Mock).mockReturnValue("dashboard:view");
    // Set up getCurrentUserRole to return a role
    (RoleService.getCurrentUserRole as jest.Mock).mockResolvedValue("Director");
    // Set up hasPermission to return true
    (PermissionService.hasPermission as jest.Mock).mockReturnValue(true);

    const result = await routeAuthorizationMiddleware(mockRequest);

    expect(result).toEqual({ type: "next" });
    expect(NextResponse.next).toHaveBeenCalled();
    expect(getPermissionForRoute).toHaveBeenCalled();
    expect(RoleService.getCurrentUserRole).toHaveBeenCalled();
    expect(PermissionService.hasPermission).toHaveBeenCalledWith("Director", "dashboard:view");
    expect(NextResponse.redirect).not.toHaveBeenCalled();
  });

  it("should redirect to forbidden page when user does not have permission", async () => {
    // Set up getPermissionForRoute to return a permission
    (getPermissionForRoute as jest.Mock).mockReturnValue("dashboard:view");
    // Set up getCurrentUserRole to return a role
    (RoleService.getCurrentUserRole as jest.Mock).mockResolvedValue("SocialWorker");
    // Set up hasPermission to return false
    (PermissionService.hasPermission as jest.Mock).mockReturnValue(false);

    await routeAuthorizationMiddleware(mockRequest);

    expect(NextResponse.next).not.toHaveBeenCalled();
    expect(getPermissionForRoute).toHaveBeenCalled();
    expect(RoleService.getCurrentUserRole).toHaveBeenCalled();
    expect(PermissionService.hasPermission).toHaveBeenCalledWith("SocialWorker", "dashboard:view");
    expect(NextResponse.redirect).toHaveBeenCalledWith(
      expect.objectContaining({
        pathname: "/forbidden",
      })
    );
  });

  it("should redirect to forbidden page when there is an error getting the role", async () => {
    // Set up getPermissionForRoute to return a permission
    (getPermissionForRoute as jest.Mock).mockReturnValue("dashboard:view");
    // Set up getCurrentUserRole to throw an error
    (RoleService.getCurrentUserRole as jest.Mock).mockRejectedValue(new Error("Test error"));

    await routeAuthorizationMiddleware(mockRequest);

    expect(NextResponse.next).not.toHaveBeenCalled();
    expect(getPermissionForRoute).toHaveBeenCalled();
    expect(RoleService.getCurrentUserRole).toHaveBeenCalled();
    expect(PermissionService.hasPermission).not.toHaveBeenCalled();
    expect(NextResponse.redirect).toHaveBeenCalledWith(
      expect.objectContaining({
        pathname: "/forbidden",
      })
    );
  });
});
