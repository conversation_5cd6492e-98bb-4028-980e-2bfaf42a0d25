import { NextRequest, NextResponse } from "next/server";
import { routePermissions } from "../config/routePermissions";
import { getPermissionForRoute } from "../utils/routeMatcher";
import { PermissionService } from "../services/PermissionService";
import { RoleService } from "../services/RoleService";
import { logger } from "@/lib/logger/services/LoggerService";

/**
 * Middleware to authorize routes based on permissions
 * @param request The Next.js request
 * @returns The Next.js response
 */
export async function routeAuthorizationMiddleware(request: NextRequest): Promise<NextResponse> {
  const pathname = request.nextUrl.pathname;

  // Skip authorization for non-protected routes
  if (!pathname.includes("/protected")) {
    return NextResponse.next();
  }

  // Get the required permission for this route
  const requiredPermission = getPermissionForRoute(pathname, routePermissions);

  // If no permission is required, allow access
  if (!requiredPermission) {
    return NextResponse.next();
  }

  try {
    // Get the current user's role
    const role = await RoleService.getCurrentUserRole();

    // Check if the user has the required permission
    const hasPermission = PermissionService.hasPermission(role, requiredPermission);

    if (!hasPermission) {
      logger.warn(
        `Permission denied: ${role} does not have ${requiredPermission} for route ${pathname}`
      );

      // Redirect to the forbidden page
      return NextResponse.redirect(new URL("/forbidden", request.url));
    }

    // User has permission, continue
    return NextResponse.next();
  } catch (error) {
    logger.error(`Error checking permission: ${error}`);

    // Redirect to the forbidden page
    return NextResponse.redirect(new URL("/forbidden", request.url));
  }
}
