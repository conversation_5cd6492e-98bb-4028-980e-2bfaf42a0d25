/**
 * Types for the authorization system
 */

/**
 * User roles in the system
 */
export type UserRole = "Director" | "Coordinator" | "SocialWorker" | "SystemAdmin";

/**
 * Permission string in the format 'domain:feature:action'
 * Examples:
 * - 'user:profile:read'
 * - 'organization:settings:update'
 * - 'case:notes:create'
 *
 * Wildcards can be used:
 * - '*' - All permissions
 * - 'domain:*' - All permissions in a domain
 * - 'domain:feature:*' - All actions for a feature
 */
export type Permission = string;
