# Authorization System

A simple, flexible role-based authorization system for the RQRSDA application.

## Overview

The authorization system provides:

- Role-based access control
- Permission-based conditional rendering
- Route-based authorization
- Server action protection

## How to Create Permissions for New Features

### 1. Define Permissions in ConfigurationService

Add permissions for your new feature domain in the `ConfigurationService`:

```typescript
// src/lib/authorization/services/ConfigurationService.ts

private static config: RolePermissionConfig = {
  'Director': ['*'], // Directors have full access
  
  'Coordinator': [
    // Existing permissions
    'user:*',
    'organization:*',
    
    // Add new feature domain permissions
    'newfeature:read',
    'newfeature:create',
    'newfeature:update',
  ],
  
  'SocialWorker': [
    // Existing permissions
    'user:profile:read',
    
    // Add limited permissions for new feature
    'newfeature:read',
  ],
};
```

### 2. Add Route Permissions (if needed)

If your feature has routes that need authorization, add them to the route permissions:

```typescript
// src/lib/authorization/config/routePermissions.ts

export const routePermissions: RoutePermissionMapping = {
  // Existing routes
  "/[lang]/protected/dashboard": "dashboard:view",
  
  // Add new feature routes
  "/[lang]/protected/newfeature": "newfeature:read",
  "/[lang]/protected/newfeature/create": "newfeature:create",
  "/[lang]/protected/newfeature/[id]/edit": "newfeature:update",
};
```

### 3. Use the AuthorizationRequired Component

In your server components, use the `AuthorizationRequired` component:

```tsx
import { AuthorizationRequired } from "@/lib/authorization/components/AuthorizationRequired";

export default async function NewFeaturePage() {
  return (
    <div>
      <h1>New Feature</h1>
      
      {/* Content visible to users with 'newfeature:read' permission */}
      <AuthorizationRequired permission="newfeature:read">
        <div>
          <p>This content is visible to users with read permission.</p>
        </div>
      </AuthorizationRequired>
      
      {/* Create button only visible to users with 'newfeature:create' permission */}
      <AuthorizationRequired 
        permission="newfeature:create"
        fallback={<p>You don't have permission to create new items.</p>}
      >
        <button>Create New Item</button>
      </AuthorizationRequired>
    </div>
  );
}
```

### 4. Add to Sidebar Menu (if needed)

To add a new menu item in the sidebar:

```tsx
// src/app/[lang]/protected/components/sidebar.tsx

const navItems = [
  // Existing items
  {
    title: "dashboard",
    href: "/dashboard",
    icon: LayoutDashboard,
    permission: "dashboard:view",
  },
  
  // Add new feature menu item
  {
    title: "newfeature",
    href: "/newfeature",
    icon: PuzzleIcon,
    permission: "newfeature:read",
  },
];
```

### 5. Protect Server Actions

For server actions, use the `withPermission` utility:

```typescript
// src/app/[lang]/protected/newfeature/actions.ts
"use server";

import { withPermission } from "@/lib/authorization/utils/withPermission";

async function createNewFeatureAction(formData: FormData) {
  // Implementation...
  return { success: true };
}

// Wrap with permission check
export const createNewFeature = withPermission("newfeature:create", createNewFeatureAction);
```

## Permission Naming Convention

Permissions follow a simple domain-based naming convention:

```
domain:action
```

Or for more specific permissions:

```
domain:subdomain:action
```

Examples:
- `user:read` - Permission to read user data
- `user:profile:update` - Permission to update user profiles
- `newfeature:create` - Permission to create items in the new feature

## Wildcard Permissions

You can use wildcards to grant access to all actions within a domain:

```
domain:*
```

For example, `user:*` grants access to all user-related actions.

## Default Permissions by Role

- **Director**: Has access to everything (`*`)
- **Coordinator**: Has broad access to most features but with some limitations
- **SocialWorker**: Has limited access to specific features they need for their job

## Components and Utilities

- `AuthorizationRequired`: Server component for conditional rendering based on permissions
- `withPermission`: Utility to protect server actions with permission checks
- `PermissionService`: Service for checking and validating permissions
- `RoleService`: Service for getting the current user's role
- `ConfigurationService`: Service for defining role-based permissions
- `routeAuthorizationMiddleware`: Middleware for route-based authorization

## Testing Permissions

During development, you can use the Role Switcher component to test different permission levels:

1. Go to the dashboard page
2. Use the Role Switcher to select a role (Director, Coordinator, or SocialWorker)
3. Refresh the page to see the UI with the selected role's permissions
