"use client";

import { createBrowserClient } from "@supabase/ssr";
import { Database } from "../types/database.types";

/**
 * Create a Supabase client for use in client components
 * This is specifically for client-side code that needs realtime subscriptions
 */
export const createClient = () => {
  return createBrowserClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL || "",
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || ""
  );
};
