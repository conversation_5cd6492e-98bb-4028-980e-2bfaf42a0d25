export type Json = string | number | boolean | null | { [key: string]: Json | undefined } | Json[];

export type Database = {
  graphql_public: {
    Tables: {
      [_ in never]: never;
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      graphql: {
        Args: {
          operationName?: string;
          query?: string;
          variables?: Json;
          extensions?: Json;
        };
        Returns: Json;
      };
    };
    Enums: {
      [_ in never]: never;
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
  public: {
    Tables: {
      app_settings: {
        Row: {
          created_at: string;
          description: string | null;
          key: string;
          updated_at: string;
          value: string;
        };
        Insert: {
          created_at?: string;
          description?: string | null;
          key: string;
          updated_at?: string;
          value: string;
        };
        Update: {
          created_at?: string;
          description?: string | null;
          key?: string;
          updated_at?: string;
          value?: string;
        };
        Relationships: [];
      };
      business_hours: {
        Row: {
          created_at: string | null;
          day_of_week: number;
          end_time: string;
          id: string;
          is_closed: boolean | null;
          location_id: string | null;
          organization_id: string;
          start_time: string;
          updated_at: string | null;
        };
        Insert: {
          created_at?: string | null;
          day_of_week: number;
          end_time: string;
          id?: string;
          is_closed?: boolean | null;
          location_id?: string | null;
          organization_id: string;
          start_time: string;
          updated_at?: string | null;
        };
        Update: {
          created_at?: string | null;
          day_of_week?: number;
          end_time?: string;
          id?: string;
          is_closed?: boolean | null;
          location_id?: string | null;
          organization_id?: string;
          start_time?: string;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "business_hours_location_id_fkey";
            columns: ["location_id"];
            isOneToOne: false;
            referencedRelation: "locations";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "business_hours_organization_id_fkey";
            columns: ["organization_id"];
            isOneToOne: false;
            referencedRelation: "organizations";
            referencedColumns: ["id"];
          },
        ];
      };
      contact_history: {
        Row: {
          action: string;
          changes: Json;
          contact_id: string;
          created_at: string | null;
          id: string;
          organization_id: string;
          user_id: string;
        };
        Insert: {
          action: string;
          changes: Json;
          contact_id: string;
          created_at?: string | null;
          id?: string;
          organization_id: string;
          user_id: string;
        };
        Update: {
          action?: string;
          changes?: Json;
          contact_id?: string;
          created_at?: string | null;
          id?: string;
          organization_id?: string;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "contact_history_contact_id_fkey";
            columns: ["contact_id"];
            isOneToOne: false;
            referencedRelation: "contacts";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "contact_history_organization_id_fkey";
            columns: ["organization_id"];
            isOneToOne: false;
            referencedRelation: "organizations";
            referencedColumns: ["id"];
          },
        ];
      };
      contact_relationships: {
        Row: {
          created_at: string | null;
          id: string;
          organization_id: string;
          related_contact_id: string;
          relationship: string;
          status: string;
          subject_contact_id: string;
          updated_at: string | null;
        };
        Insert: {
          created_at?: string | null;
          id?: string;
          organization_id: string;
          related_contact_id: string;
          relationship: string;
          status?: string;
          subject_contact_id: string;
          updated_at?: string | null;
        };
        Update: {
          created_at?: string | null;
          id?: string;
          organization_id?: string;
          related_contact_id?: string;
          relationship?: string;
          status?: string;
          subject_contact_id?: string;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "contact_relationships_organization_id_fkey";
            columns: ["organization_id"];
            isOneToOne: false;
            referencedRelation: "organizations";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "contact_relationships_related_contact_id_fkey";
            columns: ["related_contact_id"];
            isOneToOne: false;
            referencedRelation: "contacts";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "contact_relationships_subject_contact_id_fkey";
            columns: ["subject_contact_id"];
            isOneToOne: false;
            referencedRelation: "contacts";
            referencedColumns: ["id"];
          },
        ];
      };
      contacts: {
        Row: {
          address: string | null;
          created_at: string | null;
          email: Json | null;
          id: string;
          name: string;
          organization_id: string;
          phone: Json | null;
          status: string;
          updated_at: string | null;
        };
        Insert: {
          address?: string | null;
          created_at?: string | null;
          email?: Json | null;
          id?: string;
          name: string;
          organization_id: string;
          phone?: Json | null;
          status?: string;
          updated_at?: string | null;
        };
        Update: {
          address?: string | null;
          created_at?: string | null;
          email?: Json | null;
          id?: string;
          name?: string;
          organization_id?: string;
          phone?: Json | null;
          status?: string;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "contacts_organization_id_fkey";
            columns: ["organization_id"];
            isOneToOne: false;
            referencedRelation: "organizations";
            referencedColumns: ["id"];
          },
        ];
      };
      drafts: {
        Row: {
          created_at: string;
          current_step: string;
          data: Json;
          id: string;
          organization_id: string;
          updated_at: string;
          user_id: string;
          workflow_type: string;
        };
        Insert: {
          created_at?: string;
          current_step: string;
          data?: Json;
          id?: string;
          organization_id: string;
          updated_at?: string;
          user_id: string;
          workflow_type: string;
        };
        Update: {
          created_at?: string;
          current_step?: string;
          data?: Json;
          id?: string;
          organization_id?: string;
          updated_at?: string;
          user_id?: string;
          workflow_type?: string;
        };
        Relationships: [
          {
            foreignKeyName: "drafts_organization_id_fkey";
            columns: ["organization_id"];
            isOneToOne: false;
            referencedRelation: "organizations";
            referencedColumns: ["id"];
          },
        ];
      };
      employee_availability: {
        Row: {
          created_at: string | null;
          created_by: string | null;
          day_of_week: number;
          employee_id: string;
          end_time: string;
          id: string;
          is_recurring: boolean | null;
          organization_id: string;
          recurrence_pattern: string | null;
          start_time: string;
          updated_at: string | null;
          updated_by: string | null;
        };
        Insert: {
          created_at?: string | null;
          created_by?: string | null;
          day_of_week: number;
          employee_id: string;
          end_time: string;
          id?: string;
          is_recurring?: boolean | null;
          organization_id: string;
          recurrence_pattern?: string | null;
          start_time: string;
          updated_at?: string | null;
          updated_by?: string | null;
        };
        Update: {
          created_at?: string | null;
          created_by?: string | null;
          day_of_week?: number;
          employee_id?: string;
          end_time?: string;
          id?: string;
          is_recurring?: boolean | null;
          organization_id?: string;
          recurrence_pattern?: string | null;
          start_time?: string;
          updated_at?: string | null;
          updated_by?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "employee_availability_employee_id_fkey";
            columns: ["employee_id"];
            isOneToOne: false;
            referencedRelation: "employees";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "employee_availability_organization_id_fkey";
            columns: ["organization_id"];
            isOneToOne: false;
            referencedRelation: "organizations";
            referencedColumns: ["id"];
          },
        ];
      };
      employee_availability_exceptions: {
        Row: {
          created_at: string | null;
          created_by: string | null;
          employee_id: string;
          end_time: string;
          exception_date: string;
          id: string;
          is_available: boolean;
          organization_id: string;
          reason: string | null;
          start_time: string;
          updated_at: string | null;
          updated_by: string | null;
        };
        Insert: {
          created_at?: string | null;
          created_by?: string | null;
          employee_id: string;
          end_time: string;
          exception_date: string;
          id?: string;
          is_available?: boolean;
          organization_id: string;
          reason?: string | null;
          start_time: string;
          updated_at?: string | null;
          updated_by?: string | null;
        };
        Update: {
          created_at?: string | null;
          created_by?: string | null;
          employee_id?: string;
          end_time?: string;
          exception_date?: string;
          id?: string;
          is_available?: boolean;
          organization_id?: string;
          reason?: string | null;
          start_time?: string;
          updated_at?: string | null;
          updated_by?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "employee_availability_exceptions_employee_id_fkey";
            columns: ["employee_id"];
            isOneToOne: false;
            referencedRelation: "employees";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "employee_availability_exceptions_organization_id_fkey";
            columns: ["organization_id"];
            isOneToOne: false;
            referencedRelation: "organizations";
            referencedColumns: ["id"];
          },
        ];
      };
      employee_time_off: {
        Row: {
          approved_at: string | null;
          approved_by: string | null;
          created_at: string | null;
          created_by: string | null;
          description: string | null;
          employee_id: string;
          end_date: string;
          end_time: string | null;
          id: string;
          organization_id: string;
          start_date: string;
          start_time: string | null;
          status: string;
          type: string;
          updated_at: string | null;
          updated_by: string | null;
        };
        Insert: {
          approved_at?: string | null;
          approved_by?: string | null;
          created_at?: string | null;
          created_by?: string | null;
          description?: string | null;
          employee_id: string;
          end_date: string;
          end_time?: string | null;
          id?: string;
          organization_id: string;
          start_date: string;
          start_time?: string | null;
          status?: string;
          type: string;
          updated_at?: string | null;
          updated_by?: string | null;
        };
        Update: {
          approved_at?: string | null;
          approved_by?: string | null;
          created_at?: string | null;
          created_by?: string | null;
          description?: string | null;
          employee_id?: string;
          end_date?: string;
          end_time?: string | null;
          id?: string;
          organization_id?: string;
          start_date?: string;
          start_time?: string | null;
          status?: string;
          type?: string;
          updated_at?: string | null;
          updated_by?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "employee_time_off_employee_id_fkey";
            columns: ["employee_id"];
            isOneToOne: false;
            referencedRelation: "employees";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "employee_time_off_organization_id_fkey";
            columns: ["organization_id"];
            isOneToOne: false;
            referencedRelation: "organizations";
            referencedColumns: ["id"];
          },
        ];
      };
      employees: {
        Row: {
          address: string | null;
          certifications: Json[] | null;
          created_at: string | null;
          date_of_birth: string | null;
          department: string | null;
          education: Json[] | null;
          emails: Json[] | null;
          employee_id: string | null;
          employment_status: string;
          first_name: string;
          gender: string | null;
          hire_date: string | null;
          id: string;
          job_title: string | null;
          last_name: string;
          organization_id: string;
          phones: Json[] | null;
          profile_image: string | null;
          specializations: string[] | null;
          supervisor_id: string | null;
          termination_date: string | null;
          updated_at: string | null;
          user_account_id: string | null;
        };
        Insert: {
          address?: string | null;
          certifications?: Json[] | null;
          created_at?: string | null;
          date_of_birth?: string | null;
          department?: string | null;
          education?: Json[] | null;
          emails?: Json[] | null;
          employee_id?: string | null;
          employment_status?: string;
          first_name: string;
          gender?: string | null;
          hire_date?: string | null;
          id?: string;
          job_title?: string | null;
          last_name: string;
          organization_id: string;
          phones?: Json[] | null;
          profile_image?: string | null;
          specializations?: string[] | null;
          supervisor_id?: string | null;
          termination_date?: string | null;
          updated_at?: string | null;
          user_account_id?: string | null;
        };
        Update: {
          address?: string | null;
          certifications?: Json[] | null;
          created_at?: string | null;
          date_of_birth?: string | null;
          department?: string | null;
          education?: Json[] | null;
          emails?: Json[] | null;
          employee_id?: string | null;
          employment_status?: string;
          first_name?: string;
          gender?: string | null;
          hire_date?: string | null;
          id?: string;
          job_title?: string | null;
          last_name?: string;
          organization_id?: string;
          phones?: Json[] | null;
          profile_image?: string | null;
          specializations?: string[] | null;
          supervisor_id?: string | null;
          termination_date?: string | null;
          updated_at?: string | null;
          user_account_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "employees_organization_id_fkey";
            columns: ["organization_id"];
            isOneToOne: false;
            referencedRelation: "organizations";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "employees_supervisor_id_fkey";
            columns: ["supervisor_id"];
            isOneToOne: false;
            referencedRelation: "employees";
            referencedColumns: ["id"];
          },
        ];
      };
      executions: {
        Row: {
          created_at: string;
          data: Json;
          error: string | null;
          id: string;
          organization_id: string;
          result: Json | null;
          status: string;
          updated_at: string;
          user_id: string;
          workflow_type: string;
        };
        Insert: {
          created_at?: string;
          data?: Json;
          error?: string | null;
          id?: string;
          organization_id: string;
          result?: Json | null;
          status: string;
          updated_at?: string;
          user_id: string;
          workflow_type: string;
        };
        Update: {
          created_at?: string;
          data?: Json;
          error?: string | null;
          id?: string;
          organization_id?: string;
          result?: Json | null;
          status?: string;
          updated_at?: string;
          user_id?: string;
          workflow_type?: string;
        };
        Relationships: [
          {
            foreignKeyName: "executions_organization_id_fkey";
            columns: ["organization_id"];
            isOneToOne: false;
            referencedRelation: "organizations";
            referencedColumns: ["id"];
          },
        ];
      };
      locations: {
        Row: {
          address: string | null;
          created_at: string | null;
          emails: Json | null;
          id: string;
          name: string;
          organization_id: string;
          phones: Json | null;
          status: string;
          updated_at: string | null;
        };
        Insert: {
          address?: string | null;
          created_at?: string | null;
          emails?: Json | null;
          id?: string;
          name: string;
          organization_id: string;
          phones?: Json | null;
          status?: string;
          updated_at?: string | null;
        };
        Update: {
          address?: string | null;
          created_at?: string | null;
          emails?: Json | null;
          id?: string;
          name?: string;
          organization_id?: string;
          phones?: Json | null;
          status?: string;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "locations_organization_id_fkey";
            columns: ["organization_id"];
            isOneToOne: false;
            referencedRelation: "organizations";
            referencedColumns: ["id"];
          },
        ];
      };
      notifications: {
        Row: {
          created_at: string;
          data: Json;
          id: string;
          message: string;
          organization_id: string;
          read: boolean;
          title: string;
          type: string;
          updated_at: string;
          user_id: string;
        };
        Insert: {
          created_at?: string;
          data?: Json;
          id?: string;
          message: string;
          organization_id: string;
          read?: boolean;
          title: string;
          type: string;
          updated_at?: string;
          user_id: string;
        };
        Update: {
          created_at?: string;
          data?: Json;
          id?: string;
          message?: string;
          organization_id?: string;
          read?: boolean;
          title?: string;
          type?: string;
          updated_at?: string;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "notifications_organization_id_fkey";
            columns: ["organization_id"];
            isOneToOne: false;
            referencedRelation: "organizations";
            referencedColumns: ["id"];
          },
        ];
      };
      organizations: {
        Row: {
          address: string | null;
          created_at: string | null;
          emails: Json | null;
          id: string;
          logo_url: string | null;
          name: string;
          phones: Json | null;
          settings: Json | null;
          status: string;
          updated_at: string | null;
          website_url: string | null;
        };
        Insert: {
          address?: string | null;
          created_at?: string | null;
          emails?: Json | null;
          id?: string;
          logo_url?: string | null;
          name: string;
          phones?: Json | null;
          settings?: Json | null;
          status?: string;
          updated_at?: string | null;
          website_url?: string | null;
        };
        Update: {
          address?: string | null;
          created_at?: string | null;
          emails?: Json | null;
          id?: string;
          logo_url?: string | null;
          name?: string;
          phones?: Json | null;
          settings?: Json | null;
          status?: string;
          updated_at?: string | null;
          website_url?: string | null;
        };
        Relationships: [];
      };
      request_contacts: {
        Row: {
          contact_id: string;
          created_at: string;
          id: string;
          notes: string | null;
          relationship_type: string;
          request_id: string;
          updated_at: string;
        };
        Insert: {
          contact_id: string;
          created_at?: string;
          id?: string;
          notes?: string | null;
          relationship_type: string;
          request_id: string;
          updated_at?: string;
        };
        Update: {
          contact_id?: string;
          created_at?: string;
          id?: string;
          notes?: string | null;
          relationship_type?: string;
          request_id?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "request_contacts_contact_id_fkey";
            columns: ["contact_id"];
            isOneToOne: false;
            referencedRelation: "contacts";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "request_contacts_request_id_fkey";
            columns: ["request_id"];
            isOneToOne: false;
            referencedRelation: "requests";
            referencedColumns: ["id"];
          },
        ];
      };
      request_history: {
        Row: {
          action: string;
          changes: Json | null;
          created_at: string;
          id: string;
          new_status: string | null;
          notes: string | null;
          previous_status: string | null;
          request_id: string;
          user_id: string | null;
        };
        Insert: {
          action: string;
          changes?: Json | null;
          created_at?: string;
          id?: string;
          new_status?: string | null;
          notes?: string | null;
          previous_status?: string | null;
          request_id: string;
          user_id?: string | null;
        };
        Update: {
          action?: string;
          changes?: Json | null;
          created_at?: string;
          id?: string;
          new_status?: string | null;
          notes?: string | null;
          previous_status?: string | null;
          request_id?: string;
          user_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "request_history_request_id_fkey";
            columns: ["request_id"];
            isOneToOne: false;
            referencedRelation: "requests";
            referencedColumns: ["id"];
          },
        ];
      };
      request_metadata: {
        Row: {
          court_judgment_date: string | null;
          court_judgment_details: string | null;
          court_judgment_document_url: string | null;
          court_judgment_reference: string | null;
          created_at: string;
          family_availability: Json | null;
          id: string;
          request_id: string;
          service_requirements: Json | null;
          updated_at: string;
        };
        Insert: {
          court_judgment_date?: string | null;
          court_judgment_details?: string | null;
          court_judgment_document_url?: string | null;
          court_judgment_reference?: string | null;
          created_at?: string;
          family_availability?: Json | null;
          id?: string;
          request_id: string;
          service_requirements?: Json | null;
          updated_at?: string;
        };
        Update: {
          court_judgment_date?: string | null;
          court_judgment_details?: string | null;
          court_judgment_document_url?: string | null;
          court_judgment_reference?: string | null;
          created_at?: string;
          family_availability?: Json | null;
          id?: string;
          request_id?: string;
          service_requirements?: Json | null;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "request_metadata_request_id_fkey";
            columns: ["request_id"];
            isOneToOne: true;
            referencedRelation: "requests";
            referencedColumns: ["id"];
          },
        ];
      };
      requests: {
        Row: {
          approval_date: string | null;
          assignee_id: string | null;
          completion_date: string | null;
          created_at: string;
          description: string | null;
          id: string;
          organization_id: string;
          priority: string | null;
          rejection_date: string | null;
          rejection_reason: string | null;
          requester_id: string | null;
          service_type: string | null;
          status: string;
          status_updated_at: string | null;
          status_updated_by: string | null;
          title: string;
          updated_at: string;
          waitlist_position: number | null;
        };
        Insert: {
          approval_date?: string | null;
          assignee_id?: string | null;
          completion_date?: string | null;
          created_at?: string;
          description?: string | null;
          id?: string;
          organization_id: string;
          priority?: string | null;
          rejection_date?: string | null;
          rejection_reason?: string | null;
          requester_id?: string | null;
          service_type?: string | null;
          status?: string;
          status_updated_at?: string | null;
          status_updated_by?: string | null;
          title: string;
          updated_at?: string;
          waitlist_position?: number | null;
        };
        Update: {
          approval_date?: string | null;
          assignee_id?: string | null;
          completion_date?: string | null;
          created_at?: string;
          description?: string | null;
          id?: string;
          organization_id?: string;
          priority?: string | null;
          rejection_date?: string | null;
          rejection_reason?: string | null;
          requester_id?: string | null;
          service_type?: string | null;
          status?: string;
          status_updated_at?: string | null;
          status_updated_by?: string | null;
          title?: string;
          updated_at?: string;
          waitlist_position?: number | null;
        };
        Relationships: [
          {
            foreignKeyName: "requests_organization_id_fkey";
            columns: ["organization_id"];
            isOneToOne: false;
            referencedRelation: "organizations";
            referencedColumns: ["id"];
          },
        ];
      };
      rooms: {
        Row: {
          capacity: number | null;
          created_at: string | null;
          description: string | null;
          id: string;
          location_id: string;
          name: string;
          organization_id: string;
          status: string;
          updated_at: string | null;
        };
        Insert: {
          capacity?: number | null;
          created_at?: string | null;
          description?: string | null;
          id?: string;
          location_id: string;
          name: string;
          organization_id: string;
          status?: string;
          updated_at?: string | null;
        };
        Update: {
          capacity?: number | null;
          created_at?: string | null;
          description?: string | null;
          id?: string;
          location_id?: string;
          name?: string;
          organization_id?: string;
          status?: string;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "rooms_location_id_fkey";
            columns: ["location_id"];
            isOneToOne: false;
            referencedRelation: "locations";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "rooms_organization_id_fkey";
            columns: ["organization_id"];
            isOneToOne: false;
            referencedRelation: "organizations";
            referencedColumns: ["id"];
          },
        ];
      };
      services: {
        Row: {
          created_at: string | null;
          description: string | null;
          duration: number;
          id: string;
          name: string;
          organization_id: string;
          price: number;
          status: string;
          updated_at: string | null;
        };
        Insert: {
          created_at?: string | null;
          description?: string | null;
          duration?: number;
          id?: string;
          name: string;
          organization_id: string;
          price?: number;
          status?: string;
          updated_at?: string | null;
        };
        Update: {
          created_at?: string | null;
          description?: string | null;
          duration?: number;
          id?: string;
          name?: string;
          organization_id?: string;
          price?: number;
          status?: string;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "services_organization_id_fkey";
            columns: ["organization_id"];
            isOneToOne: false;
            referencedRelation: "organizations";
            referencedColumns: ["id"];
          },
        ];
      };
      user_profiles: {
        Row: {
          created_at: string | null;
          email: string;
          first_name: string;
          id: string;
          language: string;
          last_name: string;
          notification_preferences: Json;
          organization_id: string | null;
          phone: string | null;
          updated_at: string | null;
        };
        Insert: {
          created_at?: string | null;
          email: string;
          first_name: string;
          id: string;
          language?: string;
          last_name: string;
          notification_preferences?: Json;
          organization_id?: string | null;
          phone?: string | null;
          updated_at?: string | null;
        };
        Update: {
          created_at?: string | null;
          email?: string;
          first_name?: string;
          id?: string;
          language?: string;
          last_name?: string;
          notification_preferences?: Json;
          organization_id?: string | null;
          phone?: string | null;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "user_profiles_organization_id_fkey";
            columns: ["organization_id"];
            isOneToOne: false;
            referencedRelation: "organizations";
            referencedColumns: ["id"];
          },
        ];
      };
      user_roles: {
        Row: {
          created_at: string | null;
          id: string;
          organization_id: string | null;
          role: string;
          user_id: string;
        };
        Insert: {
          created_at?: string | null;
          id?: string;
          organization_id?: string | null;
          role: string;
          user_id: string;
        };
        Update: {
          created_at?: string | null;
          id?: string;
          organization_id?: string | null;
          role?: string;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "user_roles_organization_id_fkey";
            columns: ["organization_id"];
            isOneToOne: false;
            referencedRelation: "organizations";
            referencedColumns: ["id"];
          },
        ];
      };
    };
    Views: {
      pg_all_foreign_keys: {
        Row: {
          fk_columns: unknown[] | null;
          fk_constraint_name: unknown | null;
          fk_schema_name: unknown | null;
          fk_table_name: unknown | null;
          fk_table_oid: unknown | null;
          is_deferrable: boolean | null;
          is_deferred: boolean | null;
          match_type: string | null;
          on_delete: string | null;
          on_update: string | null;
          pk_columns: unknown[] | null;
          pk_constraint_name: unknown | null;
          pk_index_name: unknown | null;
          pk_schema_name: unknown | null;
          pk_table_name: unknown | null;
          pk_table_oid: unknown | null;
        };
        Relationships: [];
      };
      tap_funky: {
        Row: {
          args: string | null;
          is_definer: boolean | null;
          is_strict: boolean | null;
          is_visible: boolean | null;
          kind: unknown | null;
          langoid: unknown | null;
          name: unknown | null;
          oid: unknown | null;
          owner: unknown | null;
          returns: string | null;
          returns_set: boolean | null;
          schema: unknown | null;
          volatility: string | null;
        };
        Relationships: [];
      };
    };
    Functions: {
      _cleanup: {
        Args: Record<PropertyKey, never>;
        Returns: boolean;
      };
      _contract_on: {
        Args: { "": string };
        Returns: unknown;
      };
      _currtest: {
        Args: Record<PropertyKey, never>;
        Returns: number;
      };
      _db_privs: {
        Args: Record<PropertyKey, never>;
        Returns: unknown[];
      };
      _definer: {
        Args: { "": unknown };
        Returns: boolean;
      };
      _dexists: {
        Args: { "": unknown };
        Returns: boolean;
      };
      _expand_context: {
        Args: { "": string };
        Returns: string;
      };
      _expand_on: {
        Args: { "": string };
        Returns: string;
      };
      _expand_vol: {
        Args: { "": string };
        Returns: string;
      };
      _ext_exists: {
        Args: { "": unknown };
        Returns: boolean;
      };
      _extensions: {
        Args: Record<PropertyKey, never> | { "": unknown };
        Returns: unknown[];
      };
      _funkargs: {
        Args: { "": unknown[] };
        Returns: string;
      };
      _get: {
        Args: { "": string };
        Returns: number;
      };
      _get_db_owner: {
        Args: { "": unknown };
        Returns: unknown;
      };
      _get_dtype: {
        Args: { "": unknown };
        Returns: string;
      };
      _get_language_owner: {
        Args: { "": unknown };
        Returns: unknown;
      };
      _get_latest: {
        Args: { "": string };
        Returns: number[];
      };
      _get_note: {
        Args: { "": number } | { "": string };
        Returns: string;
      };
      _get_opclass_owner: {
        Args: { "": unknown };
        Returns: unknown;
      };
      _get_rel_owner: {
        Args: { "": unknown };
        Returns: unknown;
      };
      _get_schema_owner: {
        Args: { "": unknown };
        Returns: unknown;
      };
      _get_tablespace_owner: {
        Args: { "": unknown };
        Returns: unknown;
      };
      _get_type_owner: {
        Args: { "": unknown };
        Returns: unknown;
      };
      _got_func: {
        Args: { "": unknown };
        Returns: boolean;
      };
      _grolist: {
        Args: { "": unknown };
        Returns: unknown[];
      };
      _has_group: {
        Args: { "": unknown };
        Returns: boolean;
      };
      _has_role: {
        Args: { "": unknown };
        Returns: boolean;
      };
      _has_user: {
        Args: { "": unknown };
        Returns: boolean;
      };
      _inherited: {
        Args: { "": unknown };
        Returns: boolean;
      };
      _is_schema: {
        Args: { "": unknown };
        Returns: boolean;
      };
      _is_super: {
        Args: { "": unknown };
        Returns: boolean;
      };
      _is_trusted: {
        Args: { "": unknown };
        Returns: boolean;
      };
      _is_verbose: {
        Args: Record<PropertyKey, never>;
        Returns: boolean;
      };
      _lang: {
        Args: { "": unknown };
        Returns: unknown;
      };
      _opc_exists: {
        Args: { "": unknown };
        Returns: boolean;
      };
      _parts: {
        Args: { "": unknown };
        Returns: unknown[];
      };
      _pg_sv_type_array: {
        Args: { "": unknown[] };
        Returns: unknown[];
      };
      _prokind: {
        Args: { p_oid: unknown };
        Returns: unknown;
      };
      _query: {
        Args: { "": string };
        Returns: string;
      };
      _refine_vol: {
        Args: { "": string };
        Returns: string;
      };
      _relexists: {
        Args: { "": unknown };
        Returns: boolean;
      };
      _returns: {
        Args: { "": unknown };
        Returns: string;
      };
      _strict: {
        Args: { "": unknown };
        Returns: boolean;
      };
      _table_privs: {
        Args: Record<PropertyKey, never>;
        Returns: unknown[];
      };
      _temptypes: {
        Args: { "": string };
        Returns: string;
      };
      _todo: {
        Args: Record<PropertyKey, never>;
        Returns: string;
      };
      _vol: {
        Args: { "": unknown };
        Returns: string;
      };
      bytea_to_text: {
        Args: { data: string };
        Returns: string;
      };
      can: {
        Args: { "": unknown[] };
        Returns: string;
      };
      casts_are: {
        Args: { "": string[] };
        Returns: string;
      };
      col_is_null: {
        Args:
          | {
              schema_name: unknown;
              table_name: unknown;
              column_name: unknown;
              description?: string;
            }
          | { table_name: unknown; column_name: unknown; description?: string };
        Returns: string;
      };
      col_not_null: {
        Args:
          | {
              schema_name: unknown;
              table_name: unknown;
              column_name: unknown;
              description?: string;
            }
          | { table_name: unknown; column_name: unknown; description?: string };
        Returns: string;
      };
      collect_tap: {
        Args: Record<PropertyKey, never> | { "": string[] };
        Returns: string;
      };
      diag: {
        Args:
          | Record<PropertyKey, never>
          | Record<PropertyKey, never>
          | { msg: string }
          | { msg: unknown };
        Returns: string;
      };
      diag_test_name: {
        Args: { "": string };
        Returns: string;
      };
      disable_rls_for_tests: {
        Args: Record<PropertyKey, never>;
        Returns: undefined;
      };
      do_tap: {
        Args: Record<PropertyKey, never> | { "": string } | { "": unknown };
        Returns: string[];
      };
      domains_are: {
        Args: { "": unknown[] };
        Returns: string;
      };
      enable_rls_for_tests: {
        Args: Record<PropertyKey, never>;
        Returns: undefined;
      };
      enums_are: {
        Args: { "": unknown[] };
        Returns: string;
      };
      extensions_are: {
        Args: { "": unknown[] };
        Returns: string;
      };
      fail: {
        Args: Record<PropertyKey, never> | { "": string };
        Returns: string;
      };
      findfuncs: {
        Args: { "": string };
        Returns: string[];
      };
      finish: {
        Args: { exception_on_failure?: boolean };
        Returns: string[];
      };
      foreign_tables_are: {
        Args: { "": unknown[] };
        Returns: string;
      };
      functions_are: {
        Args: { "": unknown[] };
        Returns: string;
      };
      get_request_history_with_context: {
        Args: { p_request_id: string };
        Returns: {
          id: string;
          request_id: string;
          previous_status: string;
          new_status: string;
          changes: Json;
          action: string;
          user_id: string;
          notes: string;
          created_at: string;
          user_name: string;
          user_email: string;
          organization_id: string;
        }[];
      };
      groups_are: {
        Args: { "": unknown[] };
        Returns: string;
      };
      has_check: {
        Args: { "": unknown };
        Returns: string;
      };
      has_composite: {
        Args: { "": unknown };
        Returns: string;
      };
      has_domain: {
        Args: { "": unknown };
        Returns: string;
      };
      has_enum: {
        Args: { "": unknown };
        Returns: string;
      };
      has_extension: {
        Args: { "": unknown };
        Returns: string;
      };
      has_fk: {
        Args: { "": unknown };
        Returns: string;
      };
      has_foreign_table: {
        Args: { "": unknown };
        Returns: string;
      };
      has_function: {
        Args: { "": unknown };
        Returns: string;
      };
      has_group: {
        Args: { "": unknown };
        Returns: string;
      };
      has_inherited_tables: {
        Args: { "": unknown };
        Returns: string;
      };
      has_language: {
        Args: { "": unknown };
        Returns: string;
      };
      has_materialized_view: {
        Args: { "": unknown };
        Returns: string;
      };
      has_opclass: {
        Args: { "": unknown };
        Returns: string;
      };
      has_pk: {
        Args: { "": unknown };
        Returns: string;
      };
      has_relation: {
        Args: { "": unknown };
        Returns: string;
      };
      has_role: {
        Args: { "": unknown };
        Returns: string;
      };
      has_schema: {
        Args: { "": unknown };
        Returns: string;
      };
      has_sequence: {
        Args: { "": unknown };
        Returns: string;
      };
      has_table: {
        Args: { "": unknown };
        Returns: string;
      };
      has_tablespace: {
        Args: { "": unknown };
        Returns: string;
      };
      has_type: {
        Args: { "": unknown };
        Returns: string;
      };
      has_unique: {
        Args: { "": string };
        Returns: string;
      };
      has_user: {
        Args: { "": unknown };
        Returns: string;
      };
      has_view: {
        Args: { "": unknown };
        Returns: string;
      };
      hasnt_composite: {
        Args: { "": unknown };
        Returns: string;
      };
      hasnt_domain: {
        Args: { "": unknown };
        Returns: string;
      };
      hasnt_enum: {
        Args: { "": unknown };
        Returns: string;
      };
      hasnt_extension: {
        Args: { "": unknown };
        Returns: string;
      };
      hasnt_fk: {
        Args: { "": unknown };
        Returns: string;
      };
      hasnt_foreign_table: {
        Args: { "": unknown };
        Returns: string;
      };
      hasnt_function: {
        Args: { "": unknown };
        Returns: string;
      };
      hasnt_group: {
        Args: { "": unknown };
        Returns: string;
      };
      hasnt_inherited_tables: {
        Args: { "": unknown };
        Returns: string;
      };
      hasnt_language: {
        Args: { "": unknown };
        Returns: string;
      };
      hasnt_materialized_view: {
        Args: { "": unknown };
        Returns: string;
      };
      hasnt_opclass: {
        Args: { "": unknown };
        Returns: string;
      };
      hasnt_pk: {
        Args: { "": unknown };
        Returns: string;
      };
      hasnt_relation: {
        Args: { "": unknown };
        Returns: string;
      };
      hasnt_role: {
        Args: { "": unknown };
        Returns: string;
      };
      hasnt_schema: {
        Args: { "": unknown };
        Returns: string;
      };
      hasnt_sequence: {
        Args: { "": unknown };
        Returns: string;
      };
      hasnt_table: {
        Args: { "": unknown };
        Returns: string;
      };
      hasnt_tablespace: {
        Args: { "": unknown };
        Returns: string;
      };
      hasnt_type: {
        Args: { "": unknown };
        Returns: string;
      };
      hasnt_user: {
        Args: { "": unknown };
        Returns: string;
      };
      hasnt_view: {
        Args: { "": unknown };
        Returns: string;
      };
      http: {
        Args: { request: Database["public"]["CompositeTypes"]["http_request"] };
        Returns: unknown;
      };
      http_delete: {
        Args: { uri: string } | { uri: string; content: string; content_type: string };
        Returns: unknown;
      };
      http_get: {
        Args: { uri: string } | { uri: string; data: Json };
        Returns: unknown;
      };
      http_head: {
        Args: { uri: string };
        Returns: unknown;
      };
      http_header: {
        Args: { field: string; value: string };
        Returns: Database["public"]["CompositeTypes"]["http_header"];
      };
      http_list_curlopt: {
        Args: Record<PropertyKey, never>;
        Returns: {
          curlopt: string;
          value: string;
        }[];
      };
      http_patch: {
        Args: { uri: string; content: string; content_type: string };
        Returns: unknown;
      };
      http_post: {
        Args: { uri: string; content: string; content_type: string } | { uri: string; data: Json };
        Returns: unknown;
      };
      http_put: {
        Args: { uri: string; content: string; content_type: string };
        Returns: unknown;
      };
      http_reset_curlopt: {
        Args: Record<PropertyKey, never>;
        Returns: boolean;
      };
      http_set_curlopt: {
        Args: { curlopt: string; value: string };
        Returns: boolean;
      };
      in_todo: {
        Args: Record<PropertyKey, never>;
        Returns: boolean;
      };
      index_is_primary: {
        Args: { "": unknown };
        Returns: string;
      };
      index_is_unique: {
        Args: { "": unknown };
        Returns: string;
      };
      is_aggregate: {
        Args: { "": unknown };
        Returns: string;
      };
      is_clustered: {
        Args: { "": unknown };
        Returns: string;
      };
      is_definer: {
        Args: { "": unknown };
        Returns: string;
      };
      is_director: {
        Args: Record<PropertyKey, never>;
        Returns: boolean;
      };
      is_director_of_organization: {
        Args: { org_id: string };
        Returns: boolean;
      };
      is_empty: {
        Args: { "": string };
        Returns: string;
      };
      is_normal_function: {
        Args: { "": unknown };
        Returns: string;
      };
      is_partitioned: {
        Args: { "": unknown };
        Returns: string;
      };
      is_procedure: {
        Args: { "": unknown };
        Returns: string;
      };
      is_strict: {
        Args: { "": unknown };
        Returns: string;
      };
      is_superuser: {
        Args: { "": unknown };
        Returns: string;
      };
      is_system_admin: {
        Args: Record<PropertyKey, never>;
        Returns: boolean;
      };
      is_window: {
        Args: { "": unknown };
        Returns: string;
      };
      isnt_aggregate: {
        Args: { "": unknown };
        Returns: string;
      };
      isnt_definer: {
        Args: { "": unknown };
        Returns: string;
      };
      isnt_empty: {
        Args: { "": string };
        Returns: string;
      };
      isnt_normal_function: {
        Args: { "": unknown };
        Returns: string;
      };
      isnt_partitioned: {
        Args: { "": unknown };
        Returns: string;
      };
      isnt_procedure: {
        Args: { "": unknown };
        Returns: string;
      };
      isnt_strict: {
        Args: { "": unknown };
        Returns: string;
      };
      isnt_superuser: {
        Args: { "": unknown };
        Returns: string;
      };
      isnt_window: {
        Args: { "": unknown };
        Returns: string;
      };
      language_is_trusted: {
        Args: { "": unknown };
        Returns: string;
      };
      languages_are: {
        Args: { "": unknown[] };
        Returns: string;
      };
      lives_ok: {
        Args: { "": string };
        Returns: string;
      };
      materialized_views_are: {
        Args: { "": unknown[] };
        Returns: string;
      };
      no_plan: {
        Args: Record<PropertyKey, never>;
        Returns: boolean[];
      };
      num_failed: {
        Args: Record<PropertyKey, never>;
        Returns: number;
      };
      ok: {
        Args: { "": boolean };
        Returns: string;
      };
      opclasses_are: {
        Args: { "": unknown[] };
        Returns: string;
      };
      operators_are: {
        Args: { "": string[] };
        Returns: string;
      };
      os_name: {
        Args: Record<PropertyKey, never>;
        Returns: string;
      };
      pass: {
        Args: Record<PropertyKey, never> | { "": string };
        Returns: string;
      };
      pg_version: {
        Args: Record<PropertyKey, never>;
        Returns: string;
      };
      pg_version_num: {
        Args: Record<PropertyKey, never>;
        Returns: number;
      };
      pgtap_version: {
        Args: Record<PropertyKey, never>;
        Returns: number;
      };
      plan: {
        Args: { "": number };
        Returns: string;
      };
      roles_are: {
        Args: { "": unknown[] };
        Returns: string;
      };
      runtests: {
        Args: Record<PropertyKey, never> | { "": string } | { "": unknown };
        Returns: string[];
      };
      schemas_are: {
        Args: { "": unknown[] };
        Returns: string;
      };
      sequences_are: {
        Args: { "": unknown[] };
        Returns: string;
      };
      skip: {
        Args: { "": number } | { "": string } | { why: string; how_many: number };
        Returns: string;
      };
      tables_are: {
        Args: { "": unknown[] };
        Returns: string;
      };
      tablespaces_are: {
        Args: { "": unknown[] };
        Returns: string;
      };
      text_to_bytea: {
        Args: { data: string };
        Returns: string;
      };
      throws_ok: {
        Args: { "": string };
        Returns: string;
      };
      todo: {
        Args:
          | { how_many: number }
          | { how_many: number; why: string }
          | { why: string }
          | { why: string; how_many: number };
        Returns: boolean[];
      };
      todo_end: {
        Args: Record<PropertyKey, never>;
        Returns: boolean[];
      };
      todo_start: {
        Args: Record<PropertyKey, never> | { "": string };
        Returns: boolean[];
      };
      types_are: {
        Args: { "": unknown[] };
        Returns: string;
      };
      urlencode: {
        Args: { data: Json } | { string: string } | { string: string };
        Returns: string;
      };
      user_belongs_to_organization: {
        Args: { org_id: string };
        Returns: boolean;
      };
      user_is_director_of_organization: {
        Args: { org_id: string };
        Returns: boolean;
      };
      users_are: {
        Args: { "": unknown[] };
        Returns: string;
      };
      validate_contact_history_changes: {
        Args: { changes: Json };
        Returns: boolean;
      };
      validate_emails_jsonb: {
        Args: { emails: Json };
        Returns: boolean;
      };
      validate_org_settings_jsonb: {
        Args: { settings: Json };
        Returns: boolean;
      };
      validate_phones_jsonb: {
        Args: { phones: Json };
        Returns: boolean;
      };
      views_are: {
        Args: { "": unknown[] };
        Returns: string;
      };
    };
    Enums: {
      [_ in never]: never;
    };
    CompositeTypes: {
      _time_trial_type: {
        a_time: number | null;
      };
      http_header: {
        field: string | null;
        value: string | null;
      };
      http_request: {
        method: unknown | null;
        uri: string | null;
        headers: Database["public"]["CompositeTypes"]["http_header"][] | null;
        content_type: string | null;
        content: string | null;
      };
      http_response: {
        status: number | null;
        content_type: string | null;
        headers: Database["public"]["CompositeTypes"]["http_header"][] | null;
        content: string | null;
      };
    };
  };
};

type DefaultSchema = Database[Extract<keyof Database, "public">];

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R;
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] & DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R;
      }
      ? R
      : never
    : never;

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I;
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I;
      }
      ? I
      : never
    : never;

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U;
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U;
      }
      ? U
      : never
    : never;

export type Enums<
  DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"] | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never;

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never;

export const Constants = {
  graphql_public: {
    Enums: {},
  },
  public: {
    Enums: {},
  },
} as const;
