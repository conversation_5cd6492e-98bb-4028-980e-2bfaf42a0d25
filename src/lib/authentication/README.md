# Authentication Service

This directory contains the Authentication Service implementation for the RQRSDA application. The AuthenticationService provides a centralized way to handle user authentication, session management, and route protection.

## Overview

The Authentication Service is designed to work with Supa<PERSON> Auth and provides methods for common authentication operations such as sign in, sign out, and password reset. It also includes middleware for protecting routes that require authentication.

## Components

### AuthenticationService

The `AuthenticationService` is responsible for handling authentication operations. It supports:

- User sign in with email and password
- User sign out
- Password reset
- Session management
- User profile retrieval

### authMiddleware

The `authMiddleware` is responsible for protecting routes that require authentication. It supports:

- Redirecting unauthenticated users to the login page
- Preserving the original URL for redirection after authentication
- Role-based access control

## Usage

### Using the AuthenticationService

```typescript
import { auth } from '@/lib/authentication/services/AuthenticationService';

// Sign in a user
const { user, error } = await auth.signIn('<EMAIL>', 'password');

// Sign out a user
await auth.signOut();

// Reset a user's password
await auth.resetPassword('<EMAIL>');

// Get the current user
const currentUser = await auth.getCurrentUser();
```

### Using the authMiddleware

The `authMiddleware` is automatically integrated into the main middleware in `src/middleware.ts`. It will protect routes that require authentication.

## Directory Structure

```
src/
  lib/
    authentication/
      services/
        AuthenticationService.ts  # Service for handling authentication
        __tests__/
          AuthenticationService.test.ts  # Tests for the service
      middleware/
        authMiddleware.ts  # Middleware for protecting routes
        __tests__/
          authMiddleware.test.ts  # Tests for the middleware
      README.md  # This file
```

## Best Practices

1. **Keep authentication logic centralized**: Use the AuthenticationService for all authentication operations
2. **Use the middleware for route protection**: Don't implement custom protection logic in individual routes
3. **Handle errors gracefully**: Always check for errors when performing authentication operations
4. **Keep sensitive information secure**: Never expose sensitive authentication information
5. **Use environment variables for configuration**: Store API keys and other sensitive configuration in environment variables
