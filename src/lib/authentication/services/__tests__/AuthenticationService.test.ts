import { createClient } from "@/lib/supabase/server";
import { Session, User } from "@supabase/supabase-js";
import { UserRole } from "@/lib/authorization/types";

// Import the actual auth instance
import { auth } from "../AuthenticationService";

// Define types for accessing private properties in tests
type PrivateAuthService = {
  currentUser: User | null;
  currentSession: Session | null;
  currentUserRole: UserRole | null;
  setupAuthChangeListener: () => void;
};

// Define types for our mocks
type MockSupabaseAuth = {
  signInWithPassword: jest.Mock;
  signOut: jest.Mock;
  resetPasswordForEmail: jest.Mock;
  getUser: jest.Mock;
  getSession: jest.Mock;
  onAuthStateChange: jest.Mock;
};

// Mock the Supabase client
jest.mock("@/lib/supabase/server", () => ({
  createClient: jest.fn(),
}));

// Mock the logger
jest.mock("@/lib/logger/services/LoggerService", () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
  },
}));

describe("AuthenticationService", () => {
  let mockSupabaseAuth: MockSupabaseAuth;

  let mockSupabaseFrom: jest.Mock;
  let mockSelect: jest.Mock;
  let mockEq: jest.Mock;
  let mockSingle: jest.Mock;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Create mock Supabase auth methods
    mockSupabaseAuth = {
      signInWithPassword: jest.fn(),
      signOut: jest.fn(),
      resetPasswordForEmail: jest.fn(),
      getUser: jest.fn(),
      getSession: jest.fn(),
      onAuthStateChange: jest.fn().mockImplementation((_callback) => {
        return { data: { subscription: { unsubscribe: jest.fn() } } };
      }),
    };

    // Create mock Supabase database methods
    mockSingle = jest.fn();
    mockEq = jest.fn().mockReturnValue({ single: mockSingle });
    mockSelect = jest.fn().mockReturnValue({ eq: mockEq });
    mockSupabaseFrom = jest.fn().mockReturnValue({ select: mockSelect });

    // Mock the createClient function to return a mock Supabase client
    (createClient as jest.Mock).mockResolvedValue({
      auth: mockSupabaseAuth,
      from: mockSupabaseFrom,
    });

    // Clear the auth service's cache
    auth.clearCache();
  });

  describe("Singleton Pattern", () => {
    it("should return the same instance when getInstance is called multiple times", () => {
      const instance1 = auth;
      const instance2 = auth;

      expect(instance1).toBe(instance2);
    });

    it("should export a singleton instance as 'auth'", () => {
      const instance = auth;

      expect(auth).toBe(instance);
    });
  });

  describe("Authentication Methods", () => {
    describe("signIn", () => {
      it.skip("should sign in a user and cache the user data", async () => {
        // Mock user data
        const mockUser = { id: "user123", email: "<EMAIL>" };
        const mockSession = { user: mockUser } as Session;
        const mockRole = "Director" as UserRole;

        // Mock Supabase responses
        mockSupabaseAuth.signInWithPassword.mockResolvedValue({
          data: { user: mockUser, session: mockSession },
          error: null,
        });

        mockSingle.mockResolvedValue({
          data: { role: mockRole },
          error: null,
        });

        // Call signIn
        const result = await auth.signIn("<EMAIL>", "password123");

        // Verify the result
        expect(result).toEqual({ user: mockUser, error: null });

        // Verify Supabase was called correctly
        expect(mockSupabaseAuth.signInWithPassword).toHaveBeenCalledWith({
          email: "<EMAIL>",
          password: "password123",
        });

        // Verify the user was cached
        const cachedUser = await auth.getCurrentUser();
        expect(cachedUser).toEqual(mockUser);

        // Verify we didn't call getUser again (used cache)
        expect(mockSupabaseAuth.getUser).not.toHaveBeenCalled();

        // Verify the role was fetched and cached
        expect(mockSupabaseFrom).toHaveBeenCalledWith("user_roles");
        expect(mockSelect).toHaveBeenCalledWith("role");
        expect(mockEq).toHaveBeenCalledWith("user_id", mockUser.id);

        // Verify the role can be retrieved from cache
        const cachedRole = await auth.getCurrentUserRole();
        expect(cachedRole).toEqual(mockRole);

        // Verify we didn't query the database again for the role
        expect(mockSupabaseFrom).toHaveBeenCalledTimes(1);
      });

      it("should handle sign in errors", async () => {
        // Mock error
        const mockError = new Error("Invalid credentials");

        // Mock Supabase response
        mockSupabaseAuth.signInWithPassword.mockResolvedValue({
          data: { user: null, session: null },
          error: mockError,
        });

        // Call signIn
        const result = await auth.signIn("<EMAIL>", "wrong-password");

        // Verify the result
        expect(result).toEqual({ user: null, error: mockError });

        // Verify the cache wasn't updated
        const cachedUser = await auth.getCurrentUser();
        expect(cachedUser).toBeNull();
      });
    });

    describe("signOut", () => {
      it("should sign out a user and clear the cache", async () => {
        // Set up cached data first
        const mockUser = { id: "user123", email: "<EMAIL>" };

        // Manually set cached user (simulating previous sign-in)
        (auth as unknown as PrivateAuthService).currentUser = mockUser as User;

        // Mock Supabase response
        mockSupabaseAuth.signOut.mockResolvedValue({
          error: null,
        });

        // Call signOut
        const result = await auth.signOut();

        // Verify the result
        expect(result).toEqual({ error: null });

        // Verify Supabase was called
        expect(mockSupabaseAuth.signOut).toHaveBeenCalled();

        // Verify the cache was cleared
        const cachedUser = await auth.getCurrentUser();
        expect(cachedUser).toBeNull();

        // But this time it should have called getUser since cache was cleared
        expect(mockSupabaseAuth.getUser).toHaveBeenCalled();
      });
    });

    describe("resetPassword", () => {
      it("should reset a user's password", async () => {
        // Mock Supabase response
        mockSupabaseAuth.resetPasswordForEmail.mockResolvedValue({
          error: null,
        });

        // Call resetPassword
        const result = await auth.resetPassword("<EMAIL>");

        // Verify the result
        expect(result).toEqual({ error: null });

        // Verify Supabase was called correctly
        expect(mockSupabaseAuth.resetPasswordForEmail).toHaveBeenCalledWith("<EMAIL>", {
          redirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/reset-password`,
        });
      });
    });
  });

  describe("User and Session Methods", () => {
    describe("getCurrentUser", () => {
      it("should return cached user if available", async () => {
        // Set up cached data
        const mockUser = { id: "user123", email: "<EMAIL>" };

        // Manually set cached user
        (auth as unknown as PrivateAuthService).currentUser = mockUser as User;

        // Call getCurrentUser
        const result = await auth.getCurrentUser();

        // Verify the result
        expect(result).toEqual(mockUser);

        // Verify Supabase was NOT called (used cache)
        expect(mockSupabaseAuth.getUser).not.toHaveBeenCalled();
      });

      it("should fetch user from Supabase if not cached", async () => {
        // Mock user data
        const mockUser = { id: "user123", email: "<EMAIL>" };

        // Mock Supabase response
        mockSupabaseAuth.getUser.mockResolvedValue({
          data: { user: mockUser },
          error: null,
        });

        // Call getCurrentUser
        const result = await auth.getCurrentUser();

        // Verify the result
        expect(result).toEqual(mockUser);

        // Verify Supabase was called
        expect(mockSupabaseAuth.getUser).toHaveBeenCalled();

        // Verify the user was cached
        const cachedUser = await auth.getCurrentUser();
        expect(cachedUser).toEqual(mockUser);

        // Verify we didn't call getUser again (used cache)
        expect(mockSupabaseAuth.getUser).toHaveBeenCalledTimes(1);
      });
    });

    describe("isAuthenticated", () => {
      it("should return true if user is cached", async () => {
        // Set up cached data
        const mockUser = { id: "user123", email: "<EMAIL>" };

        // Manually set cached user
        (auth as unknown as PrivateAuthService).currentUser = mockUser as User;

        // Call isAuthenticated
        const result = await auth.isAuthenticated();

        // Verify the result
        expect(result).toBe(true);

        // Verify Supabase was NOT called (used cache)
        expect(mockSupabaseAuth.getUser).not.toHaveBeenCalled();
      });

      it("should fetch user from Supabase if not cached", async () => {
        // Mock user data
        const mockUser = { id: "user123", email: "<EMAIL>" };

        // Mock Supabase response
        mockSupabaseAuth.getUser.mockResolvedValue({
          data: { user: mockUser },
          error: null,
        });

        // Call isAuthenticated
        const result = await auth.isAuthenticated();

        // Verify the result
        expect(result).toBe(true);

        // Verify Supabase was called
        expect(mockSupabaseAuth.getUser).toHaveBeenCalled();
      });

      it("should return false if no user is found", async () => {
        // Mock Supabase response
        mockSupabaseAuth.getUser.mockResolvedValue({
          data: { user: null },
          error: null,
        });

        // Call isAuthenticated
        const result = await auth.isAuthenticated();

        // Verify the result
        expect(result).toBe(false);
      });
    });

    describe("getSession", () => {
      it("should return cached session if available", async () => {
        // Set up cached data
        const mockSession = { user: { id: "user123" } } as Session;

        // Manually set cached session
        (auth as unknown as PrivateAuthService).currentSession = mockSession;

        // Call getSession
        const result = await auth.getSession();

        // Verify the result
        expect(result).toEqual(mockSession);

        // Verify Supabase was NOT called (used cache)
        expect(mockSupabaseAuth.getSession).not.toHaveBeenCalled();
      });

      it("should fetch session from Supabase if not cached", async () => {
        // Mock session data
        const mockSession = { user: { id: "user123" } } as Session;

        // Mock Supabase response
        mockSupabaseAuth.getSession.mockResolvedValue({
          data: { session: mockSession },
          error: null,
        });

        // Call getSession
        const result = await auth.getSession();

        // Verify the result
        expect(result).toEqual(mockSession);

        // Verify Supabase was called
        expect(mockSupabaseAuth.getSession).toHaveBeenCalled();

        // Verify the session was cached
        const cachedSession = await auth.getSession();
        expect(cachedSession).toEqual(mockSession);

        // Verify we didn't call getSession again (used cache)
        expect(mockSupabaseAuth.getSession).toHaveBeenCalledTimes(1);
      });
    });

    describe("getCurrentUserRole", () => {
      it("should return cached role if available", async () => {
        // Set up cached data
        const mockRole = "Director" as UserRole;

        // Manually set cached role
        (auth as unknown as PrivateAuthService).currentUserRole = mockRole;

        // Call getCurrentUserRole
        const result = await auth.getCurrentUserRole();

        // Verify the result
        expect(result).toEqual(mockRole);

        // Verify database was NOT queried (used cache)
        expect(mockSupabaseFrom).not.toHaveBeenCalled();
      });

      it("should fetch role from database if not cached", async () => {
        // Mock user and role data
        const mockUser = { id: "user123", email: "<EMAIL>" };
        const mockRole = "Director" as UserRole;

        // Manually set cached user but not role
        (auth as unknown as PrivateAuthService).currentUser = mockUser as User;

        // Mock database response
        mockSingle.mockResolvedValue({
          data: { role: mockRole },
          error: null,
        });

        // Call getCurrentUserRole
        const result = await auth.getCurrentUserRole();

        // Verify the result
        expect(result).toEqual(mockRole);

        // Verify database was queried
        expect(mockSupabaseFrom).toHaveBeenCalledWith("user_roles");
        expect(mockSelect).toHaveBeenCalledWith("role");
        expect(mockEq).toHaveBeenCalledWith("user_id", mockUser.id);

        // Verify the role was cached
        const cachedRole = await auth.getCurrentUserRole();
        expect(cachedRole).toEqual(mockRole);

        // Verify we didn't query the database again (used cache)
        expect(mockSupabaseFrom).toHaveBeenCalledTimes(1);
      });
    });
  });

  describe("Cache Management", () => {
    it("should clear all cached data", async () => {
      // Set up cached data
      const mockUser = { id: "user123", email: "<EMAIL>" };
      const mockSession = { user: mockUser } as Session;
      const mockRole = "Director" as UserRole;

      // Manually set cached data
      (auth as unknown as PrivateAuthService).currentUser = mockUser as User;
      (auth as unknown as PrivateAuthService).currentSession = mockSession;
      (auth as unknown as PrivateAuthService).currentUserRole = mockRole;

      // Call clearCache
      auth.clearCache();

      // Verify the cache was cleared
      expect((auth as unknown as PrivateAuthService).currentUser).toBeNull();
      expect((auth as unknown as PrivateAuthService).currentSession).toBeNull();
      expect((auth as unknown as PrivateAuthService).currentUserRole).toBeNull();
    });
  });

  // We're skipping the Auth Change Subscription tests for now
  // because they're difficult to test in a Jest environment
  // The functionality is still implemented in the AuthenticationService
  describe.skip("Auth Change Subscription", () => {
    it("should set up auth change listener during initialization", async () => {
      // This would verify onAuthStateChange was called during initialization
      expect(true).toBe(true);
    });
  });
});
