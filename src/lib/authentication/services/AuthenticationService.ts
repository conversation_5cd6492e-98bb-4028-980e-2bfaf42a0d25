import { SupabaseClient, User, Session } from "@supabase/supabase-js";
import { createClient } from "@/lib/supabase/server";
import { logger } from "@/lib/logger/services/LoggerService";
import { UserRole } from "@/lib/authorization/types";
import { UserProfile } from "@/app/[lang]/protected/user/lib/types";
import { UserProfileService } from "@/app/[lang]/protected/user/lib/services/UserProfileService";

/**
 * Authentication service for handling user authentication operations
 */
export class AuthenticationService {
  // Cached user and session
  private currentUser: User | null = null;
  private currentSession: Session | null = null;
  private currentUserRole: UserRole | null = null;
  private currentUserProfile: UserProfile | null = null;

  // Singleton instance
  private static instance: AuthenticationService;

  /**
   * Private constructor - sets up auth change listener
   */
  private constructor() {
    // Initialize auth change listener when the service is created
    //this.setupAuthChangeListener();
  }

  /**
   * Get the singleton instance
   */
  public static getInstance(): AuthenticationService {
    if (!AuthenticationService.instance) {
      AuthenticationService.instance = new AuthenticationService();
    }
    return AuthenticationService.instance;
  }

  /**
   * Get a Supabase client instance
   * @returns Promise resolving to a Supabase client
   * @private
   */
  private async getSupabaseClient(): Promise<SupabaseClient> {
    return await createClient();
  }

  /**
   * Set up listener for auth changes
   * @private
   */
  private async setupAuthChangeListener(): Promise<void> {
    try {
      const supabase = await this.getSupabaseClient();

      // Subscribe to auth changes
      supabase.auth.onAuthStateChange(async (event, session) => {
        logger.info(`Auth state changed: ${event}`);

        if (event === "SIGNED_IN" || event === "TOKEN_REFRESHED") {
          // Update cached session
          this.currentSession = session;

          // Update cached user
          if (session?.user) {
            this.currentUser = session.user;

            // Update cached role
            try {
              this.currentUserRole = await this.fetchUserRole(session.user.id);
            } catch (error) {
              logger.error(`Error fetching role after auth change: ${error}`);
            }

            // Update cached profile
            try {
              this.currentUserProfile = await UserProfileService.getCurrentUserProfile();
              logger.info("Updated user profile cache after auth change");
            } catch (error) {
              logger.error(`Error fetching user profile after auth change: ${error}`);
            }
          }

          logger.info("Updated cached user and session after auth change");
        } else if (event === "SIGNED_OUT") {
          // Clear cached data
          this.clearCache();
          logger.info("Cleared cached user and session after sign out");
        }
      });

      logger.info("Set up auth change listener");
    } catch (error) {
      logger.error(`Error setting up auth change listener: ${error}`);
    }
  }

  /**
   * Sign in a user with email and password
   * @param email User's email
   * @param password User's password
   * @returns User data or error
   */
  async signIn(
    email: string,
    password: string
  ): Promise<{ user: User | null; error: Error | null }> {
    try {
      const supabase = await this.getSupabaseClient();
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        logger.error("Sign in failed", error);
        return { user: null, error };
      }

      // Update cached data
      this.currentUser = data.user;
      this.currentSession = data.session;

      // Update cached role
      if (data.user) {
        this.currentUserRole = await this.fetchUserRole(data.user.id);

        // Update cached profile
        try {
          this.currentUserProfile = await UserProfileService.getCurrentUserProfile();
          logger.info("Updated user profile cache after sign in");
        } catch (error) {
          logger.error(`Error fetching user profile after sign in: ${error}`);
        }
      }

      logger.info("User signed in successfully", { email });
      return { user: data.user, error: null };
    } catch (error) {
      logger.error("Unexpected error during sign in", error as Error);
      return { user: null, error: error as Error };
    }
  }

  /**
   * Sign out the current user
   * @returns Success or error
   */
  async signOut(): Promise<{ error: Error | null }> {
    try {
      const supabase = await this.getSupabaseClient();
      const { error } = await supabase.auth.signOut();

      if (error) {
        logger.error("Sign out failed", error);
        return { error };
      }

      // Clear cached data
      this.clearCache();

      logger.info("User signed out successfully");
      return { error: null };
    } catch (error) {
      logger.error("Unexpected error during sign out", error as Error);
      return { error: error as Error };
    }
  }

  /**
   * Reset a user's password
   * @param email User's email
   * @returns Success or error
   */
  async resetPassword(email: string): Promise<{ error: Error | null }> {
    try {
      const supabase = await this.getSupabaseClient();
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/reset-password`,
      });

      if (error) {
        logger.error("Password reset failed", error);
        return { error };
      }

      logger.info("Password reset email sent", { email });
      return { error: null };
    } catch (error) {
      logger.error("Unexpected error during password reset", error as Error);
      return { error: error as Error };
    }
  }

  /**
   * Get the current user
   * @returns Current user or null
   */
  async getCurrentUser(): Promise<User | null> {
    // Return cached user if available
    if (this.currentUser) {
      logger.info("Returning cached user");
      return this.currentUser;
    }

    try {
      const supabase = await this.getSupabaseClient();
      const { data } = await supabase.auth.getUser();

      // Update cached user
      this.currentUser = data?.user || null;

      return this.currentUser;
    } catch (error) {
      logger.error("Error getting current user", error as Error);
      return null;
    }
  }

  /**
   * Check if a user is authenticated
   * @returns True if authenticated, false otherwise
   */
  async isAuthenticated(): Promise<boolean> {
    // Return true if we have a cached user
    if (this.currentUser) {
      return true;
    }

    // Otherwise check with Supabase
    const user = await this.getCurrentUser();
    return !!user;
  }

  /**
   * Get the current session
   * @returns Current session or null
   */
  async getSession() {
    // Return cached session if available
    if (this.currentSession) {
      logger.info("Returning cached session");
      return this.currentSession;
    }

    try {
      const supabase = await this.getSupabaseClient();
      const { data, error } = await supabase.auth.getSession();

      if (error) {
        logger.error("Error getting session", error);
        return null;
      }

      // Update cached session
      this.currentSession = data.session;

      return this.currentSession;
    } catch (error) {
      logger.error("Unexpected error getting session", error as Error);
      return null;
    }
  }

  /**
   * Fetch a user's role from the database
   * @param userId User ID
   * @returns User role or null
   */
  async fetchUserRole(userId: string): Promise<UserRole | null> {
    try {
      logger.info(`Fetching role for user: ${userId}`);

      const supabase = await this.getSupabaseClient();
      const { data, error } = await supabase
        .from("user_roles")
        .select("role")
        .eq("user_id", userId)
        .single();

      if (error) {
        logger.error(`Error getting role for user ${userId}: ${error.message}`);
        return null;
      }

      const role = data.role as UserRole;
      logger.info(`Role for user ${userId}: ${role}`);

      return role;
    } catch (error) {
      logger.error(`Error getting user role: ${error}`);
      return null;
    }
  }

  /**
   * Get the current user's role
   * @returns The current user's role or null if not found
   */
  async getCurrentUserRole(): Promise<UserRole | null> {
    // Return cached role if available
    if (this.currentUserRole) {
      logger.info(`Returning cached role: ${this.currentUserRole}`);
      return this.currentUserRole;
    }

    try {
      const user = await this.getCurrentUser();

      if (!user) {
        return null;
      }

      // Fetch and cache the role
      this.currentUserRole = await this.fetchUserRole(user.id);

      return this.currentUserRole;
    } catch (error) {
      logger.error(`Error getting current user role: ${error}`);
      return null;
    }
  }

  /**
   * Clear all cached data
   */
  clearCache(): void {
    this.currentUser = null;
    this.currentSession = null;
    this.currentUserRole = null;
    this.currentUserProfile = null;
    logger.info("Cleared all authentication cache");
  }

  /**
   * Get the current user's profile
   * @returns Promise resolving to the user's profile or null
   */
  async getCurrentUserProfile(): Promise<UserProfile | null> {
    // Return cached profile if available
    if (this.currentUserProfile) {
      logger.info("Returning cached user profile");
      return this.currentUserProfile;
    }

    try {
      // Check if we have a user first
      const user = await this.getCurrentUser();
      if (!user) {
        logger.warn("No authenticated user found when getting profile");
        return null;
      }

      // Fetch the profile using the UserProfileService
      const profile = await UserProfileService.getCurrentUserProfile();

      // Cache the profile for future use
      this.currentUserProfile = profile;

      return profile;
    } catch (error) {
      logger.error(`Error getting current user profile: ${error}`);
      return null;
    }
  }

  /**
   * Get the current user's organization ID
   * @returns Promise resolving to the user's organization ID or null
   */
  async getCurrentUserOrganizationId(): Promise<string | null> {
    try {
      // Get the user profile
      const profile = await this.getCurrentUserProfile();

      // Return the organization ID if available
      if (profile && profile.organizationId) {
        return profile.organizationId;
      }

      logger.warn("No organization ID found in user profile");
      return null;
    } catch (error) {
      logger.error(`Error getting current user organization ID: ${error}`);
      return null;
    }
  }
}

// Create a singleton instance
export const auth = AuthenticationService.getInstance();
