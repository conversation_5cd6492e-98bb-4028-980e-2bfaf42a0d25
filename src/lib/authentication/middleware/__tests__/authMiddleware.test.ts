import { NextResponse } from "next/server";
import { authMiddleware } from "../authMiddleware";
import { auth } from "../../services/AuthenticationService";
import { logger } from "@/lib/logger/services/LoggerService";

// Define a type for our mock request
type MockRequest = {
  nextUrl: {
    pathname: string;
  };
  url?: string;
};

// Mock the AuthenticationService
jest.mock("../../services/AuthenticationService", () => ({
  auth: {
    getSession: jest.fn(),
  },
}));

// Mock the logger
jest.mock("@/lib/logger/services/LoggerService", () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
  },
}));

// Mock NextResponse
jest.mock("next/server", () => ({
  NextRequest: jest.fn().mockImplementation((url) => ({
    url,
    nextUrl: new URL(url),
  })),
  NextResponse: {
    redirect: jest.fn().mockImplementation((url) => ({
      url,
      status: 302,
      statusText: "Found",
      headers: new Headers(),
    })),
  },
}));

describe("authMiddleware", () => {
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
  });

  it("should allow access to non-protected routes without checking authentication", async () => {
    // Create a mock request for a public route
    const request: MockRequest = {
      nextUrl: {
        pathname: "/en/(public)/about",
      },
    };

    // Call the middleware
    const response = await authMiddleware(request as unknown as Request);

    // Verify that the middleware returns undefined (allowing the request to continue)
    expect(response).toBeUndefined();

    // Verify that the auth service was not called
    expect(auth.getSession).not.toHaveBeenCalled();
  });

  it("should allow access to root route without checking authentication", async () => {
    // Create a mock request for the root route
    const request: MockRequest = {
      nextUrl: {
        pathname: "/",
      },
    };

    // Call the middleware
    const response = await authMiddleware(request as unknown as Request);

    // Verify that the middleware returns undefined (allowing the request to continue)
    expect(response).toBeUndefined();

    // Verify that the auth service was not called
    expect(auth.getSession).not.toHaveBeenCalled();
  });

  it("should redirect unauthenticated users to the login page for protected routes", async () => {
    // Mock no session (unauthenticated)
    (auth.getSession as jest.Mock).mockResolvedValue(null);

    // Create a mock request for a protected route
    const request: MockRequest = {
      nextUrl: {
        pathname: "/en/(protected)/dashboard",
      },
      url: "https://example.com/en/(protected)/dashboard",
    };

    // Call the middleware
    await authMiddleware(request as unknown as Request);

    // Verify that the middleware redirects to the login page
    expect(NextResponse.redirect).toHaveBeenCalled();

    // Verify that the auth service was called
    expect(auth.getSession).toHaveBeenCalled();

    // Verify that the logger was called
    expect(logger.info).toHaveBeenCalledWith(
      "Unauthenticated user attempting to access protected route",
      { pathname: "/en/(protected)/dashboard" }
    );
  });

  it("should allow authenticated users to access protected routes", async () => {
    // Mock session (authenticated)
    const mockSession = { user: { id: "user123" } };
    (auth.getSession as jest.Mock).mockResolvedValue(mockSession);

    // Create a mock request for a protected route
    const request: MockRequest = {
      nextUrl: {
        pathname: "/en/(protected)/dashboard",
      },
    };

    // Call the middleware
    const response = await authMiddleware(request as unknown as Request);

    // Verify that the middleware returns undefined (allowing the request to continue)
    expect(response).toBeUndefined();

    // Verify that the auth service was called
    expect(auth.getSession).toHaveBeenCalled();

    // Verify that the logger was not called
    expect(logger.info).not.toHaveBeenCalled();
  });

  it("should handle errors during authentication check", async () => {
    // Mock error during authentication check
    const mockError = new Error("Authentication error");
    (auth.getSession as jest.Mock).mockRejectedValue(mockError);

    // Create a mock request for a protected route
    const request: MockRequest = {
      nextUrl: {
        pathname: "/en/(protected)/dashboard",
      },
      url: "https://example.com/en/(protected)/dashboard",
    };

    // Call the middleware
    await authMiddleware(request as unknown as Request);

    // Verify that the middleware redirects to the login page
    expect(NextResponse.redirect).toHaveBeenCalled();

    // Verify that the auth service was called
    expect(auth.getSession).toHaveBeenCalled();

    // Verify that the logger was called
    expect(logger.error).toHaveBeenCalledWith("Error during authentication check", mockError);
  });

  it("should extract the correct language from the URL", async () => {
    // Mock no session (unauthenticated)
    (auth.getSession as jest.Mock).mockResolvedValue(null);

    // Create a mock request for a protected route with French language
    const request: MockRequest = {
      nextUrl: {
        pathname: "/fr/(protected)/dashboard",
      },
      url: "https://example.com/fr/(protected)/dashboard",
    };

    // Call the middleware
    await authMiddleware(request as unknown as Request);

    // Verify that the middleware redirects to the login page
    expect(NextResponse.redirect).toHaveBeenCalled();
  });
});
