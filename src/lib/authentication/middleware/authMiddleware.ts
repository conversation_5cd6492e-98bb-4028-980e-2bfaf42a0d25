import { NextRequest, NextResponse } from "next/server";
import { auth } from "../services/AuthenticationService";
import { logger } from "@/lib/logger/services/LoggerService";

/**
 * Middleware to protect routes that require authentication
 * @param request Next.js request
 * @returns Next.js response or undefined to continue
 */
export async function authMiddleware(request: NextRequest) {
  // Get the pathname from the request
  const pathname = request.nextUrl.pathname;

  // Extract the language from the URL (e.g., /en/... or /fr/...)
  const lang = pathname.split("/")[1] || "fr"; // Default to French if no language is specified

  // Check if this is a protected route
  if (!isProtectedRoute(pathname)) {
    // Not a protected route, no need to check authentication
    return;
  }

  try {
    // Check if the user is authenticated
    const session = await auth.getSession();

    if (!session) {
      // User is not authenticated, redirect to login page
      logger.info("Unauthenticated user attempting to access protected route", { pathname });

      // Create the redirect URL with the original URL as a query parameter
      const redirectUrl = new URL(`/${lang}/auth/signin`, request.url);
      redirectUrl.searchParams.set("redirectTo", pathname);

      return NextResponse.redirect(redirectUrl);
    }

    // User is authenticated, continue to the protected route
    return;
  } catch (error) {
    // Error during authentication check, redirect to login page
    logger.error("Error during authentication check", error as Error);

    // Create the redirect URL with the original URL as a query parameter
    const redirectUrl = new URL(`/${lang}/auth/signin`, request.url);
    redirectUrl.searchParams.set("redirectTo", pathname);

    return NextResponse.redirect(redirectUrl);
  }
}

/**
 * Check if a route is protected (requires authentication)
 * @param pathname Route pathname
 * @returns True if the route is protected, false otherwise
 */
function isProtectedRoute(pathname: string): boolean {
  // Check if the pathname contains the (protected) route group
  return pathname.includes("protected");
}
