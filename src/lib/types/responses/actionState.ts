/**
 * Standard state type for server actions
 */

/**
 * Standard state type for server actions
 * Used with useActionState hook
 * @template T The type of data returned on success
 */
export interface ActionState<T = unknown> {
  success: boolean;
  error: string;
  data?: T | null;
}

/**
 * Create initial state for server actions
 * @returns Initial action state
 */
export function initialActionState<T>(): ActionState<T> {
  return {
    success: false,
    error: "",
    data: null,
  };
}

/**
 * Create a success state for server actions
 * @param data Optional data to include
 */
export function successActionState<T>(data: T | null = null): ActionState<T> {
  return {
    success: true,
    error: "",
    data,
  };
}

/**
 * Create an error state for server actions
 * @param error Error message
 */
export function errorActionState<T>(error: string): ActionState<T> {
  return {
    success: false,
    error,
    data: null,
  };
}
