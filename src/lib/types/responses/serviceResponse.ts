/**
 * Standard response type for service methods
 */

/**
 * Standard response type for all service methods
 * @template T The type of data returned on success
 * @template E The type of error returned on failure (defaults to Error)
 */
export interface ServiceResponse<T = unknown, E = Error> {
  success: boolean;
  data: T | null;
  error: E | null;
  message: string;
}

/**
 * Create a successful service response
 * @param data The data to return
 * @param message A success message
 */
export function successResponse<T>(
  data: T | null = null,
  message: string = "Operation completed successfully"
): ServiceResponse<T> {
  return {
    success: true,
    data,
    error: null,
    message,
  };
}

/**
 * Create an error service response
 * @param error The error that occurred (can be any type, will be converted to Error if needed)
 * @param message An error message
 */
export function errorResponse<T>(
  error: unknown = null,
  message: string = "Operation failed"
): ServiceResponse<T, Error> {
  // Convert the error to an Error object if it's not already one
  const errorObject = error === null ? null : toError(error);

  return {
    success: false,
    data: null,
    error: errorObject,
    message,
  };
}

/**
 * Helper function to convert unknown error to Error object
 * @param error The unknown error to convert
 * @returns An Error object
 */
export function toError(error: unknown): Error {
  if (error instanceof Error) {
    return error;
  }
  return new Error(typeof error === "string" ? error : JSON.stringify(error));
}
