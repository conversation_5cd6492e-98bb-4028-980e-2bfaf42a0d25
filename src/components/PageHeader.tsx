"use client";

import React, { ReactNode } from "react";

interface PageHeaderProps {
  /**
   * The title of the page
   */
  title: string;

  /**
   * Optional description text
   */
  description?: string;

  /**
   * Optional actions to display (e.g., buttons)
   */
  actions?: ReactNode;

  /**
   * Additional CSS classes
   */
  className?: string;
}

/**
 * Reusable page header component with title, optional description, and actions
 *
 * @example
 * ```tsx
 * <PageHeader
 *   title="Users"
 *   description="Manage system users"
 *   actions={<Button>Create User</Button>}
 * />
 * ```
 */
export function PageHeader({ title, description, actions, className = "" }: PageHeaderProps) {
  return (
    <div className={`mb-6 flex justify-between items-start ${className}`}>
      <div>
        <h1 className="text-3xl font-bold">{title}</h1>
        {description && <p className="text-muted-foreground mt-1">{description}</p>}
      </div>
      {actions && <div className="flex gap-2">{actions}</div>}
    </div>
  );
}
