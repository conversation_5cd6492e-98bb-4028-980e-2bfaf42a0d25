import React, { ReactNode } from "react";
import { cn } from "@/lib/utils";

interface TypographyProps {
  children: ReactNode;
  className?: string;
}

/**
 * H1 - Page Title
 * Used for main page titles
 */
export function H1({ children, className }: TypographyProps) {
  return <h1 className={cn("text-3xl font-bold", className)}>{children}</h1>;
}

/**
 * H2 - Section Title
 * Used for major section headings
 */
export function H2({ children, className }: TypographyProps) {
  return <h2 className={cn("text-2xl font-semibold", className)}>{children}</h2>;
}

/**
 * H3 - Subsection Title
 * Used for subsection headings
 */
export function H3({ children, className }: TypographyProps) {
  return <h3 className={cn("text-xl font-semibold", className)}>{children}</h3>;
}

/**
 * H4 - Card Title
 * Used for card titles and minor sections
 */
export function H4({ children, className }: TypographyProps) {
  return <h4 className={cn("text-lg font-medium", className)}>{children}</h4>;
}

/**
 * H5 - Small Title
 * Used for small titles and labels
 */
export function H5({ children, className }: TypographyProps) {
  return <h5 className={cn("text-base font-medium", className)}>{children}</h5>;
}

/**
 * Paragraph - Body Text
 * Used for main body text
 */
export function P({ children, className }: TypographyProps) {
  return <p className={cn("text-base", className)}>{children}</p>;
}

/**
 * Small - Secondary Text
 * Used for secondary text, captions, and helper text
 */
export function Small({ children, className }: TypographyProps) {
  return <p className={cn("text-sm", className)}>{children}</p>;
}

/**
 * Tiny - Very Small Text
 * Used for very small text, footnotes, and legal text
 */
export function Tiny({ children, className }: TypographyProps) {
  return <p className={cn("text-xs", className)}>{children}</p>;
}

/**
 * Muted - Secondary Text with muted color
 * Used for secondary text with muted color
 */
export function Muted({ children, className }: TypographyProps) {
  return <p className={cn("text-sm text-muted-foreground", className)}>{children}</p>;
}

/**
 * Lead - Large Body Text
 * Used for introductory paragraphs or emphasized text
 */
export function Lead({ children, className }: TypographyProps) {
  return <p className={cn("text-lg text-muted-foreground", className)}>{children}</p>;
}

/**
 * PageTitle - Complete page title with optional description
 * Combines H1 with an optional description
 */
interface PageTitleProps extends TypographyProps {
  description?: ReactNode;
  descriptionClassName?: string;
}

export function PageTitle({
  children,
  className,
  description,
  descriptionClassName,
}: PageTitleProps) {
  return (
    <div className="mb-6">
      <H1 className={className}>{children}</H1>
      {description && <Lead className={cn("mt-2", descriptionClassName)}>{description}</Lead>}
    </div>
  );
}

/**
 * SectionTitle - Section title with optional description
 * Combines H2 with an optional description
 */
export function SectionTitle({
  children,
  className,
  description,
  descriptionClassName,
}: PageTitleProps) {
  return (
    <div className="mb-4">
      <H2 className={className}>{children}</H2>
      {description && <Muted className={cn("mt-1", descriptionClassName)}>{description}</Muted>}
    </div>
  );
}

/**
 * SubsectionTitle - Subsection title with optional description
 * Combines H3 with an optional description
 */
export function SubsectionTitle({
  children,
  className,
  description,
  descriptionClassName,
}: PageTitleProps) {
  return (
    <div className="mb-3">
      <H3 className={className}>{children}</H3>
      {description && <Muted className={cn("mt-1", descriptionClassName)}>{description}</Muted>}
    </div>
  );
}

/**
 * CardTitle - Card title with optional description
 * Combines H4 with an optional description
 */
export function CardTitle({
  children,
  className,
  description,
  descriptionClassName,
}: PageTitleProps) {
  return (
    <div className="mb-2">
      <H4 className={className}>{children}</H4>
      {description && <Muted className={cn("mt-1", descriptionClassName)}>{description}</Muted>}
    </div>
  );
}
