"use client";

import React from "react";
import { ErrorDisplay } from "@/components/ui/error-display";

interface ErrorTemplateProps {
  /**
   * The error object
   */
  error: Error & { digest?: string };

  /**
   * Function to reset the error boundary
   */
  reset: () => void;

  /**
   * Custom title for the error display
   */
  title?: string;

  /**
   * Custom message for the error display
   */
  message?: string;
}

/**
 * Standard error template for Next.js error.tsx files
 *
 * @example
 * ```tsx
 * // In your error.tsx file:
 * import { ErrorTemplate } from "@/components/templates/error";
 *
 * export default function Error({ error, reset }: { error: Error & { digest?: string }, reset: () => void }) {
 *   return (
 *     <ErrorTemplate
 *       error={error}
 *       reset={reset}
 *       title="Error Loading Users"
 *       message="There was a problem loading the user list."
 *     />
 *   );
 * }
 * ```
 */
export function ErrorTemplate({
  error,
  reset,
  title = "An error occurred",
  message = "There was a problem loading this page.",
}: ErrorTemplateProps) {
  return <ErrorDisplay title={title} message={message} error={error} reset={reset} />;
}
