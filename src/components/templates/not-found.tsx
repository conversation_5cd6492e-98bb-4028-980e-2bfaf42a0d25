import React from "react";
import { NotFoundDisplay } from "@/components/ui/not-found-display";

interface NotFoundTemplateProps {
  /**
   * The title to display
   */
  title?: string;

  /**
   * The description to display
   */
  description?: string;

  /**
   * The text for the back button
   */
  backText?: string;
}

/**
 * Standard not found template for Next.js not-found.tsx files
 *
 * @example
 * ```tsx
 * // In your not-found.tsx file:
 * import { NotFoundTemplate } from "@/components/templates/not-found";
 *
 * export default function NotFound() {
 *   return (
 *     <NotFoundTemplate
 *       title="User Not Found"
 *       description="The requested user could not be found."
 *       backText="Back to Users"
 *     />
 *   );
 * }
 * ```
 */
export function NotFoundTemplate({
  title = "Not Found",
  description = "The requested resource could not be found.",
  backText = "Back",
}: NotFoundTemplateProps) {
  return <NotFoundDisplay title={title} description={description} backText={backText} />;
}
