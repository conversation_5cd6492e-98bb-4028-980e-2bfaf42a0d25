import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

interface LoadingTemplateProps {
  /**
   * The title to display while loading
   */
  title?: string;

  /**
   * The number of skeleton items to display
   */
  itemCount?: number;

  /**
   * The type of loading template to display
   */
  type?: "table" | "form" | "detail";

  /**
   * Additional CSS classes
   */
  className?: string;
}

/**
 * Standard loading template for Next.js loading.tsx files
 *
 * @example
 * ```tsx
 * // In your loading.tsx file:
 * import { LoadingTemplate } from "@/components/templates/loading";
 *
 * export default function Loading() {
 *   return <LoadingTemplate title="Loading Users" type="table" itemCount={5} />;
 * }
 * ```
 */
export function LoadingTemplate({
  title,
  itemCount = 3,
  type = "table",
  className = "",
}: LoadingTemplateProps) {
  return (
    <Card className={className}>
      {title && (
        <CardHeader>
          <CardTitle>{title}</CardTitle>
        </CardHeader>
      )}
      <CardContent className="space-y-4">
        {type === "table" && (
          <>
            <div className="flex items-center justify-between">
              <Skeleton className="h-8 w-48" />
              <Skeleton className="h-8 w-24" />
            </div>
            <div className="rounded-md border">
              <div className="h-12 border-b px-4 flex items-center">
                <Skeleton className="h-4 w-full" />
              </div>
              {Array.from({ length: itemCount }).map((_, i) => (
                <div key={i} className="h-16 border-b px-4 flex items-center">
                  <Skeleton className="h-4 w-full" />
                </div>
              ))}
            </div>
          </>
        )}

        {type === "form" && (
          <>
            {Array.from({ length: itemCount }).map((_, i) => (
              <div key={i} className="space-y-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-10 w-full" />
              </div>
            ))}
            <div className="flex justify-between pt-4">
              <Skeleton className="h-10 w-24" />
              <Skeleton className="h-10 w-24" />
            </div>
          </>
        )}

        {type === "detail" && (
          <>
            <div className="flex items-center space-x-4 mb-6">
              <Skeleton className="h-12 w-12 rounded-full" />
              <div className="space-y-2">
                <Skeleton className="h-4 w-48" />
                <Skeleton className="h-4 w-24" />
              </div>
            </div>
            <div className="space-y-6">
              {Array.from({ length: itemCount }).map((_, i) => (
                <div key={i} className="space-y-2">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-2/3" />
                </div>
              ))}
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}
