import React, { ReactNode } from "react";

interface PageLayoutProps {
  /**
   * The title of the page
   */
  title?: string;

  /**
   * The description of the page
   */
  description?: string;

  /**
   * Actions to display in the header (e.g., buttons)
   */
  actions?: ReactNode;

  /**
   * The main content of the page
   */
  children: ReactNode;

  /**
   * Additional CSS classes to apply to the container
   */
  className?: string;
}

/**
 * Standard page layout component
 *
 * This component provides a consistent layout for pages with a title,
 * description, actions, and content area.
 *
 * @example
 * ```tsx
 * <PageLayout
 *   title="Users"
 *   description="Manage system users"
 *   actions={<Button>Create User</Button>}
 * >
 *   <UserList />
 * </PageLayout>
 * ```
 */
export function PageLayout({
  title,
  description,
  actions,
  children,
  className = "",
}: PageLayoutProps) {
  return (
    <div className={`container mx-auto py-6 ${className}`}>
      {(title || description || actions) && (
        <div className="flex justify-between items-center mb-6">
          <div>
            {title && <h1 className="text-3xl font-bold">{title}</h1>}
            {description && <p className="text-muted-foreground mt-1">{description}</p>}
          </div>
          {actions && <div className="flex gap-2">{actions}</div>}
        </div>
      )}
      <div className="mt-6">{children}</div>
    </div>
  );
}
