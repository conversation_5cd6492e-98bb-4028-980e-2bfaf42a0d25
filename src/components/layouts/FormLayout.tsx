"use client";

import Re<PERSON>, { ReactNode } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { H2, Muted } from "@/components/typography";

interface FormLayoutProps {
  /**
   * The title of the form
   */
  title?: ReactNode;

  /**
   * Optional description text
   */
  description?: ReactNode;

  /**
   * The form content (fields)
   */
  children: ReactNode;

  /**
   * Form actions (e.g., submit, cancel buttons)
   */
  actions?: ReactNode;

  /**
   * Optional error message to display
   */
  error?: string;

  /**
   * Form action handler
   */
  formAction?: (formData: FormData) => Promise<void> | void;

  /**
   * Additional CSS classes for the Card
   */
  className?: string;
}

/**
 * Standard form layout component
 *
 * @example
 * ```tsx
 * <FormLayout
 *   title="Create User"
 *   description="Add a new user to the system"
 *   formAction={createUser}
 *   error={state.error}
 *   actions={
 *     <>
 *       <Button variant="outline">Cancel</Button>
 *       <Button type="submit">Create</Button>
 *     </>
 *   }
 * >
 *   <div className="space-y-4">
 *     <div className="space-y-2">
 *       <Label htmlFor="name">Name</Label>
 *       <Input id="name" name="name" required />
 *     </div>
 *     <div className="space-y-2">
 *       <Label htmlFor="email">Email</Label>
 *       <Input id="email" name="email" type="email" required />
 *     </div>
 *   </div>
 * </FormLayout>
 * ```
 */
export function FormLayout({
  title,
  description,
  children,
  actions,
  error,
  formAction,
  className = "",
}: FormLayoutProps) {
  return (
    <Card className={`w-full ${className}`}>
      {(title || description) && (
        <CardHeader>
          {title && <H2 className="text-center">{title}</H2>}
          {description && <Muted className="text-center">{description}</Muted>}
        </CardHeader>
      )}
      <form action={formAction}>
        <CardContent className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          {children}
        </CardContent>
        {actions && (
          <CardFooter className="flex justify-between pt-6 mt-4 border-t">{actions}</CardFooter>
        )}
      </form>
    </Card>
  );
}
