"use client";

import Re<PERSON>, { ReactNode } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { H2, Muted } from "@/components/typography";

interface Tab {
  /**
   * The value of the tab (used for selection)
   */
  value: string;

  /**
   * The label of the tab
   */
  label: string;

  /**
   * The content of the tab
   */
  content: ReactNode;
}

interface DetailLayoutProps {
  /**
   * The title of the detail view
   */
  title?: ReactNode;

  /**
   * Optional description text
   */
  description?: string;

  /**
   * The tabs to display
   */
  tabs?: Tab[];

  /**
   * The content to display when not using tabs
   */
  children?: ReactNode;

  /**
   * Optional footer content (e.g., actions)
   */
  footer?: ReactNode;

  /**
   * The default selected tab
   */
  defaultTab?: string;

  /**
   * Additional CSS classes for the Card
   */
  className?: string;
}

/**
 * Standard detail layout component with optional tabs
 *
 * @example
 * ```tsx
 * <DetailLayout
 *   title={<div className="flex items-center gap-2">User Details <Badge>Active</Badge></div>}
 *   description="View and manage user information"
 *   tabs={[
 *     {
 *       value: "details",
 *       label: "Details",
 *       content: <UserDetails user={user} />
 *     },
 *     {
 *       value: "history",
 *       label: "History",
 *       content: <UserHistory userId={user.id} />
 *     }
 *   ]}
 *   footer={
 *     <div className="flex justify-between">
 *       <Button variant="outline">Back</Button>
 *       <div className="flex gap-2">
 *         <Button variant="outline">Edit</Button>
 *         <Button variant="destructive">Delete</Button>
 *       </div>
 *     </div>
 *   }
 * />
 * ```
 */
export function DetailLayout({
  title,
  description,
  tabs,
  children,
  footer,
  defaultTab,
  className = "",
}: DetailLayoutProps) {
  return (
    <Card className={`w-full ${className}`}>
      {(title || description) && (
        <CardHeader>
          {title && (typeof title === "string" ? <H2>{title}</H2> : title)}
          {description && <Muted>{description}</Muted>}
        </CardHeader>
      )}
      <CardContent>
        {tabs ? (
          <Tabs defaultValue={defaultTab || tabs[0].value}>
            <TabsList className="mb-4">
              {tabs.map((tab) => (
                <TabsTrigger key={tab.value} value={tab.value}>
                  {tab.label}
                </TabsTrigger>
              ))}
            </TabsList>
            {tabs.map((tab) => (
              <TabsContent key={tab.value} value={tab.value}>
                {tab.content}
              </TabsContent>
            ))}
          </Tabs>
        ) : (
          children
        )}
      </CardContent>
      {footer && <CardFooter>{footer}</CardFooter>}
    </Card>
  );
}
