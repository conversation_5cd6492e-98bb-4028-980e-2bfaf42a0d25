"use client";

import React, { ReactNode } from "react";
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardHeader } from "@/components/ui/card";
import { H2, Muted } from "@/components/typography";

interface ContentLayoutProps {
  /**
   * The title of the content section
   */
  title?: ReactNode;

  /**
   * Optional description text
   */
  description?: string;

  /**
   * The main content
   */
  children: ReactNode;

  /**
   * Optional footer content (e.g., actions)
   */
  footer?: ReactNode;

  /**
   * Optional actions to display in the header
   */
  actions?: ReactNode;

  /**
   * Additional CSS classes for the Card
   */
  className?: string;
}

/**
 * Standard content layout component using Card
 *
 * @example
 * ```tsx
 * <ContentLayout
 *   title="User Details"
 *   description="View and manage user information"
 *   actions={<Button>Add User</Button>}
 *   footer={<Button>Edit</Button>}
 * >
 *   <UserDetails user={user} />
 * </ContentLayout>
 * ```
 */
export function ContentLayout({
  title,
  description,
  children,
  footer,
  actions,
  className = "",
}: ContentLayoutProps) {
  return (
    <Card className={className}>
      {(title || description || actions) && (
        <CardHeader className={actions ? "flex flex-row items-start justify-between" : ""}>
          <div>
            {title && (typeof title === "string" ? <H2>{title}</H2> : title)}
            {description && <Muted>{description}</Muted>}
          </div>
          {actions && <div className="flex items-center space-x-2">{actions}</div>}
        </CardHeader>
      )}
      <CardContent>{children}</CardContent>
      {footer && <CardFooter className="pt-6 mt-4 border-t">{footer}</CardFooter>}
    </Card>
  );
}
