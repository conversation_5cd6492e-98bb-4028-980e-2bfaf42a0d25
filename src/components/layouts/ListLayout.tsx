"use client";

import React, { ReactNode } from "react";
import { Card, CardContent, CardFooter } from "@/components/ui/card";

interface ListLayoutProps {
  /**
   * The header content (e.g., title, actions)
   */
  header?: ReactNode;

  /**
   * The list content (e.g., table, items)
   */
  children: ReactNode;

  /**
   * Optional footer content (e.g., pagination)
   */
  footer?: ReactNode;

  /**
   * Additional CSS classes for the Card
   */
  className?: string;
}

/**
 * Standard list layout component
 *
 * @example
 * ```tsx
 * <ListLayout
 *   header={
 *     <div className="flex justify-between items-center mb-4">
 *       <h2 className="text-2xl font-semibold">Users</h2>
 *       <Button>Add User</Button>
 *     </div>
 *   }
 *   footer={<Pagination />}
 * >
 *   <Table>
 *     <TableHeader>...</TableHeader>
 *     <TableBody>...</TableBody>
 *   </Table>
 * </ListLayout>
 * ```
 */
export function ListLayout({ header, children, footer, className = "" }: ListLayoutProps) {
  return (
    <div className="space-y-4">
      {header}
      <Card className={className}>
        <CardContent className="p-0">{children}</CardContent>
        {footer && <CardFooter>{footer}</CardFooter>}
      </Card>
    </div>
  );
}
