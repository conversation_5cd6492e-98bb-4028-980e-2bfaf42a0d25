import React, { ReactNode } from "react";

interface SidebarLayoutProps {
  /**
   * The main content
   */
  children: ReactNode;

  /**
   * The sidebar content
   */
  sidebar: ReactNode;

  /**
   * The side the sidebar should appear on
   */
  sidebarPosition?: "left" | "right";

  /**
   * The width of the sidebar
   */
  sidebarWidth?: string;

  /**
   * Additional CSS classes for the container
   */
  className?: string;
}

/**
 * Layout component with a main content area and a sidebar
 *
 * @example
 * ```tsx
 * <SidebarLayout
 *   sidebar={<CalendarSidebar />}
 *   sidebarPosition="right"
 *   sidebarWidth="350px"
 * >
 *   <MainContent />
 * </SidebarLayout>
 * ```
 */
export function SidebarLayout({
  children,
  sidebar,
  sidebarPosition = "right",
  sidebarWidth = "320px",
  className = "",
}: SidebarLayoutProps) {
  return (
    <div className={`flex h-full ${className}`}>
      {sidebarPosition === "left" && (
        <div className="border-r bg-muted/20 overflow-auto" style={{ width: sidebarWidth }}>
          {sidebar}
        </div>
      )}

      <div className="flex-1 overflow-auto">{children}</div>

      {sidebarPosition === "right" && (
        <div className="border-l bg-muted/20 overflow-auto" style={{ width: sidebarWidth }}>
          {sidebar}
        </div>
      )}
    </div>
  );
}
