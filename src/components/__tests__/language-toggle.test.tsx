import { render } from "@testing-library/react";
import { LanguageToggle } from "../language-toggle";
import { usePathname } from "next/navigation";

// Mock the usePathname hook
jest.mock("next/navigation", () => ({
  usePathname: jest.fn(),
}));

describe("LanguageToggle Component", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders English toggle when current language is French", () => {
    // Mock the pathname
    (usePathname as jest.Mock).mockReturnValue("/fr/dashboard");

    const { getByText } = render(
      <LanguageToggle currentLang="fr" labels={{ en: "English", fr: "Français" }} />
    );

    const toggleButton = getByText("English");
    expect(toggleButton).toBeInTheDocument();

    // Check if the link points to the English version
    const link = toggleButton.closest("a");
    expect(link).toHaveAttribute("href", "/en/dashboard");
  });

  it("renders French toggle when current language is English", () => {
    // Mock the pathname
    (usePathname as jest.Mock).mockReturnValue("/en/dashboard");

    const { getByText } = render(
      <LanguageToggle currentLang="en" labels={{ en: "English", fr: "Français" }} />
    );

    const toggleButton = getByText("Français");
    expect(toggleButton).toBeInTheDocument();

    // Check if the link points to the French version
    const link = toggleButton.closest("a");
    expect(link).toHaveAttribute("href", "/fr/dashboard");
  });
});
