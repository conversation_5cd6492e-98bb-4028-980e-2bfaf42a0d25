import React from "react";
import { render } from "@testing-library/react";
import { ThemeProvider } from "../theme-provider";

// Mock the next-themes ThemeProvider
jest.mock("next-themes", () => ({
  ThemeProvider: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="next-themes-provider">{children}</div>
  ),
}));

describe("ThemeProvider Component", () => {
  it("renders children correctly", () => {
    const { getByText } = render(
      <ThemeProvider>
        <div>Test Content</div>
      </ThemeProvider>
    );

    expect(getByText("Test Content")).toBeInTheDocument();
  });

  it("passes props to the next-themes ThemeProvider", () => {
    const { getByTestId } = render(
      <ThemeProvider attribute="class" defaultTheme="dark" enableSystem>
        <div>Test Content</div>
      </ThemeProvider>
    );

    const provider = getByTestId("next-themes-provider");
    expect(provider).toBeInTheDocument();
  });
});
