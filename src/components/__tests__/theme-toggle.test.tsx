import React from "react";
import { render, screen } from "@testing-library/react";
import { ThemeToggle } from "../theme-toggle";
import { useTheme } from "next-themes";

// Mock the next-themes useTheme hook
jest.mock("next-themes", () => ({
  useTheme: jest.fn(),
}));

// Mock the dropdown menu components
jest.mock("@/components/ui/dropdown-menu", () => ({
  DropdownMenu: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  DropdownMenuTrigger: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  DropdownMenuContent: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  DropdownMenuItem: ({
    children,
    onClick,
  }: {
    children: React.ReactNode;
    onClick?: () => void;
  }) => <button onClick={onClick}>{children}</button>,
}));

describe("ThemeToggle Component", () => {
  const mockSetTheme = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (useTheme as jest.Mock).mockReturnValue({
      setTheme: mockSetTheme,
    });
  });

  it("renders the toggle button", () => {
    render(<ThemeToggle />);

    // Check if the sun and moon icons are present
    expect(screen.getByText("Toggle theme")).toBeInTheDocument();
  });
});
