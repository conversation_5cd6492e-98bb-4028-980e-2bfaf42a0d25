"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import Link from "next/link";
import { usePathname, useSearchParams } from "next/navigation";
import { i18n } from "@/lib/i18n/services/I18nService";

interface SearchBarProps {
  lang: string;
  placeholder?: string;
  buttonText?: string;
  clearButtonText?: string;
  basePath?: string;
}

/**
 * Reusable search bar component that creates a link with search params
 * Uses the i18n service for translations
 */
export function SearchBar({
  lang,
  placeholder,
  buttonText,
  clearButtonText,
  basePath,
}: SearchBarProps) {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const [searchTerm, setSearchTerm] = useState(searchParams.get("search") || "");

  // Get translations
  const dictionary = i18n.getDictionary(lang);
  const searchButtonText = buttonText || dictionary.common?.search || "Search";
  const searchPlaceholder = placeholder || dictionary.common?.searchPlaceholder || "Search...";
  const clearText = clearButtonText || dictionary.common?.clear || "Clear";

  // Check if there's an active search
  const hasActiveSearch = searchParams.has("search");

  // Create URL for search
  const createSearchUrl = () => {
    const params = new URLSearchParams();
    if (searchTerm) params.set("search", searchTerm);
    params.set("page", "1"); // Reset to page 1 when searching

    // Use provided basePath or current pathname
    const path = basePath || pathname;
    return `${path}?${params.toString()}`;
  };

  // Create URL for clearing search
  const createClearUrl = () => {
    const params = new URLSearchParams(searchParams.toString());
    params.delete("search");
    params.set("page", "1"); // Reset to page 1 when clearing search

    // Use provided basePath or current pathname
    const path = basePath || pathname;
    return `${path}?${params.toString()}`;
  };

  return (
    <div className="flex gap-2 mb-6">
      <Input
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        placeholder={searchPlaceholder}
        className="max-w-sm"
      />

      {/* Show search button when there's no active search or when the search term has changed */}
      {(!hasActiveSearch || searchTerm !== searchParams.get("search")) && (
        <Button asChild variant="default">
          <Link href={createSearchUrl()}>{searchButtonText}</Link>
        </Button>
      )}

      {/* Show clear button when there's an active search */}
      {hasActiveSearch && (
        <Button asChild variant="outline">
          <Link href={createClearUrl()}>{clearText}</Link>
        </Button>
      )}
    </div>
  );
}
