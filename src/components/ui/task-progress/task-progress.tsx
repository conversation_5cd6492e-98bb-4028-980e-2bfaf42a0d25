"use client";

import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardDes<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>le,
} from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { <PERSON>ton } from "@/components/ui/button";
import { <PERSON><PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { CheckCircle2, AlertCircle, Loader2, XCircle } from "lucide-react";
import { cva } from "class-variance-authority";

export type TaskStatus = "pending" | "running" | "completed" | "failed" | "canceled";

export interface TaskStep {
  id: string;
  name: string;
  status: TaskStatus;
  message?: string;
}

export interface TaskProgressProps {
  /**
   * The ID of the task
   */
  taskId: string;

  /**
   * The title of the task
   */
  title: string;

  /**
   * The description of the task
   */
  description?: string;

  /**
   * The current status of the task
   */
  status: TaskStatus;

  /**
   * The progress of the task (0-100)
   */
  progress: number;

  /**
   * The steps of the task
   */
  steps?: TaskStep[];

  /**
   * The error message if the task failed
   */
  errorMessage?: string;

  /**
   * Function to call when the task is canceled
   */
  onCancel?: () => void;

  /**
   * Function to call when the task is dismissed
   */
  onDismiss?: () => void;

  /**
   * Function to call when the task is retried
   */
  onRetry?: () => void;

  /**
   * Whether to show the steps
   */
  showSteps?: boolean;

  /**
   * Additional CSS classes
   */
  className?: string;
}

const statusIconVariants = cva("h-5 w-5", {
  variants: {
    status: {
      pending: "text-muted-foreground",
      running: "text-primary animate-spin",
      completed: "text-success",
      failed: "text-destructive",
      canceled: "text-muted-foreground",
    },
  },
  defaultVariants: {
    status: "pending",
  },
});

/**
 * Task progress component for displaying the progress of asynchronous tasks
 *
 * @example
 * ```tsx
 * <TaskProgress
 *   taskId="task-123"
 *   title="Generating Report"
 *   description="Please wait while we generate your report"
 *   status="running"
 *   progress={75}
 *   steps={[
 *     { id: "1", name: "Collecting data", status: "completed" },
 *     { id: "2", name: "Processing data", status: "running" },
 *     { id: "3", name: "Generating PDF", status: "pending" }
 *   ]}
 *   onCancel={handleCancel}
 * />
 * ```
 */
export function TaskProgress({
  taskId,
  title,
  description,
  status,
  progress,
  steps = [],
  errorMessage,
  onCancel,
  onDismiss,
  onRetry,
  showSteps = true,
  className = "",
}: TaskProgressProps) {
  const getStatusIcon = (status: TaskStatus) => {
    switch (status) {
      case "pending":
        return <Loader2 className={statusIconVariants({ status })} />;
      case "running":
        return <Loader2 className={statusIconVariants({ status })} />;
      case "completed":
        return <CheckCircle2 className={statusIconVariants({ status })} />;
      case "failed":
        return <AlertCircle className={statusIconVariants({ status })} />;
      case "canceled":
        return <XCircle className={statusIconVariants({ status })} />;
    }
  };

  const getStatusText = (status: TaskStatus) => {
    switch (status) {
      case "pending":
        return "Pending";
      case "running":
        return "Running";
      case "completed":
        return "Completed";
      case "failed":
        return "Failed";
      case "canceled":
        return "Canceled";
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle>{title}</CardTitle>
            {description && <CardDescription>{description}</CardDescription>}
          </div>
          <div className="flex items-center">
            {getStatusIcon(status)}
            <span className="ml-2 text-sm font-medium">{getStatusText(status)}</span>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Progress</span>
            <span>{progress}%</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>

        {errorMessage && status === "failed" && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{errorMessage}</AlertDescription>
          </Alert>
        )}

        {showSteps && steps.length > 0 && (
          <div className="space-y-2 mt-4">
            <h4 className="text-sm font-medium">Steps</h4>
            <ul className="space-y-2">
              {steps.map((step) => (
                <li key={step.id} className="flex items-start">
                  <div className="mt-0.5 mr-2">{getStatusIcon(step.status)}</div>
                  <div>
                    <p className="text-sm font-medium">{step.name}</p>
                    {step.message && (
                      <p className="text-xs text-muted-foreground">{step.message}</p>
                    )}
                  </div>
                </li>
              ))}
            </ul>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-end gap-2">
        {status === "running" && onCancel && (
          <Button variant="outline" size="sm" onClick={onCancel}>
            Cancel
          </Button>
        )}
        {status === "failed" && onRetry && (
          <Button variant="outline" size="sm" onClick={onRetry}>
            Retry
          </Button>
        )}
        {(status === "completed" || status === "failed" || status === "canceled") && onDismiss && (
          <Button variant="outline" size="sm" onClick={onDismiss}>
            Dismiss
          </Button>
        )}
      </CardFooter>
    </Card>
  );
}
