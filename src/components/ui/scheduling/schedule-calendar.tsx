"use client";

import React, { useState } from "react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { Badge } from "@/components/ui/badge";
import { ChevronLeft, ChevronRight, Calendar as CalendarIcon, Clock } from "lucide-react";
import { format, addMonths, subMonths, isSameDay } from "date-fns";

export interface ScheduleEvent {
  /**
   * The ID of the event
   */
  id: string;

  /**
   * The title of the event
   */
  title: string;

  /**
   * The start date and time of the event
   */
  start: Date;

  /**
   * The end date and time of the event
   */
  end: Date;

  /**
   * The type of the event
   */
  type?: string;

  /**
   * The color of the event
   */
  color?: string;

  /**
   * Additional data for the event
   */
  data?: any;
}

interface ScheduleCalendarProps {
  /**
   * The events to display
   */
  events: ScheduleEvent[];

  /**
   * Function to call when an event is clicked
   */
  onEventClick?: (event: ScheduleEvent) => void;

  /**
   * Function to call when a date is clicked
   */
  onDateClick?: (date: Date) => void;

  /**
   * The title of the calendar
   */
  title?: string;

  /**
   * The description of the calendar
   */
  description?: string;

  /**
   * Whether to show the header
   */
  showHeader?: boolean;

  /**
   * Whether to show the footer
   */
  showFooter?: boolean;

  /**
   * Function to call when the add button is clicked
   */
  onAddClick?: () => void;

  /**
   * Additional CSS classes
   */
  className?: string;
}

/**
 * Schedule calendar component for displaying events
 *
 * @example
 * ```tsx
 * <ScheduleCalendar
 *   events={events}
 *   onEventClick={handleEventClick}
 *   onDateClick={handleDateClick}
 *   title="Schedule"
 *   description="View and manage your schedule"
 *   onAddClick={handleAddClick}
 * />
 * ```
 */
export function ScheduleCalendar({
  events,
  onEventClick,
  onDateClick,
  title = "Schedule",
  description,
  showHeader = true,
  showFooter = true,
  onAddClick,
  className = "",
}: ScheduleCalendarProps) {
  const [date, setDate] = useState<Date>(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());

  const handlePrevMonth = () => {
    setDate(subMonths(date, 1));
  };

  const handleNextMonth = () => {
    setDate(addMonths(date, 1));
  };

  const handleDateSelect = (date: Date | undefined) => {
    setSelectedDate(date);
    if (date) {
      onDateClick?.(date);
    }
  };

  const getEventsForDate = (date: Date) => {
    return events.filter((event) => isSameDay(event.start, date));
  };

  const selectedDateEvents = selectedDate ? getEventsForDate(selectedDate) : [];

  const renderEventBadge = (event: ScheduleEvent) => {
    const colorMap: Record<string, string> = {
      meeting: "bg-blue-500",
      appointment: "bg-green-500",
      reminder: "bg-yellow-500",
      deadline: "bg-red-500",
    };

    const bgColor = event.color || (event.type && colorMap[event.type]) || "bg-primary";

    return (
      <Badge
        key={event.id}
        className={cn("text-white", bgColor)}
        onClick={(e) => {
          e.stopPropagation();
          onEventClick?.(event);
        }}
      >
        {event.title}
      </Badge>
    );
  };

  return (
    <Card className={className}>
      {showHeader && (
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle>{title}</CardTitle>
              {description && <CardDescription>{description}</CardDescription>}
            </div>
            {onAddClick && (
              <Button size="sm" onClick={onAddClick}>
                Add Event
              </Button>
            )}
          </div>
        </CardHeader>
      )}
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-7 gap-6">
          <div className="md:col-span-4">
            <div className="flex items-center justify-between mb-4">
              <Button variant="outline" size="icon" onClick={handlePrevMonth}>
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <h3 className="text-lg font-medium">{format(date, "MMMM yyyy")}</h3>
              <Button variant="outline" size="icon" onClick={handleNextMonth}>
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
            <div className="border rounded-md p-4">
              <div className="grid grid-cols-7 gap-1">
                {["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"].map((day, i) => (
                  <div key={i} className="text-center text-sm font-medium text-muted-foreground">
                    {day}
                  </div>
                ))}
                {Array.from({ length: 35 }, (_, i) => {
                  const currentDate = new Date(
                    date.getFullYear(),
                    date.getMonth(),
                    i - date.getDay() + 1
                  );
                  const isCurrentMonth = currentDate.getMonth() === date.getMonth();
                  const isSelected =
                    selectedDate &&
                    currentDate.getDate() === selectedDate.getDate() &&
                    currentDate.getMonth() === selectedDate.getMonth() &&
                    currentDate.getFullYear() === selectedDate.getFullYear();
                  const eventsForDay = getEventsForDate(currentDate);

                  return (
                    <div
                      key={i}
                      className={cn(
                        "h-10 flex items-center justify-center rounded-md relative cursor-pointer",
                        isCurrentMonth ? "text-foreground" : "text-muted-foreground opacity-50",
                        isSelected ? "bg-primary text-primary-foreground" : "hover:bg-muted"
                      )}
                      onClick={() => handleDateSelect(currentDate)}
                    >
                      <span>{currentDate.getDate()}</span>
                      {eventsForDay.length > 0 && (
                        <div className="absolute bottom-1 left-0 right-0 flex justify-center">
                          <div className="h-1 w-1 rounded-full bg-primary"></div>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
          <div className="md:col-span-3">
            {selectedDate && (
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <CalendarIcon className="h-5 w-5 text-muted-foreground" />
                  <h3 className="text-lg font-medium">
                    {format(selectedDate, "EEEE, MMMM d, yyyy")}
                  </h3>
                </div>
                {selectedDateEvents.length === 0 ? (
                  <p className="text-muted-foreground">No events scheduled</p>
                ) : (
                  <div className="space-y-2">
                    {selectedDateEvents.map((event) => (
                      <div
                        key={event.id}
                        className="p-3 rounded-md border hover:bg-muted/50 cursor-pointer"
                        onClick={() => onEventClick?.(event)}
                      >
                        <div className="flex justify-between items-start">
                          <h4 className="font-medium">{event.title}</h4>
                          {renderEventBadge(event)}
                        </div>
                        <div className="flex items-center mt-2 text-sm text-muted-foreground">
                          <Clock className="h-4 w-4 mr-1" />
                          <span>
                            {format(event.start, "h:mm a")} - {format(event.end, "h:mm a")}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </CardContent>
      {showFooter && (
        <CardFooter>
          <div className="flex items-center space-x-2 text-sm text-muted-foreground">
            <div className="flex items-center">
              <div className="h-2 w-2 rounded-full bg-primary mr-1"></div>
              <span>Events</span>
            </div>
          </div>
        </CardFooter>
      )}
    </Card>
  );
}
