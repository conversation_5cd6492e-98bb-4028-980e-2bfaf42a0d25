"use client";

import React, { useState } from "react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";

export interface TimeSlot {
  /**
   * The day of the week (0-6, where 0 is Sunday)
   */
  day: number;

  /**
   * The hour of the day (0-23)
   */
  hour: number;

  /**
   * Whether the time slot is available
   */
  available: boolean;
}

export interface AvailabilityGridProps {
  /**
   * The time slots to display
   */
  timeSlots: TimeSlot[];

  /**
   * Function to call when a time slot is toggled
   */
  onTimeSlotToggle?: (timeSlot: TimeSlot) => void;

  /**
   * The start hour of the day (0-23)
   */
  startHour?: number;

  /**
   * The end hour of the day (0-23)
   */
  endHour?: number;

  /**
   * The days of the week to display
   */
  daysOfWeek?: string[];

  /**
   * Whether to show the header
   */
  showHeader?: boolean;

  /**
   * The title of the grid
   */
  title?: string;

  /**
   * The description of the grid
   */
  description?: string;

  /**
   * Whether to show the footer with actions
   */
  showFooter?: boolean;

  /**
   * Function to call when the save button is clicked
   */
  onSave?: () => void;

  /**
   * Function to call when the reset button is clicked
   */
  onReset?: () => void;

  /**
   * Additional CSS classes
   */
  className?: string;
}

/**
 * Availability grid component for selecting available time slots
 *
 * @example
 * ```tsx
 * <AvailabilityGrid
 *   timeSlots={timeSlots}
 *   onTimeSlotToggle={handleTimeSlotToggle}
 *   startHour={8}
 *   endHour={18}
 *   title="Weekly Availability"
 *   description="Select your available time slots"
 *   onSave={handleSave}
 *   onReset={handleReset}
 * />
 * ```
 */
export function AvailabilityGrid({
  timeSlots,
  onTimeSlotToggle,
  startHour = 8,
  endHour = 18,
  daysOfWeek = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"],
  showHeader = true,
  title = "Availability",
  description,
  showFooter = true,
  onSave,
  onReset,
  className = "",
}: AvailabilityGridProps) {
  const [view, setView] = useState<"week" | "day">("week");
  const [selectedDay, setSelectedDay] = useState(0);

  const hours = Array.from({ length: endHour - startHour + 1 }, (_, i) => startHour + i);

  const formatHour = (hour: number) => {
    const period = hour >= 12 ? "PM" : "AM";
    const displayHour = hour % 12 === 0 ? 12 : hour % 12;
    return `${displayHour}:00 ${period}`;
  };

  const isAvailable = (day: number, hour: number) => {
    return timeSlots.some((slot) => slot.day === day && slot.hour === hour && slot.available);
  };

  const handleToggle = (day: number, hour: number) => {
    const existingSlot = timeSlots.find((slot) => slot.day === day && slot.hour === hour);

    if (existingSlot) {
      onTimeSlotToggle?.({
        ...existingSlot,
        available: !existingSlot.available,
      });
    } else {
      onTimeSlotToggle?.({
        day,
        hour,
        available: true,
      });
    }
  };

  const renderWeekView = () => (
    <div className="grid grid-cols-8 gap-1">
      <div className="col-span-1"></div>
      {daysOfWeek.map((day, index) => (
        <div key={index} className="text-center font-medium text-sm py-2">
          {day}
        </div>
      ))}

      {hours.map((hour) => (
        <React.Fragment key={hour}>
          <div className="text-right pr-2 py-2 text-sm text-muted-foreground">
            {formatHour(hour)}
          </div>
          {Array.from({ length: 7 }, (_, day) => (
            <div
              key={day}
              className="flex items-center justify-center"
              onClick={() => handleToggle(day, hour)}
            >
              <div
                className={cn(
                  "w-full h-8 rounded-md cursor-pointer border transition-colors",
                  isAvailable(day, hour)
                    ? "bg-primary/20 border-primary hover:bg-primary/30"
                    : "bg-muted/20 border-muted hover:bg-muted/30"
                )}
              ></div>
            </div>
          ))}
        </React.Fragment>
      ))}
    </div>
  );

  const renderDayView = () => (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <Select
          value={selectedDay.toString()}
          onValueChange={(value) => setSelectedDay(parseInt(value))}
        >
          <SelectTrigger className="w-40">
            <SelectValue placeholder="Select day" />
          </SelectTrigger>
          <SelectContent>
            {daysOfWeek.map((day, index) => (
              <SelectItem key={index} value={index.toString()}>
                {day}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        {hours.map((hour) => (
          <div key={hour} className="flex items-center space-x-2">
            <Checkbox
              id={`hour-${hour}`}
              checked={isAvailable(selectedDay, hour)}
              onCheckedChange={() => handleToggle(selectedDay, hour)}
            />
            <Label htmlFor={`hour-${hour}`} className="flex-1">
              {formatHour(hour)}
            </Label>
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <Card className={className}>
      {showHeader && (
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle>{title}</CardTitle>
              {description && <CardDescription>{description}</CardDescription>}
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant={view === "week" ? "default" : "outline"}
                size="sm"
                onClick={() => setView("week")}
              >
                Week
              </Button>
              <Button
                variant={view === "day" ? "default" : "outline"}
                size="sm"
                onClick={() => setView("day")}
              >
                Day
              </Button>
            </div>
          </div>
        </CardHeader>
      )}
      <CardContent>{view === "week" ? renderWeekView() : renderDayView()}</CardContent>
      {showFooter && (
        <CardFooter className="flex justify-between">
          <Button variant="outline" onClick={onReset}>
            Reset
          </Button>
          <Button onClick={onSave}>Save</Button>
        </CardFooter>
      )}
    </Card>
  );
}
