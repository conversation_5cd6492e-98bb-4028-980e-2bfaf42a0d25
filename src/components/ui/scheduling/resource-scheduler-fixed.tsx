"use client";

import React, { useState } from "react";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { ChevronLeft, ChevronRight, Calendar as CalendarIcon } from "lucide-react";
import { format, addDays, subDays, startOfDay, endOfDay } from "date-fns";

export interface Resource {
  /**
   * The ID of the resource
   */
  id: string;

  /**
   * The name of the resource
   */
  name: string;

  /**
   * The type of the resource
   */
  type?: string;

  /**
   * The color of the resource
   */
  color?: string;

  /**
   * Additional data for the resource
   */
  data?: any;
}

export interface ResourceEvent {
  /**
   * The ID of the event
   */
  id: string;

  /**
   * The title of the event
   */
  title: string;

  /**
   * The start date and time of the event
   */
  start: Date;

  /**
   * The end date and time of the event
   */
  end: Date;

  /**
   * The ID of the resource
   */
  resourceId: string;

  /**
   * The type of the event
   */
  type?: string;

  /**
   * The color of the event
   */
  color?: string;

  /**
   * Additional data for the event
   */
  data?: any;
}

interface ResourceSchedulerProps {
  /**
   * The resources to display
   */
  resources: Resource[];

  /**
   * The events to display
   */
  events: ResourceEvent[];

  /**
   * Function to call when an event is clicked
   */
  onEventClick?: (event: ResourceEvent) => void;

  /**
   * Function to call when a time slot is clicked
   */
  onTimeSlotClick?: (resourceId: string, time: Date) => void;

  /**
   * The title of the scheduler
   */
  title?: string;

  /**
   * The description of the scheduler
   */
  description?: string;

  /**
   * Whether to show the header
   */
  showHeader?: boolean;

  /**
   * Whether to show the footer
   */
  showFooter?: boolean;

  /**
   * Function to call when the add button is clicked
   */
  onAddClick?: () => void;

  /**
   * The start hour of the day (0-23)
   */
  startHour?: number;

  /**
   * The end hour of the day (0-23)
   */
  endHour?: number;

  /**
   * The interval in minutes
   */
  interval?: number;

  /**
   * Additional CSS classes
   */
  className?: string;
}

/**
 * Resource scheduler component for displaying and managing resource schedules
 *
 * @example
 * ```tsx
 * <ResourceScheduler
 *   resources={resources}
 *   events={events}
 *   onEventClick={handleEventClick}
 *   onTimeSlotClick={handleTimeSlotClick}
 *   title="Resource Scheduler"
 *   description="Schedule resources"
 *   onAddClick={handleAddClick}
 *   startHour={8}
 *   endHour={18}
 *   interval={30}
 * />
 * ```
 */
export function ResourceScheduler({
  resources,
  events,
  onEventClick,
  onTimeSlotClick,
  title = "Resource Scheduler",
  description,
  showHeader = true,
  showFooter = true,
  onAddClick,
  startHour = 8,
  endHour = 18,
  interval = 60,
  className = "",
}: ResourceSchedulerProps) {
  const [date, setDate] = useState<Date>(new Date());
  const [view, setView] = useState<"day" | "week">("day");

  const handlePrevDay = () => {
    setDate(subDays(date, view === "day" ? 1 : 7));
  };

  const handleNextDay = () => {
    setDate(addDays(date, view === "day" ? 1 : 7));
  };

  const handleToday = () => {
    setDate(new Date());
  };

  const formatHour = (hour: number) => {
    const period = hour >= 12 ? "PM" : "AM";
    const displayHour = hour % 12 === 0 ? 12 : hour % 12;
    return `${displayHour}:00 ${period}`;
  };

  const getTimeSlots = () => {
    const slots = [];
    const totalMinutes = (endHour - startHour) * 60;
    const totalSlots = totalMinutes / interval;

    for (let i = 0; i <= totalSlots; i++) {
      const minutes = i * interval;
      const hour = startHour + Math.floor(minutes / 60);
      const minute = minutes % 60;

      slots.push({
        hour,
        minute,
        label: `${formatHour(hour)}${minute > 0 ? `:${minute.toString().padStart(2, "0")}` : ""}`,
      });
    }

    return slots;
  };

  const timeSlots = getTimeSlots();

  const getEventsForResourceAndTime = (resourceId: string, time: Date, slotDuration: number) => {
    const slotStart = new Date(time);
    const slotEnd = new Date(time);
    slotEnd.setMinutes(slotEnd.getMinutes() + slotDuration);

    return events.filter((event) => {
      return (
        event.resourceId === resourceId &&
        (isWithinInterval(event.start, { start: slotStart, end: slotEnd }) ||
          isWithinInterval(event.end, { start: slotStart, end: slotEnd }) ||
          (event.start <= slotStart && event.end >= slotEnd))
      );
    });
  };

  // Helper function for date comparison
  function isWithinInterval(date: Date, interval: { start: Date; end: Date }) {
    const dateTime = date.getTime();
    return dateTime >= interval.start.getTime() && dateTime <= interval.end.getTime();
  }

  return (
    <Card className={className}>
      {showHeader && (
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle>{title}</CardTitle>
              {description && <CardDescription>{description}</CardDescription>}
            </div>
            <div className="flex items-center space-x-2">
              <Select value={view} onValueChange={(value) => setView(value as "day" | "week")}>
                <SelectTrigger className="w-24">
                  <SelectValue placeholder="View" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="day">Day</SelectItem>
                  <SelectItem value="week">Week</SelectItem>
                </SelectContent>
              </Select>
              {onAddClick && (
                <Button size="sm" onClick={onAddClick}>
                  Add Event
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
      )}
      <CardContent>
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="icon" onClick={handlePrevDay}>
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button variant="outline" onClick={handleToday}>
              Today
            </Button>
            <Button variant="outline" size="icon" onClick={handleNextDay}>
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
          <h3 className="text-lg font-medium flex items-center">
            <CalendarIcon className="h-5 w-5 mr-2" />
            {format(date, "EEEE, MMMM d, yyyy")}
          </h3>
        </div>

        <div className="overflow-auto">
          <div className="min-w-[800px] border rounded-md">
            {/* Simple day view for better rendering */}
            <div className="grid grid-cols-1">
              {/* Time slots grid */}
              <div className="grid grid-cols-[100px_1fr]">
                {/* Time column */}
                <div className="border-r">
                  <div className="h-12 border-b p-2 text-sm font-medium bg-muted/20">Time</div>
                  {timeSlots.map((slot, index) => (
                    <div key={index} className="h-12 border-b p-2 text-sm text-muted-foreground">
                      {slot.label}
                    </div>
                  ))}
                </div>

                {/* Resources column */}
                <div>
                  {/* Resource headers */}
                  <div className="grid grid-cols-5 h-12 border-b">
                    {resources.map((resource) => (
                      <div
                        key={resource.id}
                        className="p-2 text-sm font-medium bg-muted/20 flex items-center"
                      >
                        <Badge className={cn("mr-2", resource.color || "bg-primary")}>&nbsp;</Badge>
                        {resource.name}
                      </div>
                    ))}
                  </div>

                  {/* Time slots for resources */}
                  {timeSlots.map((slot, slotIndex) => (
                    <div key={slotIndex} className="grid grid-cols-5 border-b">
                      {resources.map((resource) => {
                        const time = new Date(date);
                        time.setHours(slot.hour, slot.minute, 0, 0);

                        const eventsInSlot = getEventsForResourceAndTime(
                          resource.id,
                          time,
                          interval
                        );
                        const hasEvents = eventsInSlot.length > 0;

                        return (
                          <div
                            key={resource.id}
                            className={cn(
                              "h-12 border-r p-1 relative",
                              hasEvents ? "bg-primary/5" : "hover:bg-muted/50 cursor-pointer"
                            )}
                            onClick={() => {
                              if (!hasEvents && onTimeSlotClick) {
                                onTimeSlotClick(resource.id, time);
                              }
                            }}
                          >
                            {eventsInSlot.map((event) => (
                              <div
                                key={event.id}
                                className={cn(
                                  "absolute inset-0 m-1 p-1 rounded text-xs overflow-hidden text-white",
                                  event.color || "bg-primary"
                                )}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  onEventClick?.(event);
                                }}
                              >
                                <div className="font-medium truncate">{event.title}</div>
                                <div className="text-[10px] opacity-80 truncate">
                                  {format(event.start, "h:mm a")}
                                </div>
                              </div>
                            ))}
                          </div>
                        );
                      })}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
      {showFooter && (
        <CardFooter className="flex justify-between">
          <div className="text-sm text-muted-foreground">
            {resources.length} resources, {events.length} events
          </div>
        </CardFooter>
      )}
    </Card>
  );
}
