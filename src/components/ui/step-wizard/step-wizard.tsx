import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>it<PERSON>,
} from "@/components/ui/card";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { CheckIcon } from "lucide-react";

interface Step {
  /**
   * The label for the step
   */
  label: string;

  /**
   * Whether the step is completed
   */
  completed?: boolean;
}

interface StepWizardProps {
  /**
   * The title of the wizard
   */
  title: string;

  /**
   * The description of the wizard
   */
  description?: string;

  /**
   * The steps in the wizard
   */
  steps: Step[];

  /**
   * The current step index (0-based)
   */
  currentStep: number;

  /**
   * The content of the current step
   */
  children: React.ReactNode;

  /**
   * The URL for the previous step
   */
  previousUrl?: string;

  /**
   * The URL for the next step
   */
  nextUrl?: string;

  /**
   * The URL for submitting the final step
   */
  submitUrl?: string;

  /**
   * Whether the wizard is in the final step
   */
  isFinalStep?: boolean;

  /**
   * Additional CSS classes
   */
  className?: string;
}

/**
 * StepWizard component for multi-step forms using Next.js routing
 * This is a server component that uses links for navigation between steps
 *
 * @example
 * ```tsx
 * // In your page.tsx
 * <StepWizard
 *   title="Create Employee"
 *   description="Complete the following steps to create a new employee"
 *   steps={[
 *     { label: "Basic Info", completed: true },
 *     { label: "Contact", completed: false },
 *     { label: "Role", completed: false }
 *   ]}
 *   currentStep={1}
 *   previousUrl="/employees/create/basic-info"
 *   nextUrl="/employees/create/role"
 * >
 *   <div className="space-y-4">
 *     {/* Step content /}
 *   </div>
 * </StepWizard>
 * ```
 */
export function StepWizard({
  title,
  description,
  steps,
  currentStep,
  children,
  previousUrl,
  nextUrl,
  submitUrl,
  isFinalStep = false,
  className = "",
}: StepWizardProps) {
  return (
    <Card className={`w-full max-w-3xl mx-auto ${className}`}>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>

      <CardContent>
        {/* Progress indicator */}
        <div className="mb-8">
          <div className="flex justify-between">
            {steps.map((step, index) => (
              <div key={index} className="flex flex-col items-center">
                <div
                  className={`w-10 h-10 rounded-full flex items-center justify-center ${
                    step.completed
                      ? "bg-primary text-primary-foreground"
                      : currentStep === index
                        ? "bg-primary/20 border-2 border-primary text-primary"
                        : "bg-muted text-muted-foreground"
                  }`}
                >
                  {step.completed ? <CheckIcon className="h-5 w-5" /> : index + 1}
                </div>
                <span className="text-xs mt-2">{step.label}</span>
              </div>
            ))}
          </div>

          <div
            className="mt-2 grid"
            style={{ gridTemplateColumns: `repeat(${steps.length - 1}, 1fr)` }}
          >
            {steps.slice(0, -1).map((_, index) => (
              <div
                key={index}
                className={`h-1 rounded-full ${steps[index].completed ? "bg-primary" : "bg-muted"}`}
              />
            ))}
          </div>
        </div>

        {/* Step content */}
        <div className="min-h-[300px]">{children}</div>
      </CardContent>

      <CardFooter className="flex justify-between">
        {/* {previousUrl ? (
          <Button variant="outline" asChild>
            <Link href={previousUrl}>Previous</Link>
          </Button>
        ) : (
          <Button variant="outline" disabled>
            Previous
          </Button>
        )} */}

        {/* {isFinalStep ? (
          <Button asChild>
            <Link href={submitUrl || "#"}>Submit</Link>
          </Button>
        ) : (
          <Button asChild>
            <Link href={nextUrl || "#"}>Next</Link>
          </Button>
        )} */}
      </CardFooter>
    </Card>
  );
}
