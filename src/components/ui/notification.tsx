"use client";

import * as React from "react";
import { toast } from "sonner";
import { CheckCircle2, AlertCircle, InfoIcon, XCircle } from "lucide-react";

export type NotificationType = "success" | "error" | "warning" | "info";

export interface NotificationProps {
  /**
   * The title of the notification
   */
  title: string;

  /**
   * The description of the notification
   */
  description?: string;

  /**
   * The type of the notification
   */
  type?: NotificationType;

  /**
   * The duration of the notification in milliseconds
   */
  duration?: number;

  /**
   * The action to perform when the notification is clicked
   */
  action?: {
    label: string;
    onClick: () => void;
  };
}

/**
 * Show a notification toast
 */
export function showNotification({
  title,
  description,
  type = "info",
  duration = 5000,
  action,
}: NotificationProps) {
  const icon = getNotificationIcon(type);

  if (action) {
    toast(title, {
      description,
      icon,
      duration,
      action: {
        label: action.label,
        onClick: action.onClick,
      },
    });
  } else {
    toast(title, {
      description,
      icon,
      duration,
    });
  }
}

function getNotificationIcon(type: NotificationType) {
  switch (type) {
    case "success":
      return <CheckCircle2 className="h-5 w-5 text-success" />;
    case "error":
      return <XCircle className="h-5 w-5 text-destructive" />;
    case "warning":
      return <AlertCircle className="h-5 w-5 text-warning" />;
    case "info":
    default:
      return <InfoIcon className="h-5 w-5 text-primary" />;
  }
}
