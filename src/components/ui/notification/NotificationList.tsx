"use client";

import React from "react";
import { Bell, CheckCircle2, AlertCircle, InfoIcon, XCircle, Check } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Notification, useNotifications } from "@/lib/notifications/NotificationProvider";
import { cn } from "@/lib/utils";
import { formatDistanceToNow } from "date-fns";

interface NotificationListProps {
  className?: string;
  maxHeight?: string;
}

/**
 * Component for displaying a list of notifications
 */
export function NotificationList({ className, maxHeight = "300px" }: NotificationListProps) {
  const { notifications, unreadCount, markAsRead, markAllAsRead, loading, error } =
    useNotifications();

  if (loading) {
    return <div className="p-4 text-center text-muted-foreground">Loading notifications...</div>;
  }

  if (error) {
    return <div className="p-4 text-center text-destructive">{error}</div>;
  }

  if (notifications.length === 0) {
    return (
      <div className="p-4 text-center text-muted-foreground">
        <Bell className="h-8 w-8 mx-auto mb-2 opacity-50" />
        <p>No notifications</p>
      </div>
    );
  }

  return (
    <div className={cn("w-full", className)}>
      <div className="flex items-center justify-between p-4 border-b">
        <h3 className="font-medium">
          Notifications {unreadCount > 0 && <span className="text-primary">({unreadCount})</span>}
        </h3>
        {unreadCount > 0 && (
          <Button variant="ghost" size="sm" onClick={() => markAllAsRead()}>
            <Check className="h-4 w-4 mr-1" />
            Mark all as read
          </Button>
        )}
      </div>
      <ScrollArea className={`max-h-[${maxHeight}]`}>
        <div className="divide-y">
          {notifications.map((notification) => (
            <NotificationItem
              key={notification.id}
              notification={notification}
              onMarkAsRead={markAsRead}
            />
          ))}
        </div>
      </ScrollArea>
    </div>
  );
}

interface NotificationItemProps {
  notification: Notification;
  onMarkAsRead: (id: string) => Promise<void>;
}

function NotificationItem({ notification, onMarkAsRead }: NotificationItemProps) {
  const handleClick = () => {
    if (!notification.read) {
      onMarkAsRead(notification.id);
    }
  };

  return (
    <div
      className={cn("p-4 hover:bg-muted/50 cursor-pointer", !notification.read && "bg-primary/5")}
      onClick={handleClick}
    >
      <div className="flex items-start gap-3">
        <div className="flex-shrink-0 mt-0.5">
          {getNotificationIcon(notification.type as "success" | "error" | "info" | "warning")}
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between">
            <p className={cn("font-medium", !notification.read && "text-primary")}>
              {notification.title}
            </p>
            <span className="text-xs text-muted-foreground ml-2 whitespace-nowrap">
              {formatDistanceToNow(new Date(notification.created_at), { addSuffix: true })}
            </span>
          </div>
          <p className="text-sm text-muted-foreground mt-1">{notification.message}</p>
        </div>
      </div>
    </div>
  );
}

function getNotificationIcon(type: "success" | "error" | "info" | "warning") {
  switch (type) {
    case "success":
      return <CheckCircle2 className="h-5 w-5 text-success" />;
    case "error":
      return <XCircle className="h-5 w-5 text-destructive" />;
    case "warning":
      return <AlertCircle className="h-5 w-5 text-warning" />;
    case "info":
    default:
      return <InfoIcon className="h-5 w-5 text-primary" />;
  }
}
