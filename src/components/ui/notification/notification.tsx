"use client";

import React from "react";
import { Toaster, toast } from "sonner";
import { Bell, CheckCircle2, AlertCircle, InfoIcon, XCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

export type NotificationType = "success" | "error" | "info" | "warning";

export interface NotificationProps {
  /**
   * The title of the notification
   */
  title: string;

  /**
   * The description of the notification
   */
  description?: string;

  /**
   * The type of notification
   */
  type?: NotificationType;

  /**
   * The duration of the notification in milliseconds
   */
  duration?: number;

  /**
   * The action to perform when the notification is clicked
   */
  action?: {
    label: string;
    onClick: () => void;
  };
}

/**
 * Notification component for displaying toast notifications
 *
 * @example
 * ```tsx
 * // Show a success notification
 * showNotification({
 *   title: "Success",
 *   description: "Your changes have been saved",
 *   type: "success",
 * });
 *
 * // Show an error notification with an action
 * showNotification({
 *   title: "Error",
 *   description: "Failed to save changes",
 *   type: "error",
 *   action: {
 *     label: "Retry",
 *     onClick: handleRetry,
 *   },
 * });
 * ```
 */
export function NotificationProvider({ children }: { children: React.ReactNode }) {
  return (
    <>
      {children}
      <Toaster
        position="top-right"
        toastOptions={{
          className: "rounded-md border bg-background text-foreground",
          duration: 5000,
        }}
      />
    </>
  );
}

export function showNotification({
  title,
  description,
  type = "info",
  duration = 5000,
  action,
}: NotificationProps) {
  const icon = getNotificationIcon(type);

  if (action) {
    toast(title, {
      description,
      icon,
      duration,
      action: {
        label: action.label,
        onClick: action.onClick,
      },
    });
  } else {
    toast(title, {
      description,
      icon,
      duration,
    });
  }
}

function getNotificationIcon(type: NotificationType) {
  switch (type) {
    case "success":
      return <CheckCircle2 className="h-5 w-5 text-success" />;
    case "error":
      return <AlertCircle className="h-5 w-5 text-destructive" />;
    case "warning":
      return <AlertCircle className="h-5 w-5 text-warning" />;
    case "info":
    default:
      return <InfoIcon className="h-5 w-5 text-primary" />;
  }
}

/**
 * Notification center component for displaying a list of notifications
 */
export interface Notification {
  id: string;
  title: string;
  description?: string;
  type: NotificationType;
  timestamp: string | Date;
  read: boolean;
  link?: string;
}

interface NotificationCenterProps {
  /**
   * The notifications to display
   */
  notifications: Notification[];

  /**
   * Function to call when a notification is marked as read
   */
  onMarkAsRead?: (id: string) => void;

  /**
   * Function to call when all notifications are marked as read
   */
  onMarkAllAsRead?: () => void;

  /**
   * Function to call when a notification is clicked
   */
  onNotificationClick?: (notification: Notification) => void;

  /**
   * Additional CSS classes
   */
  className?: string;
}

export function NotificationCenter({
  notifications,
  onMarkAsRead,
  onMarkAllAsRead,
  onNotificationClick,
  className = "",
}: NotificationCenterProps) {
  const unreadCount = notifications.filter((n) => !n.read).length;

  return (
    <div className={cn("w-full max-w-sm rounded-lg border bg-background shadow-lg", className)}>
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center">
          <Bell className="h-5 w-5 mr-2" />
          <h3 className="font-semibold">Notifications</h3>
          {unreadCount > 0 && (
            <div className="ml-2 rounded-full bg-primary px-2 py-0.5 text-xs text-primary-foreground">
              {unreadCount}
            </div>
          )}
        </div>
        {unreadCount > 0 && onMarkAllAsRead && (
          <Button variant="ghost" size="sm" onClick={onMarkAllAsRead}>
            Mark all as read
          </Button>
        )}
      </div>

      <div className="max-h-[400px] overflow-y-auto">
        {notifications.length === 0 ? (
          <div className="p-4 text-center text-muted-foreground">
            <p>No notifications</p>
          </div>
        ) : (
          <ul className="divide-y">
            {notifications.map((notification) => (
              <li
                key={notification.id}
                className={cn(
                  "p-4 hover:bg-muted/50 cursor-pointer",
                  !notification.read && "bg-muted/20"
                )}
                onClick={() => onNotificationClick?.(notification)}
              >
                <div className="flex items-start gap-3">
                  <div className="mt-0.5">{getNotificationIcon(notification.type)}</div>
                  <div className="flex-1">
                    <div className="flex justify-between items-start">
                      <p className={cn("font-medium", !notification.read && "font-semibold")}>
                        {notification.title}
                      </p>
                      <span className="text-xs text-muted-foreground">
                        {typeof notification.timestamp === "string"
                          ? notification.timestamp
                          : new Date(notification.timestamp).toLocaleTimeString()}
                      </span>
                    </div>
                    {notification.description && (
                      <p className="text-sm text-muted-foreground mt-1">
                        {notification.description}
                      </p>
                    )}
                  </div>
                </div>
              </li>
            ))}
          </ul>
        )}
      </div>
    </div>
  );
}
