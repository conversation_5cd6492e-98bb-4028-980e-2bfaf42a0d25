"use client";

import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { useRouter } from "next/navigation";
import { cn } from "@/lib/utils";
import { buttonVariants } from "@/components/ui/button";
import { type VariantProps } from "class-variance-authority";

interface GoBackButtonProps
  extends Omit<
    React.ComponentProps<"button"> & VariantProps<typeof buttonVariants> & { asChild?: boolean },
    "onClick"
  > {
  /**
   * The text to display on the button
   * @default "Back"
   */
  label?: string;

  /**
   * Additional class names to apply to the button
   */
  className?: string;

  /**
   * Whether to use a custom onClick handler instead of router.back()
   */
  onClick?: () => void;
}

/**
 * A reusable button component that navigates back to the previous page
 * using the Next.js router's back() function
 */
export function GoBackButton({
  label = "Back", // Will be overridden by i18n in most cases
  variant = "outline",
  size = "default",
  className,
  onClick,
  ...props
}: GoBackButtonProps) {
  const router = useRouter();

  const handleClick = () => {
    if (onClick) {
      onClick();
    } else {
      router.back();
    }
  };

  return (
    <Button
      variant={variant}
      size={size}
      className={cn(className)}
      onClick={handleClick}
      {...props}
    >
      <ArrowLeft className="h-4 w-4 mr-2" />
      {label}
    </Button>
  );
}
