"use client";

import { ReactNode } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/drawer";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";

interface ContactInfo {
  id: string;
  name: string;
  email?: any;
  phone?: any;
  address?: string;
  status?: string;
}

interface ContactDrawerProps {
  /**
   * Whether the drawer is open
   */
  isOpen: boolean;

  /**
   * Callback when the drawer is closed
   */
  onClose: () => void;

  /**
   * Contact information to display
   */
  contact?: ContactInfo | null;

  /**
   * Language for links
   */
  lang: string;
}

/**
 * Reusable drawer component for displaying contact information
 *
 * @example
 * ```tsx
 * <ContactDrawer
 *   isOpen={isOpen}
 *   onClose={handleClose}
 *   contact={contactData}
 *   lang="en"
 * />
 * ```
 */
export function ContactDrawer({ isOpen, onClose, contact, lang }: ContactDrawerProps) {
  // Don't render anything if drawer is closed or no contact
  if (!isOpen) return null;

  // Format email display
  const formatEmail = () => {
    if (!contact || !contact.email) return "No email provided";

    if (typeof contact.email === "object") {
      const parts = [];
      if (contact.email.personal) parts.push(`Personal: ${contact.email.personal}`);
      if (contact.email.work) parts.push(`Work: ${contact.email.work}`);
      return parts.length > 0 ? parts.join(", ") : "No email provided";
    }

    return String(contact.email);
  };

  // Format phone display
  const formatPhone = () => {
    if (!contact || !contact.phone) return "No phone provided";

    if (typeof contact.phone === "object") {
      const parts = [];
      if (contact.phone.mobile) parts.push(`Mobile: ${contact.phone.mobile}`);
      if (contact.phone.work) parts.push(`Work: ${contact.phone.work}`);
      if (contact.phone.home) parts.push(`Home: ${contact.phone.home}`);
      return parts.length > 0 ? parts.join(", ") : "No phone provided";
    }

    return String(contact.phone);
  };

  return (
    <Drawer direction="right" open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DrawerContent side="right">
        <div className="h-full flex flex-col">
          <DrawerHeader>
            {contact && (
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center text-primary text-xl font-semibold">
                  {contact.name ? contact.name.charAt(0) : "?"}
                </div>
                <div>
                  <DrawerTitle>{contact.name || "Contact"}</DrawerTitle>
                  <DrawerDescription>Contact Information</DrawerDescription>
                </div>
              </div>
            )}
          </DrawerHeader>

          <div className="p-4 flex-1 overflow-auto">
            {contact ? (
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Email</h3>
                  <p className="text-sm mt-1">{formatEmail()}</p>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Phone</h3>
                  <p className="text-sm mt-1">{formatPhone()}</p>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Address</h3>
                  <p className="text-sm mt-1">{contact.address || "No address provided"}</p>
                </div>
              </div>
            ) : (
              <div className="py-4 text-center text-muted-foreground">
                Loading contact information...
              </div>
            )}
          </div>

          <DrawerFooter className="flex flex-row justify-between mt-auto border-t pt-4">
            <DrawerClose asChild>
              <Button variant="outline" onClick={onClose}>
                Close
              </Button>
            </DrawerClose>
            {contact && (
              <Button asChild>
                <Link href={`/${lang}/protected/contact/management/${contact.id}/view`}>
                  View Full Profile
                </Link>
              </Button>
            )}
          </DrawerFooter>
        </div>
      </DrawerContent>
    </Drawer>
  );
}
