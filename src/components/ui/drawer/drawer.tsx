"use client";

import React from "react";
import {
  Sheet,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON><PERSON>le,
  SheetDes<PERSON>,
} from "@/components/ui/sheet";
import { X } from "lucide-react";
import { Button } from "@/components/ui/button";

/**
 * Props for the Drawer component
 */
interface DrawerProps {
  /**
   * Whether the drawer is open
   */
  open: boolean;

  /**
   * Function to call when the drawer is closed
   */
  onClose: () => void;

  /**
   * The direction the drawer should appear from
   */
  direction?: "right" | "bottom";

  /**
   * The title of the drawer
   */
  title?: React.ReactNode;

  /**
   * The description of the drawer
   */
  description?: React.ReactNode;

  /**
   * The content of the drawer
   */
  children: React.ReactNode;

  /**
   * Whether to show the close button
   */
  showCloseButton?: boolean;
}

/**
 * Drawer component for displaying content in a slide-in panel
 */
export function Drawer({
  open,
  onClose,
  direction = "right",
  title,
  description,
  children,
  showCloseButton = true,
}: DrawerProps) {
  return (
    <Sheet open={open} onOpenChange={(open) => !open && onClose()}>
      <SheetContent side={direction} className="overflow-y-auto">
        {showCloseButton && (
          <Button variant="ghost" size="icon" className="absolute right-4 top-4" onClick={onClose}>
            <X className="h-4 w-4" />
            <span className="sr-only">Close</span>
          </Button>
        )}

        {(title || description) && (
          <SheetHeader>
            {title && <SheetTitle>{title}</SheetTitle>}
            {description && <SheetDescription>{description}</SheetDescription>}
          </SheetHeader>
        )}

        <div className="mt-6">{children}</div>
      </SheetContent>
    </Sheet>
  );
}
