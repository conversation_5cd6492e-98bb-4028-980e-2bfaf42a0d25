"use client";

import React, { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  File as FileIcon,
  Trash2,
  Eye,
  Download,
  Upload,
  FileText,
  FileImage,
  Archive as FileArchive,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";

export interface Document {
  id: string;
  name: string;
  type: string;
  size: number;
  uploadedBy: string;
  uploadedAt: string | Date;
  url: string;
}

interface DocumentAttachmentProps {
  /**
   * The documents to display
   */
  documents: Document[];

  /**
   * Function to call when a document is uploaded
   */
  onUpload?: (files: File[]) => Promise<void>;

  /**
   * Function to call when a document is deleted
   */
  onDelete?: (documentId: string) => Promise<void>;

  /**
   * Function to call when a document is viewed
   */
  onView?: (document: Document) => void;

  /**
   * Function to call when a document is downloaded
   */
  onDownload?: (document: Document) => void;

  /**
   * Whether to allow uploading documents
   */
  allowUpload?: boolean;

  /**
   * Whether to allow deleting documents
   */
  allowDelete?: boolean;

  /**
   * The title of the component
   */
  title?: string;

  /**
   * The description of the component
   */
  description?: string;

  /**
   * Additional CSS classes
   */
  className?: string;
}

/**
 * Document attachment component for uploading, viewing, and managing documents
 *
 * @example
 * ```tsx
 * <DocumentAttachment
 *   title="Contact Documents"
 *   description="Upload and manage documents for this contact"
 *   documents={documents}
 *   onUpload={handleUpload}
 *   onDelete={handleDelete}
 *   onView={handleView}
 *   onDownload={handleDownload}
 * />
 * ```
 */
export function DocumentAttachment({
  documents,
  onUpload,
  onDelete,
  onView,
  onDownload,
  allowUpload = true,
  allowDelete = true,
  title = "Documents",
  description,
  className = "",
}: DocumentAttachmentProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [viewDocument, setViewDocument] = useState<Document | null>(null);
  const fileInputRef = React.useRef<HTMLInputElement>(null);

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0 || !onUpload) return;

    try {
      setIsUploading(true);
      await onUpload(Array.from(files));
      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    } catch (error) {
      console.error("Error uploading files:", error);
    } finally {
      setIsUploading(false);
    }
  };

  const handleDelete = async (documentId: string) => {
    if (!onDelete) return;

    try {
      await onDelete(documentId);
    } catch (error) {
      console.error("Error deleting document:", error);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";

    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const getFileIcon = (type: string) => {
    if (type.includes("image")) {
      return <FileImage className="h-4 w-4" />;
    } else if (type.includes("zip") || type.includes("rar") || type.includes("tar")) {
      return <FileArchive className="h-4 w-4" />;
    } else {
      return <FileText className="h-4 w-4" />;
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle>{title}</CardTitle>
            {description && <CardDescription>{description}</CardDescription>}
          </div>
          {allowUpload && (
            <div>
              <Input
                ref={fileInputRef}
                type="file"
                multiple
                className="hidden"
                onChange={handleFileChange}
                id="document-upload"
              />
              <Button
                variant="outline"
                size="sm"
                onClick={() => fileInputRef.current?.click()}
                disabled={isUploading}
              >
                {isUploading ? (
                  "Uploading..."
                ) : (
                  <>
                    <Upload className="h-4 w-4 mr-2" />
                    Upload
                  </>
                )}
              </Button>
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {documents.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <FileIcon className="h-12 w-12 mx-auto mb-2 text-muted-foreground/50" />
            <p>No documents attached</p>
            {allowUpload && (
              <Button
                variant="outline"
                size="sm"
                className="mt-4"
                onClick={() => fileInputRef.current?.click()}
              >
                <Upload className="h-4 w-4 mr-2" />
                Upload Document
              </Button>
            )}
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Size</TableHead>
                <TableHead>Uploaded</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {documents.map((doc) => (
                <TableRow key={doc.id}>
                  <TableCell className="font-medium flex items-center">
                    {getFileIcon(doc.type)}
                    <span className="ml-2">{doc.name}</span>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">
                      {doc.type.split("/")[1]?.toUpperCase() || doc.type}
                    </Badge>
                  </TableCell>
                  <TableCell>{formatFileSize(doc.size)}</TableCell>
                  <TableCell>{new Date(doc.uploadedAt).toLocaleDateString()}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      {onView && (
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => setViewDocument(doc)}
                          title="View"
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      )}
                      {onDownload && (
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => onDownload(doc)}
                          title="Download"
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                      )}
                      {allowDelete && onDelete && (
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleDelete(doc.id)}
                          title="Delete"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>

      {/* Document Viewer Dialog */}
      <Dialog open={!!viewDocument} onOpenChange={(open) => !open && setViewDocument(null)}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              {viewDocument && getFileIcon(viewDocument.type)}
              <span className="ml-2">{viewDocument?.name}</span>
            </DialogTitle>
            <DialogDescription>
              {viewDocument && (
                <div className="flex items-center gap-4 text-sm">
                  <span>{formatFileSize(viewDocument.size)}</span>
                  <span>Uploaded by {viewDocument.uploadedBy}</span>
                  <span>{new Date(viewDocument.uploadedAt).toLocaleDateString()}</span>
                </div>
              )}
            </DialogDescription>
          </DialogHeader>
          <div className="h-[60vh] overflow-auto border rounded-md">
            {viewDocument && viewDocument.type.includes("image") ? (
              <img
                src={viewDocument.url}
                alt={viewDocument.name}
                className="w-full h-full object-contain"
              />
            ) : viewDocument && viewDocument.type.includes("pdf") ? (
              <iframe src={viewDocument.url} title={viewDocument.name} className="w-full h-full" />
            ) : (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <FileIcon className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
                  <p>Preview not available</p>
                  {onDownload && viewDocument && (
                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-4"
                      onClick={() => onDownload(viewDocument)}
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Download
                    </Button>
                  )}
                </div>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </Card>
  );
}
