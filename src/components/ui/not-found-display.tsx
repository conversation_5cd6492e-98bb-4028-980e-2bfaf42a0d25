import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { FileQuestion } from "lucide-react";
import { GoBackButton } from "./go-back-button";

interface NotFoundDisplayProps {
  title?: string;
  description?: string;
  backText?: string;
}

/**
 * Reusable not found display component
 * Can be used in not-found.tsx files throughout the application
 */
export function NotFoundDisplay({
  title = "Not Found",
  description = "The resource you are looking for could not be found.",
  backText = "Go Back",
}: NotFoundDisplayProps) {
  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center">
          <FileQuestion className="h-5 w-5 mr-2" />
          <h1>{title}</h1>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <p>{description}</p>
      </CardContent>
      <CardFooter>
        <GoBackButton label={backText} />
      </CardFooter>
    </Card>
  );
}
