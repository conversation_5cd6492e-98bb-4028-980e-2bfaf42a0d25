import React from "react";
import { LucideIcon } from "lucide-react";

interface MetadataItem {
  icon: LucideIcon;
  label: string;
  value: string;
}

interface PageHeaderProps {
  title: string;
  metadata?: MetadataItem[];
  actions?: React.ReactNode;
  className?: string;
}

export function PageHeader({ title, metadata = [], actions, className = "" }: PageHeaderProps) {
  return (
    <div className={`lg:flex lg:items-center lg:justify-between ${className}`}>
      <div className="min-w-0 flex-1">
        <h2 className="text-2xl font-bold text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight break-words">
          {title}
        </h2>
        {metadata.length > 0 && (
          <div className="mt-1 flex flex-col sm:mt-0 sm:flex-row sm:flex-wrap sm:space-x-6">
            {metadata.map((item, index) => (
              <div key={index} className="mt-2 flex items-center text-sm text-gray-500">
                <item.icon className="mr-1.5 h-5 w-5 shrink-0 text-gray-400" />
                {item.label && <span className="mr-1">{item.label}:</span>}
                {item.value}
              </div>
            ))}
          </div>
        )}
      </div>
      {actions && (
        <div className="mt-5 flex lg:mt-0 lg:ml-4">
          {actions}
        </div>
      )}
    </div>
  );
}
