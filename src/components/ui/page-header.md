# PageHeader Component

A reusable header component that follows the Tailwind UI pattern for detail pages. Provides a consistent layout with title, metadata, and actions.

## Usage

```tsx
import { <PERSON>Header } from "@/components/ui/page-header";
import { Hash, FileText, Calendar, User, Download, Eye } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

<PageHeader
  title="Document Name.pdf"
  metadata={[
    {
      icon: Hash,
      label: "Type",
      value: "PDF",
    },
    {
      icon: FileText,
      label: "Size",
      value: "2.4 MB",
    },
    {
      icon: Calendar,
      label: "Date",
      value: "Dec 15, 2024",
    },
    {
      icon: User,
      label: "Author",
      value: "<PERSON>",
    },
  ]}
  actions={
    <>
      <Button variant="outline">
        <Eye className="mr-1.5 -ml-0.5 h-5 w-5" />
        View Related
      </Button>
      <Button className="ml-3">
        <Download className="mr-1.5 -ml-0.5 h-5 w-5" />
        Download
      </Button>
    </>
  }
/>
```

## Props

### `title` (required)
- Type: `string`
- The main title displayed prominently at the top

### `metadata` (optional)
- Type: `MetadataItem[]`
- Array of metadata items to display below the title
- Each item has:
  - `icon`: Lucide icon component
  - `label`: Optional label text (can be empty string)
  - `value`: The value to display

### `actions` (optional)
- Type: `React.ReactNode`
- Action buttons or components to display on the right side
- Automatically responsive (stacks on mobile)

### `className` (optional)
- Type: `string`
- Additional CSS classes to apply to the container

## Features

- ✅ **Responsive Design**: Automatically stacks on mobile devices
- ✅ **Flexible Metadata**: Support for any number of metadata items with icons
- ✅ **Action Support**: Any React components can be used as actions
- ✅ **Consistent Styling**: Follows Tailwind UI patterns
- ✅ **Accessible**: Proper semantic HTML structure

## Examples

### Simple Header (Title Only)
```tsx
<PageHeader title="Simple Page Title" />
```

### With Metadata (No Labels)
```tsx
<PageHeader
  title="Document.pdf"
  metadata={[
    { icon: Hash, label: "", value: "PDF" },
    { icon: FileText, label: "", value: "2.4 MB" },
  ]}
/>
```

### With Metadata (With Labels)
```tsx
<PageHeader
  title="User Profile"
  metadata={[
    { icon: User, label: "Role", value: "Administrator" },
    { icon: Calendar, label: "Joined", value: "Jan 2024" },
  ]}
/>
```

### Full Example
```tsx
<PageHeader
  title="Project Dashboard"
  metadata={[
    { icon: Hash, label: "Status", value: "Active" },
    { icon: Calendar, label: "Due", value: "Dec 31, 2024" },
    { icon: User, label: "Owner", value: "Jane Smith" },
  ]}
  actions={
    <>
      <Button variant="outline">Edit</Button>
      <Button className="ml-3">Share</Button>
    </>
  }
/>
```

## Design System Integration

This component follows the established Tailwind UI patterns and integrates seamlessly with your design system:

- Uses standard Tailwind classes for consistency
- Responsive breakpoints match your design system
- Icon sizing and spacing follow established patterns
- Typography scales appropriately across devices
