import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

interface ListLoadingProps {
  rowCount?: number;
  columnCount?: number;
  showSearch?: boolean;
  showPagination?: boolean;
}

/**
 * Reusable loading component for list pages
 */
export function ListLoading({
  rowCount = 5,
  columnCount = 4,
  showSearch = true,
  showPagination = true,
}: ListLoadingProps) {
  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>
              <Skeleton className="h-8 w-48" />
            </CardTitle>
            <CardDescription>
              <Skeleton className="h-4 w-64 mt-2" />
            </CardDescription>
          </div>
          <Skeleton className="h-10 w-28" />
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {showSearch && (
            <div className="flex gap-2 mb-6">
              <Skeleton className="h-10 w-64" />
              <Skeleton className="h-10 w-24" />
            </div>
          )}

          <div className="border rounded-md">
            <div className="p-4 border-b">
              <div className={`grid grid-cols-${columnCount} gap-4`}>
                {Array.from({ length: columnCount }).map((_, i) => (
                  <Skeleton key={i} className="h-5 w-full" />
                ))}
              </div>
            </div>
            {Array.from({ length: rowCount }).map((_, i) => (
              <div key={i} className="p-4 border-b">
                <div className={`grid grid-cols-${columnCount} gap-4`}>
                  {Array.from({ length: columnCount }).map((_, j) => (
                    <Skeleton key={j} className="h-5 w-full" />
                  ))}
                </div>
              </div>
            ))}
          </div>

          {showPagination && (
            <div className="flex justify-between items-center mt-4">
              <Skeleton className="h-5 w-48" />
              <div className="flex gap-2">
                <Skeleton className="h-9 w-24" />
                <Skeleton className="h-9 w-24" />
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
