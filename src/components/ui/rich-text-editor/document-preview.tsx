"use client";

import React from "react";
import { cn } from "@/lib/utils";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Download, Printer, Share2 } from "lucide-react";

export interface DocumentPreviewProps {
  /**
   * The HTML content to preview
   */
  content: string;

  /**
   * The title of the document
   */
  title?: string;

  /**
   * The description of the document
   */
  description?: string;

  /**
   * The author of the document
   */
  author?: string;

  /**
   * The date the document was created or last modified
   */
  date?: string | Date;

  /**
   * Whether to show the header
   */
  showHeader?: boolean;

  /**
   * Whether to show the footer with actions
   */
  showFooter?: boolean;

  /**
   * Function to call when the print button is clicked
   */
  onPrint?: () => void;

  /**
   * Function to call when the download button is clicked
   */
  onDownload?: () => void;

  /**
   * Function to call when the share button is clicked
   */
  onShare?: () => void;

  /**
   * Additional CSS classes
   */
  className?: string;
}

/**
 * Document preview component for displaying rich text content
 *
 * @example
 * ```tsx
 * <DocumentPreview
 *   content={htmlContent}
 *   title="Project Report"
 *   description="Quarterly project status report"
 *   author="John Doe"
 *   date={new Date()}
 *   onPrint={handlePrint}
 *   onDownload={handleDownload}
 * />
 * ```
 */
export function DocumentPreview({
  content,
  title,
  description,
  author,
  date,
  showHeader = true,
  showFooter = true,
  onPrint,
  onDownload,
  onShare,
  className = "",
}: DocumentPreviewProps) {
  const handlePrint = () => {
    if (onPrint) {
      onPrint();
    } else {
      window.print();
    }
  };

  const formatDate = (date: string | Date) => {
    if (!date) return "";

    const dateObj = typeof date === "string" ? new Date(date) : date;
    return dateObj.toLocaleDateString(undefined, {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  return (
    <Card className={cn("print:shadow-none print:border-none", className)}>
      {showHeader && (title || description || author || date) && (
        <CardHeader className="print:pb-0">
          {title && <CardTitle className="print:text-xl">{title}</CardTitle>}
          {description && <CardDescription>{description}</CardDescription>}
          {(author || date) && (
            <div className="flex flex-col text-sm text-muted-foreground print:text-foreground">
              {author && <span>Author: {author}</span>}
              {date && <span>Date: {formatDate(date)}</span>}
            </div>
          )}
        </CardHeader>
      )}
      <CardContent className="print:pt-4">
        <div
          className="prose dark:prose-invert max-w-none"
          dangerouslySetInnerHTML={{ __html: content }}
        />
      </CardContent>
      {showFooter && (
        <CardFooter className="flex justify-end gap-2 print:hidden">
          {onPrint && (
            <Button variant="outline" size="sm" onClick={handlePrint}>
              <Printer className="h-4 w-4 mr-2" />
              Print
            </Button>
          )}
          {onDownload && (
            <Button variant="outline" size="sm" onClick={onDownload}>
              <Download className="h-4 w-4 mr-2" />
              Download
            </Button>
          )}
          {onShare && (
            <Button variant="outline" size="sm" onClick={onShare}>
              <Share2 className="h-4 w-4 mr-2" />
              Share
            </Button>
          )}
        </CardFooter>
      )}
    </Card>
  );
}
