"use client";

import React from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { FileText, Download, Eye, Upload } from "lucide-react";

export interface AttachmentItem {
  id: string;
  document_name: string;
  document_type: string;
  file_size: number;
  attached_to_type: string;
  attached_to_id: string;
  uploaded_at: string;
  uploaded_by: string;
  metadata?: {
    category?: string;
    tags?: string[];
    description?: string;
  };
}

interface AttachmentManagerProps {
  attachments: AttachmentItem[];
  onView?: (attachment: AttachmentItem) => void;
  onDownload?: (attachment: AttachmentItem) => void;
  onUpload?: () => void;
  loading?: boolean;
  title?: string;
  description?: string;
}

export function AttachmentManager({
  attachments,
  onView,
  onDownload,
  onUpload,
  loading = false,
  title = "Attachments",
  description,
}: AttachmentManagerProps) {
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="text-muted-foreground mt-2">Loading attachments...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (attachments.length === 0) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center py-12">
            <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium text-muted-foreground mb-2">No attachments found</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Upload your first document to get started
            </p>
            {onUpload && (
              <Button onClick={onUpload}>
                <Upload className="h-4 w-4 mr-2" />
                Upload Document
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle>{title}</CardTitle>
            {description && <CardDescription>{description}</CardDescription>}
          </div>
          {onUpload && (
            <Button onClick={onUpload}>
              <Upload className="h-4 w-4 mr-2" />
              Upload
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {attachments.map((attachment) => (
            <Card key={attachment.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <FileText className="h-8 w-8 text-gray-500" />
                    <Badge variant="outline" className="text-xs">
                      {attachment.document_type}
                    </Badge>
                  </div>

                  <div>
                    <h4 className="font-medium text-sm truncate" title={attachment.document_name}>
                      {attachment.document_name}
                    </h4>
                    <p className="text-xs text-muted-foreground">
                      {formatFileSize(attachment.file_size)}
                    </p>
                  </div>

                  <div className="space-y-1">
                    {attachment.metadata?.category && (
                      <Badge variant="secondary" className="text-xs">
                        {attachment.metadata.category}
                      </Badge>
                    )}

                    <p className="text-xs text-muted-foreground">
                      {formatDate(attachment.uploaded_at)}
                    </p>
                  </div>

                  <div className="flex gap-1 pt-2 border-t">
                    {onView && (
                      <Button variant="ghost" size="sm" onClick={() => onView(attachment)}>
                        <Eye className="h-4 w-4" />
                      </Button>
                    )}
                    {onDownload && (
                      <Button variant="ghost" size="sm" onClick={() => onDownload(attachment)}>
                        <Download className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
