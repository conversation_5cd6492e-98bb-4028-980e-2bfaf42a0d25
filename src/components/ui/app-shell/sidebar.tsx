"use client";

import React, { ReactNode } from "react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Sheet, Sheet<PERSON>ontent, SheetTrigger } from "@/components/ui/sheet";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Menu } from "lucide-react";
import { H2 } from "@/components/typography";
import { cn } from "@/lib/utils";

interface SidebarProps {
  /**
   * The content to display in the sidebar
   */
  children: ReactNode;

  /**
   * The title to display at the top of the sidebar
   */
  title?: ReactNode;

  /**
   * Additional CSS classes to apply to the sidebar
   */
  className?: string;
}

/**
 * Sidebar component that provides a consistent sidebar layout
 * with responsive behavior for mobile and desktop.
 */
export function Sidebar({ children, title, className }: SidebarProps) {
  return (
    <>
      {/* Mobile sidebar trigger */}
      <MobileSidebar title={title}>{children}</MobileSidebar>

      {/* Desktop sidebar */}
      <div className={cn("hidden lg:block w-64 border-r bg-card", className)}>
        <div className="flex flex-col h-full">
          {title && (
            <div className="p-6">{typeof title === "string" ? <H2>{title}</H2> : title}</div>
          )}
          <ScrollArea className="flex-1">{children}</ScrollArea>
        </div>
      </div>
    </>
  );
}

interface MobileSidebarProps {
  children: ReactNode;
  title?: ReactNode;
}

/**
 * Mobile sidebar component that provides a responsive sidebar
 * that can be toggled on mobile devices.
 */
function MobileSidebar({ children, title }: MobileSidebarProps) {
  const [open, setOpen] = React.useState(false);

  return (
    <div className="lg:hidden fixed top-4 left-4 z-40">
      <Sheet open={open} onOpenChange={setOpen}>
        <SheetTrigger asChild>
          <Button variant="outline" size="icon" className="ml-2">
            <Menu className="h-5 w-5" />
            <span className="sr-only">Toggle menu</span>
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="p-0">
          <div className="flex flex-col h-full">
            {title && (
              <div className="p-6">{typeof title === "string" ? <H2>{title}</H2> : title}</div>
            )}
            <ScrollArea className="flex-1">{children}</ScrollArea>
          </div>
        </SheetContent>
      </Sheet>
    </div>
  );
}

// Keep the existing type for backward compatibility
export interface SidebarItem {
  title: string;
  href?: string;
  icon?: React.ReactNode;
  active?: boolean;
  children?: SidebarItem[];
  expanded?: boolean;
}
