"use client";

import React, { ReactNode } from "react";
import { cn } from "@/lib/utils";

interface AppShellProps {
  /**
   * The sidebar component to display on the left side of the app shell
   */
  sidebar: ReactNode;

  /**
   * The navbar component to display at the top of the app shell
   */
  navbar: ReactNode;

  /**
   * The main content of the app shell
   */
  children: ReactNode;

  /**
   * Additional CSS classes to apply to the app shell
   */
  className?: string;
}

/**
 * AppShell component that provides a consistent layout for the application
 * with a sidebar, navbar, and main content area.
 */
export function AppShell({ sidebar, navbar, children, className }: AppShellProps) {
  return (
    <div className={cn("flex h-screen overflow-hidden bg-background", className)}>
      {/* Sidebar */}
      {sidebar}

      {/* Main content */}
      <div className="flex flex-col flex-1 overflow-hidden">
        {/* Navbar */}
        {navbar}

        {/* Page content */}
        <main className="flex-1 overflow-auto">{children}</main>
      </div>
    </div>
  );
}
