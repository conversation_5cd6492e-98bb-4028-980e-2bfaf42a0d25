"use client";

import React, { ReactNode } from "react";
import { cn } from "@/lib/utils";

interface NavbarProps {
  /**
   * The content to display on the left side of the navbar
   */
  left?: ReactNode;

  /**
   * The content to display on the right side of the navbar
   */
  right?: ReactNode;

  /**
   * Additional CSS classes to apply to the navbar
   */
  className?: string;
}

/**
 * Navbar component that provides a consistent navbar layout
 * with left and right sections.
 */
export function Navbar({ left, right, className }: NavbarProps) {
  return (
    <header
      className={cn(
        "sticky top-0 z-30 flex h-16 items-center gap-4 border-b bg-background px-6",
        className
      )}
    >
      {/* Left section */}
      <div className="flex items-center">{left}</div>

      {/* Spacer */}
      <div className="flex-1" />

      {/* Right section */}
      <div className="flex items-center gap-4">{right}</div>
    </header>
  );
}

// Keep the existing types for backward compatibility
export interface NavbarItem {
  title: string;
  href: string;
  active?: boolean;
}

export interface UserMenuAction {
  label: string;
  href?: string;
  onClick?: () => void;
  icon?: React.ReactNode;
}
