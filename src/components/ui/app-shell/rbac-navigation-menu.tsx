"use server";

import React from "react";
import { NavigationMenu } from "./navigation-menu";
import { Permission } from "@/lib/authorization/types";
import { PermissionService } from "@/lib/authorization/services/PermissionService";
import { auth } from "@/lib/authentication/services/AuthenticationService";

export interface RbacNavigationItem {
  /**
   * The title of the navigation item
   */
  title: string;

  /**
   * The href of the navigation item
   */
  href: string;

  /**
   * The icon to display next to the navigation item
   * For server components, this should be a string name of a Lucide icon
   */
  icon?: string;

  /**
   * The permission required to view this navigation item
   */
  permission: Permission;

  /**
   * Additional CSS classes to apply to the navigation item
   */
  className?: string;
}

interface RbacNavigationMenuProps {
  /**
   * The items to display in the navigation menu
   */
  items: RbacNavigationItem[];

  /**
   * The current pathname to determine active items
   */
  pathname: string;

  /**
   * The current language
   */
  lang: string;

  /**
   * Dictionary for translations
   */
  dictionary?: Record<string, string>;

  /**
   * Additional CSS classes to apply to the navigation menu
   */
  className?: string;
}

/**
 * RbacNavigationMenu component that provides a consistent navigation menu
 * with items that are only displayed if the user has the required permission.
 */
export async function RbacNavigationMenu({
  items,
  pathname,
  lang,
  dictionary,
  className,
}: RbacNavigationMenuProps) {
  // Get the current user's role
  const role = await auth.getCurrentUserRole();

  if (!role) {
    return null;
  }

  // Filter items based on permissions
  const authorizedItems = items.filter((item) =>
    PermissionService.hasPermission(role, item.permission)
  );

  // Map to NavigationItem format
  const navigationItems = authorizedItems.map((item) => ({
    title: dictionary?.[item.title] || item.title,
    href: `/${lang}/protected${item.href}`,
    icon: item.icon,
    active: pathname.includes(item.href),
    className: item.className,
  }));

  return <NavigationMenu items={navigationItems} className={className} />;
}
