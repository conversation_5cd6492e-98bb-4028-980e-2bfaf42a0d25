"use client";

import React, { ReactNode } from "react";
import { cn } from "@/lib/utils";
import { PageTitle } from "@/components/typography";

interface ContentLayoutProps {
  /**
   * The title of the content
   */
  title?: ReactNode;

  /**
   * The description of the content
   */
  description?: ReactNode;

  /**
   * The main content
   */
  children: ReactNode;

  /**
   * Additional actions to display in the header
   */
  actions?: ReactNode;

  /**
   * Additional content to display in the footer
   */
  footer?: ReactNode;

  /**
   * Additional CSS classes to apply to the content layout
   */
  className?: string;
}

/**
 * ContentLayout component that provides a consistent layout for content
 * with a title, description, actions, and footer.
 */
export function ContentLayout({
  title,
  description,
  children,
  actions,
  footer,
  className,
}: ContentLayoutProps) {
  return (
    <div className={cn("p-6 space-y-6", className)}>
      {/* Header */}
      {(title || actions) && (
        <div className="flex items-center justify-between">
          <div>
            {typeof title === "string" && typeof description === "string" ? (
              <PageTitle description={description}>{title}</PageTitle>
            ) : (
              <>
                {title}
                {description && <div className="mt-2 text-muted-foreground">{description}</div>}
              </>
            )}
          </div>
          {actions && <div className="flex items-center gap-2">{actions}</div>}
        </div>
      )}

      {/* Main content */}
      <div>{children}</div>

      {/* Footer */}
      {footer && <div className="pt-4 border-t">{footer}</div>}
    </div>
  );
}
