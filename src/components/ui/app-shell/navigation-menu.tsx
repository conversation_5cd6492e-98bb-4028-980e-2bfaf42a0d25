"use client";

import React from "react";
import Link from "next/link";
import {
  LucideIcon,
  Contact,
  CoffeeIcon,
  Building,
  Users,
  Settings,
  Home,
  FileText,
  Calendar,
  ClipboardList,
  <PERSON><PERSON>hart,
  Cog,
} from "lucide-react";
import { cn } from "@/lib/utils";

// Map of icon names to Lucide components
const iconMap: Record<string, LucideIcon> = {
  Contact,
  CoffeeIcon,
  Building,
  Users,
  Settings,
  Home,
  FileText,
  Calendar,
  ClipboardList,
  BarChart,
  Cog,
};

export interface NavigationItem {
  /**
   * The title of the navigation item
   */
  title: string;

  /**
   * The href of the navigation item
   */
  href: string;

  /**
   * The icon to display next to the navigation item
   * Can be either a Lucide icon component or a string name of a Lucide icon
   */
  icon?: LucideIcon | string;

  /**
   * Whether the navigation item is active
   */
  active?: boolean;

  /**
   * Additional CSS classes to apply to the navigation item
   */
  className?: string;
}

interface NavigationMenuProps {
  /**
   * The items to display in the navigation menu
   */
  items: NavigationItem[];

  /**
   * Additional CSS classes to apply to the navigation menu
   */
  className?: string;
}

/**
 * NavigationMenu component that provides a consistent navigation menu
 * with items that can be active or inactive.
 */
export function NavigationMenu({ items, className }: NavigationMenuProps) {
  return (
    <nav className={cn("px-4 py-2 space-y-1", className)}>
      {items.map((item) => (
        <NavigationItem
          key={item.href}
          title={item.title}
          href={item.href}
          icon={item.icon}
          active={item.active}
          className={item.className}
        />
      ))}
    </nav>
  );
}

interface NavigationItemProps extends NavigationItem {}

/**
 * NavigationItem component that provides a consistent navigation item
 * with an icon and title.
 */
export function NavigationItem({ title, href, icon, active, className }: NavigationItemProps) {
  // Determine which icon to use
  let IconComponent: LucideIcon | undefined;

  if (typeof icon === "string") {
    // If icon is a string, look it up in the iconMap
    IconComponent = iconMap[icon];
  } else if (icon) {
    // If icon is already a component, use it directly
    IconComponent = icon;
  }

  return (
    <Link
      href={href}
      className={cn(
        "flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium",
        active
          ? "bg-primary text-primary-foreground"
          : "hover:bg-accent hover:text-accent-foreground",
        className
      )}
    >
      {IconComponent && <IconComponent className="h-4 w-4" />}
      <span>{title}</span>
    </Link>
  );
}
