"use client";

import React from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { X } from "lucide-react";
import { Button } from "@/components/ui/button";

interface ModalProps {
  /**
   * Whether the modal is open
   */
  open: boolean;

  /**
   * Function to call when the modal is closed
   */
  onClose: () => void;

  /**
   * The title of the modal
   */
  title?: React.ReactNode;

  /**
   * The description of the modal
   */
  description?: React.ReactNode;

  /**
   * The content of the modal
   */
  children: React.ReactNode;

  /**
   * The footer content of the modal
   */
  footer?: React.ReactNode;

  /**
   * Whether to show the close button
   */
  showCloseButton?: boolean;
}

/**
 * Modal component for displaying content in a dialog
 */
export function Modal({
  open,
  onClose,
  title,
  description,
  children,
  footer,
  showCloseButton = true,
}: ModalProps) {
  return (
    <Dialog open={open} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[425px]">
        {showCloseButton && (
          <Button variant="ghost" size="icon" className="absolute right-4 top-4" onClick={onClose}>
            <X className="h-4 w-4" />
            <span className="sr-only">Close</span>
          </Button>
        )}

        {(title || description) && (
          <DialogHeader>
            {title && <DialogTitle>{title}</DialogTitle>}
            {description && <DialogDescription>{description}</DialogDescription>}
          </DialogHeader>
        )}

        <div className="py-4">{children}</div>

        {footer && <div className="mt-4">{footer}</div>}
      </DialogContent>
    </Dialog>
  );
}
