"use client";

import React from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";

interface BackButtonProps {
  /**
   * The URL to navigate to
   */
  href: string;

  /**
   * The button text
   */
  children?: React.ReactNode;

  /**
   * Additional CSS classes
   */
  className?: string;
}

/**
 * Standard back button component
 *
 * @example
 * ```tsx
 * <BackButton href="/users">Back to Users</BackButton>
 * ```
 */
export function BackButton({ href, children = "Back", className = "" }: BackButtonProps) {
  return (
    <Button variant="outline" size="sm" asChild className={`gap-1 ${className}`}>
      <Link href={href}>
        <ArrowLeft className="h-4 w-4" />
        {children}
      </Link>
    </Button>
  );
}
