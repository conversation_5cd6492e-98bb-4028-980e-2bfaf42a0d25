"use client";

import Link from "next/link";
import { Button } from "@/components/ui/button";
import { usePathname } from "next/navigation";

/**
 * A simple language toggle component that switches between available languages
 * using direct URL links without client-side state.
 *
 * @param currentLang The current language code (e.g., 'en', 'fr')
 * @param pathname The current pathname (without language prefix)
 * @param className Optional additional CSS classes
 * @returns Language toggle component
 */
export function LanguageToggle({
  currentLang,
  className,
  labels = { en: "English", fr: "Français" },
}: {
  currentLang: string;
  className?: string;
  labels?: { en: string; fr: string };
}) {
  // Get the current pathname
  const pathname = usePathname();

  // Get the target language (the one we're not currently using)
  const targetLang = currentLang === "en" ? "fr" : "en";

  // Extract the path without the language prefix
  const pathWithoutLang = pathname.replace(new RegExp(`^/${currentLang}`), "");

  // Construct the target URL with the current path but different language
  const targetUrl = `/${targetLang}${pathWithoutLang}`;

  // Get the display name for the target language
  const targetLangDisplay = labels[targetLang as keyof typeof labels];

  return (
    <Link href={targetUrl} className={className}>
      <Button variant="outline" size="sm">
        {targetLangDisplay}
      </Button>
    </Link>
  );
}
