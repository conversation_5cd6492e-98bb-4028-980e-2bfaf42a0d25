import React from "react";
import { LucideIcon } from "lucide-react";
import { H1 } from "@/components/typography";

interface MetadataItem {
  icon: LucideIcon;
  label: string;
  value: string;
}

interface DetailPageHeaderProps {
  title: string;
  metadata?: MetadataItem[];
  actions?: React.ReactNode;
  className?: string;
}

/**
 * Detail page header component following Tailwind UI patterns
 * Perfect for detail pages with title, metadata, and actions
 *
 * @example
 * ```tsx
 * <DetailPageHeader
 *   title="Document.pdf"
 *   metadata={[
 *     { icon: Hash, label: "", value: "PDF" },
 *     { icon: FileText, label: "", value: "2.4 MB" },
 *     { icon: Calendar, label: "", value: "Dec 15, 2024" },
 *     { icon: User, label: "", value: "John Doe" },
 *   ]}
 *   actions={
 *     <>
 *       <Button variant="outline">Edit</Button>
 *       <Button>Download</Button>
 *     </>
 *   }
 * />
 * ```
 */
export function DetailPageHeader({ title, metadata = [], actions, className = "" }: DetailPageHeaderProps) {
  return (
    <div className={`lg:flex lg:items-center lg:justify-between ${className}`}>
      <div className="min-w-0 flex-1">
        <H1 className="sm:truncate break-words">
          {title}
        </H1>
        {metadata.length > 0 && (
          <div className="mt-1 flex flex-col sm:mt-0 sm:flex-row sm:flex-wrap sm:space-x-6">
            {metadata.map((item, index) => (
              <div key={index} className="mt-2 flex items-center text-sm text-gray-500">
                <item.icon className="mr-1.5 h-5 w-5 shrink-0 text-gray-400" />
                {item.label && <span className="mr-1">{item.label}:</span>}
                {item.value}
              </div>
            ))}
          </div>
        )}
      </div>
      {actions && (
        <div className="mt-5 flex lg:mt-0 lg:ml-4">
          {actions}
        </div>
      )}
    </div>
  );
}
