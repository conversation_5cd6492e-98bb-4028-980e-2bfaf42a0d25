"use client";

import {
  Pagination as ShadcnPagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
} from "@/components/ui/pagination";
import { ChevronLeftIcon, ChevronRightIcon } from "lucide-react";
import { usePathname, useSearchParams } from "next/navigation";
import { i18n } from "@/lib/i18n/services/I18nService";

interface PaginationProps {
  totalItems: number;
  pageSize: number;
  currentPage: number;
  lang: string;
  search?: string;
  itemName?: string;
  basePath?: string;
}

/**
 * Reusable pagination component that creates links with search params
 * Can be used in any list view
 */
export function Pagination({
  totalItems,
  pageSize,
  currentPage,
  lang,
  search,
  itemName = "items",
  basePath,
}: PaginationProps) {
  // Calculate total pages
  const totalPages = Math.max(1, Math.ceil(totalItems / pageSize));

  // Get current pathname for creating URLs
  const pathname = usePathname();
  const searchParams = useSearchParams();

  // Get translations
  const dictionary = i18n.getDictionary(lang);
  const previous = dictionary.common?.pagination?.previous || "Previous";
  const next = dictionary.common?.pagination?.next || "Next";
  const showingText =
    dictionary.common?.pagination?.showing || "Showing {count} of {total} {items}";

  // Create URL for a specific page
  const createPageUrl = (page: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set("page", page.toString());
    if (search) params.set("search", search);

    // Use provided basePath or current pathname
    const path = basePath || pathname;
    return `${path}?${params.toString()}`;
  };

  // Format the showing text with the actual values
  const formatShowingText = (count: number, total: number) => {
    return showingText
      .replace("{count}", count.toString())
      .replace("{total}", total.toString())
      .replace("{items}", itemName);
  };

  // Don't show pagination if there's only one page
  if (totalPages <= 1) {
    return (
      <div className="flex justify-between w-full">
        <div className="text-sm text-muted-foreground">
          {formatShowingText(totalItems, totalItems)}
        </div>
      </div>
    );
  }

  const currentCount = Math.min(pageSize, totalItems - (currentPage - 1) * pageSize);

  return (
    <div className="flex justify-between w-full items-center">
      <div className="text-sm text-muted-foreground">
        {formatShowingText(currentCount, totalItems)}
      </div>

      <ShadcnPagination>
        <PaginationContent>
          {/* Previous button */}
          <PaginationItem>
            {currentPage > 1 ? (
              <PaginationLink
                aria-label="Go to previous page"
                size="default"
                className="gap-1 px-2.5 sm:pl-2.5"
                href={createPageUrl(currentPage - 1)}
              >
                <ChevronLeftIcon />
                <span className="hidden sm:block">{previous}</span>
              </PaginationLink>
            ) : (
              <PaginationLink
                aria-label="Go to previous page"
                size="default"
                className="gap-1 px-2.5 sm:pl-2.5 pointer-events-none opacity-50"
                href="#"
              >
                <ChevronLeftIcon />
                <span className="hidden sm:block">{previous}</span>
              </PaginationLink>
            )}
          </PaginationItem>

          {/* First page */}
          <PaginationItem>
            <PaginationLink href={createPageUrl(1)} isActive={currentPage === 1}>
              1
            </PaginationLink>
          </PaginationItem>

          {/* Last page if more than 1 page */}
          {totalPages > 1 && (
            <PaginationItem>
              <PaginationLink
                href={createPageUrl(totalPages)}
                isActive={currentPage === totalPages}
              >
                {totalPages}
              </PaginationLink>
            </PaginationItem>
          )}

          {/* Next button */}
          <PaginationItem>
            {currentPage < totalPages ? (
              <PaginationLink
                aria-label="Go to next page"
                size="default"
                className="gap-1 px-2.5 sm:pr-2.5"
                href={createPageUrl(currentPage + 1)}
              >
                <span className="hidden sm:block">{next}</span>
                <ChevronRightIcon />
              </PaginationLink>
            ) : (
              <PaginationLink
                aria-label="Go to next page"
                size="default"
                className="gap-1 px-2.5 sm:pr-2.5 pointer-events-none opacity-50"
                href="#"
              >
                <span className="hidden sm:block">{next}</span>
                <ChevronRightIcon />
              </PaginationLink>
            )}
          </PaginationItem>
        </PaginationContent>
      </ShadcnPagination>
    </div>
  );
}
