---
name: Bug Report
about: Report a bug or issue in the RQRSDA platform
title: '[BUG] '
labels: bug
assignees: ''
---

## Description
A clear and concise description of the bug.

## Steps to Reproduce
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

## Expected Behavior
A clear and concise description of what you expected to happen.

## Actual Behavior
A clear and concise description of what actually happened.

## Screenshots
If applicable, add screenshots to help explain your problem.

## Environment
- Browser: [e.g. Chrome, Safari]
- Version: [e.g. 22]
- OS: [e.g. Windows, macOS]
- Device: [e.g. Desktop, iPhone]

## Possible Solution
If you have suggestions on how to fix the bug, please describe them here.

## Implementation
- [ ] Identify root cause
- [ ] Write test that reproduces the bug
- [ ] Fix the bug
- [ ] Verify fix works correctly

## Testing
- [ ] Verify fix with unit tests
- [ ] Verify fix with integration tests
- [ ] Verify no regressions

## Review
- [ ] Code review
- [ ] Address feedback

## Integration
- [ ] Merge to main branch
- [ ] Deploy changes
- [ ] Update documentation if needed

## Acceptance Criteria
- Bug is fixed and does not reoccur
- All tests pass
- No regressions introduced
