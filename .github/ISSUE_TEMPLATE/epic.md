---
name: Epic
about: Create a new epic to group related features and tasks
title: '[EPIC] '
labels: epic
assignees: ''
---

## Epic Description
A high-level description of this epic and its overall purpose.

## Requirements Traceability
List the specific requirements from the requirements document that this epic addresses:

- REQ-001: Requirement description
- REQ-002: Requirement description
- REQ-003: Requirement description

## Objectives
- Primary objective 1
- Primary objective 2
- Primary objective 3

## Success Metrics
How will we measure the success of this epic?

## Timeline
- Estimated start date:
- Estimated completion date:

## Dependencies
List any dependencies on other epics or external factors:

- Dependency 1
- Dependency 2

## Stakeholders
Who are the key stakeholders for this epic?

## Risks and Mitigation
Identify potential risks and mitigation strategies:

| Risk | Impact | Mitigation |
|------|--------|------------|
| Risk 1 | High/Medium/Low | Strategy to mitigate |
| Risk 2 | High/Medium/Low | Strategy to mitigate |

## Acceptance Criteria
High-level criteria that define when this epic is considered complete:

- [ ] Criterion 1
- [ ] Criterion 2
- [ ] Criterion 3
