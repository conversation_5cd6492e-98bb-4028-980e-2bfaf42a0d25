name: Test Coverage

on:
  pull_request:
    branches: [ main ]
  push:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '20'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run tests with coverage
      run: npm run test:coverage
      
    - name: Upload coverage report to Codecov
      uses: codecov/codecov-action@v3
      
    - name: Add Coverage PR Comment
      uses: MishaKav/jest-coverage-comment@main
      with:
        coverage-summary-path: ./coverage/coverage-summary.json
        title: 'Test Coverage Report'
        summary-title: '💯 Test Coverage'
        badge-title: 'Coverage'
        hide-comment: false
        create-new-comment: false
        hide-summary: false
